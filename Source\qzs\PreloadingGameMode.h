// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/GameMode.h"
#include "Engine/StreamableManager.h"
#include "PreloadingGameMode.generated.h"

// 进度状态枚举
UENUM(BlueprintType)
enum class EProgressState : uint8
{
	NotStarted		UMETA(DisplayName = "Not Started"),
	InProgress		UMETA(DisplayName = "In Progress"),
	Completed		UMETA(DisplayName = "Completed")
};

// 偏差状态枚举
UENUM(BlueprintType)
enum class EDeviationState : uint8
{
	NotStarted			UMETA(DisplayName = "Not Started"),
	InProgress			UMETA(DisplayName = "In Progress"),
	Completed			UMETA(DisplayName = "Completed"),
	NotStartedOverdue	UMETA(DisplayName = "Not Started Overdue"),
	InProgressOverdue	UMETA(DisplayName = "In Progress Overdue"),
	CompletedOverdue	UMETA(DisplayName = "Completed Overdue")
};

// 任务信息结构体
USTRUCT(BlueprintType)
struct FTaskInfo
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	FString ConstructCode;

	UPROPERTY(BlueprintReadWrite)
	FDateTime ActualStartDate;

	UPROPERTY(BlueprintReadWrite)
	FDateTime ActualEndDate;

	UPROPERTY(BlueprintReadWrite)
	FDateTime PlanStartDate;

	UPROPERTY(BlueprintReadWrite)
	FDateTime PlanEndDate;

	UPROPERTY(BlueprintReadWrite)
	FDateTime MonthPlanStartDate;

	UPROPERTY(BlueprintReadWrite)
	FDateTime MonthPlanEndDate;

	UPROPERTY(BlueprintReadWrite)
	FDateTime YearPlanStartDate;

	UPROPERTY(BlueprintReadWrite)
	FDateTime YearPlanEndDate;

	// 构造函数
	FTaskInfo()
	{
		ConstructCode = TEXT("");
		ActualStartDate = FDateTime::MinValue();
		ActualEndDate = FDateTime::MinValue();
		PlanStartDate = FDateTime::MinValue();
		PlanEndDate = FDateTime::MinValue();
		MonthPlanStartDate = FDateTime::MinValue();
		MonthPlanEndDate = FDateTime::MinValue();
		YearPlanStartDate = FDateTime::MinValue();
		YearPlanEndDate = FDateTime::MinValue();
	}
};

// 进度记录结构体
USTRUCT(BlueprintType)
struct FProgressRecord
{
	GENERATED_BODY()

	UPROPERTY(BlueprintReadWrite)
	FDateTime CurrentDate;

	UPROPERTY(BlueprintReadWrite)
	FString ConstructCode;

	UPROPERTY(BlueprintReadWrite)
	EProgressState TotalActualState;

	UPROPERTY(BlueprintReadWrite)
	EProgressState TotalPlanState;

	UPROPERTY(BlueprintReadWrite)
	EProgressState MonthPlanState;

	UPROPERTY(BlueprintReadWrite)
	EProgressState YearPlanState;

	UPROPERTY(BlueprintReadWrite)
	EDeviationState DeviationState;

	// 构造函数
	FProgressRecord()
	{
		CurrentDate = FDateTime::Now();
		ConstructCode = TEXT("");
		TotalActualState = EProgressState::NotStarted;
		TotalPlanState = EProgressState::NotStarted;
		MonthPlanState = EProgressState::NotStarted;
		YearPlanState = EProgressState::NotStarted;
		DeviationState = EDeviationState::NotStarted;
	}
};

DECLARE_DYNAMIC_MULTICAST_DELEGATE(FPreloadingStatusNotificationDelegate);

/**
 *
 */
UCLASS()
class QZS_API APreloadingGameMode : public AGameMode
{
	GENERATED_BODY()

public:
	void NotifyPreloadingStatus() {
		if (OnPreloadingStatus.IsBound()) {
			OnPreloadingStatus.Broadcast();
		}
	}

	UPROPERTY(BlueprintReadWrite)
	FString LevelNameAfterPreloading = "NewWorld";

	UFUNCTION(BlueprintCallable)
	void StartPreload();
	UFUNCTION(BlueprintCallable)
	static void SetWindowMinimize();
	UFUNCTION(BlueprintCallable)
	void InitProgressDB();

	// 状态计算函数
	UFUNCTION(BlueprintCallable)
	static EProgressState CalcState(const FDateTime& CurrentDate, const FDateTime& StartDate, const FDateTime& EndDate);

	UFUNCTION(BlueprintCallable)
	static EDeviationState CalcDeviationState(const FDateTime& CurrentDate, const FDateTime& ActualStartDate,
		const FDateTime& ActualEndDate, const FDateTime& PlanStartDate, const FDateTime& PlanEndDate,
		const FDateTime& MonthPlanStartDate, const FDateTime& MonthPlanEndDate,
		const FDateTime& YearPlanStartDate, const FDateTime& YearPlanEndDate);

	// 存储进度记录的数组
	UPROPERTY(BlueprintReadOnly)
	TArray<FProgressRecord> ProgressRecords;
	UFUNCTION(BlueprintCallable)
	static void OpenFolder(FString path);
	UFUNCTION(BlueprintCallable)
	static void OpenBrowser(FString path);
	UFUNCTION(BlueprintCallable)
	TArray<FVector> GetRandomVerticesFromStaticMesh(UStaticMeshComponent* StaticMeshComponent, int32 NumVerticesToGet);
	UFUNCTION(BlueprintCallable)
	static int FindApp(FString path);
	
	// Multi-Dynamic delegate to notify Blueprint that preloading done
	UPROPERTY(BlueprintAssignable)
	FPreloadingStatusNotificationDelegate OnPreloadingStatus;
	


	UFUNCTION(BlueprintCallable)
	void ForceQuitGame();

	UFUNCTION(BlueprintCallable)
	void CheckAndKillCefHelperProcess();


	UFUNCTION(BlueprintCallable)
	void LoadLevelAsync();

	UFUNCTION(BlueprintPure)
	float GetLoadingProgress();

	FStreamableManager StreamableManager;
	TSharedPtr<FStreamableHandle> LoadingHandle;
};
