// Fill out your copyright notice in the Description page of Project Settings.


#include "PreloadingGameMode.h"
#include "BIMAssetManager.h"
#include "Engine/GameEngine.h"
#include "EngineGlobals.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "StaticMeshResources.h"

#include <stdio.h>
#include <stdlib.h>
#include "HAL/PlatformProcess.h"
#include "HAL/FileManager.h"
#include "Misc/Paths.h"

#include "Windows/AllowWindowsPlatformTypes.h"
#include "Windows/PreWindowsApi.h"
#include <windows.h> //冲突头文件
#include <shellapi.h>
#include <tchar.h>
#include "Windows/PostWindowsApi.h"
#include "Windows/HideWindowsPlatformTypes.h"
#include <string>

#include "Engine/LevelStreamingDynamic.h"
#include "Kismet/GameplayStatics.h"
#include <tlhelp32.h>
using namespace std;


void APreloadingGameMode::ForceQuitGame()
{

	// 杀死 cefhelper.exe
	FPlatformProcess::ExecProcess(TEXT("taskkill"), TEXT("/f /im cefhelper.exe"), nullptr, nullptr, nullptr);
	FString ScriptPath = FPaths::ProjectDir() / TEXT("clean_saved.bat");
    FPlatformProcess::CreateProc(*ScriptPath, nullptr, true, false, false, nullptr, 0, nullptr, nullptr);
	// 获取本地AppData路径
	// FString LocalAppDataPath = FPlatformProcess::UserSettingsDir();

	// // 构建完整路径
	// FString SavedPath = FPaths::Combine(LocalAppDataPath, TEXT("qzs"), TEXT("Saved"));
	// UE_LOG(LogTemp, Warning, TEXT("SavedPath SavedPath: %s"), *SavedPath);

	// // 规范化路径
	// FPaths::NormalizeFilename(SavedPath);
	// UE_LOG(LogTemp, Warning, TEXT("SavedPath NormalizeFilename: %s"), *SavedPath);
	// // 检查目录是否存在
	// if (IFileManager::Get().DirectoryExists(*SavedPath))
	// {
	// 	// 删除目录及其所有内容
	// 	IFileManager::Get().DeleteDirectory(*SavedPath, true, true);

	// 	UE_LOG(LogTemp, Log, TEXT("SavedPath 已删除目录: %s"), *SavedPath);
	// }
	// else
	// {
	// 	UE_LOG(LogTemp, Warning, TEXT("SavedPath 目录不存在: %s"), *SavedPath);
	// }

	HANDLE hProcess = GetCurrentProcess();
	TerminateProcess(hProcess, 0);

	// 延迟2秒执行逻辑
	// FTimerHandle TimerHandle;
	// GetWorld()->GetTimerManager().SetTimer(TimerHandle, this, &APreloadingGameMode::CheckAndKillCefHelperProcess, 2.0f, false);
}


void APreloadingGameMode::CheckAndKillCefHelperProcess()
{
#if PLATFORM_WINDOWS
	// 查找cefhelper.exe进程
	HANDLE hSnapshot = CreateToolhelp32Snapshot(TH32CS_SNAPPROCESS, 0);
	if (hSnapshot == INVALID_HANDLE_VALUE)
	{
		return;
	}

	PROCESSENTRY32 pe;
	pe.dwSize = sizeof(PROCESSENTRY32);

	if (Process32First(hSnapshot, &pe))
	{
		do
		{
			if (_wcsicmp(pe.szExeFile, L"cefhelper.exe") == 0)
			{
				// 找到cefhelper.exe进程，杀死它
				HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0, pe.th32ProcessID);
				if (hProcess != NULL)
				{
					TerminateProcess(hProcess, 0);
					CloseHandle(hProcess);
				}
				break;
			}
		} while (Process32Next(hSnapshot, &pe));
	}

	CloseHandle(hSnapshot);
#endif
}

// 开始异步加载关卡
void APreloadingGameMode::LoadLevelAsync()
{
	// 定义需要加载的资源路径
	TArray<FSoftObjectPath> AssetPaths;
	AssetPaths.Add(FSoftObjectPath("/BIMModelDLC/Maps/Map_BGMODEL"));
	AssetPaths.Add(FSoftObjectPath("/BIMModelDLC/Maps/Map_JianCe"));
	AssetPaths.Add(FSoftObjectPath("/BIMModelDLC/Maps/Map_BaoZhaTu"));
	AssetPaths.Add(FSoftObjectPath("/Game/StarterContent/Maps/Map_BGMODEL_V3"));

	// 使用 FStreamableManager 异步加载关卡
	LoadingHandle = StreamableManager.RequestAsyncLoad(AssetPaths);
}

// 获取当前加载进度
float APreloadingGameMode::GetLoadingProgress()
{
	if (LoadingHandle.IsValid())
	{
		return LoadingHandle->GetProgress(); // 如果没有加载句柄，返回 1 表示已完成
	}
	return 1.0f; // 如果没有加载句柄，返回 1 表示已完成
}

void APreloadingGameMode::StartPreload()
{
	UBIMAssetManager* AssetManager = (UBIMAssetManager*)GEngine->AssetManager;

	AssetManager->StartPreload(this);
}

void APreloadingGameMode::SetWindowMinimize() {
	GEngine->GameViewport->GetWindow()->Minimize();
}
// 状态计算函数 - 对应Python的calc_state函数
EProgressState APreloadingGameMode::CalcState(const FDateTime& CurrentDate, const FDateTime& StartDate, const FDateTime& EndDate)
{
	// 检查StartDate是否有效（不是MinValue）
	if (StartDate != FDateTime::MinValue())
	{
		if (CurrentDate < StartDate)
		{
			return EProgressState::NotStarted;
		}
		else
		{
			// 检查EndDate是否有效
			if (EndDate != FDateTime::MinValue())
			{
				if (CurrentDate < EndDate)
				{
					return EProgressState::InProgress;
				}
				else
				{
					return EProgressState::Completed;
				}
			}
			else
			{
				return EProgressState::InProgress;
			}
		}
	}
	else
	{
		return EProgressState::NotStarted;
	}
}

// 偏差状态计算函数 - 对应Python的calc_deviation_state函数
EDeviationState APreloadingGameMode::CalcDeviationState(const FDateTime& CurrentDate, const FDateTime& ActualStartDate,
	const FDateTime& ActualEndDate, const FDateTime& PlanStartDate, const FDateTime& PlanEndDate,
	const FDateTime& MonthPlanStartDate, const FDateTime& MonthPlanEndDate,
	const FDateTime& YearPlanStartDate, const FDateTime& YearPlanEndDate)
{
	// 检查ActualStartDate是否有效
	if (ActualStartDate != FDateTime::MinValue())
	{
		// 检查ActualEndDate是否有效（已完成）
		if (ActualEndDate != FDateTime::MinValue())
		{
			// 已完成，检查是否超期
			if (MonthPlanEndDate != FDateTime::MinValue() && MonthPlanEndDate < ActualEndDate)
			{
				return EDeviationState::CompletedOverdue;
			}
			else if (YearPlanEndDate != FDateTime::MinValue() && YearPlanEndDate < ActualEndDate)
			{
				return EDeviationState::CompletedOverdue;
			}
			else if (PlanEndDate != FDateTime::MinValue() && PlanEndDate < ActualEndDate)
			{
				return EDeviationState::CompletedOverdue;
			}
			else
			{
				return EDeviationState::Completed;
			}
		}
		else
		{
			// 运行中，检查是否超期
			if (MonthPlanEndDate != FDateTime::MinValue() && MonthPlanEndDate < CurrentDate)
			{
				return EDeviationState::InProgressOverdue;
			}
			else if (YearPlanEndDate != FDateTime::MinValue() && YearPlanEndDate < CurrentDate)
			{
				return EDeviationState::InProgressOverdue;
			}
			else if (PlanEndDate != FDateTime::MinValue() && PlanEndDate < CurrentDate)
			{
				return EDeviationState::InProgressOverdue;
			}
			else
			{
				return EDeviationState::InProgress;
			}
		}
	}
	else
	{
		// 未启动，检查是否超期
		if (MonthPlanEndDate != FDateTime::MinValue() && MonthPlanEndDate < CurrentDate)
		{
			return EDeviationState::NotStartedOverdue;
		}
		else if (YearPlanEndDate != FDateTime::MinValue() && YearPlanEndDate < CurrentDate)
		{
			return EDeviationState::NotStartedOverdue;
		}
		else if (PlanEndDate != FDateTime::MinValue() && PlanEndDate < CurrentDate)
		{
			return EDeviationState::NotStartedOverdue;
		}
		else
		{
			return EDeviationState::NotStarted;
		}
	}
}

void APreloadingGameMode::InitProgressDB()
{
	UE_LOG(LogTemp, Warning, TEXT("开始初始化进度数据库"));

	// 清空之前的记录
	ProgressRecords.Empty();

	// 模拟从API获取的日期范围（这里用测试数据代替）
	TArray<FDateTime> DateRange;
	FDateTime StartDate = FDateTime::Now() - FTimespan::FromDays(30); // 30天前开始
	FDateTime EndDate = FDateTime::Now() + FTimespan::FromDays(30);   // 30天后结束

	// 生成日期范围（每天一个记录）
	for (FDateTime CurrentDate = StartDate; CurrentDate <= EndDate; CurrentDate += FTimespan::FromDays(1))
	{
		DateRange.Add(CurrentDate);
	}

	// 模拟从API获取的任务数据（这里用测试数据代替）
	TArray<FTaskInfo> Tasks;

	// 创建一些测试任务
	FTaskInfo Task1;
	Task1.ConstructCode = TEXT("TASK001");
	Task1.ActualStartDate = FDateTime::Now() - FTimespan::FromDays(10);
	Task1.ActualEndDate = FDateTime::MinValue(); // 未完成
	Task1.PlanStartDate = FDateTime::Now() - FTimespan::FromDays(15);
	Task1.PlanEndDate = FDateTime::Now() + FTimespan::FromDays(5);
	Task1.MonthPlanStartDate = FDateTime::Now() - FTimespan::FromDays(20);
	Task1.MonthPlanEndDate = FDateTime::Now() + FTimespan::FromDays(10);
	Task1.YearPlanStartDate = FDateTime::Now() - FTimespan::FromDays(30);
	Task1.YearPlanEndDate = FDateTime::Now() + FTimespan::FromDays(60);
	Tasks.Add(Task1);

	FTaskInfo Task2;
	Task2.ConstructCode = TEXT("TASK002");
	Task2.ActualStartDate = FDateTime::Now() - FTimespan::FromDays(20);
	Task2.ActualEndDate = FDateTime::Now() - FTimespan::FromDays(5); // 已完成
	Task2.PlanStartDate = FDateTime::Now() - FTimespan::FromDays(25);
	Task2.PlanEndDate = FDateTime::Now() - FTimespan::FromDays(10);
	Task2.MonthPlanStartDate = FDateTime::Now() - FTimespan::FromDays(30);
	Task2.MonthPlanEndDate = FDateTime::Now() - FTimespan::FromDays(15);
	Task2.YearPlanStartDate = FDateTime::Now() - FTimespan::FromDays(40);
	Task2.YearPlanEndDate = FDateTime::Now() + FTimespan::FromDays(30);
	Tasks.Add(Task2);

	FTaskInfo Task3;
	Task3.ConstructCode = TEXT("TASK003");
	Task3.ActualStartDate = FDateTime::MinValue(); // 未开始
	Task3.ActualEndDate = FDateTime::MinValue();
	Task3.PlanStartDate = FDateTime::Now() + FTimespan::FromDays(5);
	Task3.PlanEndDate = FDateTime::Now() + FTimespan::FromDays(20);
	Task3.MonthPlanStartDate = FDateTime::Now() + FTimespan::FromDays(3);
	Task3.MonthPlanEndDate = FDateTime::Now() + FTimespan::FromDays(25);
	Task3.YearPlanStartDate = FDateTime::Now();
	Task3.YearPlanEndDate = FDateTime::Now() + FTimespan::FromDays(90);
	Tasks.Add(Task3);

	UE_LOG(LogTemp, Warning, TEXT("开始计算进度状态"));

	// 遍历日期范围和任务，计算状态
	for (int32 DateIndex = 0; DateIndex < DateRange.Num(); DateIndex++)
	{
		FDateTime CurrentDate = DateRange[DateIndex];

		for (int32 TaskIndex = 0; TaskIndex < Tasks.Num(); TaskIndex++)
		{
			const FTaskInfo& Task = Tasks[TaskIndex];

			// 计算各种状态
			EProgressState TotalActualState = CalcState(CurrentDate, Task.ActualStartDate, Task.ActualEndDate);
			EProgressState TotalPlanState = CalcState(CurrentDate, Task.PlanStartDate, Task.PlanEndDate);
			EProgressState MonthPlanState = CalcState(CurrentDate, Task.MonthPlanStartDate, Task.MonthPlanEndDate);
			EProgressState YearPlanState = CalcState(CurrentDate, Task.YearPlanStartDate, Task.YearPlanEndDate);
			EDeviationState DeviationState = CalcDeviationState(CurrentDate, Task.ActualStartDate, Task.ActualEndDate,
				Task.PlanStartDate, Task.PlanEndDate, Task.MonthPlanStartDate, Task.MonthPlanEndDate,
				Task.YearPlanStartDate, Task.YearPlanEndDate);

			// 创建进度记录
			FProgressRecord Record;
			Record.CurrentDate = CurrentDate;
			Record.ConstructCode = Task.ConstructCode;
			Record.TotalActualState = TotalActualState;
			Record.TotalPlanState = TotalPlanState;
			Record.MonthPlanState = MonthPlanState;
			Record.YearPlanState = YearPlanState;
			Record.DeviationState = DeviationState;

			// 添加到记录数组
			ProgressRecords.Add(Record);
		}
	}

	UE_LOG(LogTemp, Warning, TEXT("进度数据库初始化完成，共生成 %d 条记录"), ProgressRecords.Num());
}

void APreloadingGameMode::OpenFolder(FString path) {
	char path2[100];
	for (int i = 0; i <= path.Len() - 1; i++) {
		path2[i] = path[i];
	}
	// system("start \"\" \"G:\\new_312\\bim-constructmgmt-ue-new\"");
	system(path2);
}

void APreloadingGameMode::OpenBrowser(FString path) {
	try {
		UE_LOG(LogTemp, Error, TEXT("OpenBrowser Start"));
		string orig = TCHAR_TO_UTF8(*path);
		UE_LOG(LogTemp, Error, TEXT("string orig:%s"), *path);
		
		size_t origsize = orig.length() + 1;
		UE_LOG(LogTemp, Error, TEXT("origsize"));
		
		size_t convertedChars = 0;
		UE_LOG(LogTemp, Error, TEXT("convertedChars"));
		
		wchar_t* wcstring = (wchar_t*)malloc(sizeof(wchar_t) * (origsize));
		UE_LOG(LogTemp, Error, TEXT("wcstring"));
		
		if (wcstring != NULL) {
			mbstowcs_s(&convertedChars, wcstring, origsize, orig.c_str(), _TRUNCATE);
			UE_LOG(LogTemp, Error, TEXT("mbstowcs_s"));
			
			HINSTANCE result = ShellExecute(NULL, _T("open"), wcstring, NULL, NULL, SW_SHOW);
			INT_PTR resultCode = reinterpret_cast<INT_PTR>(result); // 使用 INT_PTR 替代 int
			if (resultCode <= 32) {
				UE_LOG(LogTemp, Error, TEXT("ShellExecute failed with error code: %lld"), resultCode);
			} else {
				UE_LOG(LogTemp, Error, TEXT("OpenBrowser End"));
			}
			
			free(wcstring); // 释放内存
		} else {
			UE_LOG(LogTemp, Error, TEXT("Failed to allocate memory for wcstring"));
		}
	} catch (const std::exception& e) {
		UE_LOG(LogTemp, Error, TEXT("Caught exception: %s"), *FString(e.what()));
	}
	// catch (exception e) {
	// 	// 处理异常
	// 	UE_LOG(LogTemp, Warning, TEXT("StaticMesh is null or does not have render data."));
	// 	UE_LOG(LogTemp, Error, TEXT("Caught exception: %s"), *FString(e.what()));
	// }
}

TArray<FVector> APreloadingGameMode::GetRandomVerticesFromStaticMesh(UStaticMeshComponent* StaticMeshComponent, int32 NumVerticesToGet)
{

	TArray<FVector3f> VectorPositions;
	TArray<FVector> WorldPositions;
	if (!StaticMeshComponent || !StaticMeshComponent->GetStaticMesh()->GetRenderData())
	{
		UE_LOG(LogTemp, Warning, TEXT("StaticMesh is null or does not have render data."));
		return WorldPositions;
	}

	FTransform WorldTransform = StaticMeshComponent->GetComponentTransform();

	// 获取LOD 0的数据
	FStaticMeshLODResources& LODResource = StaticMeshComponent->GetStaticMesh()->GetRenderData()->LODResources[0];

	// 获取顶点缓冲区
	FPositionVertexBuffer& VertexBuffer = LODResource.VertexBuffers.PositionVertexBuffer;

	// 顶点总数
	int32 TotalVertexCount = VertexBuffer.GetNumVertices();
	// NumVerticesToGet = TotalVertexCount; //强制赋值，使得结果返回所有顶点坐标

	UE_LOG(LogTemp, Log, TEXT("Total number of vertices: %d"), TotalVertexCount);

	if (NumVerticesToGet > TotalVertexCount)
	{
		UE_LOG(LogTemp, Warning, TEXT("Requested more vertices than are available in the mesh."));
		return WorldPositions;
	}
	// 随机选择N个顶点
	TArray<int32> SelectedIndices;
	while (SelectedIndices.Num() < NumVerticesToGet)
	{
		int32 RandomIndex = FMath::RandRange(0, TotalVertexCount - 1);
		if (!SelectedIndices.Contains(RandomIndex))
		{
			SelectedIndices.Add(RandomIndex);
			UE_LOG(LogTemp, Warning, TEXT("zzzRandomIndex"), RandomIndex);
			FVector3f VertexPosition = VertexBuffer.VertexPosition(RandomIndex);
			UE_LOG(LogTemp, Log, TEXT("Random Vertex %d: (%f, %f, %f)"), RandomIndex, VertexPosition.X, VertexPosition.Y, VertexPosition.Z);
			VectorPositions.Add(VertexPosition);
			FVector Vector(VertexPosition.X, VertexPosition.Y, VertexPosition.Z);
			FVector WorldPosition = WorldTransform.TransformPosition(Vector); // 将局部坐标转换为世界坐标
			WorldPositions.Add(WorldPosition);
		}
	}
	return WorldPositions;
}


int APreloadingGameMode::FindApp(FString path) {
	string orig = TCHAR_TO_UTF8(*path);
	// string orig = "http://************:18201/api/gx/QualityData2_detail/%E5%88%86%E9%A1%B9%E5%B7%A5%E7%A8%8B%E7%8E%B0%E5%9C%BA%E6%A3%80%E6%B5%8B%E8%AE%B0%E5%BD%95%E8%A1%A8.aip?token=RH-l1qIkG-V5mH9DtR6wZgd1g8dNug3rxdKfNyEP7C0&id=40894";
	size_t origsize = orig.length() + 1;
	const size_t newsize = 100;
	size_t convertedChars = 0;
	wchar_t* wcstring = (wchar_t*)malloc(sizeof(wchar_t) * (orig.length() - 1));
	mbstowcs_s(&convertedChars, wcstring, origsize, orig.c_str(), _TRUNCATE);
	FDateTime Time = FDateTime::Now();
	int64 Timestamp = Time.ToUnixTimestamp();
	UE_LOG(LogTemp, Warning, TEXT("IN StartPreload: %d"), Timestamp);
	HINSTANCE hNewExe = ShellExecute(NULL, _T("open"), wcstring, NULL, NULL, 0);
	//printf("return value:%d\n", orig);
	//SearchPath(NULL, L"winaip", L".exe", 100, NULL, NULL);
	return reinterpret_cast<intptr_t>(hNewExe);

}
