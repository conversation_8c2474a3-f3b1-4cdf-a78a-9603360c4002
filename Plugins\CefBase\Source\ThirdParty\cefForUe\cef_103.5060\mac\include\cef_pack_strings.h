// Copyright (c) 2023 <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file is generated by the make_pack_header.py tool.
//

#ifndef CEF_INCLUDE_CEF_PACK_STRINGS_H_
#define CEF_INCLUDE_CEF_PACK_STRINGS_H_
#pragma once

// ---------------------------------------------------------------------------
// From blink_accessibility_strings.h:

#define IDS_AX_UNLABELED_IMAGE_ROLE_DESCRIPTION 31520
#define IDS_AX_IMAGE_ELIGIBLE_FOR_ANNOTATION 31523
#define IDS_AX_IMAGE_ANNOTATION_PENDING 31524
#define IDS_AX_IMAGE_ANNOTATION_ADULT 31525
#define IDS_AX_IMAGE_ANNOTATION_NO_DESCRIPTION 31526
#define IDS_AX_IMAGE_ANNOTATION_OCR_CONTEXT 31527
#define IDS_AX_IMAGE_ANNOTATION_DESCRIPTION_CONTEXT 31528
#define IDS_AX_IMAGE_ANNOTATION_ICON_PLUS 31529
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_BACKWARD 31530
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_FORWARD 31531
#define IDS_AX_IMAGE_ANNOTATION_ICON_CALL 31532
#define IDS_AX_IMAGE_ANNOTATION_ICON_CHAT 31533
#define IDS_AX_IMAGE_ANNOTATION_ICON_CHECK 31534
#define IDS_AX_IMAGE_ANNOTATION_ICON_X 31535
#define IDS_AX_IMAGE_ANNOTATION_ICON_DELETE 31536
#define IDS_AX_IMAGE_ANNOTATION_ICON_EDIT 31537
#define IDS_AX_IMAGE_ANNOTATION_ICON_EMOJI 31538
#define IDS_AX_IMAGE_ANNOTATION_ICON_END_CALL 31539
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_DOWNWARD 31540
#define IDS_AX_IMAGE_ANNOTATION_ICON_HEART 31541
#define IDS_AX_IMAGE_ANNOTATION_ICON_HOME 31542
#define IDS_AX_IMAGE_ANNOTATION_ICON_INFO 31543
#define IDS_AX_IMAGE_ANNOTATION_ICON_LAUNCH_APPS 31544
#define IDS_AX_IMAGE_ANNOTATION_ICON_THUMBS_UP 31545
#define IDS_AX_IMAGE_ANNOTATION_ICON_THREE_BARS 31546
#define IDS_AX_IMAGE_ANNOTATION_ICON_THREE_DOTS 31547
#define IDS_AX_IMAGE_ANNOTATION_ICON_NOTIFICATIONS 31548
#define IDS_AX_IMAGE_ANNOTATION_ICON_PAUSE 31549
#define IDS_AX_IMAGE_ANNOTATION_ICON_PLAY 31550
#define IDS_AX_IMAGE_ANNOTATION_ICON_REFRESH 31551
#define IDS_AX_IMAGE_ANNOTATION_ICON_MAGNIFYING_GLASS 31552
#define IDS_AX_IMAGE_ANNOTATION_ICON_SEND 31553
#define IDS_AX_IMAGE_ANNOTATION_ICON_SETTINGS 31554
#define IDS_AX_IMAGE_ANNOTATION_ICON_SHARE 31555
#define IDS_AX_IMAGE_ANNOTATION_ICON_STAR 31556
#define IDS_AX_IMAGE_ANNOTATION_ICON_TAKE_PHOTO 31557
#define IDS_AX_IMAGE_ANNOTATION_ICON_TIME 31558
#define IDS_AX_IMAGE_ANNOTATION_ICON_VIDEOCAM 31559
#define IDS_AX_IMAGE_ANNOTATION_ICON_EXPAND 31560
#define IDS_AX_IMAGE_ANNOTATION_ICON_CONTRACT 31561
#define IDS_AX_IMAGE_ANNOTATION_ICON_GOOGLE 31562
#define IDS_AX_IMAGE_ANNOTATION_ICON_TWITTER 31563
#define IDS_AX_IMAGE_ANNOTATION_ICON_FACEBOOK 31564
#define IDS_AX_IMAGE_ANNOTATION_ICON_ASSISTANT 31565
#define IDS_AX_IMAGE_ANNOTATION_ICON_WEATHER 31566
#define IDS_AX_IMAGE_ANNOTATION_ICON_SHOPPING_CART 31567
#define IDS_AX_IMAGE_ANNOTATION_ICON_UPLOAD 31568
#define IDS_AX_IMAGE_ANNOTATION_ICON_QUESTION 31569
#define IDS_AX_IMAGE_ANNOTATION_ICON_MIC 31570
#define IDS_AX_IMAGE_ANNOTATION_ICON_MIC_MUTE 31571
#define IDS_AX_IMAGE_ANNOTATION_ICON_GALLERY 31572
#define IDS_AX_IMAGE_ANNOTATION_ICON_COMPASS 31573
#define IDS_AX_IMAGE_ANNOTATION_ICON_PEOPLE 31574
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_UPWARD 31575
#define IDS_AX_IMAGE_ANNOTATION_ICON_ENVELOPE 31576
#define IDS_AX_IMAGE_ANNOTATION_ICON_EMOJI_FACE 31577
#define IDS_AX_IMAGE_ANNOTATION_ICON_PAPERCLIP 31578
#define IDS_AX_IMAGE_ANNOTATION_ICON_CAST 31579
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_UP 31580
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_DOWN 31581
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_STATE 31582
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_MUTE 31583
#define IDS_AX_IMAGE_ANNOTATION_ICON_STOP 31584
#define IDS_AX_IMAGE_ANNOTATION_ICON_SHOPPING_BAG 31585
#define IDS_AX_IMAGE_ANNOTATION_ICON_LIST 31586
#define IDS_AX_IMAGE_ANNOTATION_ICON_LOCATION 31587
#define IDS_AX_IMAGE_ANNOTATION_ICON_CALENDAR 31588
#define IDS_AX_IMAGE_ANNOTATION_ICON_THUMBS_DOWN 31589
#define IDS_AX_IMAGE_ANNOTATION_ICON_HEADSET 31590
#define IDS_AX_IMAGE_ANNOTATION_ICON_REDO 31591
#define IDS_AX_IMAGE_ANNOTATION_ICON_UNDO 31592
#define IDS_AX_IMAGE_ANNOTATION_ICON_DOWNLOAD 31593
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_DOWNWARD 31594
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_UPWARD 31595
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_FORWARD 31596
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_BACKWARD 31597
#define IDS_AX_IMAGE_ANNOTATION_ICON_HISTORY 31598
#define IDS_AX_IMAGE_ANNOTATION_ICON_PERSON 31599
#define IDS_AX_IMAGE_ANNOTATION_ICON_HAPPY_FACE 31600
#define IDS_AX_IMAGE_ANNOTATION_ICON_SAD_FACE 31601
#define IDS_AX_IMAGE_ANNOTATION_ICON_MOON 31602
#define IDS_AX_IMAGE_ANNOTATION_ICON_CLOUD 31603
#define IDS_AX_IMAGE_ANNOTATION_ICON_SUN 31604

// ---------------------------------------------------------------------------
// From blink_strings.h:

#define IDS_DETAILS_WITHOUT_SUMMARY_LABEL 31620
#define IDS_FORM_CALENDAR_CLEAR 31621
#define IDS_FORM_CALENDAR_TODAY 31622
#define IDS_FORM_SUBMIT_LABEL 31623
#define IDS_FORM_INPUT_ALT 31624
#define IDS_FORM_RESET_LABEL 31625
#define IDS_FORM_FILE_BUTTON_LABEL 31626
#define IDS_FORM_MULTIPLE_FILES_BUTTON_LABEL 31627
#define IDS_FORM_FILE_NO_FILE_LABEL 31628
#define IDS_FORM_FILE_MULTIPLE_UPLOAD 31629
#define IDS_FORM_OTHER_COLOR_LABEL 31630
#define IDS_FORM_OTHER_DATE_LABEL 31631
#define IDS_FORM_OTHER_MONTH_LABEL 31632
#define IDS_FORM_OTHER_WEEK_LABEL 31633
#define IDS_FORM_PLACEHOLDER_FOR_DAY_OF_MONTH_FIELD 31634
#define IDS_FORM_PLACEHOLDER_FOR_MONTH_FIELD 31635
#define IDS_FORM_PLACEHOLDER_FOR_YEAR_FIELD 31636
#define IDS_FORM_SELECT_MENU_LIST_TEXT 31637
#define IDS_FORM_THIS_MONTH_LABEL 31638
#define IDS_FORM_THIS_WEEK_LABEL 31639
#define IDS_FORM_WEEK_NUMBER_LABEL 31640
#define IDS_AX_CALENDAR_SHOW_DATE_PICKER 31641
#define IDS_AX_CALENDAR_SHOW_DATE_TIME_LOCAL_PICKER 31642
#define IDS_AX_CALENDAR_SHOW_MONTH_PICKER 31643
#define IDS_AX_CALENDAR_SHOW_TIME_PICKER 31644
#define IDS_AX_CALENDAR_SHOW_WEEK_PICKER 31645
#define IDS_AX_CALENDAR_SHOW_MONTH_SELECTOR 31646
#define IDS_AX_CALENDAR_SHOW_NEXT_MONTH 31647
#define IDS_AX_CALENDAR_SHOW_PREVIOUS_MONTH 31648
#define IDS_AX_CALENDAR_WEEK_DESCRIPTION 31649
#define IDS_AX_COLOR_EDIT_BLUE 31650
#define IDS_AX_COLOR_EDIT_GREEN 31651
#define IDS_AX_COLOR_EDIT_HEXADECIMAL 31652
#define IDS_AX_COLOR_EDIT_HUE 31653
#define IDS_AX_COLOR_EDIT_LIGHTNESS 31654
#define IDS_AX_COLOR_EDIT_RED 31655
#define IDS_AX_COLOR_EDIT_SATURATION 31656
#define IDS_AX_COLOR_EYEDROPPER 31657
#define IDS_AX_COLOR_FORMAT_TOGGLER 31658
#define IDS_AX_COLOR_HUE_SLIDER 31659
#define IDS_AX_COLOR_WELL 31660
#define IDS_AX_COLOR_WELL_ROLEDESCRIPTION 31661
#define IDS_SYSTEM_COLOR_CHOOSER 31662
#define IDS_AX_AM_PM_FIELD_TEXT 31684
#define IDS_AX_DAY_OF_MONTH_FIELD_TEXT 31685
#define IDS_AX_HOUR_FIELD_TEXT 31686
#define IDS_AX_MEDIA_DEFAULT 31687
#define IDS_AX_MEDIA_AUDIO_ELEMENT 31688
#define IDS_AX_MEDIA_VIDEO_ELEMENT 31689
#define IDS_AX_MEDIA_MUTE_BUTTON 31690
#define IDS_AX_MEDIA_UNMUTE_BUTTON 31691
#define IDS_AX_MEDIA_PLAY_BUTTON 31692
#define IDS_AX_MEDIA_PAUSE_BUTTON 31693
#define IDS_AX_MEDIA_CURRENT_TIME_DISPLAY 31694
#define IDS_AX_MEDIA_TIME_REMAINING_DISPLAY 31695
#define IDS_AX_MEDIA_ENTER_FULL_SCREEN_BUTTON 31696
#define IDS_AX_MEDIA_EXIT_FULL_SCREEN_BUTTON 31697
#define IDS_AX_MEDIA_DISPLAY_CUT_OUT_FULL_SCREEN_BUTTON 31698
#define IDS_AX_MEDIA_ENTER_PICTURE_IN_PICTURE_BUTTON 31699
#define IDS_AX_MEDIA_EXIT_PICTURE_IN_PICTURE_BUTTON 31700
#define IDS_AX_MEDIA_LOADING_PANEL 31701
#define IDS_AX_MEDIA_SHOW_CLOSED_CAPTIONS_MENU_BUTTON 31702
#define IDS_AX_MEDIA_HIDE_CLOSED_CAPTIONS_MENU_BUTTON 31703
#define IDS_AX_MEDIA_SHOW_PLAYBACK_SPEED_MENU_BUTTON 31704
#define IDS_AX_MEDIA_HIDE_PLAYBACK_SPEED_MENU_BUTTON 31705
#define IDS_AX_MEDIA_CAST_OFF_BUTTON 31706
#define IDS_AX_MEDIA_CAST_ON_BUTTON 31707
#define IDS_AX_MEDIA_DOWNLOAD_BUTTON 31708
#define IDS_AX_MEDIA_OVERFLOW_BUTTON 31709
#define IDS_AX_MEDIA_AUDIO_ELEMENT_HELP 31710
#define IDS_AX_MEDIA_VIDEO_ELEMENT_HELP 31711
#define IDS_AX_MEDIA_AUDIO_SLIDER_HELP 31712
#define IDS_AX_MEDIA_VIDEO_SLIDER_HELP 31713
#define IDS_AX_MEDIA_VOLUME_SLIDER_HELP 31714
#define IDS_AX_MEDIA_CURRENT_TIME_DISPLAY_HELP 31715
#define IDS_AX_MEDIA_TIME_REMAINING_DISPLAY_HELP 31716
#define IDS_AX_MEDIA_OVERFLOW_BUTTON_HELP 31717
#define IDS_AX_MILLISECOND_FIELD_TEXT 31718
#define IDS_AX_MINUTE_FIELD_TEXT 31719
#define IDS_AX_MONTH_FIELD_TEXT 31720
#define IDS_AX_SECOND_FIELD_TEXT 31721
#define IDS_AX_WEEK_OF_YEAR_FIELD_TEXT 31722
#define IDS_AX_YEAR_FIELD_TEXT 31723
#define IDS_AX_OBJECT_SELECTED 31724
#define IDS_AX_OBJECT_NOT_SELECTED 31725
#define IDS_VIEW_SOURCE_LINE_WRAP 31726
#define IDS_FORM_INPUT_WEEK_TEMPLATE 31727
#define IDS_FORM_VALIDATION_VALUE_MISSING_MULTIPLE_FILE 31728
#define IDS_FORM_VALIDATION_TYPE_MISMATCH 31729
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY 31730
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY_DOMAIN 31731
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY_LOCAL 31732
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_DOMAIN 31733
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_DOTS 31734
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_LOCAL 31735
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_NO_AT_SIGN 31736
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_MULTIPLE_EMAIL 31737
#define IDS_FORM_VALIDATION_VALUE_NOT_EQUAL 31738
#define IDS_FORM_VALIDATION_VALUE_NOT_EQUAL_DATETIME 31739
#define IDS_FORM_VALIDATION_RANGE_UNDERFLOW 31740
#define IDS_FORM_VALIDATION_RANGE_UNDERFLOW_DATETIME 31741
#define IDS_FORM_VALIDATION_RANGE_OVERFLOW 31742
#define IDS_FORM_VALIDATION_RANGE_OVERFLOW_DATETIME 31743
#define IDS_FORM_VALIDATION_REVERSED_RANGE_OUT_OF_RANGE_TIME 31744
#define IDS_FORM_VALIDATION_RANGE_INVALID_DATETIME 31745
#define IDS_FORM_VALIDATION_BAD_INPUT_DATETIME 31746
#define IDS_FORM_VALIDATION_BAD_INPUT_NUMBER 31747
#define IDS_FORM_VALIDATION_VALUE_MISSING 31748
#define IDS_FORM_VALIDATION_VALUE_MISSING_CHECKBOX 31749
#define IDS_FORM_VALIDATION_VALUE_MISSING_FILE 31750
#define IDS_FORM_VALIDATION_VALUE_MISSING_RADIO 31751
#define IDS_FORM_VALIDATION_VALUE_MISSING_SELECT 31752
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL 31753
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_URL 31754
#define IDS_FORM_VALIDATION_PATTERN_MISMATCH 31755
#define IDS_FORM_VALIDATION_STEP_MISMATCH 31756
#define IDS_FORM_VALIDATION_STEP_MISMATCH_CLOSE_TO_LIMIT 31757
#define IDS_FORM_VALIDATION_TOO_LONG 31758
#define IDS_FORM_VALIDATION_TOO_SHORT 31759
#define IDS_FORM_VALIDATION_TOO_SHORT_PLURAL 31760
#define IDS_MEDIA_SESSION_FILE_SOURCE 31761
#define IDS_MEDIA_OVERFLOW_MENU_CLOSED_CAPTIONS 31762
#define IDS_MEDIA_OVERFLOW_MENU_CLOSED_CAPTIONS_SUBMENU_TITLE 31763
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED 31764
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_SUBMENU_TITLE 31765
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_0_25X_TITLE 31766
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_0_5X_TITLE 31767
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_0_75X_TITLE 31768
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_NORMAL_TITLE 31769
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_1_25X_TITLE 31770
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_1_5X_TITLE 31771
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_1_75X_TITLE 31772
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_2X_TITLE 31773
#define IDS_MEDIA_OVERFLOW_MENU_CAST 31774
#define IDS_MEDIA_OVERFLOW_MENU_ENTER_FULLSCREEN 31775
#define IDS_MEDIA_OVERFLOW_MENU_EXIT_FULLSCREEN 31776
#define IDS_MEDIA_OVERFLOW_MENU_MUTE 31777
#define IDS_MEDIA_OVERFLOW_MENU_UNMUTE 31778
#define IDS_MEDIA_OVERFLOW_MENU_PLAY 31779
#define IDS_MEDIA_OVERFLOW_MENU_PAUSE 31780
#define IDS_MEDIA_OVERFLOW_MENU_DOWNLOAD 31781
#define IDS_MEDIA_OVERFLOW_MENU_ENTER_PICTURE_IN_PICTURE 31782
#define IDS_MEDIA_OVERFLOW_MENU_EXIT_PICTURE_IN_PICTURE 31783
#define IDS_MEDIA_PICTURE_IN_PICTURE_INTERSTITIAL_TEXT 31784
#define IDS_MEDIA_REMOTING_CAST_TEXT 31785
#define IDS_MEDIA_REMOTING_CAST_TO_UNKNOWN_DEVICE_TEXT 31786
#define IDS_MEDIA_REMOTING_STOP_TEXT 31787
#define IDS_MEDIA_REMOTING_STOP_BY_PLAYBACK_QUALITY_TEXT 31788
#define IDS_MEDIA_REMOTING_STOP_BY_ERROR_TEXT 31789
#define IDS_MEDIA_SCRUBBING_MESSAGE_TEXT 31790
#define IDS_MEDIA_TRACKS_NO_LABEL 31791
#define IDS_MEDIA_TRACKS_OFF 31792
#define IDS_PLUGIN_INITIALIZATION_ERROR 31793
#define IDS_MEDIA_PLAYBACK_ERROR 31794
#define IDS_UNITS_KIBIBYTES 31795
#define IDS_UNITS_MEBIBYTES 31796
#define IDS_UNITS_GIBIBYTES 31797
#define IDS_UNITS_TEBIBYTES 31798
#define IDS_UNITS_PEBIBYTES 31799
#define CONTENT_INVALID_TRUE 31800
#define CONTENT_INVALID_SPELLING 31801
#define CONTENT_INVALID_GRAMMAR 31802

// ---------------------------------------------------------------------------
// From cef_strings.h:

#define IDS_TEXT_FILES 64000
#define IDS_CONTENT_CONTEXT_NO_SPELLING_SUGGESTIONS 64001

// ---------------------------------------------------------------------------
// From chromium_strings.h:

#define IDS_RELAUNCH_CONFIRMATION_DIALOG_TITLE 400
#define IDS_SETTINGS_ABOUT_PROGRAM 401
#define IDS_SETTINGS_GET_HELP_USING_CHROME 402
#define IDS_SETTINGS_UPGRADE_UPDATING 403
#define IDS_SETTINGS_UPGRADE_UPDATING_PERCENT 404
#define IDS_SETTINGS_UPGRADE_SUCCESSFUL_RELAUNCH 405
#define IDS_SETTINGS_UPGRADE_UP_TO_DATE 406
#define IDS_SETTINGS_GOOGLE_PAYMENTS_CACHED 411
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_OFFLINE 412
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_SIGNED_OUT 413
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_NO_PASSWORDS 414
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_QUOTA_LIMIT_GOOGLE_ACCOUNT 415
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_QUOTA_LIMIT 416
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_GENERIC 417
#define IDS_SETTINGS_NO_COMPROMISED_CREDENTIALS_LABEL 418
#define IDS_SETTINGS_SIGNED_OUT_USER_LABEL 419
#define IDS_SETTINGS_SIGNED_OUT_USER_HAS_COMPROMISED_CREDENTIALS_LABEL 420
#define IDS_SETTINGS_WEAK_PASSWORDS_DESCRIPTION_GENERATION 421
#define IDS_SETTINGS_COMPROMISED_EDIT_DISCLAIMER_DESCRIPTION 422
#define IDS_SETTINGS_DEFAULT_BROWSER_DEFAULT 423
#define IDS_SETTINGS_DEFAULT_BROWSER_MAKE_DEFAULT 424
#define IDS_SETTINGS_DEFAULT_BROWSER_ERROR 425
#define IDS_SETTINGS_DEFAULT_BROWSER_SECONDARY 426
#define IDS_SETTINGS_SPELLING_PREF_DESC 428
#define IDS_SETTINGS_RESTART_TO_APPLY_CHANGES 429
#define IDS_SETTINGS_SIGNIN_ALLOWED 430
#define IDS_SETTINGS_SIGNIN_ALLOWED_DESC 431
#define IDS_SETTINGS_SITE_SETTINGS_PDFS_BLOCKED 432
#define IDS_SETTINGS_PRIVACY_GUIDE_PROMO_BODY 433
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_FEATURE_DESCRIPTION1 434
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_FEATURE_DESCRIPTION2 435
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_FEATURE_DESCRIPTION3 436
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_STANDARD_PROTECTION_FEATURE_DESCRIPTION2 437
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_STANDARD_PROTECTION_PRIVACY_DESCRIPTION1 438
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_PRIVACY_SANDBOX_SUB_LABEL 439
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_WAA_SUB_LABEL 440
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_PRIVACY_DESCRIPTION2 441
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_TITLE 442
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_DATA_TYPES 443
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_DATA_USAGE 444
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_DATA_MANAGEMENT 445
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_DATA_TYPES 446
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_DATA_USAGE 447
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_DESCRIPTION 448
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_DESCRIPTION_TRIALS_OFF 449
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_DESCRIPTION_LISTS_EMPTY 450
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_TITLE 451
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_LEARN_MORE_1 452
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_LEARN_MORE_2 453
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_LEARN_MORE_3 454
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_LEARN_MORE_1 455
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_DESCRIPTION 456
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_DESCRIPTION_TRIALS_OFF 457
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_BEFORE 458
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_FAILED_OFFLINE 459
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_FAILED 460
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_UNKNOWN 461
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_SIGNED_OUT 462
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_DISABLED 463
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_TWO 464
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_BULLET_TWO 465
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE_WARNING_WITH_COUNTS_SINGULAR 466
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE_WARNING_WITH_COUNTS_PLURAL 467
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE_WARNING_WITHOUT_COUNTS 468
#define IDS_SETTINGS_CUSTOMIZE_YOUR_CHROME_PROFILE 469
#define IDS_SETTING_NAME_YOUR_PROFILE 470
#define IDS_SETTINGS_PEOPLE_SIGN_IN_PROMPT_SECONDARY_WITH_ACCOUNT 471
#define IDS_SETTINGS_SYNC_DATA_ENCRYPTED_TEXT 473
#define IDS_SETTINGS_SYNC_DISCONNECT_TITLE 474
#define IDS_DRIVE_SUGGEST_PREF_DESC 475
#define IDS_SETTINGS_SYNC_SIGN_IN_PROMPT_WITH_NO_ACCOUNT 476
#define IDS_SETTINGS_SYSTEM_BACKGROUND_APPS_LABEL 479
#define IDS_SETTINGS_RESET_PROFILE_FEEDBACK 480
#define IDS_PRODUCT_NAME 103
#define IDS_SHORT_PRODUCT_NAME 481
#define IDS_FIRST_RUN_DIALOG_WINDOW_TITLE 487
#define IDS_PRODUCT_LOGO_ENTERPRISE_ALT_TEXT 491
#define IDS_TASK_MANAGER_TITLE 493
#define IDS_SESSION_CRASHED_VIEW_UMA_OPTIN 494
#define IDS_BROWSER_WINDOW_TITLE_FORMAT 495
#define IDS_CAPTIVE_PORTAL_BROWSER_WINDOW_TITLE_FORMAT 496
#define IDS_ACCESSIBLE_BROWSER_WINDOW_TITLE_FORMAT 497
#define IDS_ACCESSIBLE_BETA_BROWSER_WINDOW_TITLE_FORMAT 498
#define IDS_ACCESSIBLE_DEV_BROWSER_WINDOW_TITLE_FORMAT 499
#define IDS_ACCESSIBLE_CANARY_BROWSER_WINDOW_TITLE_FORMAT 500
#define IDS_ABOUT_VERSION_COMPANY_NAME 501
#define IDS_ABOUT_VERSION_COPYRIGHT 502
#define IDS_ABOUT_TERMS_OF_SERVICE 506
#define IDS_MAC_10_11_OBSOLETE 507
#define IDS_MAC_10_12_OBSOLETE 508
#define IDS_ACCNAME_APP 313
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_HELP_TEXT_NO_DEVICES 512
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_HELP_TEXT_NO_DEVICES_ORIGIN 513
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_DEVICE_NOT_FOUND 514
#define IDS_FR_CUSTOMIZE_DEFAULT_BROWSER 518
#define IDS_STATUS_TRAY_KEEP_CHROME_RUNNING_IN_BACKGROUND 519
#define IDS_CANT_WRITE_USER_DIRECTORY_SUMMARY 520
#define IDS_PROFILE_TOO_NEW_ERROR 522
#define IDS_PREFERENCES_UNREADABLE_ERROR 523
#define IDS_PREFERENCES_CORRUPT_ERROR 524
#define IDS_CRASH_RECOVERY_TITLE 527
#define IDS_PASSWORD_GENERATION_PROMPT 529
#define IDS_PASSWORD_MANAGER_ONBOARDING_DETAILS_C 530
#define IDS_PASSWORD_MANAGER_TITLE_BRAND 531
#define IDS_PASSWORD_BUBBLES_PASSWORD_MANAGER_LINK_TEXT_SAVING_ON_DEVICE 532
#define IDS_PASSWORD_MANAGER_SAVE_PASSWORD_SIGNED_OUT_MESSAGE_DESCRIPTION 533
#define IDS_PASSWORD_MANAGER_UPDATE_PASSWORD_SIGNED_OUT_MESSAGE_DESCRIPTION 534
#define IDS_PASSWORDS_PAGE_AUTHENTICATION_PROMPT 535
#define IDS_PASSWORDS_PAGE_COPY_AUTHENTICATION_PROMPT 536
#define IDS_PASSWORDS_PAGE_EDIT_AUTHENTICATION_PROMPT 537
#define IDS_PASSWORDS_PAGE_EXPORT_AUTHENTICATION_PROMPT 538
#define IDS_DEFAULT_BROWSER_INFOBAR_TEXT 555
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_NOTIFICATION_DESCRIPTION 556
#define IDS_DOWNLOAD_BUBBLE_DANGEROUS_FILE 562
#define IDS_DOWNLOAD_BUBBLE_MALICIOUS_URL_BLOCKED 563
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_UNKNOWN_SOURCE 564
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_ENCRYPTED 565
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_MALWARE 566
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_TOO_BIG 567
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_DEEP_SCANNING_PROMPT 568
#define IDS_DOWNLOAD_STATUS_CRX_INSTALL_RUNNING 569
#define IDS_PROMPT_DOWNLOAD_CHANGES_SETTINGS 570
#define IDS_PROMPT_MALICIOUS_DOWNLOAD_URL 571
#define IDS_PROMPT_MALICIOUS_DOWNLOAD_CONTENT 572
#define IDS_BLOCK_REASON_DANGEROUS_DOWNLOAD 573
#define IDS_BLOCK_REASON_UNWANTED_DOWNLOAD 574
#define IDS_ABANDON_DOWNLOAD_DIALOG_BROWSER_MESSAGE 575
#define IDS_QUIT_WITH_APPS_TITLE 334
#define IDS_MISSING_GOOGLE_API_KEYS 576
#define IDS_EXTENSION_INSTALLED_HEADING 577
#define IDS_EXTENSION_UNINSTALL_PROMPT_REMOVE_DATA_CHECKBOX 578
#define IDS_EXTENSION_ALERT_ITEM_BLOCKLISTED_MALWARE 579
#define IDS_EXTENSIONS_ALERT_ITEM_BLOCKLISTED_MALWARE_TITLE 580
#define IDS_EXTENSIONS_INCOGNITO_WARNING 581
#define IDS_EXTENSIONS_UNINSTALL 582
#define IDS_EXTENSIONS_SHORTCUT_SCOPE_IN_CHROME 583
#define IDS_EXTENSIONS_MULTIPLE_UNSUPPORTED_DISABLED_BODY 584
#define IDS_EXTENSIONS_SINGLE_UNSUPPORTED_DISABLED_BODY 585
#define IDS_APPMENU_TOOLTIP 265
#define IDS_APPMENU_TOOLTIP_UPDATE_AVAILABLE 586
#define IDS_APPMENU_TOOLTIP_ALERT 587
#define IDS_OPEN_IN_CHROME 588
#define IDS_ABOUT 302
#define IDS_RELAUNCH_TO_UPDATE 589
#define IDS_APP_MENU_PRODUCT_NAME 102
#define IDS_HELPER_NAME 590
#define IDS_SHORT_HELPER_NAME 591
#define IDS_CHROME_SIGNIN_TITLE 592
#define IDS_PROFILES_DICE_SYNC_PROMO 593
#define IDS_ONE_CLICK_SIGNIN_DIALOG_TITLE_NEW 594
#define IDS_ONE_CLICK_SIGNIN_DIALOG_MESSAGE_NEW 595
#define IDS_SYNC_WRONG_EMAIL 596
#define IDS_SYNC_USED_PROFILE_ERROR 597
#define IDS_ENTERPRISE_SIGNIN_TITLE 598
#define IDS_ENTERPRISE_SIGNIN_EXPLANATION_WITHOUT_PROFILE_CREATION 599
#define IDS_ENTERPRISE_SIGNIN_EXPLANATION_WITH_PROFILE_CREATION 600
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_TITLE 601
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_CREATION 602
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_ISOLATION_NOTICE 603
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_EXPLANATION 604
#define IDS_ABOUT_BROWSER_SWITCH_DESCRIPTION_UNKNOWN_BROWSER 605
#define IDS_ABOUT_BROWSER_SWITCH_DESCRIPTION_KNOWN_BROWSER 606
#define IDS_NTP_CUSTOMIZE_BUTTON_LABEL 607
#define IDS_SIGNIN_EMAIL_CONFIRMATION_TITLE 608
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_CREATE_BUBBLE_TITLE 609
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_SWITCH_BUBBLE_TITLE 610
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_SWITCH_BUBBLE_DESC 611
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_CONSUMER_BUBBLE_DESC 612
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_CONSUMER_BUBBLE_DESC_MANAGED_DEVICE 613
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_ENTERPRISE_BUBBLE_DESC_MANAGED_DEVICE 614
#define IDS_PROFILE_CUSTOMIZATION_TEXT 615
#define IDS_APP_SHORTCUTS_SUBDIR_NAME 622
#define IDS_APP_SHORTCUTS_SUBDIR_NAME_CANARY 623
#define IDS_MEDIA_STREAM_STATUS_TRAY_TEXT_AUDIO_AND_VIDEO 627
#define IDS_MEDIA_STREAM_STATUS_TRAY_TEXT_AUDIO_ONLY 628
#define IDS_MEDIA_STREAM_STATUS_TRAY_TEXT_VIDEO_ONLY 629
#define IDS_PROFILE_IN_USE_POSIX 630
#define IDS_FIRSTRUN_DLG_MAC_SET_DEFAULT_BROWSER_LABEL 631
#define IDS_LOGIN_POD_USER_REMOVE_WARNING_SYNC 632
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_INTRO_TITLE 633
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_INTRO_TEXT 634
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_YOUR_CHROME_TITLE 635
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_YOUR_CHROME_TEXT 636
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_GUEST_TEXT 637
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_FRIENDS_TEXT 638
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_OUTRO_TEXT 639
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_OUTRO_ADD_USER 640
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_START_PAGES_SPECIFIC 641
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_START_PAGES 642
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_START_PAGES 643
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_START_AND_HOME 644
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_START_AND_SEARCH 645
#define IDS_WEBSTORE_APP_DESCRIPTION 246
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_TEXT 654
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_TEXT_ONCE 655
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_TEXT 656
#define IDS_CONTENT_CONTEXT_OPENLINKNEWTAB_INAPP 657
#define IDS_CONTENT_CONTEXT_OPENLINKOFFTHERECORD_INAPP 658
#define IDS_UPDATE_RECOMMENDED_DIALOG_TITLE 659
#define IDS_UPDATE_RECOMMENDED 660
#define IDS_RELAUNCH_AND_UPDATE 661
#define IDS_REINSTALL_APP 662
#define IDS_UPGRADE_BUBBLE_MENU_ITEM 663
#define IDS_UPGRADE_BUBBLE_TITLE 664
#define IDS_UPGRADE_BUBBLE_TEXT 665
#define IDS_SYNC_ERROR_USER_MENU_UPGRADE_BUTTON 666
#define IDS_SYNC_UPGRADE_CLIENT 667
#define IDS_SYNC_UPGRADE_CLIENT_BUTTON 668
#define IDS_RECOVERY_BUBBLE_TITLE 669
#define IDS_RUN_RECOVERY 670
#define IDS_RECOVERY_BUBBLE_TEXT 671
#define IDS_CRITICAL_NOTIFICATION_TITLE 672
#define IDS_CRITICAL_NOTIFICATION_TITLE_ALTERNATE 673
#define IDS_CRITICAL_NOTIFICATION_TEXT 674
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_TAB 675
#define IDS_RUNTIME_PERMISSION_OS_REASON_TEXT 688
#define IDS_WELCOME_HEADER 691
#define IDS_RELAUNCH_RECOMMENDED_TITLE 693
#define IDS_RELAUNCH_RECOMMENDED_BODY 694
#define IDS_RELAUNCH_REQUIRED_TITLE_DAYS 695
#define IDS_RELAUNCH_REQUIRED_TITLE_HOURS 696
#define IDS_RELAUNCH_REQUIRED_TITLE_MINUTES 697
#define IDS_RELAUNCH_REQUIRED_TITLE_SECONDS 698
#define IDS_RELAUNCH_REQUIRED_BODY 699
#define IDS_ENTERPRISE_STARTUP_CLOUD_POLICY_ENROLLMENT_TOOLTIP 700
#define IDS_ENTERPRISE_STARTUP_CLOUD_POLICY_ENROLLMENT_ERROR 701
#define IDS_ENTERPRISE_STARTUP_RELAUNCH_BUTTON 702
#define IDS_DESKTOP_MEDIA_PICKER_TITLE_WEB_CONTENTS_ONLY 703
#define IDS_HATS_BUBBLE_TITLE 704
#define IDS_PROFILE_PICKER_MAIN_VIEW_TITLE 710
#define IDS_PROFILE_PICKER_MAIN_VIEW_TITLE_V2 711
#define IDS_PROFILE_PICKER_MAIN_VIEW_SUBTITLE 712
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_PROFILE_TYPE_CHOICE_SUBTITLE 713
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_PROFILE_TYPE_CHOICE_TITLE 714
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_TITLE 715
#define IDS_PROFILE_PICKER_IPH_FOR_PROFILES_TEXT 716
#define IDS_PROFILE_PICKER_IPH_FOR_ADD_PROFILE_TEXT 717
#define IDS_PROFILE_PICKER_PROFILE_SWITCH_TITLE 718
#define IDS_PROFILE_PICKER_PROFILE_SWITCH_SUBTITLE 719
#define IDS_PROFILE_SWITCH_PROMO 720
#define IDS_PROFILE_SWITCH_PROMO_SCREENREADER 721
#define IDS_CHROMELABS_RELAUNCH_FOOTER_MESSAGE 723
#define IDS_BLUETOOTH_DEVICE_CHOOSER_AUTHORIZE_BLUETOOTH 724
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_NOTIFICATION_TITLE 725
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_MESSAGE_TITLE 726
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_MESSAGE_DESCRIPTION 727
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_MESSAGE_ACCEPT 728
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_SUBTITLE 729
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_DESCRIPTION_2 730
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_LABEL 731
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_HEADER 732
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_BULLET_POINT_1 733
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_BULLET_POINT_2 734
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_BULLET_POINT_3 735
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_BULLET_POINT_1 736
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_BULLET_POINT_2 737
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BOTTOM_SUMMARY 738
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_SUBTITLE 739
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_DESCRIPTION_2 740
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BOTTOM_SUMMARY 741

// ---------------------------------------------------------------------------
// From components_chromium_strings.h:

#define IDS_ERRORPAGES_SUGGESTION_NETWORK_PREDICTION_BODY 24030
#define IDS_ERRORPAGES_SUGGESTION_FIREWALL_CONFIG_HEADER 24031
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_CLIENT 24032
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_ENROLLMENT_CHECK_PENDING 24033
#define IDS_ERRORPAGES_SUGGESTION_PROXY_DISABLE_PLATFORM 24034
#define IDS_FLAGS_UI_RELAUNCH_NOTICE 24035
#define IDS_DEPRECATED_FEATURES_RELAUNCH_NOTICE 24036
#define IDS_CRASH_DISABLED_MESSAGE 24037
#define IDS_SHORT_PRODUCT_LOGO_ALT_TEXT 24038
#define IDS_VERSION_UI_LICENSE 24039
#define IDS_PAGE_INFO_INTERNAL_PAGE 24040
#define IDS_SESSION_CRASHED_VIEW_MESSAGE 24041

// ---------------------------------------------------------------------------
// From components_strings.h:

#define IDS_ASH_ARC_APP_COMPAT_DISABLED_COMPAT_MODE_BUTTON_TOOLTIP_PHONE 24070
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_TITLE 24071
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_BODY 24072
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_ACCEPT 24073
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_DONT_ASK_ME 24074
#define IDS_ASH_ARC_NEARBY_SHARE_FILE_PREPARATION_PROGRESS 24075
#define IDS_ASH_ARC_NEARBY_SHARE_FILES_PREPARATION_PROGRESS 24076
#define IDS_ASH_ARC_NEARBY_SHARE_ERROR_DIALOG_MESSAGE 24077
#define IDS_ASH_ARC_NEARBY_SHARE_LOW_DISK_SPACE_DIALOG_TITLE 24078
#define IDS_ASH_ARC_NEARBY_SHARE_LOW_DISK_SPACE_DIALOG_MESSAGE 24079
#define IDS_ASH_ARC_NEARBY_SHARE_LOW_DISK_SPACE_DIALOG_STORAGE_BUTTON 24080
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_PHONE 24081
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_TABLET 24082
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_RESIZABLE 24083
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_RESIZE_SETTINGS 24084
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_TITLE 24085
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_TITLE 24086
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_BODY 24087
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_BODY_UNRESIZABLE 24088
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_CLOSE 24089
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_LINK 24090
#define IDS_ARC_COMPAT_MODE_DISABLE_RESIZE_LOCK_TOAST 24091
#define IDS_ARC_GHOST_WINDOW_APP_LAUNCHING_ICON 24092
#define IDS_ARC_GHOST_WINDOW_APP_LAUNCHING_THROBBER 24093
#define IDS_AUTOFILL_ASSISTANT_PAYMENT_INFO_CONFIRM 24094
#define IDS_AUTOFILL_ASSISTANT_PAYMENT_INFORMATION_MISSING 24095
#define IDS_AUTOFILL_ASSISTANT_DEFAULT_ERROR 24096
#define IDS_AUTOFILL_ASSISTANT_LOADING 24097
#define IDS_AUTOFILL_ASSISTANT_GIVE_UP 24098
#define IDS_AUTOFILL_ASSISTANT_MAYBE_GIVE_UP 24099
#define IDS_AUTOFILL_ASSISTANT_DETAILS_DIFFER 24100
#define IDS_AUTOFILL_ASSISTANT_CONTINUE_BUTTON 24101
#define IDS_AUTOFILL_ASSISTANT_STOPPED 24102
#define IDS_AUTOFILL_ASSISTANT_SEND_FEEDBACK 24103
#define IDS_AUTOFILL_NO_THANKS_DESKTOP_LOCAL_SAVE 24104
#define IDS_AUTOFILL_NO_THANKS_DESKTOP_UPLOAD_SAVE 24105
#define IDS_AUTOFILL_FIELD_LABEL_PHONE 24112
#define IDS_AUTOFILL_FIELD_LABEL_BILLING_ADDRESS 24113
#define IDS_AUTOFILL_SAVE_CARD_BUBBLE_LOCAL_SAVE_ACCEPT 24115
#define IDS_AUTOFILL_SAVE_CARD_BUBBLE_UPLOAD_SAVE_ACCEPT 24116
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_CONTINUE 24123
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_LOCAL 24124
#define IDS_AUTOFILL_FIX_FLOW_PROMPT_SAVE_CARD_LABEL 24125
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_TO_CLOUD 24126
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_TO_CLOUD_V3 24127
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_TO_CLOUD_V4 24128
#define IDS_AUTOFILL_CARD_SAVED 24129
#define IDS_AUTOFILL_MANAGE_CARDS 24130
#define IDS_AUTOFILL_DONE 24131
#define IDS_AUTOFILL_FAILURE_BUBBLE_TITLE 24132
#define IDS_AUTOFILL_FAILURE_BUBBLE_EXPLANATION 24133
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION 24134
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_V3 24135
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_V3_WITH_NAME 24136
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_CARDHOLDER_NAME 24137
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_CARDHOLDER_NAME_TOOLTIP 24138
#define IDS_AUTOFILL_SAVE_CARD_CARDHOLDER_NAME_FIX_FLOW_HEADER 24139
#define IDS_AUTOFILL_SAVE_CARD_UPDATE_EXPIRATION_DATE_TITLE 24140
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_TOOLTIP 24143
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_AND_CARDHOLDER_NAME_TOOLTIP 24144
#define IDS_AUTOFILL_GOOGLE_PAY_LOGO_ACCESSIBLE_NAME 24145
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_ANIMATION_LABEL 24146
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_BUBBLE_TITLE 24147
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_BUBBLE_BUTTON_LABEL 24148
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_BUBBLE_BODY_TEXT 24149
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TITLE_OFFER 24150
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TITLE_DONE 24151
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TITLE_FIX 24152
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_CHECKBOX_UNCHECK_WARNING 24153
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_OFFER 24154
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_DONE 24155
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_ERROR 24156
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_FIX 24157
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_INVALID_CARD_REMOVED 24158
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_SAVE 24159
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_CANCEL 24160
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_DONE 24161
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_VIEW_CARDS 24162
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_LABEL_INVALID_CARDS 24163
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TRASH_CAN_BUTTON_TOOLTIP 24164
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_TRY_AGAIN_CVC 24165
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_TRY_AGAIN_CVC_AND_EXPIRATION_V2 24166
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_PERMANENT 24171
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_NETWORK 24172
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_TITLE 24173
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_TITLE_V2 24174
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_EXPIRED_TITLE 24175
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS 24176
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS_LOCAL_CARD 24177
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS_V2 24178
#define IDS_AUTOFILL_CARD_UNMASK_CVC_IMAGE_DESCRIPTION 24179
#define IDS_AUTOFILL_CARD_UNMASK_CONFIRM_BUTTON 24184
#define IDS_AUTOFILL_CARD_UNMASK_VERIFY_BUTTON 24185
#define IDS_AUTOFILL_CARD_UNMASK_EXPIRATION_MONTH 24186
#define IDS_AUTOFILL_CARD_UNMASK_EXPIRATION_YEAR 24187
#define IDS_AUTOFILL_CARD_UNMASK_VERIFICATION_IN_PROGRESS 24188
#define IDS_AUTOFILL_CARD_UNMASK_VERIFICATION_SUCCESS 24189
#define IDS_AUTOFILL_CARD_UNMASK_INVALID_EXPIRATION_DATE 24190
#define IDS_AUTOFILL_EXPIRATION_DATE_SEPARATOR 24191
#define IDS_AUTOFILL_CARD_UNMASK_NEW_CARD_LINK 24192
#define IDS_AUTOFILL_DIALOG_PLACEHOLDER_CVC 24193
#define IDS_AUTOFILL_CARD_UNMASK_PROGRESS_BAR_MESSAGE 24194
#define IDS_AUTOFILL_CARD_UNMASK_CANCEL_BUTTON_LABEL 24195
#define IDS_AUTOFILL_CARD_UNMASK_CONFIRMATION_MESSAGE 24196
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_TITLE 24197
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_TITLE_ERROR 24198
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_INSTRUCTION 24199
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_OK_BUTTON_LABEL 24200
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_INSTRUCTION_ERROR 24201
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_CANCEL_BUTTON_LABEL 24202
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_CANCEL_BUTTON_LABEL_ERROR 24203
#define IDS_AUTOFILL_WEBAUTHN_VERIFY_PENDING_DIALOG_TITLE 24204
#define IDS_AUTOFILL_WEBAUTHN_VERIFY_PENDING_DIALOG_CANCEL_BUTTON_LABEL 24205
#define IDS_AUTOFILL_WALLET_MANAGEMENT_LINK_TEXT 24206
#define IDS_AUTOFILL_FROM_GOOGLE_ACCOUNT_LONG 24207
#define IDS_AUTOFILL_CLOUD_TOKEN_DROPDOWN_OPTION_LABEL 24208
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_FALLBACK_ICON_TOOLTIP 24209
#define IDS_AUTOFILL_VIRTUAL_CARD_ENTRY_PREFIX 24210
#define IDS_AUTOFILL_VIRTUAL_CARD_ENTRY_PREFIX_TWO 24211
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_CONTENT_TITLE 24212
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_CONTENT_EXPLANATION 24213
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_OK_BUTTON_LABEL 24214
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_CANCEL_BUTTON_LABEL 24215
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_TITLE 24216
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_EDUCATIONAL_BODY_LABEL 24217
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_LEARN_MORE_LINK_LABEL 24218
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_CARD_NUMBER_LABEL 24219
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_EXP_DATE_LABEL 24220
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_CARDHOLDER_NAME_LABEL 24221
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_CVC_LABEL 24222
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_ICON_TOOLTIP 24223
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_BUTTON_TOOLTIP_NORMAL 24224
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_BUTTON_TOOLTIP_CLICKED 24225
#define IDS_AUTOFILL_VIRTUAL_CARD_SUGGESTION_IPH_BUBBLE_LABEL 24226
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_ACCEPT_BUTTON_LABEL 24227
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DIALOG_TITLE_LABEL 24228
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DIALOG_CONTENT_LABEL 24229
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DECLINE_BUTTON_LABEL_SKIP 24230
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DECLINE_BUTTON_LABEL_NO_THANKS 24231
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_LEARN_MORE_LINK_LABEL 24232
#define IDS_AUTOFILL_VIRTUAL_CARD_SUGGESTION_OPTION_VALUE 24233
#define IDS_AUTOFILL_VIRTUAL_CARD_TEMPORARY_ERROR_TITLE 24234
#define IDS_AUTOFILL_VIRTUAL_CARD_PERMANENT_ERROR_TITLE 24235
#define IDS_AUTOFILL_VIRTUAL_CARD_TEMPORARY_ERROR_DESCRIPTION 24236
#define IDS_AUTOFILL_VIRTUAL_CARD_PERMANENT_ERROR_DESCRIPTION 24237
#define IDS_AUTOFILL_VIRTUAL_CARD_NOT_ELIGIBLE_ERROR_TITLE 24238
#define IDS_AUTOFILL_VIRTUAL_CARD_NOT_ELIGIBLE_ERROR_DESCRIPTION 24239
#define IDS_AUTOFILL_ERROR_DIALOG_NEGATIVE_BUTTON_LABEL 24240
#define IDS_AUTOFILL_VIRTUAL_CARD_NUMBER_SWITCH_LABEL 24241
#define IDS_AUTOFILL_CARD_UNMASK_AUTHENTICATION_SELECTION_DIALOG_ISSUER_CONFIRMATION_TEXT 24244
#define IDS_AUTOFILL_AUTHENTICATION_MODE_TEXT_MESSAGE_LABEL 24245
#define IDS_AUTOFILL_CARD_UNMASK_AUTHENTICATION_SELECTION_DIALOG_CURRENT_INFO_NOT_SEEN_TEXT 24246
#define IDS_AUTOFILL_CARD_UNMASK_AUTHENTICATION_SELECTION_DIALOG_OK_BUTTON_LABEL 24247
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_TITLE 24248
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_FOOTER_MESSAGE 24249
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_NEW_CODE_MESSAGE 24250
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_TEXTFIELD_PLACEHOLDER_MESSAGE 24251
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_PENDING_MESSAGE 24252
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_VERIFICATION_CODE_EXPIRED_LABEL 24253
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_ENTER_CORRECT_CODE_LABEL 24254
#define IDS_AUTOFILL_SAVE_UPI_PROMPT_TITLE 24255
#define IDS_AUTOFILL_SAVE_UPI_PROMPT_ACCEPT 24256
#define IDS_AUTOFILL_SAVE_UPI_PROMPT_REJECT 24257
#define IDS_AUTOFILL_OFFERS_CASHBACK 24258
#define IDS_AUTOFILL_CARD_LINKED_OFFER_REMINDER_TITLE 24259
#define IDS_AUTOFILL_OFFERS_REMINDER_POSITIVE_BUTTON_LABEL 24260
#define IDS_AUTOFILL_OFFERS_REMINDER_DESCRIPTION_TEXT 24261
#define IDS_AUTOFILL_OFFERS_REMINDER_ICON_TOOLTIP_TEXT 24262
#define IDS_AUTOFILL_PROMO_CODE_OFFERS_REMINDER_TITLE 24264
#define IDS_AUTOFILL_PROMO_CODE_OFFER_BUTTON_TOOLTIP_NORMAL 24265
#define IDS_AUTOFILL_PROMO_CODE_OFFER_BUTTON_TOOLTIP_CLICKED 24266
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_TITLE 24267
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_CANT_FIND_CODE_MESSAGE 24268
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_OTP_INPUT_HINT 24269
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_POSITIVE_BUTTON_LABEL 24270
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_NEGATIVE_BUTTON_LABEL 24271
#define IDS_AUTOFILL_CLEAR_FORM_MENU_ITEM 24274
#define IDS_AUTOFILL_WARNING_INSECURE_CONNECTION 24276
#define IDS_AUTOFILL_WARNING_MIXED_FORM 24277
#define IDS_AUTOFILL_CREDIT_CARD_SIGNIN_PROMO 24278
#define IDS_AUTOFILL_DELETE_AUTOCOMPLETE_SUGGESTION_CONFIRMATION_BODY 24279
#define IDS_AUTOFILL_DELETE_CREDIT_CARD_SUGGESTION_CONFIRMATION_BODY 24280
#define IDS_AUTOFILL_DELETE_PROFILE_SUGGESTION_CONFIRMATION_BODY 24281
#define IDS_AUTOFILL_CC_AMEX 24282
#define IDS_AUTOFILL_CC_AMEX_SHORT 24283
#define IDS_AUTOFILL_CC_DINERS 24284
#define IDS_AUTOFILL_CC_DISCOVER 24285
#define IDS_AUTOFILL_CC_ELO 24286
#define IDS_AUTOFILL_CC_GOOGLE_PAY 24287
#define IDS_AUTOFILL_CC_JCB 24288
#define IDS_AUTOFILL_CC_MASTERCARD 24289
#define IDS_AUTOFILL_CC_MIR 24290
#define IDS_AUTOFILL_CC_TROY 24291
#define IDS_AUTOFILL_CC_UNION_PAY 24292
#define IDS_AUTOFILL_CC_VISA 24293
#define IDS_AUTOFILL_CC_GENERIC 24294
#define IDS_AUTOFILL_ADDRESS_SUMMARY_SEPARATOR 24295
#define IDS_AUTOFILL_FIELD_LABEL_STATE 24296
#define IDS_AUTOFILL_FIELD_LABEL_AREA 24297
#define IDS_AUTOFILL_FIELD_LABEL_COUNTY 24298
#define IDS_AUTOFILL_FIELD_LABEL_DEPARTMENT 24299
#define IDS_AUTOFILL_FIELD_LABEL_DISTRICT 24300
#define IDS_AUTOFILL_FIELD_LABEL_EMIRATE 24301
#define IDS_AUTOFILL_FIELD_LABEL_ISLAND 24302
#define IDS_AUTOFILL_FIELD_LABEL_PARISH 24303
#define IDS_AUTOFILL_FIELD_LABEL_PREFECTURE 24304
#define IDS_AUTOFILL_FIELD_LABEL_PROVINCE 24305
#define IDS_AUTOFILL_FIELD_LABEL_ZIP_CODE 24306
#define IDS_AUTOFILL_FIELD_LABEL_POSTAL_CODE 24307
#define IDS_AUTOFILL_HIDE_SUGGESTIONS 24308
#define IDS_AUTOFILL_MANAGE 24309
#define IDS_AUTOFILL_MANAGE_ADDRESSES 24310
#define IDS_AUTOFILL_MANAGE_PAYMENT_METHODS 24311
#define IDS_AUTOFILL_MANAGE_PASSWORDS 24312
#define IDS_AUTOFILL_SCAN_CREDIT_CARD 24313
#define IDS_AUTOFILL_SHOW_ALL_SAVED_FALLBACK 24314
#define IDS_AUTOFILL_SHOW_ACCOUNT_CARDS 24315
#define IDS_AUTOFILL_POPUP_ACCESSIBLE_NODE_DATA 24316
#define IDS_AUTOFILL_SUGGESTION_LABEL_SEPARATOR 24317
#define IDS_AUTOFILL_CREDIT_CARD_EXPIRATION_DATE_ABBR 24318
#define IDS_AUTOFILL_CREDIT_CARD_EXPIRATION_DATE_ABBR_V2 24319
#define IDS_AUTOFILL_CREDIT_CARD_TWO_LINE_LABEL_FROM_NAME 24320
#define IDS_AUTOFILL_CREDIT_CARD_TWO_LINE_LABEL_FROM_CARD_NUMBER 24321
#define IDS_AUTOFILL_LOADING_REGIONS 24322
#define IDS_AUTOFILL_SELECT 24323
#define IDS_AUTOFILL_NO_SAVED_ADDRESS 24324
#define IDS_AUTOFILL_ADDRESSES 24325
#define IDS_AUTOFILL_ENABLE_PROFILES_TOGGLE_SUBLABEL 24326
#define IDS_AUTOFILL_ENABLE_CREDIT_CARDS_TOGGLE_SUBLABEL 24327
#define IDS_AUTOFILL_ADDRESSES_SETTINGS_TITLE 24328
#define IDS_AUTOFILL_PAYMENT_METHODS 24329
#define IDS_AUTOFILL_ENABLE_PROFILES_TOGGLE_LABEL 24330
#define IDS_AUTOFILL_ENABLE_CREDIT_CARDS_TOGGLE_LABEL 24331
#define IDS_ENABLE_CREDIT_CARD_FIDO_AUTH_LABEL 24332
#define IDS_ENABLE_CREDIT_CARD_FIDO_AUTH_SUBLABEL 24333
#define IDS_AUTOFILL_ENABLE_PAYMENTS_INTEGRATION_CHECKBOX_LABEL 24334
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_OK_BUTTON_LABEL 24340
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_OK_BUTTON_LABEL 24341
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_NEW_VALUES_SECTION_LABEL 24342
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_OLD_VALUES_SECTION_LABEL 24343
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_TITLE 24344
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_CANCEL_BUTTON_LABEL 24355
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_EDIT_BUTTON_TOOLTIP 24347
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_TITLE 24345
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_CANCEL_BUTTON_LABEL 24356
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_TITLE 24348
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_OK_BUTTON_LABEL_SAVE 24357
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_OK_BUTTON_LABEL_UPDATE 24358
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_CANCEL_BUTTON_LABEL 24359
#define IDS_BOOKMARK_BAR_FOLDER_NAME 239
#define IDS_BOOKMARK_BAR_MOBILE_FOLDER_NAME 241
#define IDS_BOOKMARK_BAR_OTHER_FOLDER_NAME 240
#define IDS_BOOKMARK_BAR_MANAGED_FOLDER_DOMAIN_NAME 24360
#define IDS_BOOKMARK_BAR_MANAGED_FOLDER_DEFAULT_NAME 259
#define IDS_BOOKMARK_EDITOR_TITLE 24361
#define IDS_BOOKMARK_EDITOR_NEW_FOLDER_NAME 24362
#define IDS_BOOKMARK_BUBBLE_REMOVE_BOOKMARK 24363
#define IDS_BOOKMARK_MANAGER_NAME_INPUT_PLACE_HOLDER 24364
#define IDS_BOOKMARK_MANAGER_URL_INPUT_PLACE_HOLDER 24365
#define IDS_TOOLTIP_STAR 273
#define IDS_CLEAR_BROWSING_DATA_CALCULATING 24369
#define IDS_DEL_BROWSING_HISTORY_COUNTER 24370
#define IDS_DEL_BROWSING_HISTORY_COUNTER_SYNCED 24371
#define IDS_DEL_CACHE_COUNTER_UPPER_ESTIMATE 24372
#define IDS_DEL_CACHE_COUNTER_ALMOST_EMPTY 24373
#define IDS_DEL_CACHE_COUNTER_BASIC 24374
#define IDS_DEL_CACHE_COUNTER_UPPER_ESTIMATE_BASIC 24375
#define IDS_DEL_CACHE_COUNTER_ALMOST_EMPTY_BASIC 24376
#define IDS_DEL_PASSWORDS_COUNTER 24377
#define IDS_DEL_PASSWORDS_COUNTER_SYNCED 24378
#define IDS_DEL_ACCOUNT_PASSWORDS_COUNTER 24379
#define IDS_DEL_PASSWORDS_DOMAINS_DISPLAY 24380
#define IDS_DEL_PASSWORDS_COUNTER_AND_X_MORE 24381
#define IDS_DEL_SIGNIN_DATA_COUNTER 24382
#define IDS_DEL_PASSWORDS_AND_SIGNIN_DATA_COUNTER_NONE 24383
#define IDS_DEL_PASSWORDS_AND_SIGNIN_DATA_COUNTER_COMBINATION 24384
#define IDS_DEL_SITE_SETTINGS_COUNTER 24385
#define IDS_DEL_AUTOFILL_COUNTER_EMPTY 24386
#define IDS_DEL_AUTOFILL_COUNTER_CREDIT_CARDS 24387
#define IDS_DEL_AUTOFILL_COUNTER_ADDRESSES 24388
#define IDS_DEL_AUTOFILL_COUNTER_SUGGESTIONS 24389
#define IDS_DEL_AUTOFILL_COUNTER_SUGGESTIONS_LONG 24390
#define IDS_DEL_AUTOFILL_COUNTER_SUGGESTIONS_SHORT 24391
#define IDS_DEL_AUTOFILL_COUNTER_ONE_TYPE_SYNCED 24392
#define IDS_DEL_AUTOFILL_COUNTER_TWO_TYPES 24393
#define IDS_DEL_AUTOFILL_COUNTER_TWO_TYPES_SYNCED 24394
#define IDS_DEL_AUTOFILL_COUNTER_THREE_TYPES 24395
#define IDS_DEL_AUTOFILL_COUNTER_THREE_TYPES_SYNCED 24396
#define IDS_DEL_COOKIES_COUNTER 24397
#define IDS_DEL_COOKIES_COUNTER_ADVANCED 24398
#define IDS_DEL_COOKIES_COUNTER_ADVANCED_WITH_EXCEPTION 24399
#define IDS_DEL_DOWNLOADS_COUNTER 24400
#define IDS_DEL_HOSTED_APPS_COUNTER 24401
#define IDS_DEL_HOSTED_APPS_COUNTER_AND_X_MORE 24402
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_TITLE 24403
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_CONTENT 24404
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_NO_THANKS 24405
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_GET_DISCOUNTS 24406
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_ACCEPTED_CONFIRMATION_TITLE 24407
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_ACCEPTED_CONFIRMATION_CONTENT 24408
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_ACCEPTED_CONFIRMATION_DONE 24409
#define IDS_NATIVE_NTP_CART_DISCOUNT_CONSENT_ACCEPT_BUTTON 24410
#define IDS_NATIVE_NTP_CART_DISCOUNT_CONSENT_TITLE 24411
#define IDS_NATIVE_NTP_CART_DISCOUNT_CONSENT_BODY 24412
#define IDS_LIVE_CAPTION_BUBBLE_TITLE 24413
#define IDS_LIVE_CAPTION_BUBBLE_CLOSE 24414
#define IDS_LIVE_CAPTION_BUBBLE_EXPAND 24415
#define IDS_LIVE_CAPTION_BUBBLE_COLLAPSE 24416
#define IDS_LIVE_CAPTION_BUBBLE_BACK_TO_TAB 24417
#define IDS_LIVE_CAPTION_BUBBLE_ERROR 24418
#define IDS_LIVE_CAPTION_BUBBLE_CONTENT_SETTINGS 24419
#define IDS_LIVE_CAPTION_BUBBLE_MEDIA_FOUNDATION_RENDERER_ERROR 24420
#define IDS_LIVE_CAPTION_BUBBLE_MEDIA_FOUNDATION_RENDERER_ERROR_CHECKBOX 24421
#define IDS_LIVE_CAPTION_BUBBLE_APPEAR_SCREENREADER_ANNOUNCEMENT 24422
#define IDS_SETTINGS_TITLE 24423
#define IDS_SETTINGS_HIDE_ADVANCED_SETTINGS 24424
#define IDS_SETTINGS_SHOW_ADVANCED_SETTINGS 24425
#define IDS_NETWORK_PREDICTION_ENABLED_DESCRIPTION 24426
#define IDS_OPTIONS_PROXIES_CONFIGURE_BUTTON 24427
#define IDS_CRASH_TITLE 24448
#define IDS_CRASH_CRASH_COUNT_BANNER_FORMAT 24449
#define IDS_CRASH_SHOW_DEVELOPER_DETAILS 24450
#define IDS_CRASH_CAPTURE_TIME_FORMAT 24451
#define IDS_CRASH_REPORT_STATUS 24452
#define IDS_CRASH_REPORT_STATUS_NOT_UPLOADED 24453
#define IDS_CRASH_REPORT_STATUS_PENDING 24454
#define IDS_CRASH_REPORT_STATUS_PENDING_USER_REQUESTED 24455
#define IDS_CRASH_REPORT_STATUS_UPLOADED 24456
#define IDS_CRASH_REPORT_UPLOADED_ID 24457
#define IDS_CRASH_REPORT_UPLOADED_TIME 24458
#define IDS_CRASH_REPORT_LOCAL_ID 24459
#define IDS_CRASH_REPORT_FILE_SIZE 24460
#define IDS_CRASH_BUG_LINK_LABEL 24461
#define IDS_CRASH_NO_CRASHES_MESSAGE 24462
#define IDS_CRASH_DISABLED_HEADER 24463
#define IDS_CRASH_UPLOAD_MESSAGE 24464
#define IDS_CRASH_UPLOAD_NOW_LINK_TEXT 24465
#define IDS_HTTP_POST_WARNING_TITLE 24466
#define IDS_HTTP_POST_WARNING 24467
#define IDS_HTTP_POST_WARNING_RESEND 24468
#define IDS_DOM_DISTILLER_JAVASCRIPT_DISABLED_CONTENT 24469
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_URL 24470
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_ADD 24471
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_ADD_FAILED 24472
#define IDS_DOM_DISTILLER_WEBUI_VIEW_URL 24473
#define IDS_DOM_DISTILLER_WEBUI_VIEW_URL_FAILED 24474
#define IDS_DOM_DISTILLER_WEBUI_REFRESH 24475
#define IDS_DOM_DISTILLER_WEBUI_FETCHING_ENTRIES 24476
#define IDS_DOM_DISTILLER_VIEWER_FAILED_TO_FIND_ARTICLE_TITLE 24477
#define IDS_DOM_DISTILLER_VIEWER_FAILED_TO_FIND_ARTICLE_CONTENT 24478
#define IDS_DOM_DISTILLER_VIEWER_LOADING_TITLE 24479
#define IDS_DOM_DISTILLER_VIEWER_CUSTOMIZE_APPEARANCE 24480
#define IDS_DOM_DISTILLER_VIEWER_FONT_STYLE 24481
#define IDS_DOM_DISTILLER_VIEWER_SANS_SERIF_FONT 24482
#define IDS_DOM_DISTILLER_VIEWER_SERIF_FONT 24483
#define IDS_DOM_DISTILLER_VIEWER_MONOSPACE_FONT 24484
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR 24485
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR_LIGHT 24486
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR_SEPIA 24487
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR_DARK 24488
#define IDS_DOM_DISTILLER_VIEWER_FONT_SIZE 24489
#define IDS_DOM_DISTILLER_VIEWER_FONT_SIZE_SMALL 24490
#define IDS_DOM_DISTILLER_VIEWER_FONT_SIZE_LARGE 24491
#define IDS_DOM_DISTILLER_VIEWER_CLOSE 24492
#define IDS_DOM_DISTILLER_VIEWER_NO_DATA_CONTENT 24493
#define IDS_DOM_DISTILLER_WEBUI_TITLE 24494
#define IDS_DOM_DISTILLER_VIEWER_TITLE_SUFFIX 24495
#define IDS_ENTERPRISE_COPY_PREVENTION_MISSING_LIST_ERROR 24496
#define IDS_ENTERPRISE_COPY_PREVENTION_DISABLE_CONTAINS_WILDCARD_ERROR 24497
#define IDS_ENTERPRISE_COPY_PREVENTION_WARNING_MESSAGE 24498
#define IDS_ERRORPAGE_NET_BUTTON_DETAILS 24499
#define IDS_ERRORPAGE_NET_BUTTON_HIDE_DETAILS 24500
#define IDS_ERRORPAGES_BUTTON_RELOAD 24501
#define IDS_ERRORPAGES_BUTTON_SHOW_SAVED_COPY 24502
#define IDS_ERRORPAGE_FUN_DISABLED 24503
#define IDS_ERRORPAGES_SUGGESTION_RELOAD_REPOST_SUMMARY 24513
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_HEADER 24514
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_BODY 24515
#define IDS_ERRORPAGES_SUGGESTION_SECURE_DNS_CONFIG_HEADER 24516
#define IDS_ERRORPAGES_SUGGESTION_SECURE_DNS_CONFIG_BODY 24517
#define IDS_ERRORPAGES_SUGGESTION_DNS_CONFIG_HEADER 24518
#define IDS_ERRORPAGES_SUGGESTION_DNS_CONFIG_BODY 24519
#define IDS_ERRORPAGES_SUGGESTION_NETWORK_PREDICTION_HEADER 24520
#define IDS_ERRORPAGES_SUGGESTION_FIREWALL_CONFIG_BODY 24521
#define IDS_ERRORPAGES_SUGGESTION_PROXY_CONFIG_HEADER 24522
#define IDS_ERRORPAGES_SUGGESTION_PROXY_CONFIG_BODY 24523
#define IDS_ERRORPAGES_SUGGESTION_VIEW_POLICIES_HEADER 24525
#define IDS_ERRORPAGES_SUGGESTION_VIEW_POLICIES_BODY 24526
#define IDS_ERRORPAGES_SUGGESTION_UNSUPPORTED_CIPHER_HEADER 24527
#define IDS_ERRORPAGES_SUGGESTION_UNSUPPORTED_CIPHER_BODY 24528
#define IDS_ERRORPAGES_SUGGESTION_NAVIGATE_TO_ORIGIN 24529
#define IDS_ERRORPAGES_HEADING_NOT_AVAILABLE 24530
#define IDS_ERRORPAGES_HEADING_NETWORK_ACCESS_DENIED 24531
#define IDS_ERRORPAGES_HEADING_INTERNET_DISCONNECTED 24532
#define IDS_ERRORPAGES_HEADING_CACHE_READ_FAILURE 24533
#define IDS_ERRORPAGES_HEADING_CONNECTION_INTERRUPTED 24534
#define IDS_ERRORPAGES_HEADING_NOT_FOUND 24535
#define IDS_ERRORPAGES_HEADING_FILE_NOT_FOUND 24536
#define IDS_ERRORPAGES_HEADING_BLOCKED 24537
#define IDS_ERRORPAGES_HEADING_BLOCKED_SCHEME 24538
#define IDS_ERRORPAGES_SUMMARY_NOT_AVAILABLE 24539
#define IDS_ERRORPAGES_SUMMARY_TIMED_OUT 24540
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_RESET 24541
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_CLOSED 24542
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_FAILED 24543
#define IDS_ERRORPAGES_SUMMARY_NETWORK_CHANGED 24544
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_REFUSED 24545
#define IDS_ERRORPAGES_SUMMARY_NAME_NOT_RESOLVED 24546
#define IDS_ERRORPAGES_SUMMARY_ICANN_NAME_COLLISION 24547
#define IDS_ERRORPAGES_SUMMARY_ADDRESS_UNREACHABLE 24548
#define IDS_ERRORPAGES_SUMMARY_FILE_ACCESS_DENIED 24549
#define IDS_ERRORPAGES_SUMMARY_NETWORK_ACCESS_DENIED 24550
#define IDS_ERRORPAGES_SUMMARY_PROXY_CONNECTION_FAILED 24551
#define IDS_ERRORPAGES_SUMMARY_CACHE_READ_FAILURE 24552
#define IDS_ERRORPAGES_SUMMARY_NETWORK_IO_SUSPENDED 24553
#define IDS_ERRORPAGES_SUMMARY_NOT_FOUND 24554
#define IDS_ERRORPAGES_SUMMARY_FILE_NOT_FOUND 24555
#define IDS_ERRORPAGES_SUMMARY_TOO_MANY_REDIRECTS 24556
#define IDS_ERRORPAGES_SUMMARY_EMPTY_RESPONSE 24557
#define IDS_ERRORPAGES_SUMMARY_INVALID_RESPONSE 24558
#define IDS_ERRORPAGES_SUMMARY_DNS_PROBE_RUNNING 24559
#define IDS_ERRORPAGES_HEADING_ACCESS_DENIED 24560
#define IDS_ERRORPAGES_HEADING_FILE_ACCESS_DENIED 24561
#define IDS_ERRORPAGES_SUMMARY_FORBIDDEN 24562
#define IDS_ERRORPAGES_SUMMARY_GONE 24563
#define IDS_ERRORPAGES_HEADING_PAGE_NOT_WORKING 24564
#define IDS_ERRORPAGES_SUMMARY_CONTACT_SITE_OWNER 24565
#define IDS_ERRORPAGES_SUMMARY_WEBSITE_CANNOT_HANDLE_REQUEST 24566
#define IDS_ERRORPAGES_SUMMARY_GATEWAY_TIMEOUT 24567
#define IDS_ERRORPAGES_SUMMARY_SSL_SECURITY_ERROR 24568
#define IDS_ERRORPAGES_SUMMARY_SSL_VERSION_OR_CIPHER_MISMATCH 24569
#define IDS_ERRORPAGES_HEADING_INSECURE_CONNECTION 24570
#define IDS_ERRORPAGES_SUMMARY_BAD_SSL_CLIENT_AUTH_CERT 24571
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_EXTENSION 24572
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_ADMINISTRATOR 24573
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_SECURITY 24574
#define IDS_ERRORPAGES_HTTP_POST_WARNING 24575
#define IDS_ERRORPAGES_SUGGESTION_LIST_HEADER 24576
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_SUMMARY 24577
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_FIREWALL_DNS_SUMMARY 24579
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_FIREWALL_SECURE_DNS_SUMMARY 24580
#define IDS_ERRORPAGES_SUGGESTION_CHECK_FIREWALL_ANTIVIRUS_SUMMARY 24581
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_FIREWALL_SUMMARY 24582
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_ADDRESS_SUMMARY 24583
#define IDS_ERRORPAGES_SUGGESTION_CONTACT_ADMIN_SUMMARY 24584
#define IDS_ERRORPAGES_SUGGESTION_CONTACT_ADMIN_SUMMARY_STANDALONE 24585
#define IDS_ERRORPAGES_SUGGESTION_LEARNMORE_SUMMARY 24586
#define IDS_ERRORPAGES_SUGGESTION_LEARNMORE_SUMMARY_STANDALONE 24587
#define IDS_ERRORPAGES_SUGGESTION_CLEAR_COOKIES_SUMMARY 24588
#define IDS_ERRORPAGES_SUGGESTION_CHECK_HARDWARE_SUMMARY 24592
#define IDS_ERRORPAGES_SUGGESTION_CHECK_WIFI_SUMMARY 24593
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE_CONNECTION_SUMMARY 24594
#define IDS_ERRORPAGES_SUGGESTION_COMPLETE_SETUP_SUMMARY 24595
#define IDS_ERRORPAGES_SUGGESTION_DISABLE_EXTENSION_SUMMARY 24596
#define IDS_ERRORPAGES_CHECK_TYPO_SUMMARY 24597
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE 24598
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE_STANDALONE 24599
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE_CHECK_TYPO_STANDALONE 24600
#define IDS_ERRORPAGES_GAME_INSTRUCTIONS 24601
#define IDS_ERRORPAGE_DINO_GAME_DESCRIPTION 24602
#define IDS_ERRORPAGE_DINO_ARIA_LABEL 24603
#define IDS_ERRORPAGE_DINO_GAME_START 24604
#define IDS_ERRORPAGE_DINO_GAME_OVER 24605
#define IDS_ERRORPAGE_DINO_HIGH_SCORE 24606
#define IDS_ERRORPAGE_DINO_JUMP 24607
#define IDS_ERRORPAGE_DINO_SLOW_SPEED_TOGGLE 24608
#define IDS_FIND_IN_PAGE_ACCESSIBLE_TITLE 24609
#define IDS_FIND_IN_PAGE_COUNT 24610
#define IDS_ACCESSIBLE_FIND_IN_PAGE_COUNT 24611
#define IDS_ACCESSIBLE_FIND_IN_PAGE_NO_RESULTS 24612
#define IDS_FIND_IN_PAGE_PREVIOUS_TOOLTIP 24613
#define IDS_FIND_IN_PAGE_NEXT_TOOLTIP 24614
#define IDS_FIND_IN_PAGE_CLOSE_TOOLTIP 24615
#define IDS_FLAGS_UI_SEARCH_PLACEHOLDER 24616
#define IDS_FLAGS_UI_SEARCH_LABEL 24617
#define IDS_FLAGS_UI_TITLE 24618
#define IDS_FLAGS_UI_PAGE_RESET 24619
#define IDS_FLAGS_UI_PAGE_WARNING 24620
#define IDS_FLAGS_UI_PAGE_WARNING_EXPLANATION 24621
#define IDS_FLAGS_UI_OWNER_WARNING 24622
#define IDS_FLAGS_UI_AVAILABLE_FEATURE 24623
#define IDS_FLAGS_UI_UNAVAILABLE_FEATURE 24624
#define IDS_FLAGS_UI_ENABLED_FEATURE 24625
#define IDS_FLAGS_UI_DISABLED_FEATURE 24626
#define IDS_FLAGS_UI_NO_RESULTS 24627
#define IDS_FLAGS_UI_NOT_AVAILABLE_ON_PLATFORM 24628
#define IDS_FLAGS_UI_RELAUNCH 24629
#define IDS_FLAGS_UI_CLEAR_SEARCH 24633
#define IDS_FLAGS_UI_RESET_ACKNOWLEDGED 24634
#define IDS_FLAGS_UI_EXPERIMENT_ENABLED 24635
#define IDS_FLAGS_UI_SEARCH_RESULTS_SINGULAR 24636
#define IDS_FLAGS_UI_SEARCH_RESULTS_PLURAL 24637
#define IDS_DEPRECATED_FEATURES_PAGE_RESET 24638
#define IDS_DEPRECATED_FEATURES_OWNER_WARNING 24639
#define IDS_DEPRECATED_FEATURES_AVAILABLE_FEATURE 24640
#define IDS_DEPRECATED_FEATURES_UNAVAILABLE_FEATURE 24641
#define IDS_DEPRECATED_FEATURES_ENABLED_FEATURE 24642
#define IDS_DEPRECATED_FEATURES_DISABLED_FEATURE 24643
#define IDS_DEPRECATED_FEATURES_NOT_AVAILABLE_ON_PLATFORM 24644
#define IDS_DEPRECATED_FEATURES_RELAUNCH 24645
#define IDS_DEPRECATED_FEATURES_SEARCH_PLACEHOLDER 24649
#define IDS_DEPRECATED_FEATURES_TITLE 24650
#define IDS_DEPRECATED_FEATURES_HEADING 24651
#define IDS_DEPRECATED_FEATURES_PAGE_WARNING_EXPLANATION 24652
#define IDS_DEPRECATED_FEATURES_NO_RESULTS 24653
#define IDS_DEPRECATED_UI_CLEAR_SEARCH 24654
#define IDS_DEPRECATED_UI_RESET_ACKNOWLEDGED 24655
#define IDS_DEPRECATED_UI_EXPERIMENT_ENABLED 24656
#define IDS_ENTERPRISE_UI_SEARCH_RESULTS_SINGULAR 24657
#define IDS_ENTERPRISE_UI_SEARCH_RESULTS_PLURAL 24658
#define IDS_EXIT_FULLSCREEN_MODE 24659
#define IDS_FULLSCREEN_HOLD_TO_EXIT_FULLSCREEN 24660
#define IDS_FULLSCREEN_PRESS_TO_EXIT_FULLSCREEN 24661
#define IDS_FULLSCREEN_PRESS_TO_EXIT_FULLSCREEN_TWO_KEYS 24662
#define IDS_PRESS_TO_EXIT_MOUSELOCK 24663
#define IDS_PRESS_TO_EXIT_MOUSELOCK_TWO_KEYS 24664
#define IDS_GLOBAL_MEDIA_CONTROLS_BACK_TO_TAB 24665
#define IDS_GLOBAL_MEDIA_CONTROLS_DISMISS_ICON_TOOLTIP_TEXT 24666
#define IDS_HEAVY_AD_INTERVENTION_BUTTON_DETAILS 24667
#define IDS_HEAVY_AD_INTERVENTION_HEADING 24668
#define IDS_HEAVY_AD_INTERVENTION_SUMMARY 24669
#define IDS_HEAVY_AD_INTERVENTION_BUTTON_RELOAD 24670
#define IDS_HISTORY_ACTION_MENU_DESCRIPTION 24671
#define IDS_HISTORY_ARIA_ROLE_DESCRIPTION 24672
#define IDS_HISTORY_CANCEL_EDITING_BUTTON 24673
#define IDS_HISTORY_DATE_WITH_RELATIVE_TIME 24674
#define IDS_HISTORY_DELETE_PRIOR_VISITS_CONFIRM_BUTTON 24675
#define IDS_HISTORY_DELETE_PRIOR_VISITS_WARNING 24676
#define IDS_HISTORY_DELETE_SELECTED_ENTRIES_BUTTON 24677
#define IDS_HISTORY_ENTRY_ACCESSIBILITY_DELETE 24678
#define IDS_HISTORY_ENTRY_ACCESSIBILITY_LABEL 24679
#define IDS_HISTORY_ENTRY_BOOKMARKED 24680
#define IDS_HISTORY_ENTRY_SUMMARY 24681
#define IDS_HISTORY_FOUND_SEARCH_RESULTS 24682
#define IDS_HISTORY_OTHER_FORMS_OF_HISTORY 24683
#define IDS_HISTORY_LOADING 24684
#define IDS_HISTORY_MORE_FROM_SITE 24685
#define IDS_HISTORY_NO_RESULTS 24686
#define IDS_HISTORY_NO_SEARCH_RESULTS 24687
#define IDS_HISTORY_OPEN_CLEAR_BROWSING_DATA_DIALOG 24688
#define IDS_HISTORY_OTHER_SESSIONS_COLLAPSE_SESSION 24689
#define IDS_HISTORY_OTHER_SESSIONS_EXPAND_SESSION 24690
#define IDS_HISTORY_OTHER_SESSIONS_HIDE_FOR_NOW 24691
#define IDS_HISTORY_OTHER_SESSIONS_OPEN_ALL 24692
#define IDS_HISTORY_REMOVE_BOOKMARK 24693
#define IDS_HISTORY_REMOVE_PAGE 24694
#define IDS_HISTORY_REMOVE_PAGE_SUCCESS 24695
#define IDS_HISTORY_REMOVE_SELECTED_ITEMS 24696
#define IDS_HISTORY_SEARCH_BUTTON 24697
#define IDS_HISTORY_SEARCH_RESULT 24698
#define IDS_HISTORY_SEARCH_RESULTS 24699
#define IDS_HISTORY_SHOW_HISTORY 280
#define IDS_HISTORY_SHOWFULLHISTORY_LINK 172
#define IDS_HISTORY_START_EDITING_BUTTON 24700
#define IDS_HISTORY_TITLE 24701
#define IDS_HISTORY_UNKNOWN_DEVICE 24702
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE 24703
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_IFRAME 24704
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_NONSTANDARD_URL 24705
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_NONSTANDARD_URL_IFRAME 24706
#define IDS_JAVASCRIPT_MESSAGEBOX_SUPPRESS_OPTION 24707
#define IDS_BEFOREUNLOAD_MESSAGEBOX_TITLE 24708
#define IDS_BEFOREUNLOAD_APP_MESSAGEBOX_TITLE 24709
#define IDS_BEFOREUNLOAD_MESSAGEBOX_OK_BUTTON_LABEL 24710
#define IDS_BEFOREUNLOAD_MESSAGEBOX_MESSAGE 24711
#define IDS_BEFORERELOAD_MESSAGEBOX_TITLE 24712
#define IDS_BEFORERELOAD_APP_MESSAGEBOX_TITLE 24713
#define IDS_BEFORERELOAD_MESSAGEBOX_OK_BUTTON_LABEL 24714
#define IDS_LOGIN_DIALOG_TITLE 24715
#define IDS_LOGIN_DIALOG_OK_BUTTON_LABEL 24716
#define IDS_LOGIN_DIALOG_AUTHORITY 24717
#define IDS_LOGIN_DIALOG_PROXY_AUTHORITY 24718
#define IDS_LOGIN_DIALOG_NOT_PRIVATE 24719
#define IDS_LOGIN_DIALOG_USERNAME_FIELD 24720
#define IDS_LOGIN_DIALOG_PASSWORD_FIELD 24721
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_PREVIOUS_TRACK 24722
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_SEEK_BACKWARD 24723
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_PLAY 24724
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_PAUSE 24725
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_SEEK_FORWARD 24726
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_NEXT_TRACK 24727
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_ENTER_PIP 24728
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_EXIT_PIP 24729
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_MUTE 24730
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_UNMUTE 24731
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACCESSIBLE_NAME 24732
#define IDS_DEFAULT_TAB_TITLE 320
#define IDS_DOWNLOAD_TAB_TITLE 24733
#define IDS_SAD_TAB_TITLE 24734
#define IDS_SAD_TAB_MESSAGE 24735
#define IDS_SAD_TAB_HELP_MESSAGE 24736
#define IDS_SAD_TAB_HELP_LINK 24737
#define IDS_SAD_TAB_RELOAD_LABEL 24738
#define IDS_SAD_TAB_RELOAD_TITLE 24741
#define IDS_SAD_TAB_OOM_MESSAGE_TABS 24742
#define IDS_SAD_TAB_OOM_MESSAGE_NOTABS 24743
#define IDS_SAD_TAB_RELOAD_TRY 24744
#define IDS_SAD_TAB_RELOAD_INCOGNITO 24745
#define IDS_SAD_TAB_RELOAD_CLOSE_TABS 24746
#define IDS_SAD_TAB_RELOAD_CLOSE_NOTABS 24747
#define IDS_SAD_TAB_RELOAD_RESTART_BROWSER 24748
#define IDS_SAD_TAB_RELOAD_RESTART_DEVICE 24749
#define IDS_SAD_TAB_ERROR_CODE 24751
#define IDS_NEW_TAB_TITLE 319
#define IDS_NEW_INCOGNITO_TAB_TITLE 24752
#define IDS_NEW_TAB_OTR_HEADING 24753
#define IDS_NEW_TAB_OTR_DESCRIPTION 24754
#define IDS_NEW_TAB_OTR_LEARN_MORE_LINK 24755
#define IDS_NEW_TAB_OTR_MESSAGE_WARNING 24756
#define IDS_NEW_TAB_UNDO_THUMBNAIL_REMOVE 24757
#define IDS_NEW_TAB_OTR_TITLE 24758
#define IDS_NEW_TAB_OTR_SUBTITLE 24759
#define IDS_NEW_TAB_OTR_SUBTITLE_WITH_READING_LIST 24760
#define IDS_NEW_TAB_OTR_NOT_SAVED 24761
#define IDS_NEW_TAB_OTR_VISIBLE 24762
#define IDS_NEW_TAB_OTR_COOKIE_CONTROLS_CONTROLLED_TOOLTIP_TEXT 24763
#define IDS_NEW_TAB_OTR_THIRD_PARTY_COOKIE 24764
#define IDS_NEW_TAB_OTR_THIRD_PARTY_COOKIE_SUBLABEL 24765
#define IDS_REVAMPED_INCOGNITO_NTP_TITLE 24766
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_HEADER 24767
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_DESCRIPTION 24768
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_NOT_HEADER 24769
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_NOT_DESCRIPTION 24770
#define IDS_REVAMPED_INCOGNITO_NTP_LEARN_MORE 24771
#define IDS_REVAMPED_INCOGNITO_NTP_OTR_THIRD_PARTY_COOKIE 24772
#define IDS_REVAMPED_INCOGNITO_NTP_OTR_THIRD_PARTY_COOKIE_SUBLABEL 24773
#define IDS_NTP_ARTICLE_SUGGESTIONS_NOT_AVAILABLE 24775
#define IDS_NTP_ARTICLE_SUGGESTIONS_SECTION_HEADER 24776
#define IDS_NTP_ARTICLE_SUGGESTIONS_SECTION_EMPTY 24777
#define IDS_NTP_READING_LIST_SUGGESTIONS_SECTION_HEADER 24778
#define IDS_NTP_READING_LIST_SUGGESTIONS_SECTION_EMPTY 24779
#define IDS_NTP_RECENT_TAB_SUGGESTIONS_SECTION_HEADER 24780
#define IDS_NTP_RECENT_TAB_SUGGESTIONS_SECTION_EMPTY 24781
#define IDS_NTP_NOTIFICATIONS_READ_THIS_STORY_AND_MORE 24782
#define IDS_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_HINT 24783
#define IDS_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_SUGGESTION_CONTENTS 24784
#define IDS_ACC_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_SUFFIX 24785
#define IDS_ACC_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA 24786
#define IDS_OMNIBOX_PEDAL_MANAGE_PASSWORDS_HINT 24787
#define IDS_OMNIBOX_PEDAL_MANAGE_PASSWORDS_SUGGESTION_CONTENTS 24788
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_PASSWORDS_SUFFIX 24789
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_PASSWORDS 24790
#define IDS_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_HINT 24791
#define IDS_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_SUGGESTION_CONTENTS 24792
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_SUFFIX 24793
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD 24794
#define IDS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_HINT 24795
#define IDS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUGGESTION_CONTENTS 24796
#define IDS_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUFFIX 24797
#define IDS_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO 24798
#define IDS_OMNIBOX_PEDAL_TRANSLATE_HINT 24799
#define IDS_OMNIBOX_PEDAL_TRANSLATE_SUGGESTION_CONTENTS 24800
#define IDS_ACC_OMNIBOX_PEDAL_TRANSLATE_SUFFIX 24801
#define IDS_ACC_OMNIBOX_PEDAL_TRANSLATE 24802
#define IDS_OMNIBOX_PEDAL_UPDATE_CHROME_HINT 24803
#define IDS_OMNIBOX_PEDAL_UPDATE_CHROME_SUGGESTION_CONTENTS 24804
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CHROME_SUFFIX 24805
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CHROME 24806
#define IDS_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_HINT 24807
#define IDS_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_SUGGESTION_CONTENTS 24808
#define IDS_ACC_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_SUFFIX 24809
#define IDS_ACC_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK 24810
#define IDS_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS_HINT 24811
#define IDS_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS_SUGGESTION_CONTENTS 24812
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS_SUFFIX 24813
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS 24814
#define IDS_OMNIBOX_PEDAL_MANAGE_COOKIES_HINT 24815
#define IDS_OMNIBOX_PEDAL_MANAGE_COOKIES_SUGGESTION_CONTENTS 24816
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_COOKIES_SUFFIX 24817
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_COOKIES 24818
#define IDS_OMNIBOX_PEDAL_MANAGE_ADDRESSES_HINT 24819
#define IDS_OMNIBOX_PEDAL_MANAGE_ADDRESSES_SUGGESTION_CONTENTS 24820
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_ADDRESSES_SUFFIX 24821
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_ADDRESSES 24822
#define IDS_OMNIBOX_PEDAL_MANAGE_SYNC_HINT 24823
#define IDS_OMNIBOX_PEDAL_MANAGE_SYNC_SUGGESTION_CONTENTS 24824
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SYNC_SUFFIX 24825
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SYNC 24826
#define IDS_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS_HINT 24827
#define IDS_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS_SUGGESTION_CONTENTS 24828
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS_SUFFIX 24829
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS 24830
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC_HINT 24831
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC_SUGGESTION_CONTENTS 24832
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC_SUFFIX 24833
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC 24834
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET_HINT 24835
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET_SUGGESTION_CONTENTS 24836
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET_SUFFIX 24837
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET 24838
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE_HINT 24839
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE_SUGGESTION_CONTENTS 24840
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE_SUFFIX 24841
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE 24842
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT_HINT 24843
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT_SUGGESTION_CONTENTS 24844
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT_SUFFIX 24845
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT 24846
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE_HINT 24847
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE_SUGGESTION_CONTENTS 24848
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE_SUFFIX 24849
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE 24850
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE_HINT 24851
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE_SUGGESTION_CONTENTS 24852
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE_SUFFIX 24853
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE 24854
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM_HINT 24855
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM_SUGGESTION_CONTENTS 24856
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM_SUFFIX 24857
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM 24858
#define IDS_OMNIBOX_PEDAL_SEE_CHROME_TIPS_HINT 24859
#define IDS_OMNIBOX_PEDAL_SEE_CHROME_TIPS_SUGGESTION_CONTENTS 24860
#define IDS_ACC_OMNIBOX_PEDAL_SEE_CHROME_TIPS_SUFFIX 24861
#define IDS_ACC_OMNIBOX_PEDAL_SEE_CHROME_TIPS 24862
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT_HINT 24863
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT_SUGGESTION_CONTENTS 24864
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT_SUFFIX 24865
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT 24866
#define IDS_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD_HINT 24867
#define IDS_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD_SUGGESTION_CONTENTS 24868
#define IDS_ACC_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD_SUFFIX 24869
#define IDS_ACC_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD 24870
#define IDS_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS_HINT 24871
#define IDS_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS_SUGGESTION_CONTENTS 24872
#define IDS_ACC_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS_SUFFIX 24873
#define IDS_ACC_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS 24874
#define IDS_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_HINT 24875
#define IDS_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_SUGGESTION_CONTENTS 24876
#define IDS_ACC_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_SUFFIX 24877
#define IDS_ACC_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME 24878
#define IDS_OMNIBOX_PEDAL_FIND_MY_PHONE_HINT 24879
#define IDS_OMNIBOX_PEDAL_FIND_MY_PHONE_SUGGESTION_CONTENTS 24880
#define IDS_ACC_OMNIBOX_PEDAL_FIND_MY_PHONE_SUFFIX 24881
#define IDS_ACC_OMNIBOX_PEDAL_FIND_MY_PHONE 24882
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY_HINT 24883
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY_SUGGESTION_CONTENTS 24884
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY_SUFFIX 24885
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY 24886
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_HINT 24887
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_SUGGESTION_CONTENTS 24888
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_SUFFIX 24889
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS 24890
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS_HINT 24891
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS_SUGGESTION_CONTENTS 24892
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS_SUFFIX 24893
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS 24894
#define IDS_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_HINT 24895
#define IDS_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_SUGGESTION_CONTENTS 24896
#define IDS_ACC_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_SUFFIX 24897
#define IDS_ACC_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY 24898
#define IDS_OMNIBOX_PEDAL_SHARE_THIS_PAGE_HINT 24899
#define IDS_OMNIBOX_PEDAL_SHARE_THIS_PAGE_SUGGESTION_CONTENTS 24900
#define IDS_ACC_OMNIBOX_PEDAL_SHARE_THIS_PAGE_SUFFIX 24901
#define IDS_ACC_OMNIBOX_PEDAL_SHARE_THIS_PAGE 24902
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY_HINT 24903
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY_SUGGESTION_CONTENTS 24904
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY_SUFFIX 24905
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY 24906
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS_HINT 24907
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS_SUGGESTION_CONTENTS 24908
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS_SUFFIX 24909
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS 24910
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES_HINT 24911
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES_SUGGESTION_CONTENTS 24912
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES_SUFFIX 24913
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES 24914
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES_HINT 24915
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES_SUGGESTION_CONTENTS 24916
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES_SUFFIX 24917
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES 24918
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY_HINT 24919
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY_SUGGESTION_CONTENTS 24920
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY_SUFFIX 24921
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY 24922
#define IDS_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_HINT 24923
#define IDS_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_SUGGESTION_CONTENTS 24924
#define IDS_ACC_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_SUFFIX 24925
#define IDS_ACC_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER 24926
#define IDS_ANDROID_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_HINT 24927
#define IDS_ANDROID_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_HINT 24928
#define IDS_ANDROID_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUGGESTION_CONTENTS 24929
#define IDS_ANDROID_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUFFIX 24930
#define IDS_ANDROID_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO 24931
#define IDS_IOS_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_HINT 24932
#define IDS_IOS_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_HINT 24933
#define IDS_IOS_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_HINT 24934
#define IDS_IOS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_HINT 24935
#define IDS_IOS_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_HINT 24936
#define IDS_IOS_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_HINT 24937
#define IDS_IOS_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_HINT 24938
#define IDS_IOS_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_HINT 24939
#define IDS_IOS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUGGESTION_CONTENTS 24940
#define IDS_IOS_OMNIBOX_PEDAL_MANAGE_PASSWORDS_HINT 24941
#define IDS_AUTOCOMPLETE_SEARCH_DESCRIPTION 361
#define IDS_EMPTY_KEYWORD_VALUE 24942
#define IDS_LINK_FROM_CLIPBOARD 24943
#define IDS_TEXT_FROM_CLIPBOARD 24944
#define IDS_IMAGE_FROM_CLIPBOARD 24945
#define IDS_SECURE_CONNECTION_EV 24946
#define IDS_SECURE_VERBOSE_STATE 24947
#define IDS_NOT_SECURE_VERBOSE_STATE 24948
#define IDS_DANGEROUS_VERBOSE_STATE 24949
#define IDS_OFFLINE_VERBOSE_STATE 24950
#define IDS_OMNIBOX_TAB_SUGGEST_HINT 24952
#define IDS_OMNIBOX_FILE 24953
#define IDS_OMNIBOX_READER_MODE 24954
#define IDS_DRIVE_SUGGESTION_DOCUMENT 24955
#define IDS_DRIVE_SUGGESTION_FORM 24956
#define IDS_DRIVE_SUGGESTION_SPREADSHEET 24957
#define IDS_DRIVE_SUGGESTION_PRESENTATION 24958
#define IDS_DRIVE_SUGGESTION_GENERAL 24959
#define IDS_DRIVE_SUGGESTION_DESCRIPTION_TEMPLATE 24960
#define IDS_DRIVE_SUGGESTION_DESCRIPTION_TEMPLATE_WITHOUT_DATE 24961
#define IDS_DRIVE_SUGGESTION_DESCRIPTION_TEMPLATE_WITHOUT_OWNER 24962
#define IDS_ACCURACY_CHECK_VERBOSE_STATE 24963
#define IDS_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH_HINT 24964
#define IDS_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH_SUGGESTION_CONTENTS 24965
#define IDS_ACC_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH_SUFFIX 24966
#define IDS_ACC_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH 24967
#define IDS_ACC_AUTOCOMPLETE_HISTORY 24968
#define IDS_ACC_AUTOCOMPLETE_SEARCH_HISTORY 24969
#define IDS_ACC_AUTOCOMPLETE_SEARCH 24970
#define IDS_ACC_AUTOCOMPLETE_SUGGESTED_SEARCH 24971
#define IDS_ACC_AUTOCOMPLETE_SUGGESTED_SEARCH_ENTITY 24972
#define IDS_ACC_AUTOCOMPLETE_QUICK_ANSWER 24973
#define IDS_ACC_AUTOCOMPLETE_BOOKMARK 24974
#define IDS_ACC_AUTOCOMPLETE_CLIPBOARD_IMAGE 24975
#define IDS_ACC_AUTOCOMPLETE_CLIPBOARD_TEXT 24976
#define IDS_ACC_AUTOCOMPLETE_CLIPBOARD_URL 24977
#define IDS_ACC_SEARCH_ICON 24978
#define IDS_ACC_AUTOCOMPLETE_N_OF_M 24979
#define IDS_ACC_TAB_SWITCH_SUFFIX 24980
#define IDS_ACC_TAB_SWITCH_BUTTON_FOCUSED_PREFIX 24981
#define IDS_ACC_TAB_SWITCH_BUTTON 24982
#define IDS_ACC_MULTIPLE_ACTIONS_SUFFIX 24983
#define IDS_ACC_KEYWORD_SUFFIX 24984
#define IDS_ACC_KEYWORD_MODE 24985
#define IDS_ACC_REMOVE_SUGGESTION_SUFFIX 24986
#define IDS_ACC_REMOVE_SUGGESTION_FOCUSED_PREFIX 24987
#define IDS_ACC_REMOVE_SUGGESTION_BUTTON 24988
#define IDS_TOOLTIP_HEADER_SHOW_SUGGESTIONS_BUTTON 24989
#define IDS_TOOLTIP_HEADER_HIDE_SUGGESTIONS_BUTTON 24990
#define IDS_ACC_HEADER_SHOW_SUGGESTIONS_BUTTON 24991
#define IDS_ACC_HEADER_HIDE_SUGGESTIONS_BUTTON 24992
#define IDS_ACC_HEADER_SECTION_SHOWN 24993
#define IDS_ACC_HEADER_SECTION_HIDDEN 24994
#define IDS_SEARCH_ENGINES_STARTER_PACK_BOOKMARKS_NAME 24995
#define IDS_SEARCH_ENGINES_STARTER_PACK_BOOKMARKS_KEYWORD 24996
#define IDS_SEARCH_ENGINES_STARTER_PACK_HISTORY_NAME 24997
#define IDS_SEARCH_ENGINES_STARTER_PACK_HISTORY_KEYWORD 24998
#define IDS_SEARCH_ENGINES_STARTER_PACK_SETTINGS_NAME 24999
#define IDS_SEARCH_ENGINES_STARTER_PACK_SETTINGS_KEYWORD 25000
#define IDS_PAGE_INFO_SECURE_SUMMARY 25001
#define IDS_PAGE_INFO_MIXED_CONTENT_SUMMARY 25002
#define IDS_PAGE_INFO_MIXED_CONTENT_SUMMARY_SHORT 25003
#define IDS_PAGE_INFO_NOT_SECURE_SUMMARY 25004
#define IDS_PAGE_INFO_NOT_SECURE_SUMMARY_SHORT 25005
#define IDS_PAGE_INFO_MALWARE_SUMMARY 25006
#define IDS_PAGE_INFO_SOCIAL_ENGINEERING_SUMMARY 25007
#define IDS_PAGE_INFO_UNWANTED_SOFTWARE_SUMMARY 25008
#define IDS_PAGE_INFO_EXTENSION_PAGE 25009
#define IDS_PAGE_INFO_VIEW_SOURCE_PAGE 25010
#define IDS_PAGE_INFO_DEVTOOLS_PAGE 25011
#define IDS_PAGE_INFO_READER_MODE_PAGE_SECURE 25012
#define IDS_PAGE_INFO_READER_MODE_PAGE 25013
#define IDS_PAGE_INFO_SAFETY_TIP_BAD_REPUTATION_TITLE 25014
#define IDS_PAGE_INFO_SAFETY_TIP_BAD_REPUTATION_LEAVE_BUTTON 25015
#define IDS_PAGE_INFO_SAFETY_TIP_MORE_INFO_LINK 25016
#define IDS_PAGE_INFO_SAFETY_TIP_LOOKALIKE_LEAVE_BUTTON 25017
#define IDS_PAGE_INFO_SAFETY_TIP_LOOKALIKE_TITLE 25018
#define IDS_PAGE_INFO_SAFETY_TIP_DESCRIPTION 25019
#define IDS_PAGE_INFO_ACCURACY_TIP_TITLE 25020
#define IDS_PAGE_INFO_ACCURACY_TIP_LEARN_MORE_BUTTON 25021
#define IDS_PAGE_INFO_ACCURACY_TIP_IGNORE_BUTTON 25022
#define IDS_PAGE_INFO_ACCURACY_TIP_OPT_OUT_BUTTON 25023
#define IDS_PAGE_INFO_ACCURACY_TIP_BODY_LINE_1 25024
#define IDS_PAGE_INFO_ACCURACY_TIP_BODY_LINE_2 25025
#define IDS_PAGE_INFO_ACCURACY_TIP_BODY_LINE_3 25026
#define IDS_PAGE_INFO_FILE_PAGE 25027
#define IDS_PAGE_INFO_SECURE_DETAILS 25028
#define IDS_PAGE_INFO_SECURE_DETAILS_V2 25029
#define IDS_PAGE_INFO_ADMIN_PROVIDED_CERT_DETAILS 25030
#define IDS_PAGE_INFO_MIXED_CONTENT_DETAILS 25031
#define IDS_PAGE_INFO_LEGACY_TLS_DETAILS 25032
#define IDS_PAGE_INFO_NOT_SECURE_DETAILS 25033
#define IDS_PAGE_INFO_MALWARE_DETAILS 25034
#define IDS_PAGE_INFO_SOCIAL_ENGINEERING_DETAILS 25035
#define IDS_PAGE_INFO_UNWANTED_SOFTWARE_DETAILS 25036
#define IDS_PAGE_INFO_SECURITY_TAB_INSECURE_IDENTITY 25037
#define IDS_PAGE_INFO_CHANGE_PASSWORD_SUMMARY 25038
#define IDS_PAGE_INFO_CHANGE_PASSWORD_SAVED_PASSWORD_SUMMARY 25039
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_SAVED 25040
#define IDS_PAGE_INFO_CHECK_PASSWORDS_BUTTON 25041
#define IDS_PAGE_INFO_INVALID_CERTIFICATE_DESCRIPTION 25042
#define IDS_PAGE_INFO_RESET_INVALID_CERTIFICATE_DECISIONS_BUTTON 25043
#define IDS_PAGE_INFO_HELP_CENTER_LINK 25044
#define IDS_PAGE_INFO_SECURITY_TAB_DEPRECATED_SIGNATURE_ALGORITHM 25045
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_CONNECTION_TEXT 25046
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_INSECURE_CONTENT_ERROR 25047
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_INSECURE_CONTENT_WARNING 25048
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_INSECURE_FORM_WARNING 25049
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_SENTENCE_LINK 25050
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTION_DETAILS 25051
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTION_DETAILS_AEAD 25052
#define IDS_PAGE_INFO_SECURITY_TAB_NON_UNIQUE_NAME 25053
#define IDS_PAGE_INFO_SECURITY_TAB_NOT_ENCRYPTED_CONNECTION_TEXT 25054
#define IDS_PAGE_INFO_SECURITY_TAB_SSL_VERSION 25055
#define IDS_PAGE_INFO_SECURITY_TAB_UNKNOWN_PARTY 25056
#define IDS_PAGE_INFO_SECURITY_TAB_WEAK_ENCRYPTION_CONNECTION_TEXT 25057
#define IDS_PAGE_INFO_CERT_INFO_BUTTON 25058
#define IDS_PAGE_INFO_ADDRESS 25059
#define IDS_PAGE_INFO_PARTIAL_ADDRESS 25060
#define IDS_PAGE_INFO_SECURITY_TAB_SECURE_IDENTITY_EV_VERIFIED 25061
#define IDS_PAGE_INFO_CERTIFICATE 25062
#define IDS_PAGE_INFO_CERTIFICATE_VALID_LINK 25063
#define IDS_PAGE_INFO_CERTIFICATE_INVALID_LINK 25064
#define IDS_PAGE_INFO_CERTIFICATE_BUTTON_TEXT 25065
#define IDS_PAGE_INFO_CERTIFICATE_VALID_PARENTHESIZED 25066
#define IDS_PAGE_INFO_CERTIFICATE_INVALID_PARENTHESIZED 25067
#define IDS_PAGE_INFO_CERTIFICATE_IS_VALID 25068
#define IDS_PAGE_INFO_CERTIFICATE_IS_NOT_VALID 25069
#define IDS_PAGE_INFO_CERTIFICATE_VALID_LINK_TOOLTIP 25070
#define IDS_PAGE_INFO_CERTIFICATE_INVALID_LINK_TOOLTIP 25071
#define IDS_PAGE_INFO_COOKIES 25073
#define IDS_PAGE_INFO_COOKIES_BUTTON_TEXT 25074
#define IDS_PAGE_INFO_NUM_COOKIES_PARENTHESIZED 25075
#define IDS_PAGE_INFO_NUM_COOKIES 25076
#define IDS_PAGE_INFO_COOKIES_TOOLTIP 25077
#define IDS_PAGE_INFO_BUTTON_TEXT_ALLOWED_BY_USER 25078
#define IDS_PAGE_INFO_BUTTON_TEXT_ALLOWED_ONCE_BY_USER 25079
#define IDS_PAGE_INFO_BUTTON_TEXT_BLOCKED_BY_USER 25080
#define IDS_PAGE_INFO_BUTTON_TEXT_MUTED_BY_USER 25081
#define IDS_PAGE_INFO_BUTTON_TEXT_ASK_BY_USER 25082
#define IDS_PAGE_INFO_BUTTON_TEXT_DETECT_IMPORTANT_CONTENT_BY_USER 25083
#define IDS_PAGE_INFO_BUTTON_TEXT_ALLOWED_BY_DEFAULT 25084
#define IDS_PAGE_INFO_BUTTON_TEXT_BLOCKED_BY_DEFAULT 25085
#define IDS_PAGE_INFO_BUTTON_TEXT_ASK_BY_DEFAULT 25086
#define IDS_PAGE_INFO_BUTTON_TEXT_AUTOMATIC_BY_DEFAULT 25087
#define IDS_PAGE_INFO_BUTTON_TEXT_MUTED_BY_DEFAULT 25088
#define IDS_PAGE_INFO_BUTTON_TEXT_DETECT_IMPORTANT_CONTENT_BY_DEFAULT 25089
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_ALLOW 25090
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_BLOCK 25091
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_ASK 25092
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_DETECT_IMPORTANT_CONTENT 25093
#define IDS_PAGE_INFO_MENU_ITEM_ALLOW 25094
#define IDS_PAGE_INFO_MENU_ITEM_BLOCK 25095
#define IDS_PAGE_INFO_MENU_ITEM_ASK 25096
#define IDS_PAGE_INFO_MENU_ITEM_DETECT_IMPORTANT_CONTENT 25097
#define IDS_PAGE_INFO_MENU_ITEM_ADS_BLOCK 25098
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED 25099
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED_ONCE_ONE_TAB 25100
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED_ONCE_MULTIPLE_TAB 25101
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED 25102
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED_IN_INCOGNITO 25103
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED_INSECURE 25104
#define IDS_PAGE_INFO_STATE_TEXT_MUTED 25105
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED_BY_DEFAULT 25106
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED_BY_DEFAULT 25107
#define IDS_PAGE_INFO_SELECTOR_TOOLTIP 25108
#define IDS_PAGE_INFO_STATE_TEXT_AR_ASK 25109
#define IDS_PAGE_INFO_STATE_TEXT_AUTOMATIC_DOWNLOADS_ASK 25110
#define IDS_PAGE_INFO_STATE_TEXT_BLUETOOTH_DEVICES_ASK 25111
#define IDS_PAGE_INFO_STATE_TEXT_CAMERA_ASK 25112
#define IDS_PAGE_INFO_STATE_TEXT_CAMERA_PAN_TILT_ZOOM_ASK 25113
#define IDS_PAGE_INFO_STATE_TEXT_CLIPBOARD_ASK 25114
#define IDS_PAGE_INFO_STATE_TEXT_FILE_SYSTEM_WRITE_ASK 25115
#define IDS_PAGE_INFO_STATE_TEXT_HID_DEVICES_ASK 25116
#define IDS_PAGE_INFO_STATE_TEXT_LOCATION_ASK 25117
#define IDS_PAGE_INFO_STATE_TEXT_MIC_ASK 25118
#define IDS_PAGE_INFO_STATE_TEXT_MIDI_ASK 25119
#define IDS_PAGE_INFO_STATE_TEXT_NOTIFICATIONS_ASK 25120
#define IDS_PAGE_INFO_STATE_TEXT_USB_ASK 25121
#define IDS_PAGE_INFO_STATE_TEXT_VR_ASK 25122
#define IDS_PAGE_INFO_STATE_TEXT_FONT_ACCESS_ASK 25123
#define IDS_PAGE_INFO_STATE_TEXT_SERIAL_ASK 25124
#define IDS_PAGE_INFO_STATE_TEXT_IDLE_DETECTION_ASK 25125
#define IDS_PAGE_INFO_STATE_TEXT_WINDOW_PLACEMENT_ASK 25126
#define IDS_PAGE_INFO_STATE_TEXT_BLUETOOTH_SCANNING_ASK 25127
#define IDS_PAGE_INFO_USB_DEVICE_SECONDARY_LABEL 25128
#define IDS_PAGE_INFO_USB_DEVICE_ALLOWED_BY_POLICY_LABEL 25129
#define IDS_PAGE_INFO_DELETE_USB_DEVICE 25130
#define IDS_PAGE_INFO_SERIAL_PORT_SECONDARY_LABEL 25131
#define IDS_PAGE_INFO_SERIAL_PORT_ALLOWED_BY_POLICY_LABEL 25132
#define IDS_PAGE_INFO_DELETE_SERIAL_PORT 25133
#define IDS_PAGE_INFO_BLUETOOTH_DEVICE_SECONDARY_LABEL 25134
#define IDS_PAGE_INFO_DELETE_BLUETOOTH_DEVICE 25135
#define IDS_PAGE_INFO_HID_DEVICE_SECONDARY_LABEL 25136
#define IDS_PAGE_INFO_HID_DEVICE_ALLOWED_BY_POLICY_LABEL 25137
#define IDS_PAGE_INFO_DELETE_HID_DEVICE 25138
#define IDS_PAGE_INFO_SITE_SETTINGS_LINK 25139
#define IDS_PAGE_INFO_SITE_SETTINGS_TOOLTIP 25140
#define IDS_PAGE_INFO_PERMISSION_ALLOWED_BY_POLICY 25141
#define IDS_PAGE_INFO_PERMISSION_BLOCKED_BY_POLICY 25142
#define IDS_PAGE_INFO_PERMISSION_ASK_BY_POLICY 25143
#define IDS_PAGE_INFO_PERMISSION_ALLOWED_BY_EXTENSION 25144
#define IDS_PAGE_INFO_PERMISSION_BLOCKED_BY_EXTENSION 25145
#define IDS_PAGE_INFO_PERMISSION_ASK_BY_EXTENSION 25146
#define IDS_PAGE_INFO_PERMISSION_AUTOMATICALLY_BLOCKED 25147
#define IDS_PAGE_INFO_PERMISSION_ADS_SUBTITLE 25148
#define IDS_PAGE_INFO_PERMISSION_MANAGED_BY_POLICY 25149
#define IDS_PAGE_INFO_PERMISSION_MANAGED_BY_EXTENSION 25150
#define IDS_PAGE_INFO_INFOBAR_TEXT 25151
#define IDS_PAGE_INFO_INFOBAR_BUTTON 25152
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS 25153
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_SYNC 25154
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_SIGNED_IN_NON_SYNC 25155
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_ENTERPRISE 25156
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_ENTERPRISE_WITH_ORG_NAME 25157
#define IDS_PAGE_INFO_CHANGE_PASSWORD_BUTTON 25158
#define IDS_PAGE_INFO_PROTECT_ACCOUNT_BUTTON 25159
#define IDS_PAGE_INFO_IGNORE_PASSWORD_WARNING_BUTTON 25160
#define IDS_PAGE_INFO_ALLOWLIST_PASSWORD_REUSE_BUTTON 25161
#define IDS_PAGE_INFO_BILLING_SUMMARY 25162
#define IDS_PAGE_INFO_BILLING_DETAILS 25163
#define IDS_PAGE_INFO_VR_PRESENTING_TEXT 25164
#define IDS_PAGE_INFO_VR_TURN_OFF_BUTTON_TEXT 25165
#define IDS_CERT_INFO_SUBJECT_GROUP 25166
#define IDS_CERT_INFO_ISSUER_GROUP 25167
#define IDS_CERT_INFO_COMMON_NAME_LABEL 25168
#define IDS_CERT_INFO_ORGANIZATION_LABEL 25169
#define IDS_CERT_INFO_ORGANIZATIONAL_UNIT_LABEL 25170
#define IDS_CERT_INFO_SERIAL_NUMBER_LABEL 25171
#define IDS_CERT_INFO_VALIDITY_GROUP 25172
#define IDS_CERT_INFO_ISSUED_ON_LABEL 25173
#define IDS_CERT_INFO_EXPIRES_ON_LABEL 25174
#define IDS_CERT_INFO_FINGERPRINTS_GROUP 25175
#define IDS_CERT_INFO_SHA256_FINGERPRINT_LABEL 25176
#define IDS_CERT_INFO_SHA1_FINGERPRINT_LABEL 25177
#define IDS_CERT_DETAILS_EXTENSIONS 25178
#define IDS_CERT_X509_SUBJECT_ALT_NAME 25179
#define IDS_PAGE_INFO_SECURITY_SUBPAGE_BUTTON 25180
#define IDS_PAGE_INFO_SECURITY_SUBPAGE_HEADER 25181
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_BUTTON_TOOLTIP 25182
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_MANAGE_BUTTON 25183
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_MANAGE_BUTTON_TOOLTIP 25184
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_REMEMBER_THIS_SETTING 25185
#define IDS_PAGE_INFO_RESET_PERMISSIONS 25186
#define IDS_PAGE_INFO_ABOUT_THIS_SITE_HEADER 25187
#define IDS_PAGE_INFO_ABOUT_THIS_SITE_TOOLTIP 25188
#define IDS_PAGE_INFO_ABOUT_THIS_SITE_SUBPAGE_FROM_LABEL 25189
#define IDS_PAGE_INFO_MORE_ABOUT_THIS_PAGE 25190
#define IDS_PAGE_INFO_MORE_ABOUT_THIS_PAGE_TOOLTIP 25191
#define IDS_PAGE_INFO_HISTORY 25192
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_TODAY 25193
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_YESTERDAY 25194
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_DAYS 25195
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_DATE 25196
#define IDS_PAGE_INFO_AD_PERSONALIZATION_HEADER 25197
#define IDS_PAGE_INFO_AD_PERSONALIZATION_TOOLTIP 25198
#define IDS_PAGE_INFO_AD_PERSONALIZATION_TOPICS_AND_INTEREST_GROUP_DESCRIPTION 25199
#define IDS_PAGE_INFO_AD_PERSONALIZATION_TOPICS_DESCRIPTION 25200
#define IDS_PAGE_INFO_AD_PERSONALIZATION_INTEREST_GROUP_DESCRIPTION 25201
#define IDS_PAGE_INFO_AD_PERSONALIZATION_SUBPAGE_MANAGE_BUTTON 25202
#define IDS_PAINT_PREVIEW_COMPOSITOR_SERVICE_DISPLAY_NAME 25203
#define IDS_LEAK_CHECK_CREDENTIALS 25204
#define IDS_CREDENTIAL_LEAK_CHANGE_AUTOMATICALLY 25205
#define IDS_CREDENTIAL_LEAK_TITLE_CHANGE 25206
#define IDS_CREDENTIAL_LEAK_TITLE_CHANGE_AUTOMATICALLY 25207
#define IDS_CREDENTIAL_LEAK_TITLE_CHECK 25208
#define IDS_CREDENTIAL_LEAK_TITLE_CHECK_GPM 25209
#define IDS_CREDENTIAL_LEAK_CHECK_PASSWORDS_MESSAGE 25210
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_MESSAGE 25211
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_AUTOMATICALLY_MESSAGE 25212
#define IDS_CREDENTIAL_LEAK_CHANGE_AND_CHECK_PASSWORDS_MESSAGE 25213
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_AUTOMATICALLY_MESSAGE_GPM 25214
#define IDS_CREDENTIAL_LEAK_CHECK_PASSWORDS_MESSAGE_GPM_BRANDED 25215
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_MESSAGE_GPM_BRANDED 25216
#define IDS_CREDENTIAL_LEAK_CHANGE_AND_CHECK_PASSWORDS_MESSAGE_GPM_BRANDED 25217
#define IDS_CREDENTIAL_LEAK_CHECK_PASSWORDS_MESSAGE_GPM_NON_BRANDED 25218
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_MESSAGE_GPM_NON_BRANDED 25219
#define IDS_CREDENTIAL_LEAK_CHANGE_AND_CHECK_PASSWORDS_MESSAGE_GPM_NON_BRANDED 25220
#define IDS_PASSWORD_MANAGER_LEAK_HELP_MESSAGE 25222
#define IDS_MANAGE_PASSWORDS_AUTO_SIGNIN_TITLE 25223
#define IDS_PASSWORD_MANAGER_EMPTY_LOGIN 25224
#define IDS_PASSWORD_MANAGER_OPT_INTO_ACCOUNT_STORE 25225
#define IDS_PASSWORD_MANAGER_RE_SIGNIN_ACCOUNT_STORE 25226
#define IDS_PASSWORD_MANAGER_NO_ACCOUNT_STORE_MATCHES 25227
#define IDS_PASSWORD_MANAGER_MANAGE_PASSWORDS 25228
#define IDS_PASSWORD_MANAGER_GENERATE_PASSWORD 25229
#define IDS_PASSWORD_MANAGER_EXCEPTIONS_TAB_TITLE 25230
#define IDS_PASSWORD_MANAGER_SHOW_PASSWORDS_TAB_TITLE 25231
#define IDS_PASSWORD_MANAGER_SMART_LOCK 25232
#define IDS_PASSWORD_MANAGER_DEFAULT_EXPORT_FILENAME 25233
#define IDS_PASSWORD_MANAGER_PASSWORD_FOR_ACCOUNT 25234
#define IDS_PAYMENTS_TITLE 25235
#define IDS_PAYMENTS_ERROR_MESSAGE_DIALOG_TITLE 25236
#define IDS_PAYMENTS_METHOD_OF_PAYMENT_LABEL 25237
#define IDS_PAYMENTS_CONTACT_DETAILS_LABEL 25238
#define IDS_PAYMENTS_ADD_CONTACT_DETAILS_LABEL 25239
#define IDS_PAYMENTS_EDIT_CONTACT_DETAILS_LABEL 25240
#define IDS_PAYMENTS_ADD_CARD_LABEL 25241
#define IDS_PAYMENTS_ADD_BILLING_ADDRESS 25242
#define IDS_PAYMENTS_ADD_NAME_ON_CARD 25243
#define IDS_PAYMENTS_ADD_VALID_CARD_NUMBER 25244
#define IDS_PAYMENTS_ADD_MORE_INFORMATION 25245
#define IDS_PAYMENTS_EDIT_CARD 25246
#define IDS_PAYMENTS_ADD_PHONE_NUMBER 25247
#define IDS_PAYMENTS_ADD_RECIPIENT 25248
#define IDS_PAYMENTS_ADD_VALID_ADDRESS 25249
#define IDS_PAYMENTS_ADD_EMAIL 25250
#define IDS_PAYMENTS_ADD_NAME 25251
#define IDS_PAYMENTS_ORDER_SUMMARY_LABEL 25252
#define IDS_PAYMENT_REQUEST_PAYMENT_METHOD_SECTION_NAME 25253
#define IDS_PAYMENT_REQUEST_CONTACT_INFO_SECTION_NAME 25254
#define IDS_PAYMENTS_SHIPPING_SUMMARY_LABEL 25255
#define IDS_PAYMENTS_SHIPPING_ADDRESS_LABEL 25256
#define IDS_PAYMENTS_SHIPPING_OPTION_LABEL 25257
#define IDS_PAYMENTS_DELIVERY_SUMMARY_LABEL 25258
#define IDS_PAYMENTS_DELIVERY_ADDRESS_LABEL 25259
#define IDS_PAYMENTS_DELIVERY_OPTION_LABEL 25260
#define IDS_PAYMENTS_PICKUP_SUMMARY_LABEL 25261
#define IDS_PAYMENTS_PICKUP_ADDRESS_LABEL 25262
#define IDS_PAYMENTS_PICKUP_OPTION_LABEL 25263
#define IDS_PAYMENTS_EDIT_BUTTON 25264
#define IDS_PAYMENTS_PAY_BUTTON 25265
#define IDS_PAYMENTS_CONTINUE_BUTTON 25266
#define IDS_PAYMENTS_ADD_CONTACT 25267
#define IDS_PAYMENTS_ADD_CARD 25268
#define IDS_PAYMENTS_ADD_ADDRESS 25269
#define IDS_PAYMENTS_EDIT_ADDRESS 25270
#define IDS_PAYMENTS_CANCEL_PAYMENT 25271
#define IDS_PAYMENTS_NAME_FIELD_IN_CONTACT_DETAILS 25272
#define IDS_PAYMENTS_PHONE_FIELD_IN_CONTACT_DETAILS 25273
#define IDS_PAYMENTS_EMAIL_FIELD_IN_CONTACT_DETAILS 25274
#define IDS_PAYMENTS_SAVE_CARD_TO_DEVICE_CHECKBOX 25275
#define IDS_PAYMENTS_ACCEPTED_CARDS_LABEL 25276
#define IDS_PAYMENTS_ACCEPTED_CREDIT_CARDS_LABEL 25282
#define IDS_PAYMENTS_CREDIT_CARD_EXPIRATION_DATE_ABBR 25277
#define IDS_PAYMENTS_LOADING_MESSAGE 25278
#define IDS_PAYMENTS_PROCESSING_MESSAGE 25279
#define IDS_PAYMENTS_CHECKING_OPTION 25280
#define IDS_PAYMENTS_UPDATED_LABEL 25281
#define IDS_PAYMENTS_ERROR_MESSAGE 25283
#define IDS_PAYMENTS_CARD_AND_ADDRESS_SETTINGS 25284
#define IDS_PAYMENTS_CARD_AND_ADDRESS_SETTINGS_SIGNED_IN 25285
#define IDS_PAYMENTS_CARD_AND_ADDRESS_SETTINGS_SIGNED_OUT 25286
#define IDS_SETTINGS_CAN_MAKE_PAYMENT_TOGGLE_LABEL 25287
#define IDS_PAYMENTS_REQUIRED_FIELD_MESSAGE 25306
#define IDS_PAYMENTS_VALIDATION_INVALID_NAME 25307
#define IDS_PAYMENTS_VALIDATION_INVALID_CREDIT_CARD_EXPIRATION_YEAR 25308
#define IDS_PAYMENTS_VALIDATION_INVALID_CREDIT_CARD_EXPIRATION_MONTH 25309
#define IDS_PAYMENTS_VALIDATION_INVALID_CREDIT_CARD_EXPIRED 25310
#define IDS_PAYMENTS_VALIDATION_UNSUPPORTED_CREDIT_CARD_TYPE 25311
#define IDS_PAYMENTS_PHONE_INVALID_VALIDATION_MESSAGE 25312
#define IDS_PAYMENTS_EMAIL_INVALID_VALIDATION_MESSAGE 25313
#define IDS_PAYMENTS_CARD_NUMBER_INVALID_VALIDATION_MESSAGE 25314
#define IDS_PAYMENTS_CARD_EXPIRATION_INVALID_VALIDATION_MESSAGE 25315
#define IDS_PAYMENTS_INVALID_ADDRESS 25316
#define IDS_PAYMENTS_BILLING_ADDRESS_REQUIRED 25317
#define IDS_PAYMENTS_NAME_ON_CARD_REQUIRED 25318
#define IDS_PAYMENTS_CARD_BILLING_ADDRESS_REQUIRED 25319
#define IDS_PAYMENTS_MORE_INFORMATION_REQUIRED 25320
#define IDS_PAYMENTS_PHONE_NUMBER_REQUIRED 25321
#define IDS_PAYMENTS_RECIPIENT_REQUIRED 25322
#define IDS_PAYMENTS_EMAIL_REQUIRED 25323
#define IDS_PAYMENTS_NAME_REQUIRED 25324
#define IDS_PREF_EDIT_DIALOG_FIELD_REQUIRED_VALIDATION_MESSAGE 25325
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_SECTION_TOTAL_FORMAT 25326
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_SHEET_TOTAL_FORMAT 25327
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_MORE_ITEMS 25328
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_MULTIPLE_CURRENCY_INDICATOR 25329
#define IDS_PAYMENTS_SELECT_SHIPPING_ADDRESS_FOR_SHIPPING_METHODS 25330
#define IDS_PAYMENTS_UNSUPPORTED_SHIPPING_ADDRESS 25331
#define IDS_PAYMENTS_UNSUPPORTED_SHIPPING_OPTION 25332
#define IDS_PAYMENTS_SELECT_DELIVERY_ADDRESS_FOR_DELIVERY_METHODS 25333
#define IDS_PAYMENTS_UNSUPPORTED_DELIVERY_ADDRESS 25334
#define IDS_PAYMENTS_UNSUPPORTED_DELIVERY_OPTION 25335
#define IDS_PAYMENTS_SELECT_PICKUP_ADDRESS_FOR_PICKUP_METHODS 25336
#define IDS_PAYMENTS_UNSUPPORTED_PICKUP_ADDRESS 25337
#define IDS_PAYMENTS_UNSUPPORTED_PICKUP_OPTION 25338
#define IDS_PAYMENTS_ANDROID_APP_ERROR 25339
#define IDS_UTILITY_PROCESS_PAYMENT_MANIFEST_PARSER_NAME 25340
#define IDS_PAYMENT_REQUEST_PAYMENT_METHODS_PREVIEW 25342
#define IDS_PAYMENT_REQUEST_SHIPPING_ADDRESSES_PREVIEW 25343
#define IDS_PAYMENT_REQUEST_SHIPPING_OPTIONS_PREVIEW 25344
#define IDS_PAYMENT_REQUEST_CONTACTS_PREVIEW 25345
#define IDS_PAYMENTS_BACK 25346
#define IDS_PAYMENTS_EDIT 25347
#define IDS_PAYMENTS_ROW_ACCESSIBLE_NAME_FORMAT 25348
#define IDS_PAYMENTS_ROW_ACCESSIBLE_NAME_SELECTED_FORMAT 25349
#define IDS_PAYMENTS_PROFILE_LABELS_ACCESSIBLE_FORMAT 25350
#define IDS_PAYMENTS_ACCESSIBLE_LABEL_WITH_ERROR 25351
#define IDS_PAYMENTS_ORDER_SUMMARY_ACCESSIBLE_LABEL 25352
#define IDS_PAYMENT_HANDLER_SHEET_DESCRIPTION 25353
#define IDS_PAYMENT_HANDLER_SHEET_OPENED_HALF 25354
#define IDS_PAYMENT_HANDLER_SHEET_OPENED_FULL 25355
#define IDS_PAYMENT_HANDLER_SHEET_CLOSED 25356
#define IDS_SECURE_PAYMENT_CONFIRMATION_VERIFY_PURCHASE 25357
#define IDS_SECURE_PAYMENT_CONFIRMATION_STORE_LABEL 25358
#define IDS_SECURE_PAYMENT_CONFIRMATION_TOTAL_LABEL 25359
#define IDS_SECURE_PAYMENT_CONFIRMATION_VERIFY_BUTTON_LABEL 25360
#define IDS_NO_MATCHING_CREDENTIAL_DESCRIPTION 25361
#define IDS_PDF_DOWNLOAD_ORIGINAL 25368
#define IDS_PDF_DOWNLOAD_EDITED 25369
#define IDS_PDF_PRESENT 25370
#define IDS_PDF_NEED_PASSWORD 25371
#define IDS_PDF_PASSWORD_DIALOG_TITLE 25372
#define IDS_PDF_PASSWORD_SUBMIT 25373
#define IDS_PDF_PASSWORD_INVALID 25374
#define IDS_PDF_PAGE_LOADING 25375
#define IDS_PDF_ERROR_DIALOG_TITLE 25376
#define IDS_PDF_PAGE_LOAD_FAILED 25377
#define IDS_PDF_PAGE_RELOAD_BUTTON 25378
#define IDS_PDF_BOOKMARKS 25379
#define IDS_PDF_BOOKMARK_EXPAND_ICON_ARIA_LABEL 25380
#define IDS_PDF_PROPERTIES_DIALOG_TITLE 25381
#define IDS_PDF_PROPERTIES_FILE_NAME 25382
#define IDS_PDF_PROPERTIES_FILE_SIZE 25383
#define IDS_PDF_PROPERTIES_TITLE 25384
#define IDS_PDF_PROPERTIES_AUTHOR 25385
#define IDS_PDF_PROPERTIES_SUBJECT 25386
#define IDS_PDF_PROPERTIES_KEYWORDS 25387
#define IDS_PDF_PROPERTIES_CREATED 25388
#define IDS_PDF_PROPERTIES_MODIFIED 25389
#define IDS_PDF_PROPERTIES_APPLICATION 25390
#define IDS_PDF_PROPERTIES_PDF_PRODUCER 25391
#define IDS_PDF_PROPERTIES_PDF_VERSION 25392
#define IDS_PDF_PROPERTIES_PAGE_COUNT 25393
#define IDS_PDF_PROPERTIES_PAGE_SIZE 25394
#define IDS_PDF_PROPERTIES_PAGE_SIZE_VALUE_INCH 25395
#define IDS_PDF_PROPERTIES_PAGE_SIZE_VALUE_MM 25396
#define IDS_PDF_PROPERTIES_PAGE_SIZE_PORTRAIT 25397
#define IDS_PDF_PROPERTIES_PAGE_SIZE_LANDSCAPE 25398
#define IDS_PDF_PROPERTIES_PAGE_SIZE_SQUARE 25399
#define IDS_PDF_PROPERTIES_PAGE_SIZE_VARIABLE 25400
#define IDS_PDF_PROPERTIES_FAST_WEB_VIEW 25401
#define IDS_PDF_PROPERTIES_FAST_WEB_VIEW_NO 25402
#define IDS_PDF_PROPERTIES_FAST_WEB_VIEW_YES 25403
#define IDS_PDF_TOOLTIP_ROTATE_CCW 25404
#define IDS_PDF_TOOLTIP_DOWNLOAD 25405
#define IDS_PDF_TOOLTIP_PRINT 25406
#define IDS_PDF_TOOLTIP_FIT_PAGE 25407
#define IDS_PDF_TOOLTIP_FIT_WIDTH 25408
#define IDS_PDF_TWO_UP_VIEW_ENABLE 25409
#define IDS_PDF_ANNOTATIONS_SHOW_TOGGLE 25410
#define IDS_PDF_ZOOM_TEXT_INPUT_ARIA_LABEL 25411
#define IDS_PDF_TOOLTIP_ZOOM_IN 25412
#define IDS_PDF_TOOLTIP_ZOOM_OUT 25413
#define IDS_PDF_TOOLTIP_THUMBNAILS 25414
#define IDS_PDF_TOOLTIP_DOCUMENT_OUTLINE 25415
#define IDS_PDF_LABEL_PAGE_NUMBER 25416
#define IDS_PDF_PAGE_INDEX 25417
#define IDS_PDF_DOCUMENT_PAGE_COUNT 25418
#define IDS_PDF_THUMBNAIL_PAGE_ARIA_LABEL 25419
#define IDS_AX_ROLE_DESCRIPTION_PDF_HIGHLIGHT 25471
#define IDS_AX_ROLE_DESCRIPTION_PDF_POPUP_NOTE 25472
#define IDS_GEOLOCATION_INFOBAR_TEXT 25473
#define IDS_GEOLOCATION_INFOBAR_PERMISSION_FRAGMENT 25500
#define IDS_NOTIFICATION_PERMISSIONS_FRAGMENT 25501
#define IDS_MIDI_SYSEX_PERMISSION_FRAGMENT 25502
#define IDS_MEDIA_CAPTURE_AUDIO_ONLY_PERMISSION_FRAGMENT 25503
#define IDS_MEDIA_CAPTURE_VIDEO_ONLY_PERMISSION_FRAGMENT 25504
#define IDS_MEDIA_CAPTURE_CAMERA_PAN_TILT_ZOOM_PERMISSION_FRAGMENT 25505
#define IDS_ACCESSIBILITY_EVENTS_PERMISSION_FRAGMENT 25506
#define IDS_CLIPBOARD_PERMISSION_FRAGMENT 25507
#define IDS_VR_PERMISSION_FRAGMENT 25508
#define IDS_AR_PERMISSION_FRAGMENT 25509
#define IDS_STORAGE_ACCESS_PERMISSION_FRAGMENT 25510
#define IDS_STORAGE_ACCESS_PERMISSION_EXPLANATION 25511
#define IDS_WINDOW_PLACEMENT_PERMISSION_FRAGMENT 25512
#define IDS_FONT_ACCESS_PERMISSION_FRAGMENT 25513
#define IDS_IDLE_DETECTION_PERMISSION_FRAGMENT 25514
#define IDS_MULTI_DOWNLOAD_PERMISSION_FRAGMENT 25515
#define IDS_SECURITY_KEY_ATTESTATION_PERMISSION_FRAGMENT 25516
#define IDS_U2F_API_PERMISSION_FRAGMENT 25517
#define IDS_U2F_API_PERMISSION_EXPLANATION 25518
#define IDS_PERMISSION_ALLOW 25519
#define IDS_PERMISSION_DENY 25520
#define IDS_PERMISSION_ALLOW_ONCE 25521
#define IDS_PERMISSION_ALLOW_ALWAYS 25522
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ONE_TIME 25523
#define IDS_BLUETOOTH_DEVICE_CHOOSER_PROMPT_ORIGIN 25524
#define IDS_BLUETOOTH_DEVICE_CHOOSER_NO_DEVICES_FOUND_PROMPT 25525
#define IDS_BLUETOOTH_DEVICE_CHOOSER_PAIR_BUTTON_TEXT 25526
#define IDS_BLUETOOTH_DEVICE_CHOOSER_SCANNING_LABEL 25527
#define IDS_BLUETOOTH_DEVICE_CHOOSER_SCANNING_LABEL_TOOLTIP 25528
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_WITH_ID 25529
#define IDS_BLUETOOTH_SCANNING_PROMPT_ORIGIN 25530
#define IDS_BLUETOOTH_SCANNING_DEVICE_UNKNOWN 25531
#define IDS_BLUETOOTH_SCANNING_PROMPT_NO_DEVICES_FOUND_PROMPT 25532
#define IDS_BLUETOOTH_SCANNING_PROMPT_ALLOW_BUTTON_TEXT 25533
#define IDS_BLUETOOTH_SCANNING_PROMPT_BLOCK_BUTTON_TEXT 25534
#define IDS_DEVICE_CHOOSER_CANCEL_BUTTON_TEXT 25535
#define IDS_DEVICE_CHOOSER_NO_DEVICES_FOUND_PROMPT 25536
#define IDS_USB_DEVICE_CHOOSER_PROMPT_ORIGIN 25537
#define IDS_USB_DEVICE_CHOOSER_CONNECT_BUTTON_TEXT 25538
#define IDS_USB_DEVICE_CHOOSER_LOADING_LABEL 25539
#define IDS_USB_DEVICE_CHOOSER_LOADING_LABEL_TOOLTIP 25540
#define IDS_GEOLOCATION_PERMISSION_CHIP 25541
#define IDS_NOTIFICATION_PERMISSIONS_CHIP 25542
#define IDS_MIDI_SYSEX_PERMISSION_CHIP 25543
#define IDS_MEDIA_CAPTURE_AUDIO_ONLY_PERMISSION_CHIP 25544
#define IDS_MEDIA_CAPTURE_VIDEO_ONLY_PERMISSION_CHIP 25545
#define IDS_MEDIA_CAPTURE_VIDEO_AND_AUDIO_PERMISSION_CHIP 25546
#define IDS_CLIPBOARD_PERMISSION_CHIP 25547
#define IDS_VR_PERMISSION_CHIP 25548
#define IDS_AR_PERMISSION_CHIP 25549
#define IDS_IDLE_DETECTION_PERMISSION_CHIP 25550
#define IDS_GEOLOCATION_PERMISSION_BLOCKED_CHIP 25551
#define IDS_NOTIFICATION_PERMISSIONS_BLOCKED_CHIP 25552
#define IDS_REQUEST_QUOTA_INFOBAR_TEXT 25553
#define IDS_REQUEST_LARGE_QUOTA_INFOBAR_TEXT 25554
#define IDS_REQUEST_QUOTA_PERMISSION_FRAGMENT 25555
#define IDS_POLICY_DM_STATUS_SUCCESS 25556
#define IDS_POLICY_DM_STATUS_REQUEST_INVALID 25557
#define IDS_POLICY_DM_STATUS_REQUEST_FAILED 25558
#define IDS_POLICY_DM_STATUS_TEMPORARY_UNAVAILABLE 25559
#define IDS_POLICY_DM_STATUS_HTTP_STATUS_ERROR 25560
#define IDS_POLICY_DM_STATUS_RESPONSE_DECODING_ERROR 25561
#define IDS_POLICY_DM_STATUS_SERVICE_MANAGEMENT_NOT_SUPPORTED 25562
#define IDS_POLICY_DM_STATUS_SERVICE_DEVICE_NOT_FOUND 25563
#define IDS_POLICY_DM_STATUS_SERVICE_MANAGEMENT_TOKEN_INVALID 25564
#define IDS_POLICY_DM_STATUS_SERVICE_ACTIVATION_PENDING 25565
#define IDS_POLICY_DM_STATUS_SERVICE_INVALID_SERIAL_NUMBER 25566
#define IDS_POLICY_DM_STATUS_SERVICE_DEVICE_ID_CONFLICT 25567
#define IDS_POLICY_DM_STATUS_SERVICE_MISSING_LICENSES 25568
#define IDS_POLICY_DM_STATUS_SERVICE_DEPROVISIONED 25569
#define IDS_POLICY_DM_STATUS_SERVICE_POLICY_NOT_FOUND 25570
#define IDS_POLICY_DM_STATUS_UNKNOWN_ERROR 25571
#define IDS_POLICY_DM_STATUS_SERVICE_DOMAIN_MISMATCH 25572
#define IDS_POLICY_DM_STATUS_CANNOT_SIGN_REQUEST 25573
#define IDS_POLICY_DM_STATUS_REQUEST_TOO_LARGE 25574
#define IDS_POLICY_DM_STATUS_SERVICE_TOO_MANY_REQUESTS 25575
#define IDS_POLICY_DM_STATUS_CONSUMER_ACCOUNT_WITH_PACKAGED_LICENSE 25576
#define IDS_POLICY_DM_STATUS_ENTERPRISE_ACCOUNT_IS_NOT_ELIGIBLE_TO_ENROLL 25577
#define IDS_POLICY_VALIDATION_OK 25578
#define IDS_POLICY_VALIDATION_BAD_INITIAL_SIGNATURE 25579
#define IDS_POLICY_VALIDATION_BAD_SIGNATURE 25580
#define IDS_POLICY_VALIDATION_ERROR_CODE_PRESENT 25581
#define IDS_POLICY_VALIDATION_PAYLOAD_PARSE_ERROR 25582
#define IDS_POLICY_VALIDATION_WRONG_POLICY_TYPE 25583
#define IDS_POLICY_VALIDATION_WRONG_SETTINGS_ENTITY_ID 25584
#define IDS_POLICY_VALIDATION_BAD_TIMESTAMP 25585
#define IDS_POLICY_VALIDATION_BAD_DM_TOKEN 25586
#define IDS_POLICY_VALIDATION_BAD_DEVICE_ID 25587
#define IDS_POLICY_VALIDATION_BAD_USER 25588
#define IDS_POLICY_VALIDATION_POLICY_PARSE_ERROR 25589
#define IDS_POLICY_VALIDATION_BAD_KEY_VERIFICATION_SIGNATURE 25590
#define IDS_POLICY_VALIDATION_VALUE_WARNING 25591
#define IDS_POLICY_VALIDATION_VALUE_ERROR 25592
#define IDS_POLICY_VALIDATION_UNKNOWN_ERROR 25593
#define IDS_POLICY_STORE_STATUS_OK 25594
#define IDS_POLICY_STORE_STATUS_LOAD_ERROR 25595
#define IDS_POLICY_STORE_STATUS_STORE_ERROR 25596
#define IDS_POLICY_STORE_STATUS_PARSE_ERROR 25597
#define IDS_POLICY_STORE_STATUS_SERIALIZE_ERROR 25598
#define IDS_POLICY_STORE_STATUS_VALIDATION_ERROR 25599
#define IDS_POLICY_STORE_STATUS_BAD_STATE 25600
#define IDS_POLICY_STORE_STATUS_UNKNOWN_ERROR 25601
#define IDS_POLICY_ASSOCIATION_STATE_ACTIVE 25602
#define IDS_POLICY_ASSOCIATION_STATE_UNMANAGED 25603
#define IDS_POLICY_ASSOCIATION_STATE_DEPROVISIONED 25604
#define IDS_POLICY_TYPE_ERROR 25605
#define IDS_POLICY_OUT_OF_RANGE_ERROR 25606
#define IDS_POLICY_VALUE_FORMAT_ERROR 25607
#define IDS_POLICY_CLOUD_SOURCE_ONLY_ERROR 25608
#define IDS_POLICY_CLOUD_USER_ONLY_ERROR 25609
#define IDS_POLICY_CLOUD_MANAGEMENT_ENROLLMENT_ONLY_ERROR 25610
#define IDS_POLICY_DEFAULT_SEARCH_DISABLED 25611
#define IDS_POLICY_NOT_SPECIFIED_ERROR 25612
#define IDS_POLICY_EXTENSION_SETTINGS_ORIGIN_LIMIT_WARNING 25613
#define IDS_POLICY_URL_ALLOW_BLOCK_LIST_MAX_FILTERS_LIMIT_WARNING 25614
#define IDS_POLICY_SUBKEY_ERROR 25615
#define IDS_POLICY_LIST_ENTRY_ERROR 25616
#define IDS_POLICY_SCHEMA_VALIDATION_ERROR 25617
#define IDS_POLICY_INVALID_JSON_ERROR 25618
#define IDS_POLICY_INVALID_SEARCH_URL_ERROR 25619
#define IDS_POLICY_INVALID_SECURE_DNS_MODE_ERROR 25620
#define IDS_POLICY_SECURE_DNS_TEMPLATES_INVALID_ERROR 25621
#define IDS_POLICY_SECURE_DNS_TEMPLATES_IRRELEVANT_MODE_ERROR 25622
#define IDS_POLICY_SECURE_DNS_TEMPLATES_INVALID_MODE_ERROR 25623
#define IDS_POLICY_SECURE_DNS_TEMPLATES_UNSET_MODE_ERROR 25624
#define IDS_POLICY_SECURE_DNS_TEMPLATES_NOT_SPECIFIED_ERROR 25625
#define IDS_POLICY_INVALID_PROXY_MODE_ERROR 25626
#define IDS_POLICY_INVALID_UPDATE_URL_ERROR 25627
#define IDS_POLICY_OFF_CWS_URL_ERROR 25629
#define IDS_POLICY_HOMEPAGE_LOCATION_ERROR 25630
#define IDS_POLICY_PROXY_MODE_DISABLED_ERROR 25631
#define IDS_POLICY_PROXY_MODE_AUTO_DETECT_ERROR 25632
#define IDS_POLICY_PROXY_MODE_PAC_URL_ERROR 25633
#define IDS_POLICY_PROXY_MODE_FIXED_SERVERS_ERROR 25634
#define IDS_POLICY_PROXY_MODE_SYSTEM_ERROR 25635
#define IDS_POLICY_PROXY_BOTH_SPECIFIED_ERROR 25636
#define IDS_POLICY_PROXY_NEITHER_SPECIFIED_ERROR 25637
#define IDS_POLICY_OVERRIDDEN 25638
#define IDS_POLICY_VALUE_DEPRECATED 25639
#define IDS_POLICY_DEPENDENCY_ERROR 25640
#define IDS_POLICY_USER_IS_NOT_AFFILIATED_ERROR 25641
#define IDS_POLICY_LEVEL_ERROR 25646
#define IDS_POLICY_OK 25647
#define IDS_POLICY_UNSET 25648
#define IDS_POLICY_UNKNOWN 25649
#define IDS_POLICY_PROTO_PARSING_ERROR 25650
#define IDS_POLICY_HEX_COLOR_ERROR 25651
#define IDS_POLICY_TITLE 25652
#define IDS_POLICY_FILTER_PLACEHOLDER 25653
#define IDS_POLICY_RELOAD_POLICIES 25654
#define IDS_POLICY_LOADING_POLICIES 25655
#define IDS_POLICY_LOAD_POLICIES_DONE 25656
#define IDS_EXPORT_POLICIES_JSON 25657
#define IDS_POLICY_STATUS 25658
#define IDS_POLICY_STATUS_DEVICE 25659
#define IDS_POLICY_STATUS_USER 25660
#define IDS_POLICY_STATUS_MACHINE 25661
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_DOMAIN 25663
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_TOKEN 25664
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_DEVICE_ID 25665
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_MACHINE_NAME 25666
#define IDS_POLICY_LABEL_USERNAME 25667
#define IDS_POLICY_LABEL_GAIA_ID 25668
#define IDS_POLICY_LABEL_CLIENT_ID 25669
#define IDS_POLICY_LABEL_ASSET_ID 25670
#define IDS_POLICY_LABEL_LOCATION 25671
#define IDS_POLICY_LABEL_DIRECTORY_API_ID 25672
#define IDS_POLICY_LABEL_MANAGED_BY 25673
#define IDS_POLICY_LABEL_TIME_SINCE_LAST_FETCH_ATTEMPT 25674
#define IDS_POLICY_LABEL_TIME_SINCE_LAST_REFRESH 25675
#define IDS_POLICY_LABEL_LAST_CLOUD_REPORT_SENT_TIMESTAMP 25676
#define IDS_POLICY_NOT_SPECIFIED 25677
#define IDS_POLICY_LABEL_PUSH_POLICIES 25678
#define IDS_POLICY_PUSH_POLICIES_ON 25679
#define IDS_POLICY_PUSH_POLICIES_OFF 25680
#define IDS_POLICY_NEVER_FETCHED 25681
#define IDS_POLICY_LABEL_REFRESH_INTERVAL 25682
#define IDS_POLICY_LABEL_CONFLICT 25683
#define IDS_POLICY_LABEL_SUPERSEDING 25684
#define IDS_POLICY_LABEL_CONFLICT_VALUE 25685
#define IDS_POLICY_LABEL_SUPERSEDED_VALUE 25686
#define IDS_POLICY_LABEL_ERROR 25687
#define IDS_POLICY_LABEL_DEPRECATED 25688
#define IDS_POLICY_LABEL_FUTURE 25689
#define IDS_POLICY_LABEL_IGNORED 25690
#define IDS_POLICY_LABEL_VALUE 25691
#define IDS_POLICY_LABEL_VERSION 25692
#define IDS_POLICY_LABEL_STATUS 25693
#define IDS_POLICY_LABEL_INFO 25694
#define IDS_POLICY_LABEL_PRECEDENCE 25695
#define IDS_POLICY_SHOW_UNSET 25696
#define IDS_POLICY_NO_POLICIES_SET 25697
#define IDS_POLICY_HEADER_SCOPE 25698
#define IDS_POLICY_HEADER_LEVEL 25699
#define IDS_POLICY_HEADER_NAME 25700
#define IDS_POLICY_HEADER_VALUE 25701
#define IDS_POLICY_HEADER_STATUS 25702
#define IDS_POLICY_HEADER_SOURCE 25703
#define IDS_POLICY_HEADER_WARNING 25704
#define IDS_POLICY_SHOW_MORE 25705
#define IDS_POLICY_SHOW_LESS 25706
#define IDS_POLICY_LEARN_MORE 25707
#define IDS_POLICY_SCOPE_USER 25708
#define IDS_POLICY_SCOPE_DEVICE 25709
#define IDS_POLICY_LEVEL_RECOMMENDED 25710
#define IDS_POLICY_LEVEL_MANDATORY 25711
#define IDS_POLICY_SOURCE_ENTERPRISE_DEFAULT 25712
#define IDS_POLICY_SOURCE_DEFAULT 25713
#define IDS_POLICY_SOURCE_COMMAND_LINE 25714
#define IDS_POLICY_SOURCE_CLOUD 25715
#define IDS_POLICY_SOURCE_MERGED 25716
#define IDS_POLICY_SOURCE_CLOUD_FROM_ASH 25717
#define IDS_POLICY_SOURCE_RESTRICTED_MANAGED_GUEST_SESSION_OVERRIDE 25718
#define IDS_POLICY_SOURCE_ACTIVE_DIRECTORY 25719
#define IDS_POLICY_SOURCE_PLATFORM 25720
#define IDS_POLICY_SOURCE_DEVICE_LOCAL_ACCOUNT_OVERRIDE 25721
#define IDS_POLICY_RISK_TAG_FULL_ADMIN_ACCESS 25722
#define IDS_POLICY_RISK_TAG_SYSTEM_SECURITY 25723
#define IDS_POLICY_RISK_TAG_WEBSITE_SHARING 25724
#define IDS_POLICY_RISK_TAG_ADMIN_SHARING 25725
#define IDS_POLICY_RISK_TAG_FILTERING 25726
#define IDS_POLICY_RISK_TAG_LOCAL_DATA_ACCESS 25727
#define IDS_POLICY_RISK_TAG_GOOGLE_SHARING 25728
#define IDS_POLICY_SHOW_EXPANDED_STATUS 25729
#define IDS_POLICY_HIDE_EXPANDED_STATUS 25730
#define IDS_POLICY_LIST_MERGING_WRONG_POLICY_TYPE_SPECIFIED 25731
#define IDS_POLICY_DICTIONARY_MERGING_WRONG_POLICY_TYPE_SPECIFIED 25732
#define IDS_POLICY_DICTIONARY_MERGING_POLICY_NOT_ALLOWED 25733
#define IDS_POLICY_CONFLICT_SAME_VALUE 25734
#define IDS_POLICY_CONFLICT_DIFF_VALUE 25735
#define IDS_POLICY_MIGRATED_OLD_POLICY 25736
#define IDS_POLICY_MIGRATED_NEW_POLICY 25737
#define IDS_POLICY_BLOCKED 25738
#define IDS_POLICY_INVALID 25739
#define IDS_POLICY_IGNORED_BY_GROUP_MERGING 25740
#define IDS_POLICY_INVALID_VALUE 25741
#define IDS_POLICY_IGNORED_MANDATORY_REPORTING_POLICY 25742
#define IDS_POLICY_IGNORED_CHROME_PROFILE 25743
#define IDS_POLICY_PRECEDENCE_PLATFORM_MACHINE 25744
#define IDS_POLICY_PRECEDENCE_PLATFORM_USER 25745
#define IDS_POLICY_PRECEDENCE_CLOUD_MACHINE 25746
#define IDS_POLICY_PRECEDENCE_CLOUD_USER 25747
#define IDS_POLICY_LABEL_IS_AFFILIATED 25750
#define IDS_POLICY_IS_AFFILIATED_YES 25751
#define IDS_POLICY_IS_AFFILIATED_NO 25752
#define IDS_POLICY_LABEL_IS_OFFHOURS_ACTIVE 25753
#define IDS_POLICY_OFFHOURS_ACTIVE 25754
#define IDS_POLICY_OFFHOURS_NOT_ACTIVE 25755
#define IDS_POLICY_SIGNIN_PROFILE 25756
#define IDS_POLICY_COPY_VALUE 25757
#define IDS_COPY_POLICIES_JSON 25758
#define IDS_CHROME_URLS_DISABLED_PAGE_HEADER 25759
#define IDS_CHROME_URLS_DISABLED_PAGE_TITLE 25760
#define IDS_CHROME_URLS_DISABLED_PAGE_MESSAGE 25761
#define IDS_POLICY_DLP_CLIPBOARD_BLOCKED_ON_PASTE 25762
#define IDS_POLICY_DLP_CLIPBOARD_BLOCKED_ON_COPY_VM 25763
#define IDS_POLICY_DLP_CLIPBOARD_BLOCK_TOAST_BUTTON 25764
#define IDS_POLICY_DLP_ANDROID_APPS 25765
#define IDS_POLICY_DLP_CLIPBOARD_BLOCK_DISMISS_BUTTON 25766
#define IDS_POLICY_DLP_CLIPBOARD_WARN_ON_PASTE 25767
#define IDS_POLICY_DLP_CLIPBOARD_WARN_PROCEED_BUTTON 25768
#define IDS_POLICY_DLP_CLIPBOARD_WARN_DISMISS_BUTTON 25769
#define IDS_POLICY_DLP_CLIPBOARD_WARN_ON_COPY_VM 25770
#define IDS_POLICY_DLP_PRINTING_BLOCKED_TITLE 25771
#define IDS_POLICY_DLP_PRINTING_BLOCKED_MESSAGE 25772
#define IDS_POLICY_DLP_PRINTING_WARN_TITLE 25773
#define IDS_POLICY_DLP_PRINTING_WARN_MESSAGE 25774
#define IDS_POLICY_DLP_PRINTING_WARN_CONTINUE_BUTTON 25775
#define IDS_POLICY_DLP_PRINTING_WARN_CANCEL_BUTTON 25776
#define IDS_POLICY_DLP_SCREEN_SHARE_BLOCKED_TITLE 25777
#define IDS_POLICY_DLP_SCREEN_SHARE_BLOCKED_MESSAGE 25778
#define IDS_POLICY_DLP_SCREEN_SHARE_PAUSED_TITLE 25779
#define IDS_POLICY_DLP_SCREEN_SHARE_PAUSED_MESSAGE 25780
#define IDS_POLICY_DLP_SCREEN_SHARE_RESUMED_TITLE 25781
#define IDS_POLICY_DLP_SCREEN_SHARE_RESUMED_MESSAGE 25782
#define IDS_POLICY_DLP_SCREEN_CAPTURE_DISABLED_TITLE 25783
#define IDS_POLICY_DLP_SCREEN_CAPTURE_DISABLED_MESSAGE 25784
#define IDS_POLICY_DLP_VIDEO_CAPTURE_STOPPED_TITLE 25785
#define IDS_POLICY_DLP_VIDEO_CAPTURE_STOPPED_MESSAGE 25786
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_TITLE 25787
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_MESSAGE 25788
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_CONTINUE_BUTTON 25789
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_CANCEL_BUTTON 25790
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_TITLE 25791
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_MESSAGE 25792
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_CONTINUE_BUTTON 25793
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_CANCEL_BUTTON 25794
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_TITLE 25795
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_MESSAGE 25796
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_CONTINUE_BUTTON 25797
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_CANCEL_BUTTON 25798
#define IDS_POLICY_DLP_CLIPBOARD_BUBBLE_MESSAGE 25799
#define IDS_POLICY_DEVICE_SCHEDULED_REBOOT_TITLE 25800
#define IDS_POLICY_DEVICE_SCHEDULED_REBOOT_MESSAGE 25801
#define IDS_POLICY_REBOOT_BUTTON 25802
#define IDS_POLICY_DEVICE_SCHEDULED_REBOOT_DIALOG_MESSAGE 25803
#define IDS_REBOOT_SCHEDULED_TITLE_MINUTES 25804
#define IDS_REBOOT_SCHEDULED_TITLE_SECONDS 25805
#define IDS_POLICY_DEVICE_POST_REBOOT_TITLE 25806
#define PRINT_PREVIEW_MEDIA_ASME_F_28X40IN 25807
#define PRINT_PREVIEW_MEDIA_ISO_2A0_1189X1682MM 25808
#define PRINT_PREVIEW_MEDIA_ISO_A0_841X1189MM 25809
#define PRINT_PREVIEW_MEDIA_ISO_A10_26X37MM 25810
#define PRINT_PREVIEW_MEDIA_ISO_A1_594X841MM 25811
#define PRINT_PREVIEW_MEDIA_ISO_A2_420X594MM 25812
#define PRINT_PREVIEW_MEDIA_ISO_A3_297X420MM 25813
#define PRINT_PREVIEW_MEDIA_ISO_A4_210X297MM 25814
#define PRINT_PREVIEW_MEDIA_ISO_A4_EXTRA_235_5X322_3MM 25815
#define PRINT_PREVIEW_MEDIA_ISO_A4_TAB_225X297MM 25816
#define PRINT_PREVIEW_MEDIA_ISO_A5_148X210MM 25817
#define PRINT_PREVIEW_MEDIA_ISO_A5_EXTRA_174X235MM 25818
#define PRINT_PREVIEW_MEDIA_ISO_A6_105X148MM 25819
#define PRINT_PREVIEW_MEDIA_ISO_A7_74X105MM 25820
#define PRINT_PREVIEW_MEDIA_ISO_A8_52X74MM 25821
#define PRINT_PREVIEW_MEDIA_ISO_A9_37X52MM 25822
#define PRINT_PREVIEW_MEDIA_ISO_B0_1000X1414MM 25823
#define PRINT_PREVIEW_MEDIA_ISO_B10_31X44MM 25824
#define PRINT_PREVIEW_MEDIA_ISO_B1_707X1000MM 25825
#define PRINT_PREVIEW_MEDIA_ISO_B2_500X707MM 25826
#define PRINT_PREVIEW_MEDIA_ISO_B3_353X500MM 25827
#define PRINT_PREVIEW_MEDIA_ISO_B4_250X353MM 25828
#define PRINT_PREVIEW_MEDIA_ISO_B5_176X250MM 25829
#define PRINT_PREVIEW_MEDIA_ISO_B5_EXTRA_201X276MM 25830
#define PRINT_PREVIEW_MEDIA_ISO_B6C4_125X324MM 25831
#define PRINT_PREVIEW_MEDIA_ISO_B6_125X176MM 25832
#define PRINT_PREVIEW_MEDIA_ISO_B7_88X125MM 25833
#define PRINT_PREVIEW_MEDIA_ISO_B8_62X88MM 25834
#define PRINT_PREVIEW_MEDIA_ISO_B9_44X62MM 25835
#define PRINT_PREVIEW_MEDIA_ISO_C0_917X1297MM 25836
#define PRINT_PREVIEW_MEDIA_ISO_C10_28X40MM 25837
#define PRINT_PREVIEW_MEDIA_ISO_C1_648X917MM 25838
#define PRINT_PREVIEW_MEDIA_ISO_C2_458X648MM 25839
#define PRINT_PREVIEW_MEDIA_ISO_C3_324X458MM 25840
#define PRINT_PREVIEW_MEDIA_ISO_C4_229X324MM 25841
#define PRINT_PREVIEW_MEDIA_ISO_C5_162X229MM 25842
#define PRINT_PREVIEW_MEDIA_ISO_C6C5_114X229MM 25843
#define PRINT_PREVIEW_MEDIA_ISO_C6_114X162MM 25844
#define PRINT_PREVIEW_MEDIA_ISO_C7C6_81X162MM 25845
#define PRINT_PREVIEW_MEDIA_ISO_C7_81X114MM 25846
#define PRINT_PREVIEW_MEDIA_ISO_C8_57X81MM 25847
#define PRINT_PREVIEW_MEDIA_ISO_C9_40X57MM 25848
#define PRINT_PREVIEW_MEDIA_ISO_DL_110X220MM 25849
#define PRINT_PREVIEW_MEDIA_JIS_EXEC_216X330MM 25850
#define PRINT_PREVIEW_MEDIA_JPN_CHOU2_111_1X146MM 25851
#define PRINT_PREVIEW_MEDIA_JPN_CHOU3_120X235MM 25852
#define PRINT_PREVIEW_MEDIA_JPN_CHOU4_90X205MM 25853
#define PRINT_PREVIEW_MEDIA_JPN_HAGAKI_100X148MM 25854
#define PRINT_PREVIEW_MEDIA_JPN_KAHU_240X322_1MM 25855
#define PRINT_PREVIEW_MEDIA_JPN_KAKU2_240X332MM 25856
#define PRINT_PREVIEW_MEDIA_JPN_OUFUKU_148X200MM 25857
#define PRINT_PREVIEW_MEDIA_JPN_YOU4_105X235MM 25858
#define PRINT_PREVIEW_MEDIA_NA_10X11_10X11IN 25859
#define PRINT_PREVIEW_MEDIA_NA_10X13_10X13IN 25860
#define PRINT_PREVIEW_MEDIA_NA_10X14_10X14IN 25861
#define PRINT_PREVIEW_MEDIA_NA_10X15_10X15IN 25862
#define PRINT_PREVIEW_MEDIA_NA_11X12_11X12IN 25863
#define PRINT_PREVIEW_MEDIA_NA_11X15_11X15IN 25864
#define PRINT_PREVIEW_MEDIA_NA_12X19_12X19IN 25865
#define PRINT_PREVIEW_MEDIA_NA_5X7_5X7IN 25866
#define PRINT_PREVIEW_MEDIA_NA_6X9_6X9IN 25867
#define PRINT_PREVIEW_MEDIA_NA_7X9_7X9IN 25868
#define PRINT_PREVIEW_MEDIA_NA_9X11_9X11IN 25869
#define PRINT_PREVIEW_MEDIA_NA_A2_4_375X5_75IN 25870
#define PRINT_PREVIEW_MEDIA_NA_ARCH_A_9X12IN 25871
#define PRINT_PREVIEW_MEDIA_NA_ARCH_B_12X18IN 25872
#define PRINT_PREVIEW_MEDIA_NA_ARCH_C_18X24IN 25873
#define PRINT_PREVIEW_MEDIA_NA_ARCH_D_24X36IN 25874
#define PRINT_PREVIEW_MEDIA_NA_ARCH_E_36X48IN 25875
#define PRINT_PREVIEW_MEDIA_NA_B_PLUS_12X19_17IN 25876
#define PRINT_PREVIEW_MEDIA_NA_C5_6_5X9_5IN 25877
#define PRINT_PREVIEW_MEDIA_NA_C_17X22IN 25878
#define PRINT_PREVIEW_MEDIA_NA_D_22X34IN 25879
#define PRINT_PREVIEW_MEDIA_NA_EDP_11X14IN 25880
#define PRINT_PREVIEW_MEDIA_NA_EUR_EDP_12X14IN 25881
#define PRINT_PREVIEW_MEDIA_NA_E_34X44IN 25882
#define PRINT_PREVIEW_MEDIA_NA_FANFOLD_EUR_8_5X12IN 25883
#define PRINT_PREVIEW_MEDIA_NA_FANFOLD_US_11X14_875IN 25884
#define PRINT_PREVIEW_MEDIA_NA_FOOLSCAP_8_5X13IN 25885
#define PRINT_PREVIEW_MEDIA_NA_F_44X68IN 25886
#define PRINT_PREVIEW_MEDIA_NA_GOVT_LEGAL_8X13IN 25887
#define PRINT_PREVIEW_MEDIA_NA_GOVT_LETTER_8X10IN 25888
#define PRINT_PREVIEW_MEDIA_NA_INDEX_3X5_3X5IN 25889
#define PRINT_PREVIEW_MEDIA_NA_INDEX_4X6_4X6IN 25890
#define PRINT_PREVIEW_MEDIA_NA_INDEX_4X6_EXT_6X8IN 25891
#define PRINT_PREVIEW_MEDIA_NA_INDEX_5X8_5X8IN 25892
#define PRINT_PREVIEW_MEDIA_NA_INVOICE_5_5X8_5IN 25893
#define PRINT_PREVIEW_MEDIA_NA_LEDGER_11X17IN 25894
#define PRINT_PREVIEW_MEDIA_NA_LEGAL_8_5X14IN 25895
#define PRINT_PREVIEW_MEDIA_NA_LEGAL_EXTRA_9_5X15IN 25896
#define PRINT_PREVIEW_MEDIA_NA_LETTER_8_5X11IN 25897
#define PRINT_PREVIEW_MEDIA_NA_LETTER_EXTRA_9_5X12IN 25898
#define PRINT_PREVIEW_MEDIA_NA_LETTER_PLUS_8_5X12_69IN 25899
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_10_4_125X9_5IN 25900
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_11_4_5X10_375IN 25901
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_12_4_75X11IN 25902
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_14_5X11_5IN 25903
#define PRINT_PREVIEW_MEDIA_NA_PERSONAL_3_625X6_5IN 25904
#define PRINT_PREVIEW_MEDIA_NA_SUPER_A_8_94X14IN 25905
#define PRINT_PREVIEW_MEDIA_NA_SUPER_B_13X19IN 25906
#define PRINT_PREVIEW_MEDIA_NA_WIDE_FORMAT_30X42IN 25907
#define PRINT_PREVIEW_MEDIA_OM_DAI_PA_KAI_275X395MM 25908
#define PRINT_PREVIEW_MEDIA_OM_FOLIO_SP_215X315MM 25909
#define PRINT_PREVIEW_MEDIA_OM_INVITE_220X220MM 25910
#define PRINT_PREVIEW_MEDIA_OM_ITALIAN_110X230MM 25911
#define PRINT_PREVIEW_MEDIA_OM_JUURO_KU_KAI_198X275MM 25912
#define PRINT_PREVIEW_MEDIA_OM_LARGE_PHOTO_200X300 25913
#define PRINT_PREVIEW_MEDIA_OM_PA_KAI_267X389MM 25914
#define PRINT_PREVIEW_MEDIA_OM_POSTFIX_114X229MM 25915
#define PRINT_PREVIEW_MEDIA_OM_SMALL_PHOTO_100X150MM 25916
#define PRINT_PREVIEW_MEDIA_PRC_10_324X458MM 25917
#define PRINT_PREVIEW_MEDIA_PRC_16K_146X215MM 25918
#define PRINT_PREVIEW_MEDIA_PRC_1_102X165MM 25919
#define PRINT_PREVIEW_MEDIA_PRC_2_102X176MM 25920
#define PRINT_PREVIEW_MEDIA_PRC_32K_97X151MM 25921
#define PRINT_PREVIEW_MEDIA_PRC_3_125X176MM 25922
#define PRINT_PREVIEW_MEDIA_PRC_4_110X208MM 25923
#define PRINT_PREVIEW_MEDIA_PRC_5_110X220MM 25924
#define PRINT_PREVIEW_MEDIA_PRC_6_120X320MM 25925
#define PRINT_PREVIEW_MEDIA_PRC_7_160X230MM 25926
#define PRINT_PREVIEW_MEDIA_PRC_8_120X309MM 25927
#define PRINT_PREVIEW_MEDIA_ROC_16K_7_75X10_75IN 25928
#define PRINT_PREVIEW_MEDIA_ROC_8K_10_75X15_5IN 25929
#define PRINT_PREVIEW_MEDIA_JIS_B0_1030X1456MM 25930
#define PRINT_PREVIEW_MEDIA_JIS_B1_728X1030MM 25931
#define PRINT_PREVIEW_MEDIA_JIS_B2_515X728MM 25932
#define PRINT_PREVIEW_MEDIA_JIS_B3_364X515MM 25933
#define PRINT_PREVIEW_MEDIA_JIS_B4_257X364MM 25934
#define PRINT_PREVIEW_MEDIA_JIS_B5_182X257MM 25935
#define PRINT_PREVIEW_MEDIA_JIS_B6_128X182MM 25936
#define PRINT_PREVIEW_MEDIA_JIS_B7_91X128MM 25937
#define PRINT_PREVIEW_MEDIA_JIS_B8_64X91MM 25938
#define PRINT_PREVIEW_MEDIA_JIS_B9_45X64MM 25939
#define PRINT_PREVIEW_MEDIA_JIS_B10_32X45MM 25940
#define IDS_PRINT_COMPOSITOR_SERVICE_DISPLAY_NAME 25942
#define IDS_PRIVACY_SANDBOX_FLOC_INVALID 26219
#define IDS_PRIVACY_SANDBOX_FLOC_DESCRIPTION 26220
#define IDS_PRIVACY_SANDBOX_FLOC_TIME_TO_NEXT_COMPUTE 26221
#define IDS_PRIVACY_SANDBOX_FLOC_TIME_TO_NEXT_COMPUTE_INVALID 26222
#define IDS_PRIVACY_SANDBOX_FLOC_RESET_EXPLANATION 26223
#define IDS_PRIVACY_SANDBOX_FLOC_STATUS_ACTIVE 26224
#define IDS_PRIVACY_SANDBOX_FLOC_STATUS_ELIGIBLE_NOT_ACTIVE 26225
#define IDS_PRIVACY_SANDBOX_FLOC_STATUS_NOT_ACTIVE 26226
#define IDS_PRIVACY_SANDBOX_TOPICS_INVALID_TOPIC 26227
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_1 26228
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_2 26229
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_3 26230
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_4 26231
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_5 26232
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_6 26233
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_7 26234
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_8 26235
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_9 26236
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_10 26237
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_11 26238
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_12 26239
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_13 26240
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_14 26241
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_15 26242
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_16 26243
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_17 26244
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_18 26245
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_19 26246
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_20 26247
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_21 26248
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_22 26249
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_23 26250
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_24 26251
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_25 26252
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_26 26253
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_27 26254
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_28 26255
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_29 26256
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_30 26257
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_31 26258
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_32 26259
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_33 26260
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_34 26261
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_35 26262
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_36 26263
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_37 26264
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_38 26265
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_39 26266
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_40 26267
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_41 26268
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_42 26269
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_43 26270
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_44 26271
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_45 26272
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_46 26273
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_47 26274
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_48 26275
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_49 26276
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_50 26277
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_51 26278
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_52 26279
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_53 26280
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_54 26281
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_55 26282
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_56 26283
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_57 26284
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_58 26285
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_59 26286
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_60 26287
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_61 26288
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_62 26289
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_63 26290
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_64 26291
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_65 26292
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_66 26293
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_67 26294
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_68 26295
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_69 26296
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_70 26297
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_71 26298
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_72 26299
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_73 26300
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_74 26301
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_75 26302
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_76 26303
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_77 26304
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_78 26305
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_79 26306
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_80 26307
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_81 26308
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_82 26309
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_83 26310
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_84 26311
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_85 26312
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_86 26313
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_87 26314
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_88 26315
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_89 26316
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_90 26317
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_91 26318
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_92 26319
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_93 26320
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_94 26321
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_95 26322
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_96 26323
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_97 26324
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_98 26325
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_99 26326
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_100 26327
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_101 26328
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_102 26329
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_103 26330
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_104 26331
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_105 26332
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_106 26333
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_107 26334
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_108 26335
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_109 26336
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_110 26337
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_111 26338
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_112 26339
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_113 26340
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_114 26341
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_115 26342
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_116 26343
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_117 26344
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_118 26345
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_119 26346
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_120 26347
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_121 26348
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_122 26349
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_123 26350
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_124 26351
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_125 26352
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_126 26353
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_127 26354
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_128 26355
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_129 26356
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_130 26357
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_131 26358
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_132 26359
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_133 26360
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_134 26361
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_135 26362
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_136 26363
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_137 26364
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_138 26365
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_139 26366
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_140 26367
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_141 26368
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_142 26369
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_143 26370
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_144 26371
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_145 26372
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_146 26373
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_147 26374
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_148 26375
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_149 26376
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_150 26377
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_151 26378
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_152 26379
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_153 26380
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_154 26381
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_155 26382
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_156 26383
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_157 26384
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_158 26385
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_159 26386
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_160 26387
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_161 26388
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_162 26389
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_163 26390
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_164 26391
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_165 26392
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_166 26393
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_167 26394
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_168 26395
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_169 26396
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_170 26397
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_171 26398
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_172 26399
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_173 26400
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_174 26401
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_175 26402
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_176 26403
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_177 26404
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_178 26405
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_179 26406
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_180 26407
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_181 26408
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_182 26409
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_183 26410
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_184 26411
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_185 26412
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_186 26413
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_187 26414
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_188 26415
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_189 26416
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_190 26417
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_191 26418
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_192 26419
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_193 26420
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_194 26421
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_195 26422
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_196 26423
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_197 26424
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_198 26425
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_199 26426
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_200 26427
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_201 26428
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_202 26429
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_203 26430
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_204 26431
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_205 26432
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_206 26433
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_207 26434
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_208 26435
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_209 26436
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_210 26437
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_211 26438
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_212 26439
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_213 26440
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_214 26441
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_215 26442
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_216 26443
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_217 26444
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_218 26445
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_219 26446
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_220 26447
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_221 26448
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_222 26449
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_223 26450
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_224 26451
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_225 26452
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_226 26453
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_227 26454
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_228 26455
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_229 26456
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_230 26457
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_231 26458
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_232 26459
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_233 26460
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_234 26461
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_235 26462
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_236 26463
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_237 26464
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_238 26465
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_239 26466
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_240 26467
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_241 26468
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_242 26469
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_243 26470
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_244 26471
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_245 26472
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_246 26473
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_247 26474
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_248 26475
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_249 26476
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_250 26477
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_251 26478
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_252 26479
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_253 26480
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_254 26481
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_255 26482
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_256 26483
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_257 26484
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_258 26485
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_259 26486
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_260 26487
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_261 26488
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_262 26489
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_263 26490
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_264 26491
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_265 26492
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_266 26493
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_267 26494
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_268 26495
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_269 26496
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_270 26497
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_271 26498
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_272 26499
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_273 26500
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_274 26501
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_275 26502
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_276 26503
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_277 26504
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_278 26505
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_279 26506
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_280 26507
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_281 26508
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_282 26509
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_283 26510
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_284 26511
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_285 26512
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_286 26513
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_287 26514
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_288 26515
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_289 26516
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_290 26517
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_291 26518
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_292 26519
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_293 26520
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_294 26521
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_295 26522
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_296 26523
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_297 26524
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_298 26525
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_299 26526
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_300 26527
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_301 26528
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_302 26529
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_303 26530
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_304 26531
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_305 26532
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_306 26533
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_307 26534
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_308 26535
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_309 26536
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_310 26537
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_311 26538
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_312 26539
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_313 26540
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_314 26541
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_315 26542
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_316 26543
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_317 26544
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_318 26545
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_319 26546
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_320 26547
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_321 26548
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_322 26549
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_323 26550
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_324 26551
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_325 26552
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_326 26553
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_327 26554
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_328 26555
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_329 26556
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_330 26557
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_331 26558
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_332 26559
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_333 26560
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_334 26561
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_335 26562
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_336 26563
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_337 26564
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_338 26565
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_339 26566
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_340 26567
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_341 26568
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_342 26569
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_343 26570
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_344 26571
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_345 26572
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_346 26573
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_347 26574
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_348 26575
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_349 26576
#define IDS_REGISTER_PROTOCOL_HANDLER_TOOLTIP 274
#define IDS_REGISTER_PROTOCOL_HANDLER_MAILTO_NAME 26577
#define IDS_REGISTER_PROTOCOL_HANDLER_WEBCAL_NAME 26578
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM 26579
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM_REPLACE 26580
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM_FRAGMENT 26581
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM_REPLACE_FRAGMENT 26582
#define IDS_REGISTER_PROTOCOL_HANDLER_ACCEPT 26583
#define IDS_REGISTER_PROTOCOL_HANDLER_DENY 26584
#define IDS_REGISTER_PROTOCOL_HANDLER_IGNORE 26585
#define IDS_RESET_PASSWORD_TITLE 26586
#define IDS_RESET_PASSWORD_WARNING_HEADING 26587
#define IDS_RESET_PASSWORD_HEADING 26588
#define IDS_RESET_PASSWORD_WARNING_EXPLANATION_PARAGRAPH 26589
#define IDS_RESET_PASSWORD_WARNING_EXPLANATION_PARAGRAPH_WITH_ORG_NAME 26590
#define IDS_RESET_PASSWORD_BUTTON 26591
#define IDS_RESET_PASSWORD_EXPLANATION_PARAGRAPH 26592
#define IDS_RESET_PASSWORD_EXPLANATION_PARAGRAPH_WITH_ORG_NAME 26593
#define IDS_SSL_OPEN_DETAILS_BUTTON 26594
#define IDS_SSL_CLOSE_DETAILS_BUTTON 26595
#define IDS_CAPTIVE_PORTAL_AUTHORIZATION_DIALOG_NAME 26596
#define IDS_CAPTIVE_PORTAL_HEADING_WIRED 26597
#define IDS_CAPTIVE_PORTAL_HEADING_WIFI 26598
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_WIRED 26599
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_WIFI 26600
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_WIFI_SSID 26601
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_NO_LOGIN_URL_WIRED 26602
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_NO_LOGIN_URL_WIFI 26603
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_NO_LOGIN_URL_WIFI_SSID 26604
#define IDS_CAPTIVE_PORTAL_BUTTON_OPEN_LOGIN_PAGE 26605
#define IDS_MITM_SOFTWARE_HEADING 26606
#define IDS_MITM_SOFTWARE_PRIMARY_PARAGRAPH_ENTERPRISE 26607
#define IDS_MITM_SOFTWARE_PRIMARY_PARAGRAPH_NONENTERPRISE 26608
#define IDS_MITM_SOFTWARE_EXPLANATION_ENTERPRISE 26609
#define IDS_MITM_SOFTWARE_EXPLANATION_NONENTERPRISE 26610
#define IDS_MITM_SOFTWARE_EXPLANATION 26611
#define IDS_LOOKALIKE_URL_TITLE 26612
#define IDS_LOOKALIKE_URL_HEADING 26613
#define IDS_LOOKALIKE_URL_IGNORE 26614
#define IDS_LOOKALIKE_URL_CONTINUE 26615
#define IDS_LOOKALIKE_URL_PRIMARY_PARAGRAPH 26616
#define IDS_LOOKALIKE_URL_HEADING_NO_SUGGESTED_URL 26617
#define IDS_LOOKALIKE_URL_PRIMARY_PARAGRAPH_NO_SUGGESTED_URL 26618
#define IDS_LOOKALIKE_URL_BACK_TO_SAFETY 26619
#define IDS_LOOKALIKE_URL_CLOSE_PAGE 26620
#define IDS_CLOCK_ERROR_TITLE 26621
#define IDS_CLOCK_ERROR_AHEAD_HEADING 26622
#define IDS_CLOCK_ERROR_BEHIND_HEADING 26623
#define IDS_CLOCK_ERROR_UPDATE_DATE_AND_TIME 26624
#define IDS_CLOCK_ERROR_PRIMARY_PARAGRAPH 26625
#define IDS_CLOCK_ERROR_EXPLANATION 26626
#define IDS_SSL_V2_TITLE 26627
#define IDS_SSL_V2_HEADING 26628
#define IDS_SSL_V2_PRIMARY_PARAGRAPH 26629
#define IDS_SSL_V2_RECURRENT_ERROR_PARAGRAPH 26630
#define IDS_SSL_OVERRIDABLE_SAFETY_BUTTON 26631
#define IDS_SSL_OVERRIDABLE_CLOSE_PAGE_BUTTON 26632
#define IDS_SSL_OVERRIDABLE_PROCEED_PARAGRAPH 26633
#define IDS_SSL_RELOAD 26634
#define IDS_SSL_NONOVERRIDABLE_PINNED 26635
#define IDS_SSL_NONOVERRIDABLE_HSTS 26636
#define IDS_SSL_NONOVERRIDABLE_REVOKED 26637
#define IDS_SSL_NONOVERRIDABLE_MORE 26638
#define IDS_SSL_NONOVERRIDABLE_INVALID 26639
#define IDS_SAFEBROWSING_V3_TITLE 26640
#define IDS_SAFEBROWSING_V3_OPEN_DETAILS_BUTTON 26641
#define IDS_SAFEBROWSING_V3_CLOSE_DETAILS_BUTTON 26642
#define IDS_SAFEBROWSING_OVERRIDABLE_SAFETY_BUTTON 26643
#define IDS_MALWARE_V3_HEADING 26644
#define IDS_MALWARE_V3_PRIMARY_PARAGRAPH 26645
#define IDS_MALWARE_V3_EXPLANATION_PARAGRAPH 26646
#define IDS_MALWARE_V3_EXPLANATION_PARAGRAPH_SUBRESOURCE 26647
#define IDS_MALWARE_V3_PROCEED_PARAGRAPH 26648
#define IDS_SAFE_BROWSING_SCOUT_REPORTING_AGREE 26649
#define IDS_SAFE_BROWSING_ENHANCED_PROTECTION_MESSAGE 26650
#define IDS_HARMFUL_V3_HEADING 26651
#define IDS_HARMFUL_V3_PRIMARY_PARAGRAPH 26652
#define IDS_HARMFUL_V3_EXPLANATION_PARAGRAPH 26653
#define IDS_HARMFUL_V3_PROCEED_PARAGRAPH 26654
#define IDS_PHISHING_V4_HEADING 26655
#define IDS_PHISHING_V4_PRIMARY_PARAGRAPH 26656
#define IDS_PHISHING_V4_EXPLANATION_PARAGRAPH 26657
#define IDS_PHISHING_V4_PROCEED_AND_REPORT_PARAGRAPH 26658
#define IDS_MALWARE_WEBVIEW_HEADING 26659
#define IDS_MALWARE_WEBVIEW_EXPLANATION_PARAGRAPH 26660
#define IDS_PHISHING_WEBVIEW_HEADING 26661
#define IDS_PHISHING_WEBVIEW_EXPLANATION_PARAGRAPH 26662
#define IDS_HARMFUL_WEBVIEW_HEADING 26663
#define IDS_HARMFUL_WEBVIEW_EXPLANATION_PARAGRAPH 26664
#define IDS_BILLING_WEBVIEW_HEADING 26665
#define IDS_BILLING_WEBVIEW_EXPLANATION_PARAGRAPH 26666
#define IDS_CONNECTION_HELP_SHOW_MORE 26667
#define IDS_CONNECTION_HELP_SHOW_LESS 26668
#define IDS_CONNECTION_HELP_TITLE 26669
#define IDS_CONNECTION_HELP_HEADING 26670
#define IDS_CONNECTION_HELP_GENERAL_HELP 26671
#define IDS_CONNECTION_HELP_SPECIFIC_ERROR_HEADING 26672
#define IDS_CONNECTION_HELP_CONNECTION_NOT_PRIVATE_TITLE 26673
#define IDS_CONNECTION_HELP_CONNECT_TO_NETWORK_TITLE 26674
#define IDS_CONNECTION_HELP_INCORRECT_CLOCK_TITLE 26675
#define IDS_CONNECTION_HELP_CONNECTION_NOT_PRIVATE_DETAILS 26676
#define IDS_CONNECTION_HELP_CONNECT_TO_NETWORK_DETAILS 26677
#define IDS_CONNECTION_HELP_INCORRECT_CLOCK_DETAILS 26678
#define IDS_BILLING_TITLE 26681
#define IDS_BILLING_HEADING 26682
#define IDS_BILLING_PRIMARY_PARAGRAPH 26683
#define IDS_BILLING_PRIMARY_BUTTON 26684
#define IDS_BILLING_PROCEED_BUTTON 26685
#define IDS_ORIGIN_POLICY_TITLE 26686
#define IDS_ORIGIN_POLICY_HEADING 26687
#define IDS_ORIGIN_POLICY_INFO 26688
#define IDS_ORIGIN_POLICY_INFO2 26689
#define IDS_ORIGIN_POLICY_BUTTON 26690
#define IDS_ORIGIN_POLICY_DETAILS 26691
#define IDS_ORIGIN_POLICY_EXPLANATION_CANNOT_LOAD 26692
#define IDS_ORIGIN_POLICY_EXPLANATION_CANNOT_PARSE_HEADER 26693
#define IDS_ORIGIN_POLICY_FINAL_PARAGRAPH 26694
#define IDS_ORIGIN_POLICY_CLOSE 26695
#define IDS_BLOCKED_INTERCEPTION_HEADING 26696
#define IDS_KNOWN_INTERCEPTION_TITLE 26697
#define IDS_KNOWN_INTERCEPTION_HEADER 26698
#define IDS_KNOWN_INTERCEPTION_BODY1 26699
#define IDS_KNOWN_INTERCEPTION_BODY2 26700
#define IDS_KNOWN_INTERCEPTION_INFOBAR_HEADING 26701
#define IDS_KNOWN_INTERCEPTION_INFOBAR_BUTTON_TEXT 26702
#define IDS_LEGACY_TLS_HEADING 26703
#define IDS_LEGACY_TLS_PRIMARY_PARAGRAPH 26704
#define IDS_LEGACY_TLS_EXPLANATION 26705
#define IDS_INSECURE_FORM_TITLE 26706
#define IDS_INSECURE_FORM_HEADING 26707
#define IDS_INSECURE_FORM_PRIMARY_PARAGRAPH 26708
#define IDS_INSECURE_FORM_BACK_BUTTON 26709
#define IDS_INSECURE_FORM_SUBMIT_BUTTON 26710
#define IDS_HTTPS_ONLY_MODE_TITLE 26711
#define IDS_HTTPS_ONLY_MODE_HEADING 26712
#define IDS_HTTPS_ONLY_MODE_PRIMARY_PARAGRAPH 26713
#define IDS_HTTPS_ONLY_MODE_BACK_BUTTON 26714
#define IDS_HTTPS_ONLY_MODE_SUBMIT_BUTTON 26715
#define IDS_SHARING_DEVICE_TYPE_COMPUTER 26716
#define IDS_SHARING_DEVICE_TYPE_DEVICE 26717
#define IDS_SHARING_DEVICE_TYPE_PHONE 26718
#define IDS_SHARING_DEVICE_TYPE_TABLET 26719
#define IDS_SITE_SETTINGS_TYPE_ADS 26720
#define IDS_SITE_SETTINGS_TYPE_ADS_MID_SENTENCE 26721
#define IDS_SITE_SETTINGS_TYPE_AR 26722
#define IDS_SITE_SETTINGS_TYPE_AR_MID_SENTENCE 26723
#define IDS_SITE_SETTINGS_TYPE_AUTOMATIC_DOWNLOADS 26724
#define IDS_SITE_SETTINGS_TYPE_AUTOMATIC_DOWNLOADS_MID_SENTENCE 26725
#define IDS_SITE_SETTINGS_TYPE_BACKGROUND_SYNC 26726
#define IDS_SITE_SETTINGS_TYPE_BACKGROUND_SYNC_MID_SENTENCE 26727
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_DEVICES 26728
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_DEVICES_MID_SENTENCE 26729
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_SCANNING 26730
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_SCANNING_MID_SENTENCE 26731
#define IDS_SITE_SETTINGS_TYPE_CAMERA 26732
#define IDS_SITE_SETTINGS_TYPE_CAMERA_MID_SENTENCE 26733
#define IDS_SITE_SETTINGS_TYPE_CAMERA_PAN_TILT_ZOOM 26734
#define IDS_SITE_SETTINGS_TYPE_CAMERA_PAN_TILT_ZOOM_MID_SENTENCE 26735
#define IDS_SITE_SETTINGS_TYPE_CLIPBOARD 26736
#define IDS_SITE_SETTINGS_TYPE_CLIPBOARD_MID_SENTENCE 26737
#define IDS_SITE_SETTINGS_TYPE_COOKIES 26738
#define IDS_SITE_SETTINGS_TYPE_COOKIES_MID_SENTENCE 26739
#define IDS_SITE_SETTINGS_TYPE_IDLE_DETECTION 26740
#define IDS_SITE_SETTINGS_TYPE_IDLE_DETECTION_MID_SENTENCE 26741
#define IDS_SITE_SETTINGS_TYPE_JAVASCRIPT 26742
#define IDS_SITE_SETTINGS_TYPE_JAVASCRIPT_MID_SENTENCE 26743
#define IDS_SITE_SETTINGS_TYPE_LOCATION 26744
#define IDS_SITE_SETTINGS_TYPE_LOCATION_MID_SENTENCE 26745
#define IDS_SITE_SETTINGS_TYPE_MIC 26746
#define IDS_SITE_SETTINGS_TYPE_MIC_MID_SENTENCE 26747
#define IDS_SITE_SETTINGS_TYPE_MIDI_SYSEX 26748
#define IDS_SITE_SETTINGS_TYPE_MIDI_SYSEX_MID_SENTENCE 26749
#define IDS_SITE_SETTINGS_TYPE_MOTION_SENSORS 26750
#define IDS_SITE_SETTINGS_TYPE_MOTION_SENSORS_MID_SENTENCE 26751
#define IDS_SITE_SETTINGS_TYPE_NFC 26752
#define IDS_SITE_SETTINGS_TYPE_NFC_MID_SENTENCE 26753
#define IDS_SITE_SETTINGS_TYPE_NOTIFICATIONS 26754
#define IDS_SITE_SETTINGS_TYPE_NOTIFICATIONS_MID_SENTENCE 26755
#define IDS_SITE_SETTINGS_TYPE_POPUPS_REDIRECTS 26756
#define IDS_SITE_SETTINGS_TYPE_POPUPS_REDIRECTS_MID_SENTENCE 26757
#define IDS_SITE_SETTINGS_TYPE_PROTECTED_MEDIA_ID 26758
#define IDS_SITE_SETTINGS_TYPE_PROTECTED_MEDIA_ID_MID_SENTENCE 26759
#define IDS_SITE_SETTINGS_TYPE_SENSORS 26760
#define IDS_SITE_SETTINGS_TYPE_SENSORS_MID_SENTENCE 26761
#define IDS_SITE_SETTINGS_TYPE_SOUND 26762
#define IDS_SITE_SETTINGS_TYPE_SOUND_MID_SENTENCE 26763
#define IDS_SITE_SETTINGS_TYPE_USB_DEVICES 26764
#define IDS_SITE_SETTINGS_TYPE_USB_DEVICES_MID_SENTENCE 26765
#define IDS_SITE_SETTINGS_TYPE_VR 26766
#define IDS_SITE_SETTINGS_TYPE_VR_MID_SENTENCE 26767
#define IDS_SITE_SETTINGS_TYPE_FEDERATED_IDENTITY_API 26768
#define IDS_SITE_SETTINGS_TYPE_FEDERATED_IDENTITY_API_MID_SENTENCE 26769
#define IDS_SITE_SETTINGS_TYPE_FILE_SYSTEM_ACCESS_WRITE 26770
#define IDS_SITE_SETTINGS_TYPE_FILE_SYSTEM_ACCESS_WRITE_MID_SENTENCE 26771
#define IDS_SITE_SETTINGS_TYPE_FONT_ACCESS 26772
#define IDS_SITE_SETTINGS_TYPE_FONT_ACCESS_MID_SENTENCE 26773
#define IDS_SITE_SETTINGS_TYPE_HANDLERS 26774
#define IDS_SITE_SETTINGS_TYPE_HANDLERS_MID_SENTENCE 26775
#define IDS_SITE_SETTINGS_TYPE_HID_DEVICES 26776
#define IDS_SITE_SETTINGS_TYPE_HID_DEVICES_MID_SENTENCE 26777
#define IDS_SITE_SETTINGS_TYPE_IMAGES 26778
#define IDS_SITE_SETTINGS_TYPE_IMAGES_MID_SENTENCE 26779
#define IDS_SITE_SETTINGS_TYPE_INSECURE_CONTENT 26780
#define IDS_SITE_SETTINGS_TYPE_INSECURE_CONTENT_MID_SENTENCE 26781
#define IDS_SITE_SETTINGS_TYPE_PAYMENT_HANDLER 26782
#define IDS_SITE_SETTINGS_TYPE_PAYMENT_HANDLER_MID_SENTENCE 26783
#define IDS_SITE_SETTINGS_TYPE_PDF_DOCUMENTS 26784
#define IDS_SITE_SETTINGS_TYPE_SERIAL_PORTS 26785
#define IDS_SITE_SETTINGS_TYPE_SERIAL_PORTS_MID_SENTENCE 26786
#define IDS_SITE_SETTINGS_TYPE_ZOOM_LEVELS 26787
#define IDS_SITE_SETTINGS_TYPE_ZOOM_LEVELS_MID_SENTENCE 26788
#define IDS_SITE_SETTINGS_TYPE_WINDOW_PLACEMENT 26789
#define IDS_SITE_SETTINGS_TYPE_WINDOW_PLACEMENT_MID_SENTENCE 26790
#define IDS_SMS_INFOBAR_TITLE 26791
#define IDS_SMS_INFOBAR_STATUS_SMS_RECEIVED 26792
#define IDS_SMS_INFOBAR_STATUS_SMS_RECEIVED_FROM_EMBEDDED_FRAME 26793
#define IDS_SMS_INFOBAR_BUTTON_OK 26794
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_ENGLISH 26795
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_FRENCH 26796
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_GERMAN 26797
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_ITALIAN 26798
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_JAPANESE 26799
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_SPANISH 26800
#define IDS_CERT_ERROR_NO_SUBJECT_ALTERNATIVE_NAMES_DETAILS 26801
#define IDS_CERT_ERROR_COMMON_NAME_INVALID_DETAILS 26802
#define IDS_CERT_ERROR_COMMON_NAME_INVALID_DESCRIPTION 26803
#define IDS_CERT_ERROR_EXPIRED_DETAILS 26804
#define IDS_CERT_ERROR_EXPIRED_DESCRIPTION 26805
#define IDS_CERT_ERROR_NOT_YET_VALID_DETAILS 26806
#define IDS_CERT_ERROR_NOT_YET_VALID_DESCRIPTION 26807
#define IDS_CERT_ERROR_NOT_VALID_AT_THIS_TIME_DETAILS 26808
#define IDS_CERT_ERROR_NOT_VALID_AT_THIS_TIME_DESCRIPTION 26809
#define IDS_CERT_ERROR_AUTHORITY_INVALID_DESCRIPTION 26810
#define IDS_CERT_ERROR_CONTAINS_ERRORS_DETAILS 26811
#define IDS_CERT_ERROR_CONTAINS_ERRORS_DESCRIPTION 26812
#define IDS_CERT_ERROR_UNABLE_TO_CHECK_REVOCATION_DETAILS 26813
#define IDS_CERT_ERROR_UNABLE_TO_CHECK_REVOCATION_DESCRIPTION 26814
#define IDS_CERT_ERROR_NO_REVOCATION_MECHANISM_DETAILS 26815
#define IDS_CERT_ERROR_NO_REVOCATION_MECHANISM_DESCRIPTION 26816
#define IDS_CERT_ERROR_REVOKED_CERT_DETAILS 26817
#define IDS_CERT_ERROR_REVOKED_CERT_DESCRIPTION 26818
#define IDS_CERT_ERROR_INVALID_CERT_DETAILS 26819
#define IDS_CERT_ERROR_INVALID_CERT_DESCRIPTION 26820
#define IDS_CERT_ERROR_WEAK_SIGNATURE_ALGORITHM_DETAILS 26821
#define IDS_CERT_ERROR_WEAK_SIGNATURE_ALGORITHM_DESCRIPTION 26822
#define IDS_CERT_ERROR_WEAK_KEY_DETAILS 26823
#define IDS_CERT_ERROR_WEAK_KEY_DESCRIPTION 26824
#define IDS_CERT_ERROR_NAME_CONSTRAINT_VIOLATION_DETAILS 26825
#define IDS_CERT_ERROR_NAME_CONSTRAINT_VIOLATION_DESCRIPTION 26826
#define IDS_CERT_ERROR_VALIDITY_TOO_LONG_DETAILS 26827
#define IDS_CERT_ERROR_VALIDITY_TOO_LONG_DESCRIPTION 26828
#define IDS_CERT_ERROR_UNKNOWN_ERROR_DETAILS 26829
#define IDS_CERT_ERROR_UNKNOWN_ERROR_DESCRIPTION 26830
#define IDS_CERT_ERROR_SUMMARY_PINNING_FAILURE_DETAILS 26831
#define IDS_CERT_ERROR_SUMMARY_PINNING_FAILURE_DESCRIPTION 26832
#define IDS_CERT_ERROR_CERTIFICATE_TRANSPARENCY_REQUIRED_DETAILS 26833
#define IDS_CERT_ERROR_CERTIFICATE_TRANSPARENCY_REQUIRED_DESCRIPTION 26834
#define IDS_SSL_ERROR_OBSOLETE_VERSION_DETAILS 26835
#define IDS_SSL_ERROR_OBSOLETE_VERSION_DESCRIPTION 26836
#define IDS_CERT_ERROR_AUTHORITY_INVALID_DETAILS 26837
#define IDS_ALWAYS_ALLOW_ADS 26838
#define IDS_BLOCKED_ADS_PROMPT_TITLE 26839
#define IDS_BLOCKED_ADS_PROMPT_EXPLANATION 26840
#define IDS_SYNC_BASIC_ENCRYPTION_DATA 26847
#define IDS_SYNC_DATATYPE_AUTOFILL 26848
#define IDS_SYNC_DATATYPE_BOOKMARKS 26849
#define IDS_SYNC_DATATYPE_PASSWORDS 26850
#define IDS_SYNC_DATATYPE_PREFERENCES 26851
#define IDS_SYNC_DATATYPE_TABS 26852
#define IDS_SYNC_DATATYPE_TYPED_URLS 26853
#define IDS_SYNC_DATATYPE_READING_LIST 26854
#define IDS_SYNC_EMPTY_PASSPHRASE_ERROR 26855
#define IDS_SYNC_ENCRYPTION_SECTION_TITLE 26856
#define IDS_SYNC_FULL_ENCRYPTION_DATA 26857
#define IDS_SYNC_LOGIN_SETTING_UP 26858
#define IDS_SYNC_PASSPHRASE_LABEL 26859
#define IDS_SYNC_PASSPHRASE_MISMATCH_ERROR 26860
#define IDS_SYNC_SERVICE_UNAVAILABLE 26861
#define IDS_SYNC_ENTER_PASSPHRASE_BODY_WITH_DATE 26862
#define IDS_SYNC_ENTER_PASSPHRASE_BODY 26863
#define IDS_TRANSLATE_INFOBAR_OPTIONS_MORE_LANGUAGE 26864
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NOT_SOURCE_LANGUAGE 26865
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NEVER_TRANSLATE_LANG 26866
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NEVER_TRANSLATE_SITE 26867
#define IDS_TRANSLATE_INFOBAR_OPTIONS_ALWAYS 26868
#define IDS_TRANSLATE_INFOBAR_OPTIONS_REPORT_ERROR 26869
#define IDS_TRANSLATE_INFOBAR_OPTIONS_ABOUT 26870
#define IDS_TRANSLATE_INFOBAR_ACCEPT 26872
#define IDS_TRANSLATE_INFOBAR_DENY 26873
#define IDS_TRANSLATE_INFOBAR_NEVER_TRANSLATE 26874
#define IDS_TRANSLATE_INFOBAR_ALWAYS_TRANSLATE 26875
#define IDS_TRANSLATE_INFOBAR_AFTER_MESSAGE 26876
#define IDS_TRANSLATE_INFOBAR_AFTER_MESSAGE_AUTODETERMINED_SOURCE_LANGUAGE 26877
#define IDS_TRANSLATE_INFOBAR_REVERT 26878
#define IDS_TRANSLATE_INFOBAR_RETRY 26879
#define IDS_TRANSLATE_NOTIFICATION_ERROR 26899
#define IDS_TRANSLATE_NOTIFICATION_ALWAYS_TRANSLATE 26900
#define IDS_TRANSLATE_NOTIFICATION_LANGUAGE_NEVER 26901
#define IDS_TRANSLATE_NOTIFICATION_SITE_NEVER 26902
#define IDS_TRANSLATE_NOTIFICATION_UNDO 26903
#define IDS_TRANSLATE_DETECTED_LANGUAGE 26904
#define IDS_TAB_GROUP_COLOR_GREY 26905
#define IDS_TAB_GROUP_COLOR_BLUE 26906
#define IDS_TAB_GROUP_COLOR_RED 26907
#define IDS_TAB_GROUP_COLOR_YELLOW 26908
#define IDS_TAB_GROUP_COLOR_GREEN 26909
#define IDS_TAB_GROUP_COLOR_PINK 26910
#define IDS_TAB_GROUP_COLOR_PURPLE 26911
#define IDS_TAB_GROUP_COLOR_CYAN 26912
#define IDS_TAB_GROUP_COLOR_ORANGE 26913
#define IDS_BOOKMARK_BAR_UNDO 26914
#define IDS_BOOKMARK_BAR_REDO 26915
#define IDS_BOOKMARK_BAR_UNDO_ADD 26916
#define IDS_BOOKMARK_BAR_REDO_ADD 26917
#define IDS_BOOKMARK_BAR_UNDO_DELETE 26918
#define IDS_BOOKMARK_BAR_REDO_DELETE 26919
#define IDS_BOOKMARK_BAR_UNDO_EDIT 26920
#define IDS_BOOKMARK_BAR_REDO_EDIT 26921
#define IDS_BOOKMARK_BAR_UNDO_MOVE 26922
#define IDS_BOOKMARK_BAR_REDO_MOVE 26923
#define IDS_BOOKMARK_BAR_UNDO_REORDER 26924
#define IDS_BOOKMARK_BAR_REDO_REORDER 26925
#define IDS_CLOSE_TUTORIAL 26926
#define IDS_CLOSE_PROMO 26927
#define IDS_PROMO_DISMISS_BUTTON 26928
#define IDS_PROMO_SNOOZE_BUTTON 26929
#define IDS_PROMO_SHOW_TUTORIAL_BUTTON 26930
#define IDS_TUTORIAL_RESTART_TUTORIAL 26931
#define IDS_TUTORIAL_CLOSE_TUTORIAL 26932
#define IDS_VERSION_UI_TITLE 26933
#define IDS_VERSION_UI_OFFICIAL 26934
#define IDS_VERSION_UI_UNOFFICIAL 26935
#define IDS_VERSION_UI_32BIT 26936
#define IDS_VERSION_UI_64BIT 26937
#define IDS_VERSION_UI_64BIT_INTEL 26938
#define IDS_VERSION_UI_64BIT_TRANSLATED_INTEL 26939
#define IDS_VERSION_UI_64BIT_ARM 26940
#define IDS_VERSION_UI_REVISION 26941
#define IDS_VERSION_UI_OS 26942
#define IDS_VERSION_UI_USER_AGENT 26944
#define IDS_VERSION_UI_COMMAND_LINE 26945
#define IDS_VERSION_UI_EXECUTABLE_PATH 26949
#define IDS_VERSION_UI_PROFILE_PATH 26950
#define IDS_VERSION_UI_PATH_NOTFOUND 26951
#define IDS_VERSION_UI_VARIATIONS 26952
#define IDS_VERSION_UI_VARIATIONS_CMD 26953
#define IDS_MANAGEMENT_TITLE 26961
#define IDS_MANAGEMENT_TOOLBAR_TITLE 26962
#define IDS_MANAGEMENT_SUBTITLE 26966
#define IDS_MANAGEMENT_SUBTITLE_MANAGED_BY 26964
#define IDS_MANAGEMENT_NOT_MANAGED_SUBTITLE 26965
#define IDS_MANAGEMENT_BROWSER_NOTICE 26967
#define IDS_MANAGEMENT_NOT_MANAGED_NOTICE 26968
#define IDS_MANAGEMENT_EXTENSION_REPORTING 27000
#define IDS_MANAGEMENT_EXTENSIONS_INSTALLED 27001
#define IDS_MANAGEMENT_EXTENSIONS_INSTALLED_BY 27002
#define IDS_MANAGEMENT_EXTENSIONS_NAME 27003
#define IDS_MANAGEMENT_EXTENSIONS_PERMISSIONS 27004
#define IDS_MANAGEMENT_MANAGED_WEBSITES 27005
#define IDS_MANAGEMENT_MANAGED_WEBSITES_EXPLANATION 27006
#define IDS_MANAGEMENT_MANAGED_WEBSITES_BY_EXPLANATION 27007
#define IDS_MANAGEMENT_BROWSER_REPORTING 27008
#define IDS_MANAGEMENT_BROWSER_REPORTING_EXPLANATION 27009
#define IDS_MANAGEMENT_EXTENSION_REPORT_MACHINE_NAME 27010
#define IDS_MANAGEMENT_EXTENSION_REPORT_MACHINE_NAME_ADDRESS 27011
#define IDS_MANAGEMENT_EXTENSION_REPORT_USERNAME 27012
#define IDS_MANAGEMENT_EXTENSION_REPORT_VERSION 27013
#define IDS_MANAGEMENT_EXTENSION_REPORT_EXTENSIONS_PLUGINS 27014
#define IDS_MANAGEMENT_EXTENSION_REPORT_USER_BROWSING_DATA 27015
#define IDS_MANAGEMENT_EXTENSION_REPORT_PERF_CRASH 27016
#define IDS_MANAGEMENT_THREAT_PROTECTION 27017
#define IDS_MANAGEMENT_THREAT_PROTECTION_DESCRIPTION 27018
#define IDS_MANAGEMENT_THREAT_PROTECTION_DESCRIPTION_BY 27019
#define IDS_MANAGEMENT_CONNECTORS_EVENT 27020
#define IDS_MANAGEMENT_CONNECTORS_VISIBLE_DATA 27021
#define IDS_MANAGEMENT_FILE_ATTACHED_EVENT 27022
#define IDS_MANAGEMENT_FILE_DOWNLOADED_EVENT 27023
#define IDS_MANAGEMENT_TEXT_ENTERED_EVENT 27024
#define IDS_MANAGEMENT_PAGE_PRINTED_EVENT 27025
#define IDS_MANAGEMENT_ENTERPRISE_REPORTING_EVENT 27026
#define IDS_MANAGEMENT_PAGE_VISITED_EVENT 27027
#define IDS_MANAGEMENT_FILE_ATTACHED_VISIBLE_DATA 27028
#define IDS_MANAGEMENT_FILE_DOWNLOADED_VISIBLE_DATA 27029
#define IDS_MANAGEMENT_TEXT_ENTERED_VISIBLE_DATA 27030
#define IDS_MANAGEMENT_PAGE_PRINTED_VISIBLE_DATA 27031
#define IDS_MANAGEMENT_ENTERPRISE_REPORTING_VISIBLE_DATA 27032
#define IDS_MANAGEMENT_PAGE_VISITED_VISIBLE_DATA 27033
#define IDS_HISTORY_CLUSTERS_DISABLE_MENU_ITEM_LABEL 27034
#define IDS_HISTORY_CLUSTERS_ENABLE_MENU_ITEM_LABEL 27035
#define IDS_HISTORY_CLUSTERS_JOURNEYS_TAB_LABEL 27036
#define IDS_HISTORY_CLUSTERS_LIST_TAB_LABEL 27037
#define IDS_HISTORY_CLUSTERS_CLUSTER_LABEL_SEARCH_TERMS 27038
#define IDS_HISTORY_CLUSTERS_CLUSTER_LABEL_MULTIPLE_HOSTNAMES 27039
#define IDS_HISTORY_CLUSTERS_LOAD_MORE_BUTTON_LABEL 27040
#define IDS_HISTORY_CLUSTERS_OPEN_ALL_IN_TABGROUP 27041
#define IDS_HISTORY_CLUSTERS_RELATED_SEARCHES_HEADER 27042
#define IDS_HISTORY_CLUSTERS_REMOVE_ALL_ITEMS 27043
#define IDS_HISTORY_CLUSTERS_REMOVE_ITEM_TOAST 27044
#define IDS_HISTORY_CLUSTERS_SAVED_IN_TABGROUP_LABEL 27045
#define IDS_HISTORY_CLUSTERS_SHOW_LESS_BUTTON_LABEL 27046
#define IDS_HISTORY_CLUSTERS_SHOW_MORE_BUTTON_LABEL 27047
#define IDS_HISTORY_CLUSTERS_SEARCH_YOUR_JOURNEYS 27048
#define IDS_CANCEL 27060
#define IDS_CLOSE 27061
#define IDS_CLEAR 27062
#define IDS_DONE 27063
#define IDS_LEARN_MORE 27064
#define IDS_OK 27065
#define IDS_RELOAD 27066
#define IDS_ADD 27067
#define IDS_REMOVE 27068
#define IDS_SAVE 27069
#define IDS_MENU 27070
#define IDS_INSTALL 27071
#define IDS_UPDATE 27072
#define IDS_NO_THANKS 27075
#define IDS_NOT_NOW 27076
#define IDS_TURN_OFF 27077
#define IDS_PLUGIN_NOT_SUPPORTED 27078
#define IDS_PRINT 125
#define IDS_RECENTLY_CLOSED 281
#define IDS_CHOOSE 27079
#define IDS_ACCNAME_BACK 306
#define IDS_ACCNAME_FORWARD 308
#define IDS_ACCNAME_OK 27080
#define IDS_ACCNAME_CANCEL 27081
#define IDS_ACCNAME_DONE 27082
#define IDS_ACCNAME_SAVE 27083
#define IDS_ACCNAME_CLOSE 321
#define IDS_ACCNAME_OPEN 27084
#define IDS_ACCNAME_PREVIOUS 27085
#define IDS_ACCNAME_NEXT 27086
#define IDS_ACCNAME_LOCATION 312
#define IDS_ACCNAME_PARTICLE_DISC 27087
#define IDS_ACCNAME_TAB_LIST 27088
#define IDS_UTILITY_PROCESS_JSON_PARSER_NAME 27089
#define IDS_SESSION_CRASHED_VIEW_RESTORE_BUTTON 27090
#define IDS_SESSION_CRASHED_VIEW_STARTUP_PAGES_BUTTON 27091
#define IDS_OPTIONS_ADVANCED_SECTION_TITLE_PRIVACY 27092
#define IDS_OPTIONS_ADVANCED_SECTION_TITLE_SAFETY_CHECK 27093
#define IDS_PATCH_SERVICE_DISPLAY_NAME 27094
#define IDS_UNZIP_SERVICE_DISPLAY_NAME 27095

// ---------------------------------------------------------------------------
// From extensions_strings.h:

#define IDS_EXTENSION_BAD_FILE_ENCODING 31060
#define IDS_EXTENSION_CANT_GET_ABSOLUTE_PATH 31061
#define IDS_EXTENSION_CONTAINS_PRIVATE_KEY 31062
#define IDS_EXTENSION_CRX_EXISTS 31063
#define IDS_EXTENSION_DIRECTORY_NO_EXISTS 31064
#define IDS_EXTENSION_ERROR_WHILE_SIGNING 31065
#define IDS_EXTENSION_FAILED_DURING_PACKAGING 31066
#define IDS_EXTENSION_LOAD_ABOUT_PAGE_FAILED 31067
#define IDS_EXTENSION_LOAD_BACKGROUND_SCRIPT_FAILED 31068
#define IDS_EXTENSION_LOAD_BACKGROUND_PAGE_FAILED 31069
#define IDS_EXTENSION_LOAD_CSS_FAILED 31070
#define IDS_EXTENSION_LOAD_JAVASCRIPT_FAILED 31071
#define IDS_EXTENSION_LOAD_OPTIONS_PAGE_FAILED 31072
#define IDS_EXTENSION_LOCALES_NO_DEFAULT_LOCALE_SPECIFIED 31073
#define IDS_EXTENSION_MANIFEST_UNREADABLE 31074
#define IDS_EXTENSION_MANIFEST_INVALID 31075
#define IDS_EXTENSION_PACKAGE_IMAGE_ERROR 31076
#define IDS_EXTENSION_PACKAGE_UNZIP_ERROR 31077
#define IDS_EXTENSION_PRIVATE_KEY_EXISTS 31078
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_READ 31079
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_EXPORT 31080
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_GENERATE 31081
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_OUTPUT 31082
#define IDS_EXTENSION_PRIVATE_KEY_INVALID 31083
#define IDS_EXTENSION_PRIVATE_KEY_NO_EXISTS 31084
#define IDS_EXTENSION_PRIVATE_KEY_INVALID_PATH 31085
#define IDS_EXTENSION_PRIVATE_KEY_INVALID_FORMAT 31086
#define IDS_EXTENSION_PUBLIC_KEY_FAILED_TO_EXPORT 31087
#define IDS_EXTENSION_SHARING_VIOLATION 31088
#define IDS_EXTENSION_CANT_INSTALL_POLICY_BLOCKED 31089
#define IDS_EXTENSION_CANT_MODIFY_POLICY_REQUIRED 31090
#define IDS_EXTENSION_CANT_UNINSTALL_POLICY_REQUIRED 31091
#define IDS_EXTENSION_DISABLED_UPDATE_REQUIRED_BY_POLICY 31092
#define IDS_DEVICE_NAME_WITH_PRODUCT_SERIAL 31093
#define IDS_DEVICE_NAME_WITH_PRODUCT_UNKNOWN_VENDOR 31094
#define IDS_DEVICE_NAME_WITH_PRODUCT_UNKNOWN_VENDOR_SERIAL 31095
#define IDS_DEVICE_NAME_WITH_PRODUCT_VENDOR 31096
#define IDS_DEVICE_NAME_WITH_PRODUCT_VENDOR_SERIAL 31097
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_UNKNOWN_VENDOR 31098
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_UNKNOWN_VENDOR_SERIAL 31099
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_VENDOR 31100
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_VENDOR_SERIAL 31101
#define IDS_DEVICE_PERMISSIONS_PROMPT_SINGLE_SELECTION 31102
#define IDS_DEVICE_PERMISSIONS_PROMPT_MULTIPLE_SELECTION 31103
#define IDS_CAMERA_FACING_USER 31104
#define IDS_CAMERA_FACING_ENVIRONMENT 31105
#define IDS_EXTENSION_USB_DEVICE_PRODUCT_NAME_AND_VENDOR 258
#define IDS_EXTENSION_TASK_MANAGER_APPVIEW_TAG_PREFIX 31106
#define IDS_EXTENSION_TASK_MANAGER_EXTENSIONOPTIONS_TAG_PREFIX 31107
#define IDS_EXTENSION_TASK_MANAGER_MIMEHANDLERVIEW_TAG_PREFIX 31108
#define IDS_EXTENSION_TASK_MANAGER_WEBVIEW_TAG_PREFIX 31109
#define IDS_EXTENSION_WARNINGS_NETWORK_DELAY 31110
#define IDS_EXTENSION_WARNINGS_DOWNLOAD_FILENAME_CONFLICT 31111
#define IDS_EXTENSION_WARNING_RELOAD_TOO_FREQUENT 31112
#define IDS_EXTENSION_WARNING_RULESET_FAILED_TO_LOAD 31113
#define IDS_EXTENSION_WARNING_ENABLED_RULE_COUNT_EXCEEDED 31114
#define IDS_EXTENSION_INSTALL_PROCESS_CRASHED 31115
#define IDS_EXTENSION_PACKAGE_ERROR_CODE 31116
#define IDS_EXTENSION_PACKAGE_ERROR_MESSAGE 31117
#define IDS_EXTENSION_PACKAGE_INSTALL_ERROR 31118
#define IDS_EXTENSION_UNPACK_FAILED 31119
#define IDS_EXTENSION_WEBGL_NOT_SUPPORTED 31120
#define IDS_EXTENSION_WINDOW_SHAPE_NOT_SUPPORTED 31121

// ---------------------------------------------------------------------------
// From generated_resources.h:

#define IDS_ACCESS_CODE_CAST_ACCESS_CODE_MESSAGE 1000
#define IDS_ACCESS_CODE_CAST_BACK 1001
#define IDS_ACCESS_CODE_CAST_CAST 1002
#define IDS_ACCESS_CODE_CAST_CONNECT 1003
#define IDS_ACCESS_CODE_CAST_DIALOG_TITLE 1004
#define IDS_ACCESS_CODE_CAST_ENTER_CHARACTER 1005
#define IDS_ACCESS_CODE_CAST_ERROR_ACCESS_CODE 1006
#define IDS_ACCESS_CODE_CAST_ERROR_NETWORK 1007
#define IDS_ACCESS_CODE_CAST_ERROR_PERMISSION 1008
#define IDS_ACCESS_CODE_CAST_ERROR_TOO_MANY_REQUESTS 1009
#define IDS_ACCESS_CODE_CAST_ERROR_UNKNOWN 1010
#define IDS_ACCESS_CODE_CAST_INPUT_ARIA_LABEL 1011
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_DAYS 1012
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_HOURS 1013
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_MONTHS 1014
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_YEARS 1015
#define IDS_ACCESS_CODE_CAST_SUBMIT 1016
#define IDS_ACCESS_CODE_CAST_USE_CAMERA 1017
#define IDS_BOOKMARK_GROUP_FROM_FIREFOX 1020
#define IDS_BOOKMARK_GROUP_FROM_SAFARI 1021
#define IDS_BOOKMARK_GROUP 1022
#define IDS_BOOKMARK_BAR_SHOW_APPS_SHORTCUT 1023
#define IDS_BOOKMARK_BAR_SHOW_READING_LIST 1024
#define IDS_BOOKMARK_BAR_SHOW_MANAGED_BOOKMARKS_DEFAULT_NAME 1025
#define IDS_BOOKMARK_BAR_SHOW_MANAGED_BOOKMARKS 1026
#define IDS_BOOKMARK_BAR_APPS_SHORTCUT_NAME 343
#define IDS_BOOKMARK_BAR_APPS_SHORTCUT_TOOLTIP 345
#define IDS_BOOKMARK_BAR_OVERFLOW_BUTTON_TOOLTIP 1027
#define IDS_BOOKMARK_BAR_OPEN_ALL 1028
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT 1029
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT_NEW_WINDOW 1030
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT_INCOGNITO 1031
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT_NEW_TAB_GROUP 1032
#define IDS_BOOKMARK_BAR_OPEN_IN_NEW_TAB 1033
#define IDS_BOOKMARK_BAR_OPEN_IN_NEW_WINDOW 1034
#define IDS_BOOKMARK_BAR_OPEN_INCOGNITO 1035
#define IDS_BOOKMARK_BAR_EDIT 1036
#define IDS_BOOKMARK_BAR_RENAME_FOLDER 1037
#define IDS_BOOKMARK_BAR_REMOVE 1038
#define IDS_BOOKMARK_BAR_ADD_NEW_BOOKMARK 1039
#define IDS_BOOKMARK_BAR_NEW_FOLDER 1040
#define IDS_SHOW_BOOKMARK_BAR 286
#define IDS_BOOKMARK_BAR_SHOULD_OPEN_ALL 1041
#define IDS_BOOKMARK_BUBBLE_PAGE_BOOKMARKED 1042
#define IDS_BOOKMARK_BUBBLE_PAGE_BOOKMARK 1043
#define IDS_BOOKMARK_BUBBLE_NAME_LABEL 1044
#define IDS_BOOKMARK_AX_BUBBLE_NAME_LABEL 1045
#define IDS_BOOKMARK_BUBBLE_FOLDER_LABEL 1046
#define IDS_BOOKMARK_AX_BUBBLE_FOLDER_LABEL 1047
#define IDS_BOOKMARK_BUBBLE_OPTIONS 1048
#define IDS_BOOKMARK_BUBBLE_CHOOSER_ANOTHER_FOLDER 1049
#define IDS_BOOKMARK_DICE_PROMO_SYNC_MESSAGE 1050
#define IDS_BOOKMARK_EDITOR_NAME_LABEL 1055
#define IDS_BOOKMARK_AX_EDITOR_NAME_LABEL 1056
#define IDS_BOOKMARK_BAR_EDIT_FOLDER_LABEL 1057
#define IDS_BOOKMARK_AX_EDITOR_URL_LABEL 1058
#define IDS_BOOKMARK_EDITOR_URL_LABEL 1059
#define IDS_BOOKMARK_EDITOR_CONFIRM_DELETE 1060
#define IDS_BOOKMARK_EDITOR_NEW_FOLDER_BUTTON 1061
#define IDS_BOOKMARK_EDITOR_NEW_FOLDER_MENU_ITEM 1062
#define IDS_BOOKMARK_FOLDER_EDITOR_WINDOW_TITLE 1063
#define IDS_BOOKMARK_FOLDER_EDITOR_WINDOW_TITLE_NEW 1064
#define IDS_BOOKMARK_ALL_TABS_DIALOG_TITLE 1065
#define IDS_BOOKMARK_MANAGER_TITLE 1066
#define IDS_BOOKMARK_MANAGER_SEARCH_BUTTON 1067
#define IDS_BOOKMARK_MANAGER 174
#define IDS_BOOKMARK_MANAGER_ORGANIZE_MENU 1068
#define IDS_BOOKMARK_MANAGER_INVALID_URL 1069
#define IDS_EXPORT_BOOKMARKS_DEFAULT_FILENAME 1070
#define IDS_BOOKMARK_MANAGER_ADD_BOOKMARK_TITLE 1071
#define IDS_BOOKMARK_MANAGER_ADD_FOLDER_TITLE 1072
#define IDS_BOOKMARK_MANAGER_CLEAR_SEARCH 1073
#define IDS_BOOKMARK_MANAGER_EMPTY_LIST 1074
#define IDS_BOOKMARK_MANAGER_EMPTY_UNMODIFIABLE_LIST 1075
#define IDS_BOOKMARK_MANAGER_FOLDER_LABEL 1076
#define IDS_BOOKMARK_MANAGER_FOLDER_RENAME_TITLE 1077
#define IDS_BOOKMARK_MANAGER_FOLDER_LIST_CHANGED 1078
#define IDS_BOOKMARK_MANAGER_LIST_AX_LABEL 1079
#define IDS_BOOKMARK_MANAGER_MENU_ADD_BOOKMARK 1080
#define IDS_BOOKMARK_MANAGER_MENU_ADD_FOLDER 1081
#define IDS_BOOKMARK_MANAGER_MENU_CUT 1082
#define IDS_BOOKMARK_MANAGER_MENU_COPY 1083
#define IDS_BOOKMARK_MANAGER_MENU_PASTE 1084
#define IDS_BOOKMARK_MANAGER_MENU_EXPORT 1085
#define IDS_BOOKMARK_MANAGER_MENU_HELP_CENTER 1086
#define IDS_BOOKMARK_MANAGER_MENU_IMPORT 1087
#define IDS_BOOKMARK_MANAGER_MENU_IMPORT_BEGAN 1088
#define IDS_BOOKMARK_MANAGER_MENU_IMPORT_ENDED 1089
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL 1090
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_WITH_COUNT 1091
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_NEW_WINDOW 1092
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_NEW_WINDOW_WITH_COUNT 1093
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_INCOGNITO 1094
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_INCOGNITO_WITH_COUNT 1095
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_IN_NEW_TAB 1096
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_IN_NEW_WINDOW 1097
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_INCOGNITO 1098
#define IDS_BOOKMARK_MANAGER_MENU_RENAME 1099
#define IDS_BOOKMARK_MANAGER_MENU_SHOW_IN_FOLDER 1100
#define IDS_BOOKMARK_MANAGER_MENU_SORT 1101
#define IDS_BOOKMARK_MANAGER_MORE_ACTIONS 1102
#define IDS_BOOKMARK_MANAGER_MORE_ACTIONS_AX_LABEL 1103
#define IDS_BOOKMARK_MANAGER_MORE_ACTIONS_MULTI_AX_LABEL 1104
#define IDS_BOOKMARK_MANAGER_OPEN_DIALOG_TITLE 1105
#define IDS_BOOKMARK_MANAGER_OPEN_DIALOG_CONFIRM 1106
#define IDS_BOOKMARK_MANAGER_ITEMS_SELECTED 1107
#define IDS_BOOKMARK_MANAGER_ITEMS_UNSELECTED 1108
#define IDS_BOOKMARK_MANAGER_SIDEBAR_AX_LABEL 1109
#define IDS_BOOKMARK_MANAGER_TOAST_FOLDER_SORTED 1110
#define IDS_BOOKMARK_MANAGER_TOAST_ITEM_DELETED 1111
#define IDS_BOOKMARK_MANAGER_TOAST_ITEMS_DELETED 1112
#define IDS_BOOKMARK_MANAGER_TOAST_ITEM_COPIED 1113
#define IDS_BOOKMARK_MANAGER_TOAST_ITEMS_COPIED 1114
#define IDS_BOOKMARKS_MENU 173
#define IDS_BOOKMARK_THIS_TAB 175
#define IDS_BOOKMARK_ALL_TABS 176
#define IDS_TOOLTIP_STARRED 1115
#define IDS_BOOKMARK_SCREEN_READER_CREATED 1116
#define IDS_BOOKMARK_SCREEN_READER_REORDERED 1117
#define IDS_BOOKMARK_SCREEN_READER_MOVED 1118
#define IDS_APP_MANAGEMENT_CAMERA 1119
#define IDS_APP_MANAGEMENT_LOCATION 1120
#define IDS_APP_MANAGEMENT_MICROPHONE 1121
#define IDS_APP_MANAGEMENT_NO_APPS_FOUND 1122
#define IDS_APP_MANAGEMENT_NOTIFICATIONS 1123
#define IDS_APP_MANAGEMENT_PERMISSIONS 1124
#define IDS_APP_MANAGEMENT_MORE_SETTINGS 1125
#define IDS_APP_MANAGEMENT_PIN_TO_SHELF 1126
#define IDS_APP_MANAGEMENT_PRESET_WINDOW_SIZES 1127
#define IDS_APP_MANAGEMENT_WINDOW 1128
#define IDS_APP_MANAGEMENT_PRESET_WINDOW_SIZES_TEXT 1129
#define IDS_APP_MANAGEMENT_PRINTING 1130
#define IDS_APP_MANAGEMENT_SEARCH_PROMPT 1131
#define IDS_APP_MANAGEMENT_UNINSTALL_APP 1132
#define IDS_APP_MANAGEMENT_CONTACTS 1133
#define IDS_APP_MANAGEMENT_STORAGE 1134
#define IDS_APP_MANAGEMENT_RUN_ON_OS_LOGIN 1135
#define IDS_APP_MANAGEMENT_POLICY_APP_POLICY_STRING 1136
#define IDS_APP_MANAGEMENT_INTENT_SETTINGS_TITLE 1137
#define IDS_APP_MANAGEMENT_INTENT_SHARING_APP_OPEN 1138
#define IDS_APP_MANAGEMENT_INTENT_SHARING_BROWSER_OPEN 1139
#define IDS_APP_MANAGEMENT_INTENT_SHARING_TAB_EXPLANATION 1140
#define IDS_APP_MANAGEMENT_INTENT_SETTINGS_DIALOG_TITLE 1141
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_CHANGE_BUTTON 1142
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TITLE 1143
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_1_APP 1144
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_2_APPS 1145
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_3_APPS 1146
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_4_APPS 1147
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_5_OR_MORE_APPS 1148
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_1_APP 1149
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_2_APPS 1150
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_3_APPS 1151
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_4_APPS 1152
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_5_OR_MORE_APPS 1153
#define IDS_APP_MANAGEMENT_APP_DETAILS_TITLE 1154
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_ANDROID 1155
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_CHROME 1156
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_WEB 1157
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_SYSTEM 1158
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_CROS_SYSTEM 1159
#define IDS_APP_MANAGEMENT_APP_DETAILS_INSTALL_SOURCE_WEB_STORE 1160
#define IDS_APP_MANAGEMENT_APP_DETAILS_INSTALL_SOURCE_PLAY_STORE 1161
#define IDS_APP_MANAGEMENT_APP_DETAILS_INSTALL_SOURCE_BROWSER 1162
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_AND_SOURCE_COMBINED 1163
#define IDS_APP_MANAGEMENT_APP_DETAILS_VERSION 1164
#define IDS_APP_MANAGEMENT_APP_DETAILS_STORAGE_TITLE 1165
#define IDS_APP_MANAGEMENT_APP_DETAILS_APP_SIZE 1166
#define IDS_APP_MANAGEMENT_APP_DETAILS_DATA_SIZE 1167
#define IDS_APP_MANAGEMENT_FILE_HANDLING_HEADER 1168
#define IDS_APP_MANAGEMENT_FILE_HANDLING_OVERFLOW_DIALOG_TITLE 1169
#define IDS_APP_MANAGEMENT_FILE_HANDLING_SET_DEFAULTS_LINK 1170
#define IDS_APP_MANAGEMENT_FILE_HANDLING_TYPES 1171
#define IDS_MEDIA_ROUTER_ICON_TOOLTIP_TEXT 1172
#define IDS_MEDIA_ROUTER_MENU_ITEM_TITLE 161
#define IDS_MEDIA_ROUTER_PRESENTATION_CAST_MODE 1173
#define IDS_MEDIA_ROUTER_DESKTOP_MIRROR_CAST_MODE 1174
#define IDS_MEDIA_ROUTER_TAB_MIRROR_CAST_MODE 1175
#define IDS_MEDIA_ROUTER_LOCAL_FILE_CAST_MODE 1176
#define IDS_MEDIA_ROUTER_CAST_LOCAL_MEDIA_TITLE 1177
#define IDS_MEDIA_ROUTER_ALTERNATIVE_SOURCES_BUTTON 1178
#define IDS_MEDIA_ROUTER_ABOUT 1179
#define IDS_MEDIA_ROUTER_HELP 1180
#define IDS_MEDIA_ROUTER_ALWAYS_SHOW_TOOLBAR_ACTION 1181
#define IDS_MEDIA_ROUTER_REPORT_ISSUE 1182
#define IDS_MEDIA_ROUTER_SHOWN_BY_POLICY 1183
#define IDS_MEDIA_ROUTER_TOGGLE_MEDIA_REMOTING 1184
#define IDS_MEDIA_ROUTER_ISSUE_CREATE_ROUTE_TIMEOUT 1185
#define IDS_MEDIA_ROUTER_ISSUE_CREATE_ROUTE_TIMEOUT_FOR_DESKTOP 1186
#define IDS_MEDIA_ROUTER_ISSUE_CREATE_ROUTE_TIMEOUT_FOR_TAB 1187
#define IDS_MEDIA_ROUTER_ISSUE_UNABLE_TO_CAST_DESKTOP 1188
#define IDS_MEDIA_ROUTER_ISSUE_FILE_CAST_GENERIC_ERROR 1189
#define IDS_MEDIA_ROUTER_ISSUE_FILE_CAST_ERROR 1190
#define IDS_MEDIA_ROUTER_ISSUE_TAB_AUDIO_NOT_SUPPORTED 1191
#define IDS_MEDIA_ROUTER_ISSUE_MAC_SCREEN_CAPTURE_PERMISSION_ERROR 1192
#define IDS_MEDIA_ROUTER_STATUS_LOOKING_FOR_DEVICES 1193
#define IDS_MEDIA_ROUTER_STATUS_NO_DEVICES_FOUND 1194
#define IDS_MEDIA_ROUTER_NO_DEVICES_FOUND_BUTTON 1195
#define IDS_MEDIA_ROUTER_DESTINATION_MISSING 1196
#define IDS_MEDIA_ROUTER_SINK_AVAILABLE 1197
#define IDS_MEDIA_ROUTER_SINK_CONNECTING 1198
#define IDS_MEDIA_ROUTER_SINK_DISCONNECTING 1199
#define IDS_MEDIA_ROUTER_STOP_CASTING 1200
#define IDS_MEDIA_ROUTER_SOURCE_NOT_SUPPORTED 1201
#define IDS_MEDIA_ROUTER_AVAILABLE_SPECIFIC_SITES 1202
#define IDS_MEDIA_ROUTER_CASTING_DESKTOP 1203
#define IDS_MEDIA_ROUTER_CASTING_TAB 1204
#define IDS_MEDIA_ROUTER_PRESENTATION_ROUTE_DESCRIPTION 1205
#define IDS_MEDIA_ROUTER_FILE_DIALOG_AUDIO_VIDEO_FILTER 1206
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_TITLE 1207
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_BODY_TEXT 1208
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_CHECKBOX 1209
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_OPTIMIZE_BUTTON 1210
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_CANCEL_BUTTON 1211
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_CANCEL_BUTTON_MACOS 1212
#define IDS_MEDIA_ROUTER_CAST_TO_MEETING_DEPRECATED 1213
#define IDS_MEDIA_ROUTER_CAST_TO_MEETING_REMOVED 1214
#define IDS_MEDIA_ROUTER_WIRED_DISPLAY_SINK_NAME 1215
#define IDS_MEDIA_ROUTER_FEEDBACK_TITLE 1216
#define IDS_MEDIA_ROUTER_FEEDBACK_NA 1217
#define IDS_MEDIA_ROUTER_FEEDBACK_FORM_DESCRIPTION 1218
#define IDS_MEDIA_ROUTER_FEEDBACK_HEADER 1219
#define IDS_MEDIA_ROUTER_FEEDBACK_YOUR_ANSWER 1220
#define IDS_MEDIA_ROUTER_FEEDBACK_REQUIRED 1221
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_QUESTION 1222
#define IDS_MEDIA_ROUTER_FEEDBACK_PROMPT 1223
#define IDS_MEDIA_ROUTER_FEEDBACK_MIRRORING_QUALITY_SUBHEADING 1224
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_SMOOTHNESS 1225
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_QUALITY 1226
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_QUALITY 1227
#define IDS_MEDIA_ROUTER_FEEDBACK_CONTENT_QUESTION 1228
#define IDS_MEDIA_ROUTER_FEEDBACK_ADDITIONAL_COMMENTS 1229
#define IDS_MEDIA_ROUTER_FEEDBACK_ALLOW_CONTACT_BY_EMAIL 1230
#define IDS_MEDIA_ROUTER_FEEDBACK_YOUR_EMAIL_ADDRESS 1231
#define IDS_MEDIA_ROUTER_FEEDBACK_EMAIL_FIELD 1232
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_BUTTON 1233
#define IDS_MEDIA_ROUTER_FEEDBACK_DISCARD_CONFIRMATION 1234
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_BUG_OR_ERROR 1235
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_FEATURE_REQUEST 1236
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_PROJECTION_QUALITY 1237
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_DISCOVERY 1238
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_OTHER 1239
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_FREEZES 1240
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_JERKY 1241
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_STUTTER 1242
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_SMOOTH 1243
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_PERFECT 1244
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_UNWATCHABLE 1245
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_POOR 1246
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_ACCEPTABLE 1247
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_GOOD 1248
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_GREAT 1249
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_UNINTELLIGIBLE 1250
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_POOR 1251
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_ACCEPTABLE 1252
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_GOOD 1253
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_PERFECT 1254
#define IDS_MEDIA_ROUTER_FEEDBACK_SENDING 1255
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_FAIL 1256
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_SUCCESS 1257
#define IDS_MEDIA_ROUTER_FEEDBACK_RESENDING 1258
#define IDS_MEDIA_ROUTER_FEEDBACK_LOGS_HEADER 1259
#define IDS_MEDIA_ROUTER_FEEDBACK_FINE_LOGS_WARNING 1260
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_LOGS_HTML 1261
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_LOGS 1262
#define IDS_MEDIA_ROUTER_FEEDBACK_PRIVACY_DATA_USAGE 1263
#define IDS_MEDIA_ROUTER_FEEDBACK_SETUP_VISIBILITY_QUESTION 1264
#define IDS_MEDIA_ROUTER_FEEDBACK_YES 1265
#define IDS_MEDIA_ROUTER_FEEDBACK_NO 1266
#define IDS_MEDIA_ROUTER_FEEDBACK_DID_NOT_TRY 1267
#define IDS_MEDIA_ROUTER_FEEDBACK_SOFTWARE_QUESTION 1268
#define IDS_MEDIA_ROUTER_FEEDBACK_UNKNOWN 1269
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_QUESTION 1270
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_SAME_WIFI 1271
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_DIFFERENT_WIFI 1272
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_WIRED_PC 1273
#define IDS_GLOBAL_MEDIA_CONTROLS_ICON_TOOLTIP_TEXT 1274
#define IDS_GLOBAL_MEDIA_CONTROLS_DEVICES_LABEL_WITH_COLON 1275
#define IDS_GLOBAL_MEDIA_CONTROLS_DEVICES_LABEL 1276
#define IDS_GLOBAL_MEDIA_CONTROLS_STOP_CASTING_BUTTON_LABEL 1277
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_ENGLISH_ONLY 1278
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION 1279
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_SHOW_LANGUAGE 1280
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_DOWNLOAD_PROGRESS 1281
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_DOWNLOAD_ERROR 1282
#define IDS_GLOBAL_MEDIA_CONTROLS_DIALOG_NAME 1283
#define IDS_GLOBAL_MEDIA_CONTROLS_SHOW_DEVICE_LIST 1284
#define IDS_GLOBAL_MEDIA_CONTROLS_HIDE_DEVICE_LIST 1285
#define IDS_GLOBAL_MEDIA_CONTROLS_CONTROL_CAST_SESSIONS_PROMO 1286
#define IDS_MEDIA_TOOLBAR_CONTEXT_REPORT_CAST_ISSUE 1287
#define IDS_MEDIA_TOOLBAR_CONTEXT_SHOW_OTHER_SESSIONS 1288
#define IDS_CHILD_INFO_ONE_CUSTODIAN 1289
#define IDS_CHILD_INFO_TWO_CUSTODIANS 1290
#define IDS_AVATAR_BUTTON_GUEST 1291
#define IDS_AVATAR_BUTTON_GUEST_TOOLTIP 1292
#define IDS_GUEST_BUBBLE_ACCESSIBLE_TITLE 1293
#define IDS_INCOGNITO_BUBBLE_ACCESSIBLE_TITLE 1294
#define IDS_AVATAR_BUTTON_INCOGNITO 1295
#define IDS_AVATAR_BUTTON_INCOGNITO_TOOLTIP 1296
#define IDS_AVATAR_BUTTON_SYNC_ERROR 1297
#define IDS_AVATAR_BUTTON_SYNC_ERROR_TOOLTIP 1298
#define IDS_AVATAR_BUTTON_SYNC_PAUSED 1299
#define IDS_BLOCK_INTERSTITIAL_DEFAULT_FEEDBACK_TEXT 1300
#define IDS_PROFILES_MENU_NAME 366
#define IDS_PROFILES_CREATE_NEW_PROFILE_OPTION 341
#define IDS_PROFILES_ADD_PROFILE_LABEL 367
#define IDS_PROFILES_PROFILE_BUBBLE_ACCESSIBLE_TITLE 1301
#define IDS_PROFILES_EXIT_PROFILE_BUTTON 1302
#define IDS_PROFILES_GAIA_SIGNIN_TITLE 1303
#define IDS_PROFILES_ACCOUNT_REMOVAL_TITLE 1304
#define IDS_PROFILES_SYNC_COMPLETE_TITLE 1305
#define IDS_PROFILES_OPEN_SYNC_SETTINGS_BUTTON 1306
#define IDS_PROFILES_DICE_SIGNIN_BUTTON 1307
#define IDS_PROFILES_DICE_NOT_SYNCING_TITLE 1308
#define IDS_PROFILES_DICE_SIGNIN_FIRST_ACCOUNT_BUTTON 1309
#define IDS_PROFILES_DICE_SIGNIN_FIRST_ACCOUNT_BUTTON_NO_NAME 1310
#define IDS_PROFILES_DICE_SYNC_DISABLED_TITLE 1311
#define IDS_PROFILES_DICE_SYNC_PAUSED_TITLE 1312
#define IDS_PROFILES_CLOSE_X_WINDOWS_BUTTON 1313
#define IDS_PROFILES_SIGNIN_PROMO 1314
#define IDS_PROFILES_PASSWORDS_LINK 1315
#define IDS_PROFILES_CREDIT_CARDS_LINK 1316
#define IDS_PROFILES_ADDRESSES_LINK 1317
#define IDS_PROFILES_LIST_PROFILES_TITLE 1318
#define IDS_PROFILES_PROFILE_MANAGE_ACCOUNTS_BUTTON 1319
#define IDS_PROFILES_PROFILE_HIDE_MANAGE_ACCOUNTS_BUTTON 1320
#define IDS_PROFILES_MANAGE_PROFILES_BUTTON_TOOLTIP 1321
#define IDS_PROFILES_CUSTOMIZE_PROFILE_BUTTON_TOOLTIP 1322
#define IDS_PROFILES_GUEST_PROFILE_NAME 1323
#define IDS_DEFAULT_PROFILE_NAME 207
#define IDS_LEGACY_DEFAULT_PROFILE_NAME 208
#define IDS_NUMBERED_PROFILE_NAME 1324
#define IDS_NEW_NUMBERED_PROFILE_NAME 1325
#define IDS_SINGLE_PROFILE_DISPLAY_NAME 1326
#define IDS_GUEST_PROFILE_NAME 1327
#define IDS_DEFAULT_AVATAR_NAME_8 1328
#define IDS_DEFAULT_AVATAR_NAME_9 1329
#define IDS_DEFAULT_AVATAR_NAME_10 1330
#define IDS_DEFAULT_AVATAR_NAME_11 1331
#define IDS_DEFAULT_AVATAR_NAME_12 1332
#define IDS_DEFAULT_AVATAR_NAME_13 1333
#define IDS_DEFAULT_AVATAR_NAME_14 1334
#define IDS_DEFAULT_AVATAR_NAME_15 1335
#define IDS_DEFAULT_AVATAR_NAME_16 1336
#define IDS_DEFAULT_AVATAR_NAME_17 1337
#define IDS_DEFAULT_AVATAR_NAME_18 1338
#define IDS_DEFAULT_AVATAR_NAME_19 1339
#define IDS_DEFAULT_AVATAR_NAME_20 1340
#define IDS_DEFAULT_AVATAR_NAME_21 1341
#define IDS_DEFAULT_AVATAR_NAME_22 1342
#define IDS_DEFAULT_AVATAR_NAME_23 1343
#define IDS_DEFAULT_AVATAR_NAME_24 1344
#define IDS_DEFAULT_AVATAR_NAME_25 1345
#define IDS_DEFAULT_AVATAR_NAME_26 1346
#define IDS_DEFAULT_AVATAR_LABEL_0 1347
#define IDS_DEFAULT_AVATAR_LABEL_1 1348
#define IDS_DEFAULT_AVATAR_LABEL_2 1349
#define IDS_DEFAULT_AVATAR_LABEL_3 1350
#define IDS_DEFAULT_AVATAR_LABEL_4 1351
#define IDS_DEFAULT_AVATAR_LABEL_5 1352
#define IDS_DEFAULT_AVATAR_LABEL_6 1353
#define IDS_DEFAULT_AVATAR_LABEL_7 1354
#define IDS_DEFAULT_AVATAR_LABEL_8 1355
#define IDS_DEFAULT_AVATAR_LABEL_9 1356
#define IDS_DEFAULT_AVATAR_LABEL_10 1357
#define IDS_DEFAULT_AVATAR_LABEL_11 1358
#define IDS_DEFAULT_AVATAR_LABEL_12 1359
#define IDS_DEFAULT_AVATAR_LABEL_13 1360
#define IDS_DEFAULT_AVATAR_LABEL_14 1361
#define IDS_DEFAULT_AVATAR_LABEL_15 1362
#define IDS_DEFAULT_AVATAR_LABEL_16 1363
#define IDS_DEFAULT_AVATAR_LABEL_17 1364
#define IDS_DEFAULT_AVATAR_LABEL_18 1365
#define IDS_DEFAULT_AVATAR_LABEL_19 1366
#define IDS_DEFAULT_AVATAR_LABEL_20 1367
#define IDS_DEFAULT_AVATAR_LABEL_21 1368
#define IDS_DEFAULT_AVATAR_LABEL_22 1369
#define IDS_DEFAULT_AVATAR_LABEL_23 1370
#define IDS_DEFAULT_AVATAR_LABEL_24 1371
#define IDS_DEFAULT_AVATAR_LABEL_25 1372
#define IDS_DEFAULT_AVATAR_LABEL_26 1373
#define IDS_DEFAULT_AVATAR_LABEL_27 1374
#define IDS_DEFAULT_AVATAR_LABEL_28 1375
#define IDS_DEFAULT_AVATAR_LABEL_29 1376
#define IDS_DEFAULT_AVATAR_LABEL_30 1377
#define IDS_DEFAULT_AVATAR_LABEL_31 1378
#define IDS_DEFAULT_AVATAR_LABEL_32 1379
#define IDS_DEFAULT_AVATAR_LABEL_33 1380
#define IDS_DEFAULT_AVATAR_LABEL_34 1381
#define IDS_DEFAULT_AVATAR_LABEL_35 1382
#define IDS_DEFAULT_AVATAR_LABEL_36 1383
#define IDS_DEFAULT_AVATAR_LABEL_37 1384
#define IDS_DEFAULT_AVATAR_LABEL_38 1385
#define IDS_DEFAULT_AVATAR_LABEL_39 1386
#define IDS_DEFAULT_AVATAR_LABEL_40 1387
#define IDS_DEFAULT_AVATAR_LABEL_41 1388
#define IDS_DEFAULT_AVATAR_LABEL_42 1389
#define IDS_DEFAULT_AVATAR_LABEL_43 1390
#define IDS_DEFAULT_AVATAR_LABEL_44 1391
#define IDS_DEFAULT_AVATAR_LABEL_45 1392
#define IDS_DEFAULT_AVATAR_LABEL_46 1393
#define IDS_DEFAULT_AVATAR_LABEL_47 1394
#define IDS_DEFAULT_AVATAR_LABEL_48 1395
#define IDS_DEFAULT_AVATAR_LABEL_49 1396
#define IDS_DEFAULT_AVATAR_LABEL_50 1397
#define IDS_DEFAULT_AVATAR_LABEL_51 1398
#define IDS_DEFAULT_AVATAR_LABEL_52 1399
#define IDS_DEFAULT_AVATAR_LABEL_53 1400
#define IDS_DEFAULT_AVATAR_LABEL_54 1401
#define IDS_DEFAULT_AVATAR_LABEL_55 1402
#define IDS_PROFILES_LOCAL_PROFILE_STATE 339
#define IDS_PROFILES_CREATE_BUTTON_LABEL 1403
#define IDS_PROFILES_MANAGE_BUTTON_LABEL 340
#define IDS_PROFILES_DEFAULT_NAME 242
#define IDS_SYNC_LOGIN_NAME_PROHIBITED 1404
#define IDS_SUPERVISED_USER_NOT_ALLOWED_BY_POLICY 1405
#define IDS_OLD_PROFILES_DISABLED_TITLE 1406
#define IDS_OLD_PROFILES_DISABLED_MESSAGE 1407
#define IDS_OLD_PROFILES_DISABLED_ADD_PERSON_SUGGESTION 1408
#define IDS_OLD_PROFILES_DISABLED_ADD_PERSON_SUGGESTION_WITH_DOMAIN 1409
#define IDS_OLD_PROFILES_DISABLED_REMOVED_OLD_PROFILE 1410
#define IDS_SYNC_USER_NAME_IN_USE_ERROR 1411
#define IDS_SYNC_USER_NAME_IN_USE_BY_ERROR 1412
#define IDS_SCREEN_LOCK_SIGN_OUT 1413
#define IDS_PROFILE_CUSTOMIZATION_DONE_BUTTON_LABEL 1420
#define IDS_PROFILE_CUSTOMIZATION_WELCOME 1421
#define IDS_PROFILE_CUSTOMIZATION_INPUT_LABEL 1422
#define IDS_ENTERPRISE_PROFILE_WELCOME_TITLE 1423
#define IDS_ENTERPRISE_PROFILE_WELCOME_ACCOUNT_MANAGED_BY 1424
#define IDS_ENTERPRISE_PROFILE_WELCOME_ACCOUNT_EMAIL_MANAGED_BY 1425
#define IDS_ENTERPRISE_PROFILE_WELCOME_DEVICE_MANAGED_BY 1426
#define IDS_ENTERPRISE_PROFILE_WELCOME_DEVICE_MANAGED 1427
#define IDS_ENTERPRISE_PROFILE_WELCOME_MANAGED_DESCRIPTION_WITH_SYNC 1428
#define IDS_ENTERPRISE_PROFILE_WELCOME_MANAGED_DESCRIPTION_WITHOUT_SYNC 1429
#define IDS_ENTERPRISE_WELCOME_PROFILE_REQUIRED_TITLE 1430
#define IDS_ENTERPRISE_WELCOME_PROFILE_WILL_BE_MANAGED_TITLE 1431
#define IDS_ENTERPRISE_PROFILE_WELCOME_CREATE_PROFILE_BUTTON 1432
#define IDS_ENTERPRISE_PROFILE_WELCOME_LINK_DATA_CHECKBOX 1433
#define IDS_PROFILE_PICKER_ADD_SPACE_BUTTON 1434
#define IDS_PROFILE_PICKER_BROWSE_AS_GUEST_BUTTON 1435
#define IDS_PROFILE_PICKER_BACK_BUTTON_ARIA_LABEL 1436
#define IDS_PROFILE_PICKER_BACK_BUTTON_SIGN_IN_LABEL 1437
#define IDS_PROFILE_PICKER_PROFILE_CARD_NEEDS_SIGNIN_PROMPT 1438
#define IDS_PROFILE_PICKER_PROFILE_CARD_LABEL 1439
#define IDS_PROFILE_PICKER_PROFILE_CARD_INPUT_LABEL 1440
#define IDS_PROFILE_PICKER_PROFILE_MENU_REMOVE_TEXT 1441
#define IDS_PROFILE_PICKER_PROFILE_MENU_REMOVE_CONFIRM 1442
#define IDS_PROFILE_PICKER_PROFILE_MENU_CUSTOMIZE_TEXT 1443
#define IDS_PROFILE_PICKER_PROFILE_MENU_INCOGNITO_TEXT 1444
#define IDS_PROFILE_PICKER_ASK_ON_STARTUP 1445
#define IDS_PROFILE_PICKER_REMOVE_WARNING_LOCAL_PROFILE_TITLE 1446
#define IDS_PROFILE_PICKER_REMOVE_WARNING_HISTORY 1447
#define IDS_PROFILE_PICKER_REMOVE_WARNING_PASSWORDS 1448
#define IDS_PROFILE_PICKER_REMOVE_WARNING_BOOKMARKS 1449
#define IDS_PROFILE_PICKER_REMOVE_WARNING_AUTOFILL 1450
#define IDS_PROFILE_PICKER_REMOVE_WARNING_CALCULATING 1451
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_SIGNIN_BUTTON_LABEL 1452
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_NOT_NOW_BUTTON_LABEL 1453
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_CUSTOMIZE_AVATAR_BUTTON_LABEL 1454
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_THEME_TEXT 1455
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_AVATAR_TEXT 1456
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_DONE 1457
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_AVATAR_DONE 1458
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_INPUT_NAME 1459
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_SHORTCUT_TEXT 1460
#define IDS_PROFILE_PICKER_TAKE_A_TOUR_BUTTON_LABEL 1461
#define IDS_PROFILE_PICKER_IPH_NEXT_BUTTON_LABEL 1462
#define IDS_PROFILE_PICKER_IPH_DONE_BUTTON_LABEL 1463
#define IDS_PROFILE_PICKER_IPH_FOR_PROFILES_TITLE 1464
#define IDS_PROFILE_PICKER_IPH_FOR_ADD_PROFILE_TITLE 1465
#define IDS_PROFILE_PICKER_IPH_FOR_CUSTOMIZE_PROFILE_TITLE 1466
#define IDS_PROFILE_PICKER_IPH_FOR_CUSTOMIZE_PROFILE_TEXT 1467
#define IDS_PROFILE_PICKER_PROFILE_SWITCH_SWITCH_BUTTON_LABEL 1468
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_DEVICE_MANAGED_BY_DESCRIPTION 1469
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_DEVICE_MANAGED_DESCRIPTION 1470
#define IDS_PROFILE_PICKER_REMOVE_WARNING_LOCAL_PROFILE 1471
#define IDS_PROFILE_PICKER_REMOVE_WARNING_SIGNED_IN_PROFILE_TITLE 1472
#define IDS_PROFILE_PICKER_REMOVE_WARNING_SIGNED_IN_PROFILE 1473
#define IDS_SETTINGS_EMPTY_STRING 5205
#define IDS_SETTINGS_CONTINUE 5206
#define IDS_SETTINGS_MORE_ACTIONS 5207
#define IDS_RELAUNCH_CONFIRMATION_DIALOG_BODY 5208
#define IDS_SETTINGS_ABOUT_PAGE_BROWSER_VERSION 5209
#define IDS_SETTINGS_ABOUT_PAGE_RELAUNCH 3415
#define IDS_ABOUT_CHROME_AUTOUPDATE_ALL 5211
#define IDS_ABOUT_CHROME_AUTOUPDATE_ALL_IS_ON 5212
#define IDS_SETTINGS_ACCESSIBILITY 5213
#define IDS_SETTINGS_ACCESSIBILITY_WEB_STORE 5214
#define IDS_SETTINGS_MORE_FEATURES_LINK 5215
#define IDS_SETTINGS_MORE_FEATURES_LINK_DESCRIPTION 5216
#define IDS_SETTINGS_ACCESSIBLE_IMAGE_LABELS_TITLE 5217
#define IDS_SETTINGS_ACCESSIBLE_IMAGE_LABELS_SUBTITLE 5218
#define IDS_SETTINGS_CAPTIONS_ENABLE_LIVE_CAPTION_TITLE 5219
#define IDS_SETTINGS_CAPTIONS_ENABLE_LIVE_CAPTION_SUBTITLE_ENGLISH_ONLY 5220
#define IDS_SETTINGS_CAPTIONS_ENABLE_LIVE_CAPTION_SUBTITLE 5221
#define IDS_SETTINGS_CAPTIONS_LIVE_CAPTION_DOWNLOAD_PROGRESS 5222
#define IDS_SETTINGS_CAPTIONS_LIVE_CAPTION_DOWNLOAD_COMPLETE 5223
#define IDS_SETTINGS_CAPTIONS_LIVE_CAPTION_DOWNLOAD_ERROR 5224
#define IDS_SETTINGS_ENABLE_CARET_BROWSING_TITLE 5225
#define IDS_SETTINGS_ENABLE_CARET_BROWSING_SUBTITLE 5226
#define IDS_SETTINGS_ACCESSIBILITY_FOCUS_HIGHLIGHT_DESCRIPTION 3772
#define IDS_SETTINGS_APPEARANCE 5227
#define IDS_SETTINGS_CUSTOM_WEB_ADDRESS 5228
#define IDS_SETTINGS_ENTER_CUSTOM_WEB_ADDRESS 5229
#define IDS_SETTINGS_HOME_BUTTON_DISABLED 5230
#define IDS_SETTINGS_THEMES 5231
#define IDS_SETTINGS_RESET_TO_DEFAULT_THEME 5236
#define IDS_SETTINGS_CHROME_COLORS 5237
#define IDS_SETTINGS_SHOW_HOME_BUTTON 5238
#define IDS_SETTINGS_SHOW_BOOKMARKS_BAR 5239
#define IDS_SETTINGS_HOME_PAGE_NTP 5240
#define IDS_SETTINGS_CHANGE_HOME_PAGE 5241
#define IDS_SETTINGS_WEB_STORE 5242
#define IDS_SETTINGS_TABS_TO_LINKS_PREF 5243
#define IDS_SETTINGS_WARN_BEFORE_QUITTING_PREF 5244
#define IDS_SETTINGS_READER_MODE 5245
#define IDS_SETTINGS_READER_MODE_DESCRIPTION 5246
#define IDS_SETTINGS_ADVANCED 5247
#define IDS_SETTINGS_BASIC 5248
#define IDS_SETTINGS_MENU_BUTTON_LABEL 5249
#define IDS_SETTINGS_MENU_EXTENSIONS_LINK_TOOLTIP 5250
#define IDS_SETTINGS_SEARCH_PROMPT 5251
#define IDS_SETTINGS_SEARCH_NO_RESULTS_HELP 5252
#define IDS_SETTINGS_SETTINGS 5253
#define IDS_SETTINGS_ALT_PAGE_TITLE 5254
#define IDS_SETTINGS_SUBPAGE_BUTTON 5255
#define IDS_SETTINGS_RESTART 5256
#define IDS_SETTINGS_CONTROLLED_BY_EXTENSION 5257
#define IDS_SETTINGS_CLEAR 5258
#define IDS_SETTINGS_CUSTOM 5259
#define IDS_SETTINGS_DELETE 5260
#define IDS_SETTINGS_EDIT 5261
#define IDS_SETTINGS_END_TIME 5262
#define IDS_SETTINGS_NOT_VALID 5263
#define IDS_SETTINGS_NOT_VALID_WEB_ADDRESS 5264
#define IDS_SETTINGS_NOT_VALID_WEB_ADDRESS_FOR_CONTENT_TYPE 5265
#define IDS_SETTINGS_RETRY 5266
#define IDS_SETTINGS_SLIDER_MIN_MAX_ARIA_ROLE_DESCRIPTION 5267
#define IDS_SETTINGS_START_TIME 5268
#define IDS_SETTINGS_AUTOFILL 5269
#define IDS_SETTINGS_GOOGLE_PAYMENTS 5270
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_ADD_TITLE 5271
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_EDIT_TITLE 5272
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_COUNTRY 5273
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_PHONE 5274
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_EMAIL 5275
#define IDS_SETTINGS_AUTOFILL_ADDRESS_HONORIFIC_LABEL 5276
#define IDS_SETTINGS_AUTOFILL_CREDIT_CARD_TYPE_COLUMN_LABEL 5277
#define IDS_SETTINGS_AUTOFILL_DETAIL 5278
#define IDS_SETTINGS_AUTOFILL_MORE_ACTIONS_FOR_ADDRESS 5279
#define IDS_SETTINGS_AUTOFILL_MORE_ACTIONS_FOR_CREDIT_CARD 5280
#define IDS_SETTINGS_AUTOFILL_MORE_ACTIONS_CARD_DESCRIPTION 5281
#define IDS_AUTOFILL_ADD_VIRTUAL_CARD 5282
#define IDS_AUTOFILL_REMOVE_VIRTUAL_CARD 5283
#define IDS_AUTOFILL_EDIT_SERVER_CREDIT_CARD 5284
#define IDS_AUTOFILL_VIRTUAL_CARD_ENABLED_LABEL 5285
#define IDS_AUTOFILL_VIRTUAL_CARD_UNENROLL_DIALOG_TITLE 5286
#define IDS_AUTOFILL_VIRTUAL_CARD_UNENROLL_DIALOG_LABEL 5287
#define IDS_AUTOFILL_VIRTUAL_CARD_UNENROLL_DIALOG_CONFIRM_BUTTON_LABEL 5288
#define IDS_SETTINGS_ADDRESS_REMOVE 5289
#define IDS_SETTINGS_ADDRESS_REMOVE_CONFIRMATION_DESCRIPTION 5290
#define IDS_SETTINGS_ADDRESS_REMOVE_CONFIRMATION_TITLE 5291
#define IDS_SETTINGS_CREDIT_CARD_REMOVE 5292
#define IDS_SETTINGS_CREDIT_CARD_CLEAR 5293
#define IDS_SETTINGS_EDIT_CREDIT_CARD_TITLE 5294
#define IDS_SETTINGS_PAYMENTS_MANAGE_CREDIT_CARDS 5295
#define IDS_SETTINGS_PAYMENTS_SAVED_TO_THIS_DEVICE_ONLY 5296
#define IDS_SETTINGS_ADD_CREDIT_CARD_TITLE 5297
#define IDS_SETTINGS_MIGRATABLE_CARDS_LABEL 5298
#define IDS_SETTINGS_SINGLE_MIGRATABLE_CARD_INFO 5299
#define IDS_SETTINGS_MULTIPLE_MIGRATABLE_CARDS_INFO 5300
#define IDS_SETTINGS_REMOTE_CREDIT_CARD_LINK_LABEL 5301
#define IDS_SETTINGS_NAME_ON_CREDIT_CARD 5302
#define IDS_SETTINGS_CREDIT_CARD_NUMBER 5303
#define IDS_SETTINGS_CREDIT_CARD_EXPIRATION_DATE 5304
#define IDS_SETTINGS_CREDIT_CARD_EXPIRATION_MONTH 5305
#define IDS_SETTINGS_CREDIT_CARD_EXPIRATION_YEAR 5306
#define IDS_SETTINGS_CREDIT_CARD_EXPIRED 5307
#define IDS_SETTINGS_CREDIT_CARD_NICKNAME 5308
#define IDS_SETTINGS_CREDIT_CARD_NICKNAME_INVALID 5309
#define IDS_SETTINGS_UPI_ID_LABEL 5310
#define IDS_SETTINGS_UPI_ID_EXPIRATION_NEVER 5311
#define IDS_SETTINGS_PASSWORDS 5312
#define IDS_SETTINGS_PASSWORD_MANAGER 5313
#define IDS_SETTINGS_PASSWORD_MANAGER_DESCRIPTION 5314
#define IDS_SETTINGS_DEVICE_PASSWORDS 5315
#define IDS_SETTINGS_DEVICE_PASSWORDS_ON_DEVICE_ONLY_HEADING 5316
#define IDS_SETTINGS_DEVICE_PASSWORDS_ON_DEVICE_AND_ACCOUNT_HEADING 5317
#define IDS_SETTINGS_CHECK_PASSWORDS 5318
#define IDS_SETTINGS_CHECK_PASSWORDS_CANCELED 5319
#define IDS_SETTINGS_CHECKED_PASSWORDS 5320
#define IDS_SETTINGS_CHECK_PASSWORDS_DESCRIPTION 5321
#define IDS_SETTINGS_COMPROMISED_PASSWORDS_COUNT 5322
#define IDS_SETTINGS_COMPROMISED_PASSWORDS_COUNT_SHORT 5323
#define IDS_SETTINGS_WEAK_PASSWORDS_COUNT 5324
#define IDS_SETTINGS_WEAK_PASSWORDS_COUNT_SHORT 5325
#define IDS_SETTINGS_INSECURE_PASSWORDS_COUNT 5326
#define IDS_SETTINGS_CHECK_PASSWORDS_AGAIN 5327
#define IDS_SETTINGS_CHECK_PASSWORDS_AGAIN_AFTER_ERROR 5328
#define IDS_SETTINGS_CHECK_PASSWORDS_PROGRESS 5329
#define IDS_SETTINGS_CHECK_PASSWORDS_STOP 5330
#define IDS_SETTINGS_PASSWORDS_JUST_NOW 5331
#define IDS_SETTINGS_COMPROMISED_PASSWORDS 5332
#define IDS_SETTINGS_COMPROMISED_PASSWORDS_ADVICE 5333
#define IDS_SETTINGS_MUTED_PASSWORDS 5334
#define IDS_SETTINGS_WEAK_PASSWORDS 5335
#define IDS_SETTINGS_WEAK_PASSWORDS_DESCRIPTION 5336
#define IDS_SETTINGS_CHANGE_PASSWORD_BUTTON 5337
#define IDS_SETTINGS_CHANGE_PASSWORD_IN_APP_LABEL 5338
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REASON_LEAKED 5339
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REASON_PHISHED 5340
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REASON_PHISHED_AND_LEAKED 5341
#define IDS_SETTINGS_COMPROMISED_PASSWORD_SHOW 5342
#define IDS_SETTINGS_COMPROMISED_PASSWORD_HIDE 5343
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REMOVE 5344
#define IDS_SETTINGS_COMPROMISED_PASSWORD_MUTE 5345
#define IDS_SETTINGS_COMPROMISED_PASSWORD_UNMUTE 5346
#define IDS_SETTINGS_REMOVE_COMPROMISED_PASSWORD_CONFIRMATION_TITLE 5347
#define IDS_SETTINGS_REMOVE_COMPROMISED_PASSWORD_CONFIRMATION_DESCRIPTION 5348
#define IDS_SETTINGS_COMPROMISED_EDIT_PASSWORD_SITE 5349
#define IDS_SETTINGS_COMPROMISED_EDIT_PASSWORD_APP 5350
#define IDS_SETTINGS_COMPROMISED_ALREADY_CHANGED_PASSWORD 5351
#define IDS_SETTINGS_COMPROMISED_EDIT_DISCLAIMER_TITLE 5352
#define IDS_SETTINGS_PASSWORDS_SAVE_PASSWORDS_TOGGLE_LABEL 5353
#define IDS_SETTINGS_PASSWORDS_AUTOSIGNIN_CHECKBOX_LABEL 5354
#define IDS_SETTINGS_PASSWORDS_AUTOSIGNIN_CHECKBOX_DESC 5355
#define IDS_SETTINGS_PASSWORDS_LEAK_DETECTION_LABEL 5356
#define IDS_SETTINGS_PASSWORDS_LEAK_DETECTION_SIGNED_OUT_ENABLED_DESC 5357
#define IDS_SETTINGS_PASSWORDS_SAVED_HEADING 5358
#define IDS_SETTINGS_PASSWORDS_EXCEPTIONS_HEADING 5359
#define IDS_SETTINGS_PASSWORDS_DELETE_EXCEPTION 5360
#define IDS_SETTINGS_PASSWORD_REMOVE 5361
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT 5362
#define IDS_SETTINGS_PASSWORD_SEARCH 5363
#define IDS_SETTINGS_PASSWORDS_VIEW_DETAILS_TITLE 5364
#define IDS_SETTINGS_PASSWORD_DETAILS 5365
#define IDS_SETTINGS_PASSWORD_EDIT_TITLE 5366
#define IDS_SETTINGS_PASSWORD_EDIT 5367
#define IDS_SETTINGS_PASSWORD_EDIT_FOOTNOTE 5368
#define IDS_SETTINGS_PASSWORD_USERNAME_ALREADY_USED 5369
#define IDS_SETTINGS_PASSWORD_VIEW_EXISTING_PASSWORD 5370
#define IDS_SETTINGS_PASSWORD_VIEW_EXISTING_PASSWORD_ARIA_DESCRIPTION 5371
#define IDS_SETTINGS_PASSWORD_MISSING_TLD 5372
#define IDS_SETTINGS_PASSWORD_ADD_TITLE 5373
#define IDS_SETTINGS_PASSWORD_ADD_FOOTNOTE 5374
#define IDS_SETTINGS_PASSWORD_ADD_STORE_OPTION_ACCOUNT 5375
#define IDS_SETTINGS_PASSWORD_COPY 5376
#define IDS_SETTINGS_PASSWORD_SEND 5377
#define IDS_SETTINGS_USERNAME_COPY 5378
#define IDS_SETTINGS_PASSWORDS_WEBSITE 5379
#define IDS_SETTINGS_PASSWORDS_ANDROID_APP 5380
#define IDS_SETTINGS_PASSWORDS_USERNAME 5381
#define IDS_SETTINGS_PASSWORDS_PASSWORD 5382
#define IDS_SETTINGS_PASSWORDS_NOTE 5383
#define IDS_SETTINGS_PASSWORDS_NOTE_CHARACTER_COUNT 5384
#define IDS_SETTINGS_PASSWORDS_NOTE_CHARACTER_COUNT_WARNING 5385
#define IDS_SETTINGS_ADDRESS_NONE 5386
#define IDS_SETTINGS_PAYMENT_METHODS_NONE 5387
#define IDS_SETTINGS_PASSWORDS_NONE 5388
#define IDS_SETTINGS_PASSWORDS_EXCEPTIONS_NONE 5389
#define IDS_SETTINGS_PASSWORD_UNDO 5390
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD 5391
#define IDS_SETTINGS_PASSWORD_STORED_ON_DEVICE 5392
#define IDS_SETTINGS_PASSWORD_STORED_IN_ACCOUNT 5393
#define IDS_SETTINGS_PASSWORD_STORED_IN_ACCOUNT_AND_ON_DEVICE 5394
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD_FROM_ACCOUNT 5395
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD_FROM_DEVICE 5396
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD_FROM_ACCOUNT_AND_DEVICE 5397
#define IDS_SETTINGS_PASSWORD_COPIED_TO_CLIPBOARD 5398
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT 5399
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_COUNT 5400
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_DIALOG_BODY_TEXT 5401
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_DIALOG_TITLE 5402
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_SNACKBAR 5403
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_TITLE 5404
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_BODY 5405
#define IDS_SETTINGS_PASSWORD_MOVE_MULTIPLE_PASSWORDS_TO_ACCOUNT_DIALOG_MOVE_BUTTON_TEXT 5406
#define IDS_SETTINGS_PASSWORD_MOVE_MULTIPLE_PASSWORDS_TO_ACCOUNT_DIALOG_CANCEL_BUTTON_TEXT 5407
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_MOVE_BUTTON_TEXT 5408
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_CANCEL_BUTTON_TEXT 5409
#define IDS_SETTINGS_PASSWORD_OPEN_MOVE_MULTIPLE_PASSWORDS_TO_ACCOUNT_DIALOG_BUTTON_TEXT 5410
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_TITLE 5411
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_BODY 5412
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_REMOVE_BUTTON_TEXT 5413
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_CANCEL_BUTTON_TEXT 5414
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_FROM_ACCOUNT_CHECKBOX_LABEL 5415
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_FROM_DEVICE_CHECKBOX_LABEL 5416
#define IDS_SETTINGS_DEVICE_PASSWORDS_LINK_LABEL 5417
#define IDS_SETTINGS_PASSWORDS_MANAGE_PASSWORDS 5418
#define IDS_SETTINGS_PASSWORDS_MANAGE_PASSWORDS_PLAINTEXT 5419
#define IDS_SETTINGS_PASSWORDS_OPT_IN_ACCOUNT_STORAGE_BODY 5420
#define IDS_SETTINGS_PASSWORDS_OPT_IN_ACCOUNT_STORAGE_LABEL 5421
#define IDS_SETTINGS_PASSWORDS_OPT_OUT_ACCOUNT_STORAGE_BODY 5422
#define IDS_SETTINGS_PASSWORDS_OPT_OUT_ACCOUNT_STORAGE_LABEL 5423
#define IDS_SETTINGS_PASSWORDS_EXPORT_MENU_ITEM 5424
#define IDS_SETTINGS_PASSWORDS_EXPORT_TITLE 5425
#define IDS_SETTINGS_PASSWORDS_EXPORT_DESCRIPTION 5426
#define IDS_SETTINGS_PASSWORDS_EXPORT 5427
#define IDS_SETTINGS_PASSWORDS_EXPORT_TRY_AGAIN 5428
#define IDS_SETTINGS_PASSWORDS_EXPORTING_TITLE 5429
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TITLE 5430
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TIPS 5431
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TIP_ENOUGH_SPACE 5432
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TIP_ANOTHER_FOLDER 5433
#define IDS_SETTINGS_PASSWORD_ROW_MORE_ACTIONS 5434
#define IDS_SETTINGS_PASSWORD_ROW_FEDERATED_MORE_ACTIONS 5435
#define IDS_SETTINGS_TRUSTED_VAULT_BANNER_LABEL 5436
#define IDS_SETTINGS_TRUSTED_VAULT_BANNER_SUB_LABEL_OFFER_OPT_IN 5437
#define IDS_SETTINGS_TRUSTED_VAULT_BANNER_SUB_LABEL_OPTED_IN 5438
#define IDS_SETTINGS_PASSWORD_SHOW_PASSWORD_A11Y 5439
#define IDS_SETTINGS_PASSWORD_HIDE_PASSWORD_A11Y 5440
#define IDS_SETTINGS_DEFAULT_BROWSER 5441
#define IDS_SETTINGS_DEFAULT_BROWSER_MAKE_DEFAULT_BUTTON 5442
#define IDS_SETTINGS_CLEAR_PERIOD_TITLE 5515
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC 5516
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC_ERROR 5517
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC_PASSPHRASE_ERROR 5518
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC_PAUSED 5519
#define IDS_SETTINGS_CLEAR_BROWSING_HISTORY 5520
#define IDS_SETTINGS_CLEAR_COOKIES_AND_SITE_DATA_SUMMARY_BASIC 5521
#define IDS_SETTINGS_CLEAR_COOKIES_AND_SITE_DATA_SUMMARY_BASIC_WITH_EXCEPTION 5522
#define IDS_SETTINGS_CLEAR_COOKIES_AND_SITE_DATA_SUMMARY_BASIC_MAIN_PROFILE 5523
#define IDS_SETTINGS_CLEAR_BROWSING_HISTORY_SUMMARY 5524
#define IDS_SETTINGS_CLEAR_BROWSING_HISTORY_SUMMARY_SIGNED_IN_NO_LINK 5525
#define IDS_SETTINGS_CLEAR_GOOGLE_SEARCH_HISTORY_GOOGLE_DSE 5526
#define IDS_SETTINGS_CLEAR_GOOGLE_SEARCH_HISTORY_NON_GOOGLE_DSE 5527
#define IDS_SETTINGS_CLEAR_NON_GOOGLE_SEARCH_HISTORY_PREPOPULATED_DSE 5528
#define IDS_SETTINGS_CLEAR_NON_GOOGLE_SEARCH_HISTORY_NON_PREPOPULATED_DSE 5529
#define IDS_SETTINGS_CLEAR_DOWNLOAD_HISTORY 5530
#define IDS_SETTINGS_CLEAR_CACHE 5531
#define IDS_SETTINGS_CLEAR_COOKIES 5532
#define IDS_SETTINGS_CLEAR_PASSWORDS 5533
#define IDS_SETTINGS_CLEAR_FORM_DATA 5534
#define IDS_SETTINGS_CLEAR_HOSTED_APP_DATA 5535
#define IDS_SETTINGS_CLEAR_PERIOD_HOUR 5536
#define IDS_SETTINGS_CLEAR_PERIOD_24_HOURS 5537
#define IDS_SETTINGS_CLEAR_PERIOD_7_DAYS 5538
#define IDS_SETTINGS_CLEAR_PERIOD_FOUR_WEEKS 5539
#define IDS_SETTINGS_CLEAR_PERIOD_EVERYTHING 5540
#define IDS_SETTINGS_CLEAR_INSTALLED_APPS_DATA_TITLE 5541
#define IDS_SETTINGS_CLEAR_INSTALLED_APPS_DATA_CONFIRM 5542
#define IDS_SETTINGS_NOTIFICATION_WARNING 5543
#define IDS_SETTINGS_DOWNLOADS 5544
#define IDS_SETTINGS_DOWNLOAD_LOCATION 5545
#define IDS_SETTINGS_CHANGE_DOWNLOAD_LOCATION 5546
#define IDS_SETTINGS_PROMPT_FOR_DOWNLOAD 5547
#define IDS_SETTINGS_OPEN_FILE_TYPES_AUTOMATICALLY 5548
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_TITLE 5549
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_LEARN_MORE 5550
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_UNLINKED_MESSAGE 5551
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_SIGN_IN_BUTTON 5552
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_LINKED_MESSAGE 5553
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_SIGN_OUT_BUTTON 5554
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_ACCOUNT_TITLE 5555
#define IDS_SETTINGS_DOWNLOAD_WEB_DRIVE_LOCATION 5556
#define IDS_SETTINGS_DOWNLOAD_LOCAL_LOCATION 5557
#define IDS_SETTINGS_ON_STARTUP 5558
#define IDS_SETTINGS_ON_STARTUP_OPEN_NEW_TAB 5559
#define IDS_SETTINGS_ON_STARTUP_CONTINUE 5560
#define IDS_SETTINGS_ON_STARTUP_OPEN_SPECIFIC 5561
#define IDS_SETTINGS_ON_STARTUP_CONTINUE_AND_OPEN_SPECIFIC 5562
#define IDS_SETTINGS_ON_STARTUP_USE_CURRENT 5563
#define IDS_SETTINGS_ON_STARTUP_ADD_NEW_PAGE 5564
#define IDS_SETTINGS_ON_STARTUP_EDIT_PAGE 5565
#define IDS_SETTINGS_ON_STARTUP_SITE_URL 5566
#define IDS_SETTINGS_ON_STARTUP_REMOVE 5567
#define IDS_SETTINGS_ON_STARTUP_PAGE_TOOLTIP 5568
#define IDS_SETTINGS_INVALID_URL 5569
#define IDS_SETTINGS_URL_TOOL_LONG 5570
#define IDS_SETTINGS_LANGUAGES_PAGE_TITLE 5571
#define IDS_SETTINGS_LANGUAGE_SEARCH 5572
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_MOVE_TO_TOP 5573
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_MOVE_UP 5574
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_MOVE_DOWN 5575
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_REMOVE 5576
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_ADD 5577
#define IDS_SETTINGS_LANGUAGES_MANAGE_LANGUAGES_TITLE 5578
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_TITLE 5579
#define IDS_SETTINGS_LANGUAGES_EXPAND_ACCESSIBILITY_LABEL 5580
#define IDS_SETTINGS_LANGUAGES_BROWSER_LANGUAGES_LIST_ORDERING_INSTRUCTIONS 5581
#define IDS_SETTINGS_LANGUAGES_OFFER_TO_TRANSLATE_IN_THIS_LANGUAGE 5582
#define IDS_SETTINGS_LANGUAGES_OFFER_TO_ENABLE_TRANSLATE 5583
#define IDS_SETTINGS_LANGUAGES_TRANSLATE_TARGET 5584
#define IDS_SETTINGS_LANGUAGES_MANAGED_DIALOG_TITLE 5585
#define IDS_SETTINGS_LANGUAGES_MANAGED_DIALOG_BODY 5586
#define IDS_SETTINGS_LANGUAGES_NO_LANGUAGES_ADDED 5587
#define IDS_SETTINGS_LANGUAGES_AUTOMATIC_TRANSLATE 5588
#define IDS_SETTINGS_LANGUAGES_NEVER_LANGUAGES 5589
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_TITLE 5590
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_BASIC_LABEL 5591
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_ENHANCED_LABEL 5592
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_ENHANCED_DESCRIPTION 5593
#define IDS_SETTINGS_PRIVACY 5609
#define IDS_SETTINGS_PRIVACY_V2 5610
#define IDS_SETTINGS_PRIVACY_MORE 5611
#define IDS_SETTINGS_PRIVACY_SANDBOX_TITLE 5613
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_HEADING 5614
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_EXPLANATION1_PHASE2 5615
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_EXPLANATION2_PHASE2 5616
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_TITLE 5617
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_EXPLANATION1_PHASE2 5618
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_EXPLANATION2_PHASE2 5619
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_EXPLANATION3_PHASE2 5620
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_DETAILS 5621
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_ENABLED 5622
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_DISABLED 5623
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_HEADING 5624
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_EXPLANATION 5625
#define IDS_SETTINGS_PRIVACY_SANDBOX_FLOC_TRIAL_ACTIVE 5626
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_STATUS 5627
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_COHORT 5628
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_COHORT_NEXT_UPDATE 5629
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_RESET_COHORT 5630
#define IDS_SETTINGS_PRIVACY_SANDBOX_COOKIES_DIALOG 5631
#define IDS_SETTINGS_PRIVACY_SANDBOX_COOKIES_DIALOG_MORE 5632
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_TITLE 5633
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_SUMMARY 5634
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_SUMMARY_LEARN_MORE 5635
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_TITLE 5636
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_SUMMARY 5637
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_TITLE 5638
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_SUMMARY 5639
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_TITLE 5640
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_SUMMARY 5641
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TITLE 5642
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_TITLE 5643
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_DATA_TYPES 5644
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_DATA_USAGE 5645
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_DATA_MANAGEMENT 5646
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_DATA_MANAGEMENT 5647
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TITLE 5648
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_REMOVED_DIALOG_TITLE 5649
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_REMOVED_DIALOG_DESCRIPTION 5650
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_EMPTY 5651
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_TOPICS_LABEL 5652
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_TOPICS_EMPTY 5653
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_TITLE 5654
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_EMPTY 5655
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_FLEDGE_LABEL 5656
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_FLEDGE_EMPTY 5657
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_LEARN_MORE_2 5658
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_LEARN_MORE_3 5659
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_TITLE 5660
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_CONTROL_MEASUREMENT 5661
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_TITLE 5662
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_1 5663
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_1_TRIALS_OFF 5664
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_2 5665
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_3 5666
#define IDS_SETTINGS_PRIVACY_GUIDE_LABEL 5667
#define IDS_SETTINGS_PRIVACY_GUIDE_SUBLABEL 5668
#define IDS_SETTINGS_PRIVACY_GUIDE_PROMO_HEADER 5669
#define IDS_SETTINGS_PRIVACY_GUIDE_PROMO_START_BUTTON 5670
#define IDS_SETTINGS_PRIVACY_GUIDE_BACK_TO_SETTINGS_ARIA_LABEL 5671
#define IDS_SETTINGS_PRIVACY_GUIDE_BACK_TO_SETTINGS_ARIA_ROLE_DESC 5672
#define IDS_SETTINGS_PRIVACY_GUIDE_BACK_BUTTON 5673
#define IDS_SETTINGS_PRIVACY_GUIDE_STEPS 5674
#define IDS_SETTINGS_PRIVACY_GUIDE_NEXT_BUTTON 5675
#define IDS_SETTINGS_PRIVACY_GUIDE_FEATURE_DESCRIPTION_HEADER 5676
#define IDS_SETTINGS_PRIVACY_GUIDE_THINGS_TO_CONSIDER 5677
#define IDS_SETTINGS_PRIVACY_GUIDE_WELCOME_CARD_HEADER 5678
#define IDS_SETTINGS_PRIVACY_GUIDE_WELCOME_CARD_SUB_HEADER 5679
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_HEADER 5680
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_SUB_HEADER 5681
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_LEAVE_BUTTON 5682
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_PRIVACY_SANDBOX_LABEL 5683
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_WAA_LABEL 5684
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_CARD_HEADER 5685
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_FEATURE_DESCRIPTION1 5686
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_FEATURE_DESCRIPTION2 5687
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_PRIVACY_DESCRIPTION1 5688
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_CARD_HEADER 5689
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_CARD_HEADER 5690
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_SETTING_LABEL 5691
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_FEATURE_DESCRIPTION1 5692
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_FEATURE_DESCRIPTION2 5693
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_PRIVACY_DESCRIPTION1 5694
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_HEADER 5695
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_SUBHEADER 5696
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_FEATURE_DESCRIPTION1 5697
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_FEATURE_DESCRIPTION2 5698
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_PRIVACY_DESCRIPTION1 5699
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_PRIVACY_DESCRIPTION2 5700
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_SUBHEADER 5701
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_FEATURE_DESCRIPTION1 5702
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_FEATURE_DESCRIPTION2 5703
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_PRIVACY_DESCRIPTION1 5704
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_HEADER 5705
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_ENHANCED_PROTECTION_PRIVACY_DESCRIPTION1 5706
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_ENHANCED_PROTECTION_PRIVACY_DESCRIPTION2 5707
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_ENHANCED_PROTECTION_PRIVACY_DESCRIPTION3 5708
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_STANDARD_PROTECTION_FEATURE_DESCRIPTION1 5709
#define IDS_SETTINGS_SAFETY_CHECK_SECTION_TITLE 5710
#define IDS_SETTINGS_SAFETY_CHECK_RUNNING 5711
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER 5712
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_MINS 5713
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_HOURS 5714
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_TIME 5715
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_TODAY 5716
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_YESTERDAY 5717
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_DAYS 5718
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_DATE 5719
#define IDS_SETTINGS_SAFETY_CHECK_ARIA_LIVE_RUNNING 5720
#define IDS_SETTINGS_SAFETY_CHECK_ARIA_LIVE_AFTER 5721
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_BUTTON 5722
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_BUTTON_ARIA_LABEL 5723
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_RUN_AGAIN_BUTTON_ARIA_LABEL 5724
#define IDS_SETTINGS_SAFETY_CHECK_ICON_RUNNING_ARIA_LABEL 5725
#define IDS_SETTINGS_SAFETY_CHECK_ICON_SAFE_ARIA_LABEL 5726
#define IDS_SETTINGS_SAFETY_CHECK_ICON_INFO_ARIA_LABEL 5727
#define IDS_SETTINGS_SAFETY_CHECK_ICON_WARNING_ARIA_LABEL 5728
#define IDS_SETTINGS_SAFETY_CHECK_REVIEW 5729
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_PRIMARY_LABEL 5730
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_DISABLED_BY_ADMIN 5731
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_PRIMARY_LABEL 5732
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_FEATURE_UNAVAILABLE 5733
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_BUTTON_ARIA_LABEL 5734
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED 5735
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED_STANDARD 5736
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED_STANDARD_AVAILABLE_ENHANCED 5737
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED_ENHANCED 5738
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_DISABLED_BY_ADMIN 5739
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_DISABLED_BY_EXTENSION 5740
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_BUTTON 5741
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_BUTTON_ARIA_LABEL 5742
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_PRIMARY_LABEL 5743
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_SAFE 5744
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BLOCKLISTED_OFF 5745
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BLOCKLISTED_ON_USER 5746
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BLOCKLISTED_ON_ADMIN 5747
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BUTTON_ARIA_LABEL 5748
#define IDS_SETTINGS_NETWORK_PREDICTION_ENABLED_LABEL 5762
#define IDS_SETTINGS_NETWORK_PREDICTION_ENABLED_DESC 5763
#define IDS_SETTINGS_NETWORK_PREDICTION_ENABLED_DESC_COOKIES_PAGE 5764
#define IDS_SETTINGS_SAFEBROWSING_ENABLEPROTECTION 5765
#define IDS_SETTINGS_SAFEBROWSING_ENABLEPROTECTION_DESC 5766
#define IDS_SETTINGS_SAFEBROWSING_ENABLE_REPORTING_DESC 5767
#define IDS_SETTINGS_SAFEBROWSING_SECTION_LABEL 5768
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED 5769
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_DESC 5770
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_EXPAND_ACCESSIBILITY_LABEL 5771
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_ONE 5772
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_THREE 5773
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_FOUR 5774
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_FIVE 5775
#define IDS_SETTINGS_SAFEBROWSING_STANDARD 5776
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_DESC 5777
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_EXPAND_ACCESSIBILITY_LABEL 5778
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_BULLET_ONE 5779
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_HELP_IMPROVE 5780
#define IDS_SETTINGS_SAFEBROWSING_NONE 5781
#define IDS_SETTINGS_SAFEBROWSING_NONE_DESC 5782
#define IDS_SETTINGS_SAFEBROWSING_DISABLE_DIALOG_TITLE 5783
#define IDS_SETTINGS_SAFEBROWSING_DISABLE_DIALOG_DESC 5784
#define IDS_SETTINGS_SAFEBROWSING_DISABLE_DIALOG_CONFIRM 5785
#define IDS_SETTINGS_ENABLE_DO_NOT_TRACK 5786
#define IDS_SETTINGS_ENABLE_DO_NOT_TRACK_DIALOG_TITLE 5787
#define IDS_SETTINGS_ENABLE_DO_NOT_TRACK_DIALOG_TEXT 5788
#define IDS_SETTINGS_PERMISSIONS 5789
#define IDS_SETTINGS_PERMISSIONS_DESCRIPTION 5790
#define IDS_SETTINGS_SECURITY 5791
#define IDS_SETTINGS_SECURITY_DESCRIPTION 5792
#define IDS_SETTINGS_ADVANCED_PROTECTION_PROGRAM 5793
#define IDS_SETTINGS_ADVANCED_PROTECTION_PROGRAM_DESC 5794
#define IDS_SETTINGS_HTTPS_ONLY_MODE 5795
#define IDS_SETTINGS_HTTPS_ONLY_MODE_DESCRIPTION 5796
#define IDS_SETTINGS_MANAGE_CERTIFICATES 5797
#define IDS_SETTINGS_MANAGE_CERTIFICATES_DESCRIPTION 5798
#define IDS_SETTINGS_SECURE_DNS 5799
#define IDS_SETTINGS_SECURE_DNS_DESCRIPTION 5800
#define IDS_SETTINGS_AUTOMATIC_MODE_DESCRIPTION 5801
#define IDS_SETTINGS_AUTOMATIC_MODE_DESCRIPTION_SECONDARY 5802
#define IDS_SETTINGS_SECURE_MODE_DESCRIPTION_ACCESSIBILITY_LABEL 5803
#define IDS_SETTINGS_SECURE_DNS_DROPDOWN_ACCESSIBILITY_LABEL 5804
#define IDS_SETTINGS_SECURE_DROPDOWN_MODE_DESCRIPTION 5805
#define IDS_SETTINGS_SECURE_DROPDOWN_MODE_PRIVACY_POLICY 5806
#define IDS_SETTINGS_SECURE_DNS_DISABLED_FOR_MANAGED_ENVIRONMENT 5807
#define IDS_SETTINGS_SECURE_DNS_DISABLED_FOR_PARENTAL_CONTROL 5808
#define IDS_SETTINGS_SECURE_DNS_CUSTOM_PLACEHOLDER 5809
#define IDS_SETTINGS_SECURE_DNS_CUSTOM_FORMAT_ERROR 5810
#define IDS_SETTINGS_SECURE_DNS_CUSTOM_CONNECTION_ERROR 5811
#define IDS_SETTINGS_CONTENT_SETTINGS 5812
#define IDS_SETTINGS_SITE_SETTINGS 5813
#define IDS_SETTINGS_SITE_SETTINGS_DESCRIPTION 5814
#define IDS_SETTINGS_CLEAR_DATA 5815
#define IDS_SETTINGS_CLEARING_DATA 5816
#define IDS_SETTINGS_CLEARED_DATA 5817
#define IDS_SETTINGS_CLEAR_BROWSING_DATA 5818
#define IDS_SETTINGS_CLEAR_DATA_DESCRIPTION 5819
#define IDS_SETTINGS_TITLE_AND_COUNT 5820
#define IDS_SETTINGS_SYNC_AND_GOOGLE_SERVICES_PRIVACY_DESC_UNIFIED_CONSENT 5821
#define IDS_SETTINGS_RECENT_PERMISSIONS_NO_CHANGES 5822
#define IDS_SETTINGS_RECENT_PERMISSIONS_ALLOWED_ONE_ITEM 5823
#define IDS_SETTINGS_RECENT_PERMISSIONS_ALLOWED_TWO_ITEMS 5824
#define IDS_SETTINGS_RECENT_PERMISSIONS_ALLOWED_MORE_THAN_TWO_ITEMS 5825
#define IDS_SETTINGS_RECENT_PERMISSIONS_AUTOMATICALLY_BLOCKED_ONE_ITEM 5826
#define IDS_SETTINGS_RECENT_PERMISSIONS_AUTOMATICALLY_BLOCKED_TWO_ITEMS 5827
#define IDS_SETTINGS_RECENT_PERMISSIONS_AUTOMATICALLY_BLOCKED_MORE_THAN_TWO_ITEMS 5828
#define IDS_SETTINGS_RECENT_PERMISSIONS_BLOCKED_ONE_ITEM 5829
#define IDS_SETTINGS_RECENT_PERMISSIONS_BLOCKED_TWO_ITEMS 5830
#define IDS_SETTINGS_RECENT_PERMISSIONS_BLOCKED_MORE_THAN_TWO_ITEMS 5831
#define IDS_SETTINGS_RESET_PROMPT_TITLE 5832
#define IDS_SETTINGS_RESET 5833
#define IDS_SETTINGS_RESET_SETTINGS_TRIGGER 5834
#define IDS_SETTINGS_RESET_AUTOMATED_DIALOG_TITLE 5835
#define IDS_SETTINGS_RESET_BANNER_TEXT 5836
#define IDS_SETTINGS_RESET_BANNER_RESET_BUTTON_TEXT 5837
#define IDS_SETTINGS_SEARCH 5840
#define IDS_SETTINGS_SEARCH_EXPLANATION 5841
#define IDS_SETTINGS_SEARCH_MANAGE_SEARCH_ENGINES 5842
#define IDS_SETTINGS_SEARCH_MANAGE_SEARCH_ENGINES_AND_SITE_SEARCH 5843
#define IDS_SETTINGS_SEARCH_MANAGE_SEARCH_ENGINES_EXPLANATION 5844
#define IDS_SETTINGS_SEARCH_ENGINES 5845
#define IDS_SETTINGS_SEARCH_ENGINES_PAGE_EXPLANATION 5846
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH 5847
#define IDS_SETTINGS_SEARCH_ENGINES_ADD_SEARCH_ENGINE 5848
#define IDS_SETTINGS_SEARCH_ENGINES_EDIT_SEARCH_ENGINE 5849
#define IDS_SETTINGS_SEARCH_ENGINES_DELETE_CONFIRMATION_TITLE 5850
#define IDS_SETTINGS_SEARCH_ENGINES_DELETE_CONFIRMATION_DESCRIPTION 5851
#define IDS_SETTINGS_SEARCH_ENGINES_DEFAULT_ENGINES 5852
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH_ENGINES 5853
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH_ENGINES_EXPLANATION 5854
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_SEARCH 5855
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_SEARCH_EXPLANATION 5856
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_SEARCH_EXPLANATION_STARTER_PACK 5857
#define IDS_SETTINGS_SEARCH_ENGINES_NO_SITES_ADDED 5858
#define IDS_SETTINGS_SEARCH_ENGINES_INACTIVE_SHORTCUTS 5859
#define IDS_SETTINGS_SEARCH_ENGINES_NO_INACTIVE_SHORTCUTS 5860
#define IDS_SETTINGS_SEARCH_ENGINES_OTHER_ENGINES 5861
#define IDS_SETTINGS_SEARCH_ENGINES_NO_OTHER_ENGINES 5862
#define IDS_SETTINGS_SEARCH_ENGINES_EXTENSION_ENGINES 5863
#define IDS_SETTINGS_SEARCH_ENGINES_EXTENSION_ENGINES_EXPLANATION 5864
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH_ENGINE 5865
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_OR_PAGE 5866
#define IDS_SETTINGS_SEARCH_ENGINES_INACTIVE_SITE 5867
#define IDS_SETTINGS_SEARCH_ENGINES_KEYWORD 5868
#define IDS_SETTINGS_SEARCH_ENGINES_SHORTCUT 5869
#define IDS_SETTINGS_SEARCH_ENGINES_QUERY_URL 5870
#define IDS_SETTINGS_SEARCH_ENGINES_QUERY_URL_EXPLANATION 5871
#define IDS_SETTINGS_SEARCH_ENGINES_MAKE_DEFAULT 5872
#define IDS_SETTINGS_SEARCH_ENGINES_ACTIVATE 5873
#define IDS_SETTINGS_SEARCH_ENGINES_DEACTIVATE 5874
#define IDS_SETTINGS_SEARCH_ENGINES_REMOVE_FROM_LIST 5875
#define IDS_SETTINGS_SEARCH_ENGINES_MANAGE_EXTENSION 5876
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_TITLE 5877
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_DESCRIPTION 5878
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_DESCRIPTION_STARTER_PACK 5879
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_SPACE_OR_TAB 5880
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_TAB 5881
#define IDS_SETTINGS_SEARCH_ENGINES_ADDITIONAL_SITES 5882
#define IDS_SETTINGS_SEARCH_ENGINES_ADDITIONAL_INACTIVE_SITES 5883
#define IDS_SETTINGS_SEARCH_ENGINES_ADDITIONAL_EXTENSIONS 5884
#define IDS_SETTINGS_EXCEPTIONS_EMBEDDED_ON_HOST 5885
#define IDS_SETTINGS_EXCEPTIONS_EMBEDDED_ON_ANY_HOST 5886
#define IDS_SETTINGS_SITE_SETTINGS_CATEGORY 5887
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES 5888
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_DESCRIPTION 5889
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SEARCH 5890
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT 5891
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT_METHOD_MOST_VISITED 5892
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT_METHOD_STORAGE 5893
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT_METHOD_NAME 5894
#define IDS_SETTINGS_SITE_SETTINGS_SITE_ENTRY_PARTITIONED_LABEL 5895
#define IDS_SETTINGS_SITE_SETTINGS_SITE_REPRESENTATION_SEPARATOR 5896
#define IDS_SETTINGS_SITE_SETTINGS_DEFAULT_BEHAVIOR 5897
#define IDS_SETTINGS_SITE_SETTINGS_DEFAULT_BEHAVIOR_DESCRIPTION 5898
#define IDS_SETTINGS_SITE_SETTINGS_CUSTOMIZED_BEHAVIORS 5899
#define IDS_SETTINGS_SITE_SETTINGS_CUSTOMIZED_BEHAVIORS_DESCRIPTION 5900
#define IDS_SETTINGS_SITE_SETTINGS_CUSTOMIZED_BEHAVIORS_DESCRIPTION_SHORT 5901
#define IDS_SETTINGS_SITE_SETTINGS_ADS_DESCRIPTION 5902
#define IDS_SETTINGS_SITE_SETTINGS_ADS_ALLOWED 5903
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCKED 5904
#define IDS_SETTINGS_SITE_SETTINGS_ADS_ALLOWED_EXCEPTIONS 5905
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCKED_EXCEPTIONS 5906
#define IDS_SETTINGS_SITE_SETTINGS_AR_DESCRIPTION 5907
#define IDS_SETTINGS_SITE_SETTINGS_AR_ALLOWED 5908
#define IDS_SETTINGS_SITE_SETTINGS_AR_BLOCKED 5909
#define IDS_SETTINGS_SITE_SETTINGS_AR_ALLOWED_EXCEPTIONS 5910
#define IDS_SETTINGS_SITE_SETTINGS_AR_BLOCKED_EXCEPTIONS 5911
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_DESCRIPTION 5912
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_ALLOWED 5913
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_BLOCKED 5914
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_ALLOWED_EXCEPTIONS 5915
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_BLOCKED_EXCEPTIONS 5916
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_DESCRIPTION 5917
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_ALLOWED 5918
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCKED 5919
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCKED_SUB_LABEL 5920
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_ALLOWED_EXCEPTIONS 5921
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCKED_EXCEPTIONS 5922
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_DESCRIPTION 5923
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_ALLOWED 5924
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_BLOCKED 5925
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_DESCRIPTION 5926
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_ALLOWED 5927
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_BLOCKED 5928
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_BLOCKED_SUB_LABEL 5929
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_ALLOWED_EXCEPTIONS 5930
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_BLOCKED_EXCEPTIONS 5931
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_DESCRIPTION 5932
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ALLOWED 5933
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_BLOCKED 5934
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ALLOWED_EXCEPTIONS 5935
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_BLOCKED_EXCEPTIONS 5936
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_DESCRIPTION 5937
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_ALLOWED 5938
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_BLOCKED 5939
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_ALLOWED_EXCEPTIONS 5940
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_BLOCKED_EXCEPTIONS 5941
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_DESCRIPTION 5942
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_ALLOWED 5943
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_BLOCKED 5944
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_ALLOWED_EXCEPTIONS 5945
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_BLOCKED_EXCEPTIONS 5946
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_DESCRIPTION 5947
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_ALLOWED 5948
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_BLOCKED 5949
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_BLOCKED_EXCEPTIONS 5950
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_DESCRIPTION 5951
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_ALLOWED 5952
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_BLOCKED 5953
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_ALLOWED_EXCEPTIONS 5954
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_BLOCKED_EXCEPTIONS 5955
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_DESCRIPTION 5956
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_ALLOWED 5957
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_BLOCKED 5958
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_DESCRIPTION 5959
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_ALLOWED 5960
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_BLOCKED 5961
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_BLOCKED_SUB_LABEL 5962
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_ALLOWED_EXCEPTIONS 5963
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_BLOCKED_EXCEPTIONS 5964
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_DESCRIPTION 5965
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_ALLOWED_EXCEPTIONS 5966
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_BLOCKED_EXCEPTIONS 5967
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_DESCRIPTION 5968
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_ALLOWED 5969
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_BLOCKED 5970
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_ALLOWED_EXCEPTIONS 5971
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_BLOCKED_EXCEPTIONS 5972
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_DESCRIPTION 5973
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_ALLOWED 5974
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_BLOCKED 5975
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_BLOCKED_SUB_LABEL 5976
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_ALLOWED_EXCEPTIONS 5977
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_BLOCKED_EXCEPTIONS 5978
#define IDS_SETTINGS_SITE_SETTINGS_MIC_DESCRIPTION 5979
#define IDS_SETTINGS_SITE_SETTINGS_MIC_ALLOWED 5980
#define IDS_SETTINGS_SITE_SETTINGS_MIC_BLOCKED 5981
#define IDS_SETTINGS_SITE_SETTINGS_MIC_BLOCKED_SUB_LABEL 5982
#define IDS_SETTINGS_SITE_SETTINGS_MIC_ALLOWED_EXCEPTIONS 5983
#define IDS_SETTINGS_SITE_SETTINGS_MIC_BLOCKED_EXCEPTIONS 5984
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_DESCRIPTION 5985
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_ALLOWED 5986
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_BLOCKED 5987
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_ALLOWED_EXCEPTIONS 5988
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_BLOCKED_EXCEPTIONS 5989
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_DESCRIPTION 5990
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_ALLOWED 5991
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_BLOCKED 5992
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_BLOCKED_SUB_LABEL 5993
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_ALLOWED_EXCEPTIONS 5994
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_BLOCKED_EXCEPTIONS 5995
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_DESCRIPTION 5996
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_ALLOWED 5997
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_PARTIAL 5998
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_PARTIAL_SUB_LABEL 5999
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCKED 6000
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCKED_SUB_LABEL 6001
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_ALLOWED_EXCEPTIONS 6002
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCKED_EXCEPTIONS 6003
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_DESCRIPTION 6004
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_ALLOWED 6005
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_BLOCKED 6006
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_ALLOWED_EXCEPTIONS 6007
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_BLOCKED_EXCEPTIONS 6008
#define IDS_SETTINGS_SITE_SETTINGS_PDFS_DESCRIPTION 6009
#define IDS_SETTINGS_SITE_SETTINGS_PDFS_ALLOWED 6010
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_DESCRIPTION 6011
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_ALLOWED 6012
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_BLOCKED 6013
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_ALLOWED_EXCEPTIONS 6014
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_BLOCKED_EXCEPTIONS 6015
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_ENABLE 6016
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_DESCRIPTION 6017
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_ALLOWED 6018
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_BLOCKED 6019
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_BLOCKED_SUB_LABEL 6020
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_DESCRIPTION 6029
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_ALLOWED 6030
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_BLOCKED 6031
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_BLOCKED_EXCEPTIONS 6032
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_DESCRIPTION 6033
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_ALLOWED 6034
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_BLOCKED 6035
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_DESCRIPTION 6036
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_ALLOWED 6037
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_BLOCKED 6038
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_BLOCKED_SUB_LABEL 6039
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_ALLOWED_EXCEPTIONS 6040
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_BLOCKED_EXCEPTIONS 6041
#define IDS_SETTINGS_SITE_SETTINGS_USB_DESCRIPTION 6042
#define IDS_SETTINGS_SITE_SETTINGS_USB_ALLOWED 6043
#define IDS_SETTINGS_SITE_SETTINGS_USB_BLOCKED 6044
#define IDS_SETTINGS_SITE_SETTINGS_VR_DESCRIPTION 6045
#define IDS_SETTINGS_SITE_SETTINGS_VR_ALLOWED 6046
#define IDS_SETTINGS_SITE_SETTINGS_VR_BLOCKED 6047
#define IDS_SETTINGS_SITE_SETTINGS_VR_ALLOWED_EXCEPTIONS 6048
#define IDS_SETTINGS_SITE_SETTINGS_VR_BLOCKED_EXCEPTIONS 6049
#define IDS_SETTINGS_SITE_SETTINGS_ZOOM_LEVELS_DESCRIPTION 6050
#define IDS_SETTINGS_SITE_SETTINGS_AR_ASK 6051
#define IDS_SETTINGS_SITE_SETTINGS_AR_ASK_RECOMMENDED 6052
#define IDS_SETTINGS_SITE_SETTINGS_AR_BLOCK 6053
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ASK 6054
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ASK_RECOMMENDED 6055
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_BLOCK 6056
#define IDS_SETTINGS_COOKIES_PAGE 6057
#define IDS_SETTINGS_COOKIES_CONTROLS 6058
#define IDS_SETTINGS_COOKIES_ALLOW_ALL 6059
#define IDS_SETTINGS_COOKIES_ALLOW_ALL_EXPAND_A11Y_LABEL 6060
#define IDS_SETTINGS_COOKIES_ALLOW_ALL_BULLET_ONE 6061
#define IDS_SETTINGS_COOKIES_ALLOW_ALL_BULLET_TWO 6062
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO 6063
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO_EXPAND_A11Y_LABEL 6064
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO_BULLET_ONE 6065
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO_BULLET_TWO 6066
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY 6067
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_EXPAND_A11Y_LABEL 6068
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_BULLET_ONE 6069
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_BULLET_TWO 6070
#define IDS_SETTINGS_COOKIES_BLOCK_ALL 6071
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_EXPAND_A11Y_LABEL 6072
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_BULLET_ONE 6073
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_BULLET_TWO 6074
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_BULLET_THREE 6075
#define IDS_SETTINGS_COOKIES_CLEAR_ON_EXIT 6076
#define IDS_SETTINGS_COOKIES_ALL_SITES_LINK 6077
#define IDS_SETTINGS_COOKIES_SITE_SPECIFIC_EXCEPTIONS 6078
#define IDS_SETTINGS_COOKIES_ALLOW_EXCEPTIONS 6079
#define IDS_SETTINGS_COOKIES_SESSION_ONLY_EXCEPTIONS 6080
#define IDS_SETTINGS_COOKIES_BLOCK_EXCEPTIONS 6081
#define IDS_SETTINGS_SITE_SETTINGS_APP_PROTOCOL_HANDLERS 6082
#define IDS_SETTINGS_SITE_SETTINGS_APP_ALLOWED_PROTOCOL_HANDLERS_DESCRIPTION 6083
#define IDS_SETTINGS_SITE_SETTINGS_APP_DISALLOWED_PROTOCOL_HANDLERS_DESCRIPTION 6084
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_ASK 6085
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_ASK_RECOMMENDED 6086
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_BLOCK 6087
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_BLOCK 6088
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLER_ALLOW_RECOMMENDED 6089
#define IDS_SETTINGS_SITE_SETTINGS_PDF_DOWNLOAD_PDFS 6090
#define IDS_SETTINGS_SITE_SETTINGS_VR_ASK_RECOMMENDED 6091
#define IDS_SETTINGS_SITE_SETTINGS_RECENT_ACTIVITY 6092
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_DEVICES_ASK_RECOMMENDED 6093
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_ASK_RECOMMENDED 6094
#define IDS_SETTINGS_SITE_SETTINGS_USB_DEVICES_ASK_RECOMMENDED 6095
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_ASK_RECOMMENDED 6096
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_ACCESS_WRITE_ASK_RECOMMENDED 6097
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_ZOOM_LEVEL 6098
#define IDS_SETTINGS_SITE_SETTINGS_MAY_SAVE_COOKIES 6099
#define IDS_SETTINGS_SITE_SETTINGS_ASK_FIRST 6100
#define IDS_SETTINGS_SITE_SETTINGS_ASK_FIRST_RECOMMENDED 6101
#define IDS_SETTINGS_SITE_SETTINGS_ASK_BEFORE_ACCESSING_RECOMMENDED 6102
#define IDS_SETTINGS_SITE_SETTINGS_ASK_BEFORE_SENDING 6103
#define IDS_SETTINGS_SITE_SETTINGS_ASK_BEFORE_SENDING_RECOMMENDED 6104
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATICALLY_BLOCKED_NOTIFICATIONS 6105
#define IDS_SETTINGS_SITE_SETTINGS_SHOW_BLOCKED_NOTIFICATIONS_INDICATOR 6106
#define IDS_SETTINGS_SITE_SETTINGS_ENABLE_QUIET_NOTIFICATION_PROMPTS 6107
#define IDS_SETTINGS_SITE_SETTINGS_ENABLE_QUIET_NOTIFICATION_PROMPTS_DESCRIPTION 6108
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCK 6109
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_ASK 6110
#define IDS_SETTINGS_SITE_SETTINGS_SHOW_ALL_RECOMMENDED 6111
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_ALLOW 6112
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_BLOCK 6113
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_BLOCK_THIRD_PARTY 6114
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO 6115
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_ALLOW_SITES 6116
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_ALLOW_SITES_RECOMMENDED 6117
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW_RECENTLY_CLOSED_SITES_RECOMMENDED 6118
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCK 6119
#define IDS_SETTINGS_SITE_SETTINGS_HANDLERS_ASK_RECOMMENDED 6120
#define IDS_SETTINGS_SITE_SETTINGS_HANDLERS_BLOCKED 6121
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCK_RECOMMENDED 6122
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_ALLOW_RECOMMENDED 6123
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOAD_ASK_RECOMMENDED 6124
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_DESCRIPTION 6125
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_ASK 6126
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_BLOCKED 6127
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_ASK_EXCEPTIONS 6128
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_BLOCKED_EXCEPTIONS 6129
#define IDS_SETTINGS_SITE_SETTINGS_IDLE_DETECTION_BLOCK 6130
#define IDS_SETTINGS_SITE_SETTINGS_ALLOWED_RECOMMENDED 6131
#define IDS_SETTINGS_SITE_SETTINGS_BLOCKED 6132
#define IDS_SETTINGS_SITE_SETTINGS_BLOCKED_RECOMMENDED 6133
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW 6134
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK 6135
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_SOUND 6136
#define IDS_SETTINGS_SITE_SETTINGS_SESSION_ONLY 6137
#define IDS_SETTINGS_SITE_SETTINGS_SITE_URL 6138
#define IDS_SETTINGS_SITE_SETTINGS_ASK_DEFAULT_MENU 6139
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW_DEFAULT_MENU 6140
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DEFAULT_MENU 6141
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_DEFAULT_MENU 6142
#define IDS_SETTINGS_SITE_SETTINGS_MUTE_DEFAULT_MENU 6143
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW_MENU 6144
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_MENU 6145
#define IDS_SETTINGS_SITE_SETTINGS_ASK_MENU 6146
#define IDS_SETTINGS_SITE_SETTINGS_MUTE_MENU 6147
#define IDS_SETTINGS_SITE_SETTINGS_RESET_MENU 6148
#define IDS_SETTINGS_SITE_SETTINGS_SESSION_ONLY_MENU 6149
#define IDS_SETTINGS_SITE_SETTINGS_USAGE 6150
#define IDS_SETTINGS_SITE_SETTINGS_USAGE_NONE 6151
#define IDS_SETTINGS_SITE_SETTINGS_PERMISSIONS 6152
#define IDS_SETTINGS_SITE_SETTINGS_PERMISSIONS_MORE 6153
#define IDS_SETTINGS_SITE_SETTINGS_CONTENT 6154
#define IDS_SETTINGS_SITE_SETTINGS_CONTENT_MORE 6155
#define IDS_SETTINGS_SITE_SETTINGS_ALLOWLISTED 6156
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCK_BLOCKLISTED_SINGULAR 6157
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCK_NOT_BLOCKLISTED_SINGULAR 6158
#define IDS_SETTINGS_SITE_SETTINGS_SOURCE_KILL_SWITCH 6159
#define IDS_SETTINGS_SITE_SETTINGS_SOURCE_INSECURE_ORIGIN 6160
#define IDS_SETTINGS_SITE_SETTINGS_RESET_BUTTON 6161
#define IDS_SETTINGS_SITE_SETTINGS_DELETE 6162
#define IDS_SETTINGS_SITE_SETTINGS_GROUP_RESET 6163
#define IDS_SETTINGS_SITE_SETTINGS_GROUP_DELETE 6164
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_HEADER 6165
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_LINK 6166
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE 6167
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_ALL 6168
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_ALL_SHOWN 6169
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_ALL_THIRD_PARTY 6170
#define IDS_SETTINGS_SITE_SETTINGS_THIRD_PARTY_COOKIE_REMOVE_DIALOG_TITLE 6171
#define IDS_SETTINGS_SITE_SETTINGS_THIRD_PARTY_COOKIE_REMOVE_CONFIRMATION 6172
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_THIRD_PARTY_COOKIES 6173
#define IDS_SETTINGS_SITE_SETTINGS_THIRD_PARTY_COOKIES_EXCEPTION_LABEL 6174
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_DIALOG_TITLE 6175
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_DESCRIPTION 6176
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_LABEL 6177
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_CONFIRMATION 6178
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_CONFIRMATION_INSTALLED 6179
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_SIGN_OUT 6180
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_DIALOG_TITLE 6181
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_SUBPAGE 6182
#define IDS_SETTINGS_SITE_SETTINGS_SITE_RESET_CONFIRMATION 6183
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_DIALOG_TITLE 6184
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_CONFIRMATION 6185
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_CONFIRMATION_NEW 6186
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_SIGN_OUT 6187
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_OFFLINE_DATA 6188
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_APPS 6189
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_RESET_DIALOG_TITLE 6190
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_RESET_CONFIRMATION 6191
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_DIALOG_TITLE 6192
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION 6193
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION_NEW 6194
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION_INSTALLED 6195
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION_INSTALLED_PLURAL 6196
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_SIGN_OUT 6197
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_OFFLINE_DATA 6198
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_APPS 6199
#define IDS_SETTINGS_SITE_SETTINGS_ORIGIN_DELETE_CONFIRMATION 6200
#define IDS_SETTINGS_SITE_SETTINGS_ORIGIN_DELETE_CONFIRMATION_INSTALLED 6201
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_MULTIPLE 6202
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_DIALOG_TITLE 6203
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_APP_DIALOG_TITLE 6204
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_PARTITIONED_DIALOG_TITLE 6205
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_DIALOG_TITLE 6206
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_APP_DIALOG_TITLE 6207
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_APP_PLURAL_DIALOG_TITLE 6208
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_LOGOUT 6209
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_LOGOUT 6210
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_OFFLINE_DATA 6211
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_PERMISSIONS 6212
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_CONFIRM 6213
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_SITE 6214
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_CLEAR_ALL 6215
#define IDS_SETTINGS_SITE_SETTINGS_SITE_RESET_ALL 6216
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE 6217
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_SEARCH 6218
#define IDS_SETTINGS_SITE_SETTINGS_HANDLER_IS_DEFAULT 6219
#define IDS_SETTINGS_SITE_SETTINGS_HANDLER_SET_DEFAULT 6220
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE 6221
#define IDS_SETTINGS_SITE_SETTINGS_INCOGNITO_ONLY 6222
#define IDS_SETTINGS_SITE_SETTINGS_INCOGNITO_SITE_EXCEPTION_DESC 6223
#define IDS_SETTINGS_SITE_SETTINGS_NO_ZOOMED_SITES 6224
#define IDS_SETTINGS_SITE_NO_SITES_ADDED 6225
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_AUTOPLAY 6226
#define IDS_SETTINGS_SITE_SETTINGS_EMPTY_ALL_SITES_PAGE 6227
#define IDS_SETTINGS_SITE_SETTINGS_NO_SITES_FOUND 6228
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_SCANNING_ASK 6229
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_SCANNING_ASK_RECOMMENDED 6230
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_SCANNING_BLOCK 6231
#define IDS_SETTINGS_NO_BLUETOOTH_DEVICES_FOUND 6232
#define IDS_SETTINGS_NO_USB_DEVICES_FOUND 6233
#define IDS_SETTINGS_NO_SERIAL_PORTS_FOUND 6234
#define IDS_SETTINGS_NO_HID_DEVICES_FOUND 6235
#define IDS_SETTINGS_ADD_SITE_TITLE 6236
#define IDS_SETTINGS_EDIT_SITE_TITLE 6237
#define IDS_SETTINGS_ADD_SITE 6238
#define IDS_SETTINGS_COOKIES_COOKIE_NAME_LABEL 6240
#define IDS_SETTINGS_COOKIES_COOKIE_CONTENT_LABEL 6241
#define IDS_SETTINGS_COOKIES_COOKIE_DOMAIN_LABEL 6242
#define IDS_SETTINGS_COOKIES_COOKIE_PATH_LABEL 6243
#define IDS_SETTINGS_COOKIES_COOKIE_SENDFOR_LABEL 6244
#define IDS_SETTINGS_COOKIES_COOKIE_ACCESSIBLE_TO_SCRIPT_LABEL 6245
#define IDS_SETTINGS_COOKIES_COOKIE_CREATED_LABEL 6246
#define IDS_SETTINGS_COOKIES_COOKIE_EXPIRES_LABEL 6247
#define IDS_SETTINGS_COOKIES_FLASH_LSO 6248
#define IDS_SETTINGS_SITE_SETTINGS_NUM_COOKIES 6249
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE_ORIGIN_LABEL 6250
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE_SIZE_ON_DISK_LABEL 6251
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE_LAST_MODIFIED_LABEL 6252
#define IDS_SETTINGS_COOKIES_DATABASE_STORAGE 6253
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE 6254
#define IDS_SETTINGS_COOKIES_MEDIA_LICENSE 6255
#define IDS_SETTINGS_COOKIES_FILE_SYSTEM 6256
#define IDS_SETTINGS_COOKIES_FILE_SYSTEM_TEMPORARY_USAGE_LABEL 6257
#define IDS_SETTINGS_COOKIES_FILE_SYSTEM_PERSISTENT_USAGE_LABEL 6258
#define IDS_SETTINGS_COOKIES_SERVICE_WORKER 6259
#define IDS_SETTINGS_COOKIES_SHARED_WORKER 6260
#define IDS_SETTINGS_COOKIES_SHARED_WORKER_WORKER_LABEL 6261
#define IDS_SETTINGS_COOKIES_CACHE_STORAGE 6262
#define IDS_SETTINGS_PEOPLE 6263
#define IDS_SETTINGS_CHANGE_PICTURE_PROFILE_PHOTO 6264
#define IDS_SETTINGS_PEOPLE_SIGN_IN 6265
#define IDS_SETTINGS_SYNC_DISCONNECT_MANAGED_PROFILE_EXPLANATION 6266
#define IDS_SETTINGS_TURN_OFF_SYNC_DIALOG_TITLE 6267
#define IDS_SETTINGS_TURN_OFF_SYNC_AND_SIGN_OUT_DIALOG_TITLE 6268
#define IDS_SETTINGS_TURN_OFF_SYNC_DIALOG_MANAGED_CONFIRM 6269
#define IDS_SETTINGS_TURN_OFF_SYNC_DIALOG_CHECKBOX 6270
#define IDS_SETTINGS_SYNC_SETTINGS_SAVED_TOAST_LABEL 6271
#define IDS_SETTINGS_PROFILE_NAME_INPUT_LABEL 6272
#define IDS_SETTINGS_PROFILE_SHORTCUT_TOGGLE_LABEL 6273
#define IDS_SETTINGS_CUSTOMIZE_PROFILE 6274
#define IDS_SETTINGS_PICK_A_THEME_COLOR 6275
#define IDS_SETTINGS_PICK_AN_AVATAR 6276
#define IDS_SETTINGS_CREATE_SHORTCUT 6277
#define IDS_SETTINGS_CREATE_SHORTCUT_SUBTITLE 6278
#define IDS_SETTINGS_SYNC_DISCONNECT_EXPLANATION 6279
#define IDS_SETTINGS_SYNC_DISCONNECT_MAIN_PROFILE_EXPLANATION 6280
#define IDS_SETTINGS_SYNC_DISCONNECT_AND_SIGN_OUT_EXPLANATION 6281
#define IDS_SETTINGS_SYNC_DISCONNECT_EXPAND_ACCESSIBILITY_LABEL 6282
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE 6283
#define IDS_SETTINGS_SYNC_WILL_START 6284
#define IDS_SETTINGS_MANAGE_GOOGLE_ACCOUNT 6285
#define IDS_SETTINGS_USER_EVENTS_CHECKBOX_LABEL 6286
#define IDS_SETTINGS_USER_EVENTS_CHECKBOX_TEXT 6287
#define IDS_SETTINGS_ENCRYPT_WITH_SYNC_PASSPHRASE_LABEL 6288
#define IDS_SETTINGS_PASSPHRASE_EXPLANATION_TEXT 6289
#define IDS_SETTINGS_PASSPHRASE_RESET_HINT_ENCRYPTION 6290
#define IDS_SETTINGS_PASSPHRASE_RESET_HINT_TOGGLE 6291
#define IDS_SETTINGS_PASSPHRASE_RECOVER 6292
#define IDS_SETTINGS_PERSONALIZE_GOOGLE_SERVICES_TITLE 6293
#define IDS_SETTINGS_IMPORT_SETTINGS_TITLE 6294
#define IDS_SETTINGS_IMPORT_FROM_LABEL 6295
#define IDS_SETTINGS_IMPORT_ITEMS_LABEL 6296
#define IDS_SETTINGS_IMPORT_LOADING_PROFILES 6297
#define IDS_SETTINGS_IMPORT_HISTORY_CHECKBOX 6298
#define IDS_SETTINGS_IMPORT_FAVORITES_CHECKBOX 6299
#define IDS_SETTINGS_IMPORT_PASSWORDS_CHECKBOX 6300
#define IDS_SETTINGS_IMPORT_SEARCH_ENGINES_CHECKBOX 6301
#define IDS_SETTINGS_IMPORT_AUTOFILL_FORM_DATA_CHECKBOX 6302
#define IDS_SETTINGS_IMPORT_CHOOSE_FILE 6303
#define IDS_SETTINGS_IMPORT_COMMIT 6304
#define IDS_SETTINGS_IMPORT_SUCCESS 6305
#define IDS_SETTINGS_IMPORT_NO_PROFILE_FOUND 6306
#define IDS_SETTINGS_PAGE_ZOOM_LABEL 6307
#define IDS_SETTINGS_FONT_SIZE_LABEL 6308
#define IDS_SETTINGS_VERY_SMALL_FONT 6309
#define IDS_SETTINGS_SMALL_FONT 6310
#define IDS_SETTINGS_MEDIUM_FONT 6311
#define IDS_SETTINGS_LARGE_FONT 6312
#define IDS_SETTINGS_VERY_LARGE_FONT 6313
#define IDS_SETTINGS_CUSTOMIZE_FONTS 6314
#define IDS_SETTINGS_FONTS 6315
#define IDS_SETTINGS_STANDARD_FONT_LABEL 6316
#define IDS_SETTINGS_SERIF_FONT_LABEL 6317
#define IDS_SETTINGS_SANS_SERIF_FONT_LABEL 6318
#define IDS_SETTINGS_FIXED_WIDTH_FONT_LABEL 6319
#define IDS_SETTINGS_MATH_FONT_LABEL 6320
#define IDS_SETTINGS_MINIMUM_FONT_SIZE_LABEL 6321
#define IDS_SETTINGS_TINY_FONT_SIZE 6322
#define IDS_SETTINGS_HUGE_FONT_SIZE 6323
#define IDS_SETTINGS_QUICK_BROWN_FOX 6324
#define IDS_SETTINGS_SYSTEM 6325
#define IDS_SETTINGS_SYSTEM_HARDWARE_ACCELERATION_LABEL 6326
#define IDS_SETTINGS_SYSTEM_PROXY_SETTINGS_LABEL 6327
#define IDS_SETTINGS_SYSTEM_PROXY_SETTINGS_EXTENSION_LABEL 6328
#define IDS_SETTINGS_SYSTEM_PROXY_SETTINGS_POLICY_LABEL 6329
#define IDS_PAGE_NOT_AVAILABLE_FOR_GUEST_HEADING 6373
#define IDS_SETTINGS_SECURITY_KEYS_TITLE 6374
#define IDS_SETTINGS_SECURITY_KEYS_DESC 6375
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN 6376
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_BUTTON 6377
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_DESC 6378
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_INITIAL_TITLE 6379
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_CREATE_TITLE 6380
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_CHANGE_TITLE 6381
#define IDS_SETTINGS_SECURITY_KEYS_RESET 6382
#define IDS_SETTINGS_SECURITY_KEYS_RESET_DESC 6383
#define IDS_SETTINGS_SECURITY_KEYS_RESET_TITLE 6384
#define IDS_SETTINGS_SECURITY_KEYS_RESET_CONFIRM_TITLE 6385
#define IDS_SETTINGS_SECURITY_KEYS_RESET_STEP1 6386
#define IDS_SETTINGS_SECURITY_KEYS_RESET_STEP2 6387
#define IDS_SETTINGS_SECURITY_KEYS_NO_RESET 6388
#define IDS_SETTINGS_SECURITY_KEYS_RESET_ERROR 6389
#define IDS_SETTINGS_SECURITY_KEYS_RESET_SUCCESS 6390
#define IDS_SETTINGS_SECURITY_KEYS_RESET_NOTALLOWED 6391
#define IDS_SETTINGS_SECURITY_KEYS_NO_PIN 6392
#define IDS_SETTINGS_SECURITY_KEYS_CURRENT_PIN_INTRO 6393
#define IDS_SETTINGS_SECURITY_KEYS_PIN_INCORRECT 6394
#define IDS_SETTINGS_SECURITY_KEYS_PIN_INCORRECT_RETRIES_SIN 6395
#define IDS_SETTINGS_SECURITY_KEYS_PIN_INCORRECT_RETRIES_PL 6396
#define IDS_SETTINGS_SECURITY_KEYS_SAME_PIN_AS_CURRENT 6397
#define IDS_SETTINGS_SECURITY_KEYS_NEW_PIN 6398
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_CONFIRM 6399
#define IDS_SETTINGS_SECURITY_KEYS_CURRENT_PIN 6400
#define IDS_SETTINGS_SECURITY_KEYS_PIN 6401
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_TOO_SHORT_SMALL 6402
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_TOO_LONG 6403
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_INVALID 6404
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_MISMATCH 6405
#define IDS_SETTINGS_SECURITY_KEYS_CONFIRM_PIN 6406
#define IDS_SETTINGS_SECURITY_KEYS_PIN_SUCCESS 6407
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR 6408
#define IDS_SETTINGS_SECURITY_KEYS_PIN_HARD_LOCK 6409
#define IDS_SETTINGS_SECURITY_KEYS_PIN_SOFT_LOCK 6410
#define IDS_SETTINGS_SECURITY_KEYS_SHOW_PINS 6411
#define IDS_SETTINGS_SECURITY_KEYS_HIDE_PINS 6412
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_LABEL 6413
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_USERNAME_LABEL 6414
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_DISPLAYNAME_LABEL 6415
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_WEBSITE_LABEL 6416
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DESC 6417
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DIALOG_TITLE 6418
#define IDS_SETTINGS_SECURITY_KEYS_UPDATE_CREDENTIAL_DIALOG_TITLE 6419
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_CONFIRM_DELETE_TITLE 6420
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_CONFIRM_DELETE_CREDENTIAL 6421
#define IDS_SETTINGS_SECURITY_KEYS_INPUT_ERROR_TOO_LONG 6422
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_NO_CREDENTIALS 6423
#define IDS_SETTINGS_SECURITY_KEYS_NO_CREDENTIAL_MANAGEMENT 6424
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_REMOVED 6425
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_NO_PIN 6426
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_ERROR 6427
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DELETE_SUCCESS 6428
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DELETE_FAILED 6429
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_UPDATE_SUCCESS 6430
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_UPDATE_FAILED 6431
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_SUBPAGE_LABEL 6432
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_SUBPAGE_DESCRIPTION 6433
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_DIALOG_TITLE 6434
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ADD_TITLE 6435
#define IDS_SETTINGS_SECURITY_KEYS_BIO_CHOOSE_NAME 6436
#define IDS_SETTINGS_SECURITY_KEYS_BIO_NAME_LABEL 6437
#define IDS_SETTINGS_SECURITY_KEYS_BIO_NAME_LABEL_TOO_LONG 6438
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_NO_ENROLLMENTS_LABEL 6439
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ENROLLMENTS_LABEL 6440
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ENROLLING_LABEL 6441
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_TRY_AGAIN_LABEL 6442
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_FAILED_LABEL 6443
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ENROLLING_COMPLETE_LABEL 6444
#define IDS_SETTINGS_SECURITY_KEYS_NO_BIOMETRIC_ENROLLMENT 6445
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_DELETE 6446
#define IDS_SETTINGS_SECURITY_KEYS_BIO_NO_PIN 6447
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_STORAGE_FULL 6448
#define IDS_SETTINGS_SECURITY_KEYS_TOUCH_TO_CONTINUE 6449
#define IDS_SETTINGS_SECURITY_KEYS_PIN_PROMPT 6450
#define IDS_SETTINGS_SECURITY_KEYS_FORCE_PIN_CHANGE 6451
#define IDS_SETTINGS_SECURITY_KEYS_PHONE_EDIT_DIALOG_TITLE 6452
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_YOUR_DEVICES 6453
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_SYNCED_DESC 6454
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_LINKED_DEVICES 6455
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_LINKED_DESC 6456
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_MANAGE 6457
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_MANAGE_DESC 6458
#define IDS_SETTINGS_SUBPAGE_BACK_BUTTON_ARIA_LABEL 6459
#define IDS_SETTINGS_SUBPAGE_BACK_BUTTON_ARIA_ROLE_DESCRIPTION 6460
#define IDS_SETTINGS_CAPTIONS 6461
#define IDS_SETTINGS_CAPTIONS_PREFERENCES_TITLE 6462
#define IDS_SETTINGS_CAPTIONS_PREFERENCES_SUBTITLE 6463
#define IDS_SETTINGS_CAPTIONS_TEXT_SIZE 6464
#define IDS_SETTINGS_CAPTIONS_TEXT_FONT 6465
#define IDS_SETTINGS_CAPTIONS_TEXT_COLOR 6466
#define IDS_SETTINGS_CAPTIONS_TEXT_OPACITY 6467
#define IDS_SETTINGS_CAPTIONS_BACKGROUND_OPACITY 6468
#define IDS_SETTINGS_CAPTIONS_OPACITY_OPAQUE 6469
#define IDS_SETTINGS_CAPTIONS_OPACITY_SEMI_TRANSPARENT 6470
#define IDS_SETTINGS_CAPTIONS_OPACITY_TRANSPARENT 6471
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW 6472
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_NONE 6473
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_RAISED 6474
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_DEPRESSED 6475
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_UNIFORM 6476
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_DROP_SHADOW 6477
#define IDS_SETTINGS_CAPTIONS_BACKGROUND_COLOR 6478
#define IDS_SETTINGS_CAPTIONS_COLOR_BLACK 6479
#define IDS_SETTINGS_CAPTIONS_COLOR_WHITE 6480
#define IDS_SETTINGS_CAPTIONS_COLOR_RED 6481
#define IDS_SETTINGS_CAPTIONS_COLOR_GREEN 6482
#define IDS_SETTINGS_CAPTIONS_COLOR_BLUE 6483
#define IDS_SETTINGS_CAPTIONS_COLOR_YELLOW 6484
#define IDS_SETTINGS_CAPTIONS_COLOR_CYAN 6485
#define IDS_SETTINGS_CAPTIONS_COLOR_MAGENTA 6486
#define IDS_SETTINGS_CAPTIONS_DEFAULT_SETTING 6487
#define IDS_SETTINGS_NEARBY_SHARE_TITLE 6488
#define IDS_SETTINGS_NEARBY_SHARE_SET_UP_BUTTON_TITLE 6489
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_ROW_TITLE 6490
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_DIALOG_TITLE 6491
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_FIELD_LABEL 6492
#define IDS_SETTINGS_NEARBY_SHARE_EDIT_DEVICE_NAME 6493
#define IDS_SETTINGS_NEARBY_SHARE_FAST_INITIATION_NOTIFICATION_TOGGLE_TITLE 6494
#define IDS_SETTINGS_NEARBY_SHARE_FAST_INITIATION_NOTIFICATION_TOGGLE_DESCRIPTION 6495
#define IDS_SETTINGS_NEARBY_SHARE_FAST_INITIATION_NOTIFICATION_TOGGLE_ARIA_LABEL 6496
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_ARIA_DESCRIPTION 6497
#define IDS_SETTINGS_NEARBY_SHARE_CONFIRM_DEVICE_NAME 6498
#define IDS_SETTINGS_NEARBY_SHARE_MANAGE_CONTACTS_LABEL 6499
#define IDS_SETTINGS_NEARBY_SHARE_MANAGE_CONTACTS_ROW_TITLE 6500
#define IDS_SETTINGS_NEARBY_SHARE_EDIT_DATA_USAGE 6501
#define IDS_SETTINGS_NEARBY_SHARE_UPDATE_DATA_USAGE 6502
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_DIALOG_TITLE 6503
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_WIFI_ONLY_LABEL 6504
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_WIFI_ONLY_DESCRIPTION 6505
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_MOBILE_DATA_LABEL 6506
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_MOBILE_DATA_DESCRIPTION 6507
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_MOBILE_DATA_TOOLTIP 6508
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_OFFLINE_LABEL 6509
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_OFFLINE_DESCRIPTION 6510
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_EDIT_BUTTON_DATA_DESCRIPTION 6511
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_EDIT_BUTTON_WIFI_ONLY_DESCRIPTION 6512
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_EDIT_BUTTON_OFFLINE_DESCRIPTION 6513
#define IDS_SETTINGS_NEARBY_SHARE_CONTACT_VISIBILITY_ROW_TITLE 6514
#define IDS_SETTINGS_NEARBY_SHARE_EDIT_VISIBILITY 6515
#define IDS_SETTINGS_NEARBY_SHARE_VISIBILITY_DIALOG_TITLE 6516
#define IDS_SETTINGS_NEARBY_SHARE_VISIBILITY_DIALOG_SAVE 6517
#define IDS_SETTINGS_NEARBY_SHARE_DESCRIPTION 6518
#define IDS_SETTINGS_NEARBY_SHARE_HIGH_VISIBILITY_TITLE 6519
#define IDS_SETTINGS_NEARBY_SHARE_HIGH_VISIBILITY_ON 6520
#define IDS_SETTINGS_NEARBY_SHARE_HIGH_VISIBILITY_OFF 6521
#define IDS_SETTINGS_ENABLE_URL_KEYED_ANONYMIZED_DATA_COLLECTION 6522
#define IDS_SETTINGS_ENABLE_URL_KEYED_ANONYMIZED_DATA_COLLECTION_DESC 6523
#define IDS_SETTINGS_SPELLING_PREF 6524
#define IDS_SETTINGS_SUGGEST_PREF 6525
#define IDS_SETTINGS_SUGGEST_PREF_DESC 6526
#define IDS_SETTINGS_ENABLE_LOGGING_PREF 6527
#define IDS_SETTINGS_ENABLE_LOGGING_PREF_DESC 6528
#define IDS_SETTINGS_LINKDOCTOR_PREF 6529
#define IDS_SETTINGS_LINKDOCTOR_PREF_DESC 6530
#define IDS_DRIVE_SUGGEST_PREF 6531
#define IDS_SETTINGS_PEOPLE_SYNCING_TO_ACCOUNT 6532
#define IDS_SETTINGS_PEOPLE_SYNC_PAUSED 6533
#define IDS_SETTINGS_PEOPLE_SIGN_IN_PROMPT 6534
#define IDS_SETTINGS_PEOPLE_SYNC_TURN_OFF 6535
#define IDS_SETTINGS_SETTINGS_CHECKBOX_LABEL 6536
#define IDS_SETTINGS_PEOPLE_SYNC_NOT_WORKING 6537
#define IDS_SETTINGS_PEOPLE_SYNC_PASSWORDS_NOT_WORKING 6538
#define IDS_SETTINGS_SYNC_ADVANCED_PAGE_TITLE 6539
#define IDS_SETTINGS_NEW_SYNC_ADVANCED_PAGE_TITLE 6540
#define IDS_SETTINGS_PEOPLE_SYNC_ANOTHER_ACCOUNT 6541
#define IDS_SETTINGS_SYNC_DISCONNECT_CONFIRM 6542
#define IDS_SETTINGS_PEOPLE_SIGN_OUT 6543
#define IDS_SETTINGS_AUTOFILL_CHECKBOX_LABEL 6544
#define IDS_SETTINGS_HISTORY_CHECKBOX_LABEL 6545
#define IDS_SETTINGS_EXTENSIONS_CHECKBOX_LABEL 6546
#define IDS_SETTINGS_OPEN_TABS_CHECKBOX_LABEL 6547
#define IDS_SETTINGS_WIFI_CONFIGURATIONS_CHECKBOX_LABEL 6548
#define IDS_SETTINGS_SYNC_EVERYTHING_CHECKBOX_LABEL 6549
#define IDS_SETTINGS_APPS_CHECKBOX_LABEL 6550
#define IDS_SETTINGS_NON_PERSONALIZED_SERVICES_SECTION_LABEL 6551
#define IDS_SETTINGS_CUSTOMIZE_SYNC 6552
#define IDS_SETTINGS_SYNC_DATA 6553
#define IDS_SETTINGS_PASSWORDS_CHECKBOX_LABEL 6554
#define IDS_SETTINGS_PASSPHRASE_PLACEHOLDER 6555
#define IDS_SETTINGS_EXISTING_PASSPHRASE_TITLE 6556
#define IDS_SETTINGS_SUBMIT_PASSPHRASE 6557
#define IDS_SETTINGS_ENCRYPT_WITH_GOOGLE_CREDENTIALS_LABEL 6558
#define IDS_SETTINGS_BOOKMARKS_CHECKBOX_LABEL 6559
#define IDS_SETTINGS_READING_LIST_CHECKBOX_LABEL 6560
#define IDS_SETTINGS_ENCRYPTION_OPTIONS 6561
#define IDS_SETTINGS_MISMATCHED_PASSPHRASE_ERROR 6562
#define IDS_SETTINGS_EMPTY_PASSPHRASE_ERROR 6563
#define IDS_SETTINGS_INCORRECT_PASSPHRASE_ERROR 6564
#define IDS_SETTINGS_NEW_MANAGE_SYNCED_DATA_TITLE_UNIFIED_CONSENT 6565
#define IDS_SETTINGS_SYNC_SYNC_AND_NON_PERSONALIZED_SERVICES 6566
#define IDS_SETTINGS_PASSPHRASE_CONFIRMATION_PLACEHOLDER 6567
#define IDS_SETTINGS_SYNC_LOADING 6568
#define IDS_SETTINGS_SYNC_TIMEOUT 6569
#define IDS_SETTINGS_SYNC 6570
#define IDS_SETTINGS_SYNC_SETTINGS_CANCEL_SYNC 6571
#define IDS_SETTINGS_SYNC_SETUP_CANCEL_DIALOG_TITLE 6572
#define IDS_SETTINGS_SYNC_SETUP_CANCEL_DIALOG_BODY 6573
#define IDS_SETTINGS_ABOUT_UPGRADE_CHECK_STARTED 6577
#define IDS_SETTINGS_THEME_CHECKBOX_LABEL 6579
#define IDS_EXTENSIONS_ALLOW_FILE_ACCESS 6583
#define IDS_EXTENSIONS_ALLOW_ON_ALL_URLS 6584
#define IDS_EXTENSIONS_ALLOW_ON_FOLLOWING_SITES 6585
#define IDS_EXTENSIONS_VIEW_ACTIVITY_LOG 6586
#define IDS_EXTENSIONS_BACKGROUND_PAGE 6587
#define IDS_EXTENSIONS_SERVICE_WORKER_BACKGROUND 6588
#define IDS_EXTENSIONS_CORRUPTED_EXTENSION 6589
#define IDS_EXTENSIONS_ENABLE_ERROR_COLLECTION 6590
#define IDS_EXTENSIONS_ERROR_NO_ERRORS_CODE_MESSAGE 6591
#define IDS_EXTENSIONS_INSTALL_DROP_TARGET 6592
#define IDS_EXTENSIONS_INSTALL_WARNINGS 6593
#define IDS_EXTENSIONS_LOG_LEVEL_ERROR 6594
#define IDS_EXTENSIONS_LOG_LEVEL_INFO 6595
#define IDS_EXTENSIONS_LOG_LEVEL_WARN 6596
#define IDS_EXTENSIONS_PATH 6597
#define IDS_EXTENSIONS_PERMISSIONS_OFF 6598
#define IDS_EXTENSIONS_RELOAD_TERMINATED 6599
#define IDS_EXTENSIONS_REPAIR_CORRUPTED 6600
#define IDS_EXTENSIONS_VIEW_IFRAME 6601
#define IDS_EXTENSIONS_VIEW_INACTIVE 6602
#define IDS_EXTENSIONS_VIEW_INCOGNITO 6603
#define IDS_EXTENSIONS_DEVELOPER_MODE 6604
#define IDS_EXTENSIONS_DISABLED_UPDATE_REQUIRED_BY_POLICY 6605
#define IDS_EXTENSIONS_MATCHING_RESTRICTED_SITES_WARNING 6606
#define IDS_EXTENSIONS_MENU_BUTTON_LABEL 6607
#define IDS_EXTENSIONS_ERROR_PAGE_HEADING 6608
#define IDS_EXTENSIONS_EDIT_SITE_PERMISSIONS_ALLOW_ALL_EXTENSIONS 6609
#define IDS_EXTENSIONS_EDIT_SITE_PERMISSIONS_RESTRICT_EXTENSIONS 6610
#define IDS_EXTENSIONS_ERROR_ANONYMOUS_FUNCTION 6611
#define IDS_EXTENSIONS_ERROR_CONTEXT 6612
#define IDS_EXTENSIONS_ERROR_CONTEXT_UNKNOWN 6613
#define IDS_EXTENSIONS_CLEAR_ACTIVITIES 6614
#define IDS_EXTENSIONS_ERROR_CLEAR_ALL 6615
#define IDS_EXTENSIONS_A11Y_CLEAR_ENTRY 6616
#define IDS_EXTENSIONS_ERROR_STACK_TRACE 6617
#define IDS_EXTENSIONS_ERROR_LINES_NOT_SHOWN 6618
#define IDS_EXTENSIONS_HOST_PERMISSIONS_DESCRIPTION 6619
#define IDS_EXTENSIONS_HOST_PERMISSIONS_EDIT 6620
#define IDS_EXTENSIONS_ITEM_ERRORS 6621
#define IDS_EXTENSIONS_ITEM_HOST_PERMISSIONS_HEADING 6622
#define IDS_EXTENSIONS_NEW_HOST_PERMISSIONS_HEADING 6623
#define IDS_EXTENSIONS_HOST_PERMISSIONS_SUB_HEADING 6624
#define IDS_EXTENSIONS_HOST_ACCESS_ON_CLICK 6625
#define IDS_EXTENSIONS_HOST_ACCESS_WHEN_CLICKED 6626
#define IDS_EXTENSIONS_HOST_ACCESS_ON_SPECIFIC_SITES 6627
#define IDS_EXTENSIONS_HOST_ACCESS_ALLOW_ON_SPECIFIC_SITES 6628
#define IDS_EXTENSIONS_HOST_ACCESS_ON_ALL_SITES 6629
#define IDS_EXTENSIONS_HOST_ACCESS_ALLOW_ON_ALL_SITES 6630
#define IDS_EXTENSIONS_ITEM_ALLOWED_HOSTS 6631
#define IDS_EXTENSIONS_ACCESSIBILITY_ERROR_LINE 6632
#define IDS_EXTENSIONS_ACCESSIBILITY_ERROR_MULTI_LINE 6633
#define IDS_EXTENSIONS_ACTIVITY_LOG_PAGE_HEADING 6634
#define IDS_EXTENSIONS_ACTIVITY_LOG_SEARCH_LABEL 6635
#define IDS_EXTENSIONS_ACTIVITY_LOG_TYPE_COLUMN 6636
#define IDS_EXTENSIONS_ACTIVITY_LOG_NAME_COLUMN 6637
#define IDS_EXTENSIONS_ACTIVITY_LOG_COUNT_COLUMN 6638
#define IDS_EXTENSIONS_ACTIVITY_LOG_TIME_COLUMN 6639
#define IDS_EXTENSIONS_ACTIVITY_LOG_HISTORY_TAB_HEADING 6640
#define IDS_EXTENSIONS_ACTIVITY_LOG_STREAM_TAB_HEADING 6641
#define IDS_EXTENSIONS_START_ACTIVITY_STREAM 6642
#define IDS_EXTENSIONS_STOP_ACTIVITY_STREAM 6643
#define IDS_EXTENSIONS_EMPTY_STREAM_STARTED 6644
#define IDS_EXTENSIONS_EMPTY_STREAM_STOPPED 6645
#define IDS_EXTENSIONS_ACTIVITY_ARGUMENTS_HEADING 6646
#define IDS_EXTENSIONS_WEB_REQUEST_INFO_HEADING 6647
#define IDS_EXTENSIONS_ACTIVITY_LOG_MORE_ACTIONS_LABEL 6648
#define IDS_EXTENSIONS_ACTIVITY_LOG_EXPAND_ALL 6649
#define IDS_EXTENSIONS_ACTIVITY_LOG_COLLAPSE_ALL 6650
#define IDS_EXTENSIONS_ACTIVITY_LOG_EXPORT_HISTORY 6651
#define IDS_EXTENSIONS_ITEM_ID 6652
#define IDS_EXTENSIONS_ITEM_INSPECT_VIEWS 6653
#define IDS_EXTENSIONS_ITEM_INSPECT_VIEWS_EXTRA 6654
#define IDS_EXTENSIONS_ITEM_NO_ACTIVE_VIEWS 6655
#define IDS_EXTENSIONS_ITEM_ALLOW_INCOGNITO 6656
#define IDS_EXTENSIONS_ITEM_DEPENDENCIES 6657
#define IDS_EXTENSIONS_DEPENDENT_ENTRY 6658
#define IDS_EXTENSIONS_ITEM_DESCRIPTION 6659
#define IDS_EXTENSIONS_ITEM_DETAILS 6660
#define IDS_EXTENSIONS_DETAILS_BACK_BUTTON_ARIA_LABEL 6661
#define IDS_EXTENSIONS_DETAILS_BACK_BUTTON_ARIA_ROLE_DESCRIPTION 6662
#define IDS_EXTENSIONS_EXTENSION_A11Y_ASSOCIATION 6663
#define IDS_EXTENSIONS_APP_ICON 6664
#define IDS_EXTENSIONS_EXTENSION_ICON 6665
#define IDS_EXTENSIONS_ITEM_ID_HEADING 6666
#define IDS_EXTENSIONS_EXTENSION_ENABLED 6667
#define IDS_EXTENSIONS_APP_ENABLED 6668
#define IDS_EXTENSIONS_ITEM_OFF 6669
#define IDS_EXTENSIONS_ITEM_ON 6670
#define IDS_EXTENSIONS_ITEM_EXTENSION_WEBSITE 6671
#define IDS_EXTENSIONS_ITEM_CHROME_WEB_STORE 6672
#define IDS_EXTENSIONS_ITEM_OPTIONS 6673
#define IDS_EXTENSIONS_ITEM_PERMISSIONS 6674
#define IDS_EXTENSIONS_ITEM_PERMISSIONS_EMPTY 6675
#define IDS_EXTENSIONS_ITEM_PERMISSIONS_AND_SITE_ACCESS_EMPTY 6676
#define IDS_EXTENSIONS_ITEM_REMOVE_EXTENSION 6677
#define IDS_EXTENSIONS_ITEM_SITE_ACCESS 6678
#define IDS_EXTENSIONS_ITEM_SITE_ACCESS_ADD_HOST 6679
#define IDS_EXTENSIONS_ITEM_SITE_ACCESS_EMPTY 6680
#define IDS_EXTENSIONS_REMOVE_SITES_DIALOG_TITLE 6681
#define IDS_EXTENSIONS_ITEM_SOURCE 6682
#define IDS_EXTENSIONS_ITEM_SOURCE_POLICY 6683
#define IDS_EXTENSIONS_ITEM_SOURCE_SIDELOADED 6684
#define IDS_EXTENSIONS_ITEM_SOURCE_UNPACKED 6685
#define IDS_EXTENSIONS_ITEM_SOURCE_WEBSTORE 6686
#define IDS_EXTENSIONS_ITEM_VERSION 6687
#define IDS_EXTENSIONS_ITEM_RELOADED 6688
#define IDS_EXTENSIONS_ITEM_RELOADING 6689
#define IDS_EXTENSIONS_LOAD_ERROR_HEADING 6690
#define IDS_EXTENSIONS_LOAD_ERROR_ERROR_LABEL 6691
#define IDS_EXTENSIONS_LOAD_ERROR_FILE_LABEL 6692
#define IDS_EXTENSIONS_LOAD_ERROR_COULD_NOT_LOAD_MANIFEST 6693
#define IDS_EXTENSIONS_LOAD_ERROR_RETRY 6694
#define IDS_EXTENSIONS_LOADING_ACTIVITIES 6695
#define IDS_MISSING_OR_UNINSTALLED_EXTENSION 6696
#define IDS_EXTENSIONS_NO_ACTIVITIES 6697
#define IDS_EXTENSIONS_NO_INSTALLED_ITEMS 6698
#define IDS_EXTENSIONS_NO_DESCRIPTION 6699
#define IDS_EXTENSIONS_PACK_DIALOG_TITLE 6700
#define IDS_EXTENSIONS_PACK_DIALOG_WARNING_TITLE 6701
#define IDS_EXTENSIONS_PACK_DIALOG_ERROR_TITLE 6702
#define IDS_EXTENSIONS_PACK_DIALOG_PROCEED_ANYWAY 6703
#define IDS_EXTENSIONS_PACK_DIALOG_BROWSE_BUTTON 6704
#define IDS_EXTENSIONS_PACK_DIALOG_EXTENSION_ROOT_LABEL 6705
#define IDS_EXTENSIONS_PACK_DIALOG_KEY_FILE_LABEL 6706
#define IDS_EXTENSIONS_PACK_DIALOG_CONFIRM_BUTTON 6707
#define IDS_EXTENSIONS_TOOLBAR_TITLE 6708
#define IDS_EXTENSIONS_SEARCH 6709
#define IDS_EXTENSIONS_SITE_PERMISSIONS 6710
#define IDS_EXTENSIONS_SITE_PERMISSIONS_PAGE_TITLE 6711
#define IDS_EXTENSIONS_SITE_PERMISSIONS_VIEW_ALL_SITES 6712
#define IDS_EXTENSIONS_SITE_PERMISSIONS_ALL_SITES_PAGE_TITLE 6713
#define IDS_EXTENSIONS_PERMITTED_SITES 6714
#define IDS_EXTENSIONS_RESTRICTED_SITES 6715
#define IDS_EXTENSIONS_NO_SITES_ADDED 6716
#define IDS_EXTENSIONS_SITE_PERMISSIONS_ADD_SITE_DIALOG_TITLE 6717
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_SITE_DIALOG_TITLE 6718
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_URL 6719
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_PERMISSIONS_DIALOG_TITLE 6720
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_PERMISSIONS 6721
#define IDS_EXTENSIONS_SITE_PERMISSIONS_DIALOG_INPUT_ERROR 6722
#define IDS_EXTENSIONS_SITE_PERMISSIONS_DIALOG_INPUT_LABEL 6723
#define IDS_EXTENSIONS_EDIT_SHORTCUT 6724
#define IDS_EXTENSIONS_SHORTCUT_NOT_SET 6725
#define IDS_EXTENSIONS_SHORTCUT_SCOPE_LABEL 6726
#define IDS_EXTENSIONS_SHORTCUT_SCOPE_GLOBAL 6727
#define IDS_EXTENSIONS_APPS_TITLE 6728
#define IDS_EXTENSIONS_REMOVE 6729
#define IDS_EXTENSIONS_RUNTIME_HOSTS_DIALOG_TITLE 6730
#define IDS_EXTENSIONS_RUNTIME_HOSTS_DIALOG_INPUT_ERROR 6731
#define IDS_EXTENSIONS_RUNTIME_HOSTS_DIALOG_INPUT_LABEL 6732
#define IDS_EXTENSIONS_SIDEBAR_EXTENSIONS 6733
#define IDS_EXTENSIONS_SIDEBAR_OPEN_CHROME_WEB_STORE 6734
#define IDS_EXTENSIONS_SIDEBAR_KEYBOARD_SHORTCUTS 6735
#define IDS_EXTENSIONS_TOOLBAR_LOAD_UNPACKED 6736
#define IDS_EXTENSIONS_TOOLBAR_LOAD_UNPACKED_DONE 6737
#define IDS_EXTENSIONS_TOOLBAR_PACK 6738
#define IDS_EXTENSIONS_TOOLBAR_UPDATE_NOW 6739
#define IDS_EXTENSIONS_TOOLBAR_UPDATE_NOW_TOOLTIP 6740
#define IDS_EXTENSIONS_TOOLBAR_UPDATE_DONE 6741
#define IDS_EXTENSIONS_TOOLBAR_UPDATING_TOAST 6742
#define IDS_EXTENSIONS_SHORTCUT_SET 6743
#define IDS_EXTENSIONS_TYPE_A_SHORTCUT 6744
#define IDS_EXTENSIONS_SUBPAGE_BUTTON 6745
#define IDS_EXTENSIONS_INCLUDE_START_MODIFIER 6746
#define IDS_EXTENSIONS_TOO_MANY_MODIFIERS 6747
#define IDS_EXTENSIONS_NEED_CHARACTER 6748
#define IDS_NEARBY_CONFIRMATION_PAGE_ADD_CONTACT_SUBTITLE 6759
#define IDS_NEARBY_CONFIRMATION_PAGE_ADD_CONTACT_TITLE 6760
#define IDS_NEARBY_CONFIRMATION_PAGE_TITLE 6761
#define IDS_NEARBY_CONTACT_VISIBILITY_DOWNLOAD_FAILED 6762
#define IDS_NEARBY_CONTACT_VISIBILITY_DOWNLOADING 6763
#define IDS_NEARBY_CONTACT_VISIBILITY_NO_CONTACTS_SUBTITLE 6764
#define IDS_NEARBY_CONTACT_VISIBILITY_NO_CONTACTS_TITLE 6765
#define IDS_NEARBY_CONTACT_VISIBILITY_NUM_UNREACHABLE 6766
#define IDS_NEARBY_CONTACT_VISIBILITY_OWN_ALL 6767
#define IDS_NEARBY_CONTACT_VISIBILITY_OWN_NONE 6768
#define IDS_NEARBY_CONTACT_VISIBILITY_OWN_SOME 6769
#define IDS_NEARBY_CONTACT_VISIBILITY_ZERO_STATE_TEXT 6770
#define IDS_NEARBY_DEVICE_NAME_EMPTY_ERROR 6771
#define IDS_NEARBY_DEVICE_NAME_TOO_LONG_ERROR 6772
#define IDS_NEARBY_DEVICE_NAME_INVALID_CHARACTERS_ERROR 6773
#define IDS_NEARBY_DISCOVERY_PAGE_INFO 6774
#define IDS_NEARBY_DISCOVERY_PAGE_SUBTITLE 6775
#define IDS_NEARBY_DISCOVERY_PAGE_TITLE 6776
#define IDS_NEARBY_DISCOVERY_PAGE_PLACEHOLDER 6777
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_NAME 6778
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_NAME_HELP 6779
#define IDS_NEARBY_ONBOARDING_PAGE_SUBTITLE 6780
#define IDS_NEARBY_ONBOARDING_PAGE_TITLE 6781
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_VISIBILITY 6782
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_VISIBILITY_HELP_ALL_CONTACTS 6783
#define IDS_NEARBY_VISIBILITY_PAGE_MANAGE_CONTACTS 6784
#define IDS_NEARBY_VISIBILITY_PAGE_SUBTITLE 6785
#define IDS_NEARBY_VISIBILITY_PAGE_TITLE 6786
#define IDS_NEARBY_SHARE_FEATURE_NAME 6787
#define IDS_NEARBY_ACTIONS_ACCEPT 6788
#define IDS_NEARBY_ACTIONS_CANCEL 6789
#define IDS_NEARBY_ACTIONS_CLOSE 6790
#define IDS_NEARBY_ACTIONS_CONFIRM 6791
#define IDS_NEARBY_ACTIONS_DECLINE 6792
#define IDS_NEARBY_ACTIONS_NEXT 6793
#define IDS_NEARBY_ACTIONS_REJECT 6794
#define IDS_NEARBY_DEFAULT_DEVICE_NAME 6795
#define IDS_NEARBY_ERROR_CANCELLED 6796
#define IDS_NEARBY_ERROR_CANT_RECEIVE 6797
#define IDS_NEARBY_ERROR_CANT_SHARE 6798
#define IDS_NEARBY_ERROR_NO_RESPONSE 6799
#define IDS_NEARBY_ERROR_TRANSFER_IN_PROGRESS 6800
#define IDS_NEARBY_ERROR_NOT_ENOUGH_SPACE 6801
#define IDS_NEARBY_ERROR_REJECTED 6802
#define IDS_NEARBY_ERROR_SOMETHING_WRONG 6803
#define IDS_NEARBY_ERROR_TIME_OUT 6804
#define IDS_NEARBY_ERROR_TRY_AGAIN 6805
#define IDS_NEARBY_ERROR_UNSUPPORTED_FILE_TYPE 6806
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_APPS 6807
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_APPS 6808
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_IMAGES 6809
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_IMAGES 6810
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_UNKNOWN 6811
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_UNKNOWN 6812
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_VIDEOS 6813
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_VIDEOS 6814
#define IDS_NEARBY_SECURE_CONNECTION_ID 6815
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_ADDRESSES 6816
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_ADDRESSES 6817
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_LINKS 6818
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_LINKS 6819
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_PHONE_NUMBERS 6820
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_PHONE_NUMBERS 6821
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_UNKNOWN 6822
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_UNKNOWN 6823
#define IDS_NEARBY_CAPITALIZED_UNKNOWN_ATTACHMENTS 6824
#define IDS_NEARBY_NOT_CAPITALIZED_UNKNOWN_ATTACHMENTS 6825
#define IDS_NEARBY_HIGH_VISIBILITY_SUB_TITLE 6826
#define IDS_NEARBY_HIGH_VISIBILITY_SUB_TITLE_MINUTES 6827
#define IDS_NEARBY_HIGH_VISIBILITY_SUB_TITLE_SECONDS 6828
#define IDS_NEARBY_HIGH_VISIBILITY_HELP_TEXT 6829
#define IDS_NEARBY_HIGH_VISIBILITY_TIMEOUT_TEXT 6830
#define IDS_NEARBY_HIGH_VISIBILITY_TRANSFER_IN_PROGRESS_ERROR 6831
#define IDS_NEARBY_HIGH_VISIBILITY_TRANSFER_IN_PROGRESS_DESCRIPTION 6832
#define IDS_NEARBY_HIGH_VISIBILITY_CONNECTION_MEDIUM_ERROR 6833
#define IDS_NEARBY_HIGH_VISIBILITY_CONNECTION_MEDIUM_DESCRIPTION 6834
#define IDS_NEARBY_RECEIVE_CONFIRM_PAGE_TITLE 6835
#define IDS_NEARBY_RECEIVE_CONFIRM_PAGE_CONNECTION_ID 6836
#define IDS_NEARBY_PREVIEW_TITLE_MULTIPLE_FILE 6837
#define IDS_NEARBY_ACCOUNT_ROW_LABEL 6838
#define IDS_NEARBY_SETTINGS_HELP_CAPTION 6839
#define IDS_NEARBY_VISIBLITY_ALL_CONTACTS 6840
#define IDS_NEARBY_VISIBLITY_ALL_CONTACTS_DESCRIPTION 6841
#define IDS_NEARBY_VISIBLITY_SOME_CONTACTS 6842
#define IDS_NEARBY_VISIBLITY_SOME_CONTACTS_DESCRIPTION 6843
#define IDS_NEARBY_VISIBLITY_HIDDEN 6844
#define IDS_NEARBY_VISIBLITY_HIDDEN_DESCRIPTION 6845
#define IDS_NEARBY_VISIBLITY_UNKNOWN 6846
#define IDS_NEARBY_VISIBLITY_UNKNOWN_DESCRIPTION 6847
#define IDS_NEARBY_NOTIFICATION_ACTION_COPY_TO_CLIPBOARD 6848
#define IDS_NEARBY_NOTIFICATION_ACTION_OPEN_FOLDER 6849
#define IDS_NEARBY_NOTIFICATION_ACTION_OPEN_NETWORK_LIST 6850
#define IDS_NEARBY_NOTIFICATION_ACTION_OPEN_URL 6851
#define IDS_NEARBY_NOTIFICATION_ACCEPT_ACTION 6852
#define IDS_NEARBY_NOTIFICATION_CONNECTION_REQUEST_MESSAGE 6853
#define IDS_NEARBY_NOTIFICATION_CONNECTION_REQUEST_MESSAGE_WIFI_CREDENTIALS 6854
#define IDS_NEARBY_NOTIFICATION_CONNECTION_REQUEST_TITLE 6855
#define IDS_NEARBY_NOTIFICATION_DECLINE_ACTION 6856
#define IDS_NEARBY_NOTIFICATION_ONBOARDING_MESSAGE 6857
#define IDS_NEARBY_NOTIFICATION_GO_VISIBLE_MESSAGE 6858
#define IDS_NEARBY_NOTIFICATION_ONBOARDING_TITLE 6859
#define IDS_NEARBY_NOTIFICATION_SET_UP_ACTION 6860
#define IDS_NEARBY_NOTIFICATION_GO_VISIBLE_ACTION 6861
#define IDS_NEARBY_NOTIFICATION_VISIBILITY_REMINDER_TITLE 6862
#define IDS_NEARBY_NOTIFICATION_VISIBILITY_REMINDER_MESSAGE 6863
#define IDS_NEARBY_NOTIFICATION_GO_TO_SETTINGS_ACTION 6864
#define IDS_NEARBY_NOTIFICATION_DISMISS_ACTION 6865
#define IDS_NEARBY_NOTIFICATION_RECEIVE_FAILURE_TITLE 6866
#define IDS_NEARBY_NOTIFICATION_RECEIVE_FAILURE_TITLE_WIFI_CREDENTIALS 6867
#define IDS_NEARBY_NOTIFICATION_RECEIVE_PROGRESS_TITLE 6868
#define IDS_NEARBY_NOTIFICATION_RECEIVE_PROGRESS_TITLE_WIFI_CREDENTIALS 6869
#define IDS_NEARBY_NOTIFICATION_RECEIVE_SUCCESS_TITLE 6870
#define IDS_NEARBY_NOTIFICATION_RECEIVE_SUCCESS_TITLE_WIFI_CREDENTIALS 6871
#define IDS_NEARBY_NOTIFICATION_SEND_FAILURE_TITLE 6872
#define IDS_NEARBY_NOTIFICATION_SEND_PROGRESS_TITLE 6873
#define IDS_NEARBY_NOTIFICATION_SEND_SUCCESS_TITLE 6874
#define IDS_NEARBY_NOTIFICATION_SOURCE 6875
#define IDS_NEARBY_NOTIFICATION_SENDER_CANCELLED 6876
#define IDS_WELCOME_NEXT 6877
#define IDS_WELCOME_SKIP 6878
#define IDS_WELCOME_STEPS 6879
#define IDS_WELCOME_BOOKMARK_ADDED 6880
#define IDS_WELCOME_BOOKMARKS_ADDED 6881
#define IDS_WELCOME_BOOKMARK_REMOVED 6882
#define IDS_WELCOME_BOOKMARKS_REMOVED 6883
#define IDS_DEFAULT_BROWSER_CHANGED 6884
#define IDS_WELCOME_GOOGLE_APPS_DESCRIPTION 6885
#define IDS_WELCOME_GOOGLE_GMAIL 6887
#define IDS_WELCOME_GOOGLE_APPS_MAPS 6888
#define IDS_WELCOME_GOOGLE_APPS_NEWS 6889
#define IDS_WELCOME_GOOGLE_APPS_TRANSLATE 6890
#define IDS_WELCOME_GOOGLE_APPS_YOUTUBE 6891
#define IDS_WELCOME_NTP_BACKGROUND_DESCRIPTION 6892
#define IDS_WELCOME_NTP_BACKGROUND_DEFAULT_TITLE 6893
#define IDS_WELCOME_NTP_BACKGROUND_ART_TITLE 6894
#define IDS_WELCOME_NTP_BACKGROUND_LANDSCAPE_TITLE 6895
#define IDS_WELCOME_NTP_BACKGROUND_CITYSCAPE_TITLE 6896
#define IDS_WELCOME_NTP_BACKGROUND_EARTH_TITLE 6897
#define IDS_WELCOME_NTP_BACKGROUND_GEOMETRIC_SHAPES_TITLE 6898
#define IDS_WELCOME_NTP_BACKGROUND_PHOTO_BY_LABEL 6899
#define IDS_WELCOME_NTP_BACKGROUND_PREVIEW_UPDATED 6900
#define IDS_WELCOME_NTP_BACKGROUND_RESET 6901
#define IDS_WELCOME_SET_AS_DEFAULT_HEADER 6902
#define IDS_WELCOME_SET_AS_DEFAULT_SUB_HEADER 6903
#define IDS_WELCOME_SET_AS_DEFAULT_SET_AS_DEFAULT 6904
#define IDS_WELCOME_LANDING_TITLE 6905
#define IDS_WELCOME_LANDING_DESCRIPTION 6906
#define IDS_WELCOME_LANDING_NEW_USER 6907
#define IDS_WELCOME_LANDING_EXISTING_USER 6908
#define IDS_WELCOME_SIGNIN_VIEW_HEADER 6909
#define IDS_WELCOME_SIGNIN_VIEW_SUB_HEADER 6910
#define IDS_WELCOME_SIGNIN_VIEW_SIGNIN 6911
#define IDS_WHATS_NEW_TITLE 6912
#define IDS_UTILITY_PROCESS_PRINTING_SERVICE_NAME 6913
#define IDS_UTILITY_PROCESS_PRINT_BACKEND_SERVICE_NAME 6914
#define IDS_PRINT_INVALID_PRINTER_SETTINGS 6915
#define IDS_PRINT_PREVIEW_TITLE 6916
#define IDS_PRINT_PREVIEW_DESCRIPTION 6917
#define IDS_PRINT_PREVIEW_LOADING 6918
#define IDS_PRINT_PREVIEW_FAILED 6919
#define IDS_PRINT_PREVIEW_INVALID_PRINTER_SETTINGS 6920
#define IDS_PRINT_PREVIEW_PRINT_BUTTON 6921
#define IDS_PRINT_PREVIEW_SAVE_BUTTON 6922
#define IDS_PRINT_PREVIEW_PRINTING 6923
#define IDS_PRINT_PREVIEW_SAVING 6924
#define IDS_PRINT_PREVIEW_OPTION_ALL_PAGES 6925
#define IDS_PRINT_PREVIEW_OPTION_ODD_PAGES 6926
#define IDS_PRINT_PREVIEW_OPTION_EVEN_PAGES 6927
#define IDS_PRINT_PREVIEW_OPTION_CUSTOM_PAGES 6928
#define IDS_PRINT_PREVIEW_DESTINATION_LABEL 6929
#define IDS_PRINT_PREVIEW_OPTION_BW 6930
#define IDS_PRINT_PREVIEW_OPTION_COLLATE 6931
#define IDS_PRINT_PREVIEW_OPTION_COLOR 6932
#define IDS_PRINT_PREVIEW_OPTION_LANDSCAPE 6933
#define IDS_PRINT_PREVIEW_OPTION_PORTRAIT 6934
#define IDS_PRINT_PREVIEW_OPTION_TWO_SIDED 6935
#define IDS_PRINT_PREVIEW_PRINT_ON_BOTH_SIDES_LABEL 6936
#define IDS_PRINT_PREVIEW_OPTION_LONG_EDGE 6937
#define IDS_PRINT_PREVIEW_OPTION_SHORT_EDGE 6938
#define IDS_PRINT_PREVIEW_PAGES_LABEL 6939
#define IDS_PRINT_PREVIEW_LAYOUT_LABEL 6940
#define IDS_PRINT_PREVIEW_COPIES_LABEL 6941
#define IDS_PRINT_PREVIEW_SCALING_LABEL 6942
#define IDS_PRINT_PREVIEW_OPTION_DEFAULT_SCALING 6943
#define IDS_PRINT_PREVIEW_OPTION_CUSTOM_SCALING 6944
#define IDS_PRINT_PREVIEW_PAGES_PER_SHEET_LABEL 6945
#define IDS_PRINT_PREVIEW_EXAMPLE_PAGE_RANGE_TEXT 6946
#define IDS_PRINT_PREVIEW_PRINT_TO_PDF 6947
#define IDS_PRINT_PREVIEW_SHEET_SUMMARY_LABEL 6948
#define IDS_PRINT_PREVIEW_PAGE_SUMMARY_LABEL 6949
#define IDS_PRINT_PREVIEW_PAGE_RANGE_SYNTAX_INSTRUCTION 6950
#define IDS_PRINT_PREVIEW_PAGE_RANGE_LIMIT_INSTRUCTION_WITH_VALUE 6951
#define IDS_PRINT_PREVIEW_COPIES_INSTRUCTION 6952
#define IDS_PRINT_PREVIEW_SCALING_INSTRUCTION 6953
#define IDS_PRINT_PREVIEW_PRINT_PAGES_LABEL 6954
#define IDS_PRINT_PREVIEW_OPTIONS_LABEL 6955
#define IDS_PRINT_PREVIEW_OPTION_HEADER_FOOTER 6956
#define IDS_PRINT_PREVIEW_OPTION_FIT_TO_PAGE 6957
#define IDS_PRINT_PREVIEW_OPTION_FIT_TO_PAPER 6958
#define IDS_PRINT_PREVIEW_OPTION_BACKGROUND_COLORS_AND_IMAGES 6959
#define IDS_PRINT_PREVIEW_OPTION_SELECTION_ONLY 6960
#define IDS_PRINT_PREVIEW_OPTION_RASTERIZE 6961
#define IDS_PRINT_PREVIEW_MARGINS_LABEL 6962
#define IDS_PRINT_PREVIEW_DEFAULT_MARGINS 6963
#define IDS_PRINT_PREVIEW_NO_MARGINS 6964
#define IDS_PRINT_PREVIEW_CUSTOM_MARGINS 6965
#define IDS_PRINT_PREVIEW_MINIMUM_MARGINS 6966
#define IDS_PRINT_PREVIEW_TOP_MARGIN_LABEL 6967
#define IDS_PRINT_PREVIEW_BOTTOM_MARGIN_LABEL 6968
#define IDS_PRINT_PREVIEW_LEFT_MARGIN_LABEL 6969
#define IDS_PRINT_PREVIEW_RIGHT_MARGIN_LABEL 6970
#define IDS_PRINT_PREVIEW_MEDIA_SIZE_LABEL 6971
#define IDS_PRINT_PREVIEW_DPI_LABEL 6972
#define IDS_PRINT_PREVIEW_NON_ISOTROPIC_DPI_ITEM_LABEL 6973
#define IDS_PRINT_PREVIEW_DPI_ITEM_LABEL 6974
#define IDS_PRINT_PREVIEW_DESTINATION_SEARCH_TITLE 6975
#define IDS_PRINT_PREVIEW_SEARCH_BOX_PLACEHOLDER 6976
#define IDS_PRINT_PREVIEW_NO_DESTINATIONS_MESSAGE 6977
#define IDS_PRINT_PREVIEW_PRINT_DESTINATIONS_TITLE 6978
#define IDS_PRINT_PREVIEW_MANAGE 6979
#define IDS_PRINT_PREVIEW_SEE_MORE 6980
#define IDS_PRINT_PREVIEW_SEE_MORE_DESTINATIONS_LABEL 6981
#define IDS_PRINT_PREVIEW_EXTENSION_DESTINATION_ICON_TOOLTIP 6982
#define IDS_MORE_OPTIONS_LABEL 6983
#define IDS_PRINT_PREVIEW_COULD_NOT_PRINT 6984
#define IDS_PRINT_PREVIEW_ADVANCED_SETTINGS_SEARCH_BOX_PLACEHOLDER 6985
#define IDS_PRINT_PREVIEW_ADVANCED_SETTINGS_DIALOG_TITLE 6986
#define IDS_PRINT_PREVIEW_NO_ADVANCED_SETTINGS_MATCH_SEARCH_HINT 6987
#define IDS_PRINT_PREVIEW_ADVANCED_SETTINGS_DIALOG_CONFIRM 6988
#define IDS_PRINT_PREVIEW_NEW_SHOW_ADVANCED_OPTIONS 6989
#define IDS_PRINT_PREVIEW_BUTTON_SELECT 6990
#define IDS_PRINT_PREVIEW_BUTTON_GO_BACK 6991
#define IDS_PRINT_PREVIEW_RESOLVE_EXTENSION_USB_DIALOG_TITLE 6992
#define IDS_PRINT_PREVIEW_RESOLVE_EXTENSION_USB_PERMISSION_MESSAGE 6993
#define IDS_PRINT_PREVIEW_RESOLVE_EXTENSION_USB_ERROR_MESSAGE 6994
#define IDS_PRINT_PREVIEW_MANAGED_SETTINGS_TEXT 6995
#define IDS_PRINT_PREVIEW_SYSTEM_DIALOG_OPTION 7019
#define IDS_PRINT_PREVIEW_OPENING_PDF_IN_PREVIEW_APP 7020
#define IDS_PRINT_PREVIEW_OPEN_PDF_IN_PREVIEW_APP 7021
#define IDS_DEFAULT_PRINT_DOCUMENT_TITLE 7022
#define IDS_PRINT_SPOOL_FAILED_TITLE_TEXT 7023
#define IDS_PRINT_SPOOL_FAILED_ERROR_TEXT 7024
#define IDS_BACK_BUTTON_AUTHENTICATOR_REQUEST_DIALOG 7106
#define IDS_BACKGROUND_CRASHED_APP_BALLOON_MESSAGE 7109
#define IDS_BACKGROUND_CRASHED_EXTENSION_BALLOON_MESSAGE 7110
#define IDS_BACKGROUND_APP_NOT_INSTALLED 7111
#define IDS_PERMISSIONS_REQUESTED_SCREENREADER_ANNOUNCEMENT 7112
#define IDS_PERMISSIONS_EXPIRED_SCREENREADER_ANNOUNCEMENT 7113
#define IDS_PERMISSIONS_BUBBLE_PROMPT 7114
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ACCESSIBLE_TITLE_ONE_PERM 7115
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ACCESSIBLE_TITLE_TWO_PERMS 7116
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ACCESSIBLE_TITLE_TWO_PERMS_MORE 7117
#define IDS_PERMISSIONS_BUBBLE_PROMPT_THIS_FILE 7118
#define IDS_PERMISSION_CUSTOMIZE 7119
#define IDS_ALTERNATE_NAV_URL_VIEW_LABEL 7120
#define IDS_DOWNLOAD_TITLE 7121
#define IDS_TAB_LOADING_TITLE 7122
#define IDS_HOVER_CARD_FILE_URL_SOURCE 7123
#define IDS_HOVER_CARD_BLOB_URL_SOURCE 7124
#define IDS_HOVER_CARD_CRASHED_TITLE 7125
#define IDS_HISTORY_SEARCH_PROMPT 7126
#define IDS_HISTORY_DELETE 7127
#define IDS_HISTORY_ITEMS_SELECTED 7128
#define IDS_HISTORY_ITEMS_UNSELECTED 7129
#define IDS_HISTORY_HISTORY_MENU_DESCRIPTION 7130
#define IDS_HISTORY_HISTORY_MENU_ITEM 7131
#define IDS_HISTORY_NO_SYNCED_RESULTS 7132
#define IDS_HISTORY_OPEN_TABS_MENU_ITEM 7133
#define IDS_HISTORY_SIGN_IN_BUTTON 7134
#define IDS_HISTORY_SIGN_IN_PROMO 7135
#define IDS_HISTORY_SIGN_IN_PROMO_DESC 7136
#define IDS_EDIT 297
#define IDS_CLEAR_SEARCH 7137
#define IDS_CONFIRM 7138
#define IDS_DISABLE 7139
#define IDS_SEARCH_CLEARED 7140
#define IDS_SEARCH_RESULTS 7141
#define IDS_SEARCH_RESULTS_SINGULAR 7142
#define IDS_SEARCH_RESULTS_PLURAL 7143
#define IDS_SEARCH_RESULT_BUBBLE_TEXT 7144
#define IDS_SEARCH_RESULTS_BUBBLE_TEXT 7145
#define IDS_SEARCH_NO_RESULTS 7146
#define IDS_SHOW_BUBBLE_INACTIVE_DESCRIPTION 7147
#define IDS_FOCUS_HELP_BUBBLE_DESCRIPTION 7148
#define IDS_FOCUS_HELP_BUBBLE_TOGGLE_DESCRIPTION 7149
#define IDS_FOCUS_HELP_BUBBLE_TUTORIAL_DESCRIPTION 7150
#define IDS_CONTENT_CONTEXT_INSPECTELEMENT 7151
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_DIALOG_TITLE 7152
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_ENABLE 7153
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_DISABLE 7154
#define IDS_CONTENT_CONTEXT_BACK 7155
#define IDS_CONTENT_CONTEXT_FORWARD 7156
#define IDS_CONTENT_CONTEXT_SAVEPAGEAS 7157
#define IDS_CONTENT_CONTEXT_PRINT 7158
#define IDS_CONTENT_CONTEXT_VIEWPAGESOURCE 7159
#define IDS_CONTENT_CONTEXT_OPENLINKWITH 7160
#define IDS_CONTENT_CONTEXT_OPENLINKWITH_CONFIGURE 7161
#define IDS_CONTENT_CONTEXT_INSPECTBACKGROUNDPAGE 7162
#define IDS_CONTENT_CONTEXT_READ_ANYTHING 7163
#define IDS_CONTENT_CONTEXT_RELOAD 7164
#define IDS_CONTENT_CONTEXT_RESTART_APP 7165
#define IDS_CONTENT_CONTEXT_RELOAD_PACKAGED_APP 7166
#define IDS_CONTENT_CONTEXT_TRANSLATE 7167
#define IDS_CONTENT_CONTEXT_EXIT_FULLSCREEN 7168
#define IDS_CONTENT_CONTEXT_RELOADFRAME 7169
#define IDS_CONTENT_CONTEXT_VIEWFRAMESOURCE 7170
#define IDS_CONTENT_CONTEXT_OPENLINKNEWTAB 7171
#define IDS_CONTENT_CONTEXT_OPENLINKNEWWINDOW 7172
#define IDS_CONTENT_CONTEXT_OPENLINKOFFTHERECORD 7173
#define IDS_CONTENT_CONTEXT_OPENLINKINPROFILES 7174
#define IDS_CONTENT_CONTEXT_OPENLINKINPROFILE 7175
#define IDS_CONTENT_CONTEXT_OPENLINKBOOKMARKAPP 7176
#define IDS_CONTENT_CONTEXT_OPENLINKBOOKMARKAPP_SAMEAPP 7177
#define IDS_CONTENT_CONTEXT_SAVELINKAS 7178
#define IDS_CONTENT_CONTEXT_COPYLINKLOCATION 7179
#define IDS_CONTENT_CONTEXT_COPYEMAILADDRESS 7180
#define IDS_CONTENT_CONTEXT_COPYLINKTEXT 7181
#define IDS_CONTENT_CONTEXT_COPYLINKTOTEXT 7182
#define IDS_CONTENT_CONTEXT_REMOVELINKTOTEXT 7183
#define IDS_CONTENT_CONTEXT_RESHARELINKTOTEXT 7184
#define IDS_CONTENT_CONTEXT_SAVEIMAGEAS 7185
#define IDS_CONTENT_CONTEXT_COPYIMAGELOCATION 7186
#define IDS_CONTENT_CONTEXT_COPYIMAGE 7187
#define IDS_CONTENT_CONTEXT_OPENIMAGENEWTAB 7188
#define IDS_CONTENT_CONTEXT_OPEN_ORIGINAL_IMAGE_NEW_TAB 7189
#define IDS_CONTENT_CONTEXT_LOAD_IMAGE 7190
#define IDS_CONTENT_CONTEXT_LOOP 7191
#define IDS_CONTENT_CONTEXT_CONTROLS 7192
#define IDS_CONTENT_CONTEXT_ROTATECW 7193
#define IDS_CONTENT_CONTEXT_ROTATECCW 7194
#define IDS_CONTENT_CONTEXT_SAVEVIDEOAS 7195
#define IDS_CONTENT_CONTEXT_COPYVIDEOLOCATION 7196
#define IDS_CONTENT_CONTEXT_OPENVIDEONEWTAB 7197
#define IDS_CONTENT_CONTEXT_SAVEAUDIOAS 7198
#define IDS_CONTENT_CONTEXT_COPYAUDIOLOCATION 7199
#define IDS_CONTENT_CONTEXT_OPENAUDIONEWTAB 7200
#define IDS_CONTENT_CONTEXT_PICTUREINPICTURE 7201
#define IDS_CONTENT_CONTEXT_UNDO 7202
#define IDS_CONTENT_CONTEXT_REDO 7203
#define IDS_CONTENT_CONTEXT_CUT 7204
#define IDS_CONTENT_CONTEXT_COPY 7205
#define IDS_CONTENT_CONTEXT_PASTE 7206
#define IDS_CONTENT_CONTEXT_PASTE_AND_MATCH_STYLE 7207
#define IDS_CONTENT_CONTEXT_ADD_TO_DICTIONARY 7208
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_MENU_OPTION 7209
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_SEND 7210
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_SEND_ONCE 7211
#define IDS_CONTENT_CONTEXT_SPELLING_ASK_GOOGLE 7212
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_TITLE 7213
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_ENABLE 7214
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_DISABLE 7215
#define IDS_CONTENT_CONTEXT_SPELLING_CHECKING 7216
#define IDS_CONTENT_CONTEXT_SPELLING_NO_SUGGESTIONS_FROM_GOOGLE 7217
#define IDS_CONTENT_CONTEXT_SELECTALL 7218
#define IDS_CONTENT_CONTEXT_SEARCHWEBFOR 7219
#define IDS_CONTENT_CONTEXT_SEARCHWEBFORIMAGE 7220
#define IDS_CONTENT_CONTEXT_SEARCHLENSFORIMAGE 7221
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH 7222
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT1 7223
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT2 7224
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT3 7225
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT4 7226
#define IDS_CONTENT_CONTEXT_GOTOURL 7227
#define IDS_CONTENT_CONTEXT_GENERATEPASSWORD 7228
#define IDS_SHARE_MENU_TITLE 7231
#define IDS_CONTENT_CONTEXT_PLUGIN_RUN 7232
#define IDS_CONTENT_CONTEXT_PLUGIN_HIDE 7233
#define IDS_CONTENT_CONTEXT_ENABLE_FLASH 7234
#define IDS_CONTENT_CONTEXT_LANGUAGE_SETTINGS 7235
#define IDS_NEW_TAB 277
#define IDS_NEW_INCOGNITO_TAB 7239
#define IDS_SHOW_AS_TAB 182
#define IDS_NEW_WINDOW 278
#define IDS_NEW_INCOGNITO_WINDOW 279
#define IDS_PIN_TO_START_SCREEN 7240
#define IDS_EDIT2 7241
#define IDS_CUT 298
#define IDS_COPY 299
#define IDS_PASTE 300
#define IDS_DELETE 7242
#define IDS_FIND 291
#define IDS_SAVE_PAGE 292
#define IDS_DISTILL_PAGE 7243
#define IDS_EXIT_DISTILLED_PAGE 7244
#define IDS_MORE_TOOLS_MENU 296
#define IDS_ZOOM_MENU 288
#define IDS_ZOOM_MENU2 7245
#define IDS_ZOOM_PLUS 7246
#define IDS_ZOOM_PLUS2 290
#define IDS_ZOOM_NORMAL 7247
#define IDS_ZOOM_MINUS 7248
#define IDS_ZOOM_MINUS2 289
#define IDS_COPY_URL 7249
#define IDS_OPEN_IN_APP_WINDOW 7250
#define IDS_MOVE_TAB_TO_NEW_WINDOW 7251
#define IDS_TOGGLE_QUICK_COMMANDS 7252
#define IDS_SEARCH_TABS 7253
#define IDS_ACCNAME_ZOOM_PLUS2 7255
#define IDS_ACCNAME_ZOOM_MINUS2 7256
#define IDS_VIEW_SOURCE 7257
#define IDS_FEEDBACK 304
#define IDS_DEV_TOOLS 295
#define IDS_DEV_TOOLS_CONSOLE 7259
#define IDS_DEV_TOOLS_DEVICES 7260
#define IDS_DEV_TOOLS_ELEMENTS 7258
#define IDS_TASK_MANAGER 294
#define IDS_TAKE_SCREENSHOT 7261
#define IDS_RESTORE_ALL_TABS 7262
#define IDS_RESTORE_TAB 7263
#define IDS_REOPEN_WINDOW 7264
#define IDS_REOPEN_GROUP 7265
#define IDS_RESTORE_WINDOW 7266
#define IDS_RESTORE_GROUP 7267
#define IDS_NAME_WINDOW 7268
#define IDS_PREFERENCES 105
#define IDS_TOS_NOTIFICATION_TITLE 7270
#define IDS_TOS_NOTIFICATION_BODY_TEXT 7271
#define IDS_TOS_NOTIFICATION_ACK_BUTTON_TEXT 7272
#define IDS_TOS_NOTIFICATION_REVIEW_BUTTON_TEXT 7273
#define IDS_TOS_NOTIFICATION_LINK 7274
#define IDS_HELP_MENU 305
#define IDS_MANAGED 7275
#define IDS_MANAGED_BY 7276
#define IDS_CHROME_TIPS 7277
#define IDS_CHROME_WHATS_NEW 7278
#define IDS_IMPORT_SETTINGS_MENU_LABEL 287
#define IDS_PROFILING_ENABLED 7279
#define IDS_FULLSCREEN 7280
#define IDS_CLEAR_BROWSING_DATA 106
#define IDS_SHOW_DOWNLOADS 284
#define IDS_SHOW_EXTENSIONS 293
#define IDS_SETTINGS 301
#define IDS_OPTIONS 7281
#define IDS_HELP_PAGE 303
#define IDS_BETA_FORUM 7282
#define IDS_GET_HELP 7283
#define IDS_EXIT 7284
#define IDS_AUTOCOMPLETE_MATCH_DESCRIPTION_SEPARATOR 362
#define IDS_MANAGE_SEARCH_ENGINES 7285
#define IDS_MANAGE_SEARCH_ENGINES_AND_SITE_SEARCH 7286
#define IDS_SEARCH_ENGINES_EDITOR_KEYWORD_COLUMN 7287
#define IDS_SEARCH_ENGINES_EDITOR_DESCRIPTION_COLUMN 7288
#define IDS_SEARCH_ENGINES_EDITOR_DEFAULT_ENGINE 7289
#define IDS_ACCNAME_DOWNLOADS_BAR 7290
#define IDS_HIDE_DOWNLOADS 7291
#define IDS_SHOW_ALL_DOWNLOADS 7292
#define IDS_DOWNLOAD_STARTED 7293
#define IDS_DOWNLOAD_INTERRUPTED_STATUS 7294
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_ACCESS_DENIED 7295
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_ACCESS_DENIED 7296
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_PATH_TOO_LONG 7297
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_PATH_TOO_LONG 7298
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_DISK_FULL 7299
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_DISK_FULL 7300
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FILE_TOO_LARGE 7301
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FILE_TOO_LARGE 7302
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_TEMPORARY_PROBLEM 7303
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_TEMPORARY_PROBLEM 7304
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_VIRUS 7305
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_VIRUS 7306
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_BLOCKED 7307
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_BLOCKED 7308
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_CONTENT_LENGTH_MISMATCH 7309
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_CONTENT_LENGTH_MISMATCH 7310
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SECURITY_CHECK_FAILED 7311
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SECURITY_CHECK_FAILED 7312
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FILE_TOO_SHORT 7313
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FILE_TOO_SHORT 7314
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FILE_SAME_AS_SOURCE 7315
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FILE_SAME_AS_SOURCE 7316
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NETWORK_TIMEOUT 7317
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NETWORK_TIMEOUT 7318
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NETWORK_DISCONNECTED 7319
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NETWORK_DISCONNECTED 7320
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SERVER_DOWN 7321
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SERVER_DOWN 7322
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NETWORK_ERROR 7323
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NETWORK_ERROR 7324
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NO_FILE 7325
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NO_FILE 7326
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SERVER_PROBLEM 7327
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SERVER_PROBLEM 7328
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_WEB_DRIVE_ERROR 7329
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SHUTDOWN 7330
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SHUTDOWN 7331
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_CRASH 7332
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_CRASH 7333
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_UNAUTHORIZED 7334
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_UNAUTHORIZED 7335
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SERVER_CERT_PROBLEM 7336
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SERVER_CERT_PROBLEM 7337
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FORBIDDEN 7338
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FORBIDDEN 7339
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_UNREACHABLE 7340
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_UNREACHABLE 7341
#define IDS_DOWNLOAD_NOTIFICATION_LABEL_OPEN_WHEN_COMPLETE 7342
#define IDS_DOWNLOAD_NOTIFICATION_LABEL_OPEN 7343
#define IDS_DOWNLOAD_STATUS_STARTING 7344
#define IDS_DOWNLOAD_STATUS_IN_PROGRESS 7354
#define IDS_DOWNLOAD_STATUS_SIZES 7355
#define IDS_DOWNLOAD_STATUS_OPEN_IN 7356
#define IDS_DOWNLOAD_STATUS_OPEN_WHEN_COMPLETE 7357
#define IDS_DOWNLOAD_STATUS_OPENING 7358
#define IDS_DOWNLOAD_STATUS_IN_PROGRESS_SHORT 7359
#define IDS_DOWNLOAD_STATUS_CANCELLED 7360
#define IDS_DOWNLOAD_STATUS_REMOVED 7361
#define IDS_DOWNLOAD_STATUS_UPLOADING 7362
#define IDS_DOWNLOAD_STATUS_UPLOADED 7363
#define IDS_DOWNLOAD_STATUS_UPLOAD_INTERRUPTED 7364
#define IDS_DOWNLOAD_STATUS_INTERRUPTED 7365
#define IDS_DOWNLOAD_UNCONFIRMED_PREFIX 7366
#define IDS_PROMPT_DANGEROUS_DOWNLOAD 7367
#define IDS_PROMPT_DANGEROUS_DOWNLOAD_EXTENSION 7368
#define IDS_PROMPT_UNCOMMON_DOWNLOAD_CONTENT 7369
#define IDS_PROMPT_UNCOMMON_DOWNLOAD_CONTENT_IN_ADVANCED_PROTECTION 7370
#define IDS_PROMPT_DEEP_SCANNING_DOWNLOAD 7371
#define IDS_PROMPT_DEEP_SCANNING_DOWNLOAD_SHORT 7372
#define IDS_PROMPT_DEEP_SCANNING_APP_DOWNLOAD 7373
#define IDS_PROMPT_CONFIRM_MIXED_CONTENT_DOWNLOAD 7374
#define IDS_PROMPT_DOWNLOAD_BLOCKED_TOO_LARGE 7375
#define IDS_PROMPT_DOWNLOAD_BLOCKED_PASSWORD_PROTECTED 7376
#define IDS_PROMPT_DOWNLOAD_DEEP_SCANNED_SAFE 7377
#define IDS_PROMPT_DOWNLOAD_SENSITIVE_CONTENT_WARNING 7378
#define IDS_PROMPT_DOWNLOAD_SENSITIVE_CONTENT_BLOCKED 7379
#define IDS_PROMPT_DOWNLOAD_DEEP_SCANNED_OPENED_DANGEROUS 7380
#define IDS_PROMPT_DOWNLOAD_MIXED_CONTENT_WARNING 7381
#define IDS_PROMPT_DOWNLOAD_MIXED_CONTENT_BLOCKED 7382
#define IDS_PROMPT_APP_DEEP_SCANNING 7383
#define IDS_PROMPT_DEEP_SCANNING 7384
#define IDS_BLOCK_REASON_UNCOMMON_DOWNLOAD 7385
#define IDS_BLOCK_REASON_UNCOMMON_DOWNLOAD_IN_ADVANCED_PROTECTION 7386
#define IDS_BLOCK_REASON_GENERIC_DOWNLOAD 7387
#define IDS_BLOCK_REASON_MIXED_CONTENT 7388
#define IDS_BLOCK_REASON_DEEP_SCANNING 7389
#define IDS_BLOCK_REASON_ACCOUNT_COMPROMISE 7390
#define IDS_DEEP_SCANNED_SAFE_DESCRIPTION 7391
#define IDS_DEEP_SCANNED_OPENED_DANGEROUS_DESCRIPTION 7392
#define IDS_BLOCK_REASON_SENSITIVE_CONTENT_WARNING 7393
#define IDS_SENSITIVE_CONTENT_BLOCKED_DESCRIPTION 7394
#define IDS_BLOCKED_TOO_LARGE_DESCRIPTION 7395
#define IDS_BLOCKED_PASSWORD_PROTECTED_DESCRIPTION 7396
#define IDS_CONFIRM_KEEP_DANGEROUS_DOWNLOAD_TITLE 7400
#define IDS_KEEP_DANGEROUS_DOWNLOAD_TITLE 7401
#define IDS_KEEP_UNCOMMON_DOWNLOAD_TITLE 7402
#define IDS_PROMPT_CONFIRM_KEEP_DANGEROUS_DOWNLOAD 7403
#define IDS_PROMPT_CONFIRM_KEEP_MALICIOUS_DOWNLOAD_BODY 7404
#define IDS_CONFIRM_DOWNLOAD_AGAIN 7405
#define IDS_CONFIRM_DOWNLOAD 7406
#define IDS_CONFIRM_DOWNLOAD_RESTORE 7407
#define IDS_CONTINUE_EXTENSION_DOWNLOAD 7408
#define IDS_DISCARD_DOWNLOAD 7409
#define IDS_OPEN_DOWNLOAD_NOW 7410
#define IDS_SCAN_DOWNLOAD 7411
#define IDS_REVIEW_DOWNLOAD 7412
#define IDS_DOWNLOAD_LINK_PAUSE 7413
#define IDS_DOWNLOAD_SEARCH 7414
#define IDS_DOWNLOAD_NO_DOWNLOADS 7415
#define IDS_DOWNLOAD_ITEM_DROPDOWN_BUTTON_ACCESSIBLE_TEXT 7416
#define IDS_DOWNLOAD_LINK_RESUME 7417
#define IDS_DOWNLOAD_LINK_REMOVE 7418
#define IDS_DOWNLOAD_LINK_REMOVE_ARIA_LABEL 7419
#define IDS_DOWNLOAD_LINK_CANCEL 7420
#define IDS_DOWNLOAD_LINK_RETRY 7421
#define IDS_DOWNLOAD_LINK_SHOW_IN_WEB_DRIVE 7422
#define IDS_DOWNLOAD_LINK_SHOW 7423
#define IDS_DOWNLOAD_TAB_CANCELLED 7424
#define IDS_DOWNLOAD_FILE_REMOVED 7425
#define IDS_DOWNLOAD_TAB_PROGRESS_STATUS_TIME_UNKNOWN 7426
#define IDS_DOWNLOAD_TAB_PROGRESS_STATUS 7427
#define IDS_DOWNLOAD_TAB_PROGRESS_SIZE 7428
#define IDS_DOWNLOAD_PROGRESS_PAUSED 7429
#define IDS_DOWNLOAD_LINK_CLEAR_ALL 7430
#define IDS_DOWNLOAD_LINK_OPEN_DOWNLOADS_FOLDER 7431
#define IDS_DOWNLOAD_MORE_ACTIONS 7432
#define IDS_DOWNLOAD_ACTION_MENU_DESCRIPTION 7433
#define IDS_DOWNLOAD_BY_EXTENSION_URL 7434
#define IDS_DOWNLOAD_IN_INCOGNITO 7435
#define IDS_UNDO_DESCRIPTION 7436
#define IDS_DOWNLOAD_UNDO 7437
#define IDS_DOWNLOAD_TOAST_REMOVED_FROM_LIST 7438
#define IDS_DOWNLOAD_TOAST_CLEARED_ALL 7439
#define IDS_DOWNLOAD_STATUS_IN_PROGRESS_ACCESSIBLE_ALERT 7440
#define IDS_DOWNLOAD_STATUS_PERCENT_COMPLETE_ACCESSIBLE_ALERT 7441
#define IDS_DOWNLOAD_STATUS_TIME_REMAINING_ACCESSIBLE_ALERT 7442
#define IDS_DOWNLOAD_FAILED_ACCESSIBLE_ALERT 7443
#define IDS_DOWNLOAD_CANCELLED_ACCESSIBLE_ALERT 7444
#define IDS_DOWNLOAD_COMPLETE_ACCESSIBLE_ALERT 7445
#define IDS_PROMPT_APP_DEEP_SCANNING_ACCESSIBLE_ALERT 7446
#define IDS_PROMPT_DEEP_SCANNING_ACCESSIBLE_ALERT 7447
#define IDS_DEEP_SCANNING_ACCESSIBLE_ALERT 7448
#define IDS_PROMPT_DOWNLOAD_MIXED_CONTENT_BLOCKED_ACCESSIBLE_ALERT 7449
#define IDS_DOWNLOAD_NOTIFICATION_COPY_TO_CLIPBOARD 7450
#define IDS_DOWNLOAD_MENU_SHOW 7451
#define IDS_DOWNLOAD_MENU_OPEN_WHEN_COMPLETE 7452
#define IDS_DOWNLOAD_MENU_OPEN 7453
#define IDS_DOWNLOAD_MENU_ALWAYS_OPEN_TYPE 7454
#define IDS_DOWNLOAD_MENU_PLATFORM_OPEN 7455
#define IDS_DOWNLOAD_MENU_PLATFORM_OPEN_ALWAYS 7456
#define IDS_DOWNLOAD_MENU_CANCEL 7457
#define IDS_DOWNLOAD_MENU_PAUSE_ITEM 7458
#define IDS_DOWNLOAD_MENU_RESUME_ITEM 7459
#define IDS_DOWNLOAD_MENU_DISCARD 7460
#define IDS_DOWNLOAD_MENU_KEEP 7461
#define IDS_DOWNLOAD_MENU_LEARN_MORE_SCANNING 7462
#define IDS_DOWNLOAD_MENU_LEARN_MORE_INTERRUPTED 7463
#define IDS_DOWNLOAD_MENU_LEARN_MORE_MIXED_CONTENT 7464
#define IDS_DOWNLOAD_MENU_DEEP_SCAN 7466
#define IDS_CUSTOMIZE_TOUCH_BAR 7467
#define IDS_TOUCH_BAR_GOOGLE_SEARCH 7468
#define IDS_TOUCH_BAR_NO_DEFAULT_SEARCH 7469
#define IDS_TOUCH_BAR_SEARCH 7470
#define IDS_TOUCH_BAR_BACK_FORWARD_CUSTOMIZATION_LABEL 7471
#define IDS_TOUCH_BAR_STOP_RELOAD_CUSTOMIZATION_LABEL 7472
#define IDS_TOUCH_BAR_HOME_CUSTOMIZATION_LABEL 7473
#define IDS_TOUCH_BAR_BOOKMARK_CUSTOMIZATION_LABEL 7474
#define IDS_TOUCH_BAR_NEW_TAB_CUSTOMIZATION_LABEL 7475
#define IDS_TOUCH_BAR_URL_CUSTOMIZATION_LABEL 7476
#define IDS_ABANDON_DOWNLOAD_DIALOG_TITLE 7477
#define IDS_ABANDON_DOWNLOAD_DIALOG_CONTINUE_BUTTON 7478
#define IDS_ABANDON_DOWNLOAD_DIALOG_INCOGNITO_MESSAGE 7479
#define IDS_ABANDON_DOWNLOAD_DIALOG_GUEST_MESSAGE 7480
#define IDS_ABANDON_DOWNLOAD_DIALOG_EXIT_BUTTON 7481
#define IDS_DOWNLOAD_BUBBLE_HEADER_TEXT 7482
#define IDS_DOWNLOAD_BUBBLE_FOOTER_LINK 7483
#define IDS_DOWNLOAD_BUBBLE_DOWNLOAD_SEPERATOR 7484
#define IDS_DOWNLOAD_BUBBLE_DOWNLOAD_SYMBOL 7485
#define IDS_DOWNLOAD_BUBBLE_STATUS_RESUMING 7486
#define IDS_DOWNLOAD_BUBBLE_STATUS_DONE 7487
#define IDS_DOWNLOAD_BUBBLE_STATUS_BLOCKED 7488
#define IDS_DOWNLOAD_BUBBLE_STATUS_MALWARE 7489
#define IDS_DOWNLOAD_BUBBLE_CHECKBOX_BYPASS 7490
#define IDS_DOWNLOAD_BUBBLE_CONTINUE 7491
#define IDS_DOWNLOAD_BUBBLE_DELETE 7492
#define IDS_DOWNLOAD_BUBBLE_SCAN 7493
#define IDS_DOWNLOAD_BUBBLE_OPEN 7494
#define IDS_DOWNLOAD_BUBBLE_OPEN_NOW 7495
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_DISK_FULL 7496
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_DISK_FULL 7497
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_PATH_TOO_LONG 7498
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_PATH_TOO_LONG 7499
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_NEEDS_PERMISSION 7500
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_FILE_TOO_LARGE 7501
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_FILE_TOO_LARGE 7502
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_UNFINISHED 7503
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_BLOCKED_ORGANIZATION 7504
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_BLOCKED_ORGANIZATION 7505
#define IDS_DOWNLOAD_BUBBLE_WARNING_SUBPAGE_SUMMARY_BLOCKED_ORGANIZATION 7506
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_WRONG 7507
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_NETWORK_ERROR 7508
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_SITE_UNAVAILABLE 7509
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_FILE_UNAVAILABLE 7510
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_FILE_UNAVAILABLE 7511
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_FILE_UNFINISHED 7512
#define IDS_DOWNLOAD_BUBBLE_WARNING_STATUS_INSECURE 7513
#define IDS_DOWNLOAD_BUBBLE_WARNING_SUBPAGE_SUMMARY_INSECURE 7514
#define IDS_DOWNLOAD_BUBBLE_STATUS_DANGEROUS 7515
#define IDS_DOWNLOAD_BUBBLE_STATUS_ENCRYPTED 7516
#define IDS_DOWNLOAD_BUBBLE_STATUS_TOO_BIG 7517
#define IDS_DOWNLOAD_BUBBLE_STATUS_ADVANCED_PROTECTION 7518
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_ADVANCED_PROTECTION 7519
#define IDS_DOWNLOAD_BUBBLE_STATUS_UNCOMMON_FILE 7520
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_UNCOMMON_FILE 7521
#define IDS_DOWNLOAD_BUBBLE_STATUS_UNKNOWN_SOURCE 7522
#define IDS_DOWNLOAD_BUBBLE_STATUS_SENSITIVE_CONTENT 7523
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_SENSITIVE_CONTENT 7524
#define IDS_DOWNLOAD_BUBBLE_TRY_AGAIN 7525
#define IDS_DOWNLOAD_BUBBLE_STATUS_DEEP_SCANNING_PROMPT 7526
#define IDS_DOWNLOAD_BUBBLE_STATUS_ASYNC_SCANNING 7527
#define IDS_OMNIBOX_PWA_INSTALL_ICON_LABEL 7528
#define IDS_OMNIBOX_PWA_INSTALL_ICON_TOOLTIP 7529
#define IDS_ADD_TO_OS_LAUNCH_SURFACE_BUBBLE_TITLE 7530
#define IDS_CREATE_SHORTCUTS_BUTTON_LABEL 7531
#define IDS_INSTALL_TO_OS_LAUNCH_SURFACE_BUBBLE_TITLE 7532
#define IDS_INSTALL_PWA_BUTTON_LABEL 7533
#define IDS_BOOKMARK_APP_AX_BUBBLE_NAME_LABEL 7534
#define IDS_BOOKMARK_APP_BUBBLE_OPEN_AS_TAB 7535
#define IDS_BOOKMARK_APP_BUBBLE_OPEN_AS_WINDOW 7536
#define IDS_BOOKMARK_APP_BUBBLE_OPEN_AS_TABBED_WINDOW 7537
#define IDS_WEBAPP_UPDATE_DIALOG_TITLE_NAME 7538
#define IDS_WEBAPP_UPDATE_DIALOG_TITLE_ICON 7539
#define IDS_WEBAPP_UPDATE_DIALOG_TITLE_NAME_AND_ICON 7540
#define IDS_WEBAPP_UPDATE_EXPLANATION 7541
#define IDS_WEBAPP_UPDATE_CURRENT_ICON 7542
#define IDS_WEBAPP_UPDATE_NEW_ICON 7543
#define IDS_WEBAPP_UPDATE_NEGATIVE_BUTTON 7544
#define IDS_FINISH_POLICY_WEB_APP_INSTALLATION 7545
#define IDS_FINISH_POLICY_WEB_APP_INSTALATION_RESTART 7546
#define IDS_FINISH_POLICY_WEB_APP_INSTALLATION_NOT_NOW 7547
#define IDS_QUIT_WITH_APPS_NOTIFICATION_DISPLAY_SOURCE 337
#define IDS_QUIT_WITH_APPS_EXPLANATION 335
#define IDS_QUIT_WITH_APPS_QUIT_LABEL 332
#define IDS_QUIT_WITH_APPS_SUPPRESSION_LABEL 333
#define IDS_ADD_TO_OS_LAUNCH_SURFACE 7548
#define IDS_INSTALL_TO_OS_LAUNCH_SURFACE 7549
#define IDS_UNINSTALL_FROM_OS_LAUNCH_SURFACE 7550
#define IDS_APPLICATION_INFO_WEB_STORE_LINK 7551
#define IDS_APPLICATION_INFO_HOMEPAGE_LINK 7552
#define IDS_APPLICATION_INFO_APP_OVERVIEW_TITLE 7554
#define IDS_APPLICATION_INFO_APP_PERMISSIONS_TITLE 7555
#define IDS_APPLICATION_INFO_UNINSTALL_BUTTON_TEXT 7556
#define IDS_APPLICATION_INFO_LICENSES_BUTTON_TEXT 7557
#define IDS_APPLICATION_INFO_SIZE_LABEL 7558
#define IDS_APPLICATION_INFO_VERSION_LABEL 7559
#define IDS_APPLICATION_INFO_CREATE_SHORTCUTS_BUTTON_TEXT 7560
#define IDS_APPLICATION_INFO_LAUNCH_OPTIONS_ACCNAME 7561
#define IDS_APPLICATION_INFO_SIZE_LOADING_LABEL 7562
#define IDS_APPLICATION_INFO_SIZE_SMALL_LABEL 7563
#define IDS_APPLICATION_INFO_REVOKE_PERMISSION_ALT_TEXT 7564
#define IDS_APPLICATION_INFO_APP_NO_PERMISSIONS_TEXT 7565
#define IDS_APPLICATION_INFO_EXTENSION_NO_PERMISSIONS_TEXT 7566
#define IDS_APPLICATION_INFO_RETAINED_FILES 7567
#define IDS_APPLICATION_INFO_RETAINED_DEVICES 7568
#define IDS_MANAGE 7579
#define IDS_LIST_BULLET 7580
#define IDS_BLOCKED_DOWNLOAD_NO_ACTION 7581
#define IDS_BLOCKED_DOWNLOAD_UNBLOCK 7582
#define IDS_ALLOWED_DOWNLOAD_TITLE 7583
#define IDS_BLOCKED_DOWNLOAD_TITLE 7584
#define IDS_BLOCKED_DOWNLOADS_EXPLANATION 7585
#define IDS_ALLOWED_DOWNLOAD_NO_ACTION 7586
#define IDS_ALLOWED_DOWNLOAD_BLOCK 7587
#define IDS_BLOCKED_COOKIES_TITLE 7588
#define IDS_ACCESSED_COOKIES_TITLE 7589
#define IDS_BLOCKED_COOKIES_MESSAGE 7590
#define IDS_ACCESSED_COOKIES_MESSAGE 7591
#define IDS_BLOCKED_COOKIES_INFO 7592
#define IDS_BLOCKED_IMAGES_TITLE 7593
#define IDS_BLOCKED_IMAGES_MESSAGE 7594
#define IDS_BLOCKED_COOKIES_UNBLOCK 7595
#define IDS_BLOCKED_IMAGES_UNBLOCK 7596
#define IDS_BLOCKED_COOKIES_NO_ACTION 7597
#define IDS_ALLOWED_COOKIES_NO_ACTION 7598
#define IDS_ALLOWED_COOKIES_BLOCK 7599
#define IDS_BLOCKED_IMAGES_NO_ACTION 7600
#define IDS_BLOCKED_POPUPS_TOOLTIP 7601
#define IDS_BLOCKED_POPUPS_TITLE 7602
#define IDS_BLOCKED_POPUPS_REDIRECTS_UNBLOCK 7603
#define IDS_BLOCKED_POPUPS_REDIRECTS_NO_ACTION 7604
#define IDS_BLOCKED_MEDIASTREAM_MIC_AND_CAMERA_ALLOW 7605
#define IDS_BLOCKED_MEDIASTREAM_MIC_ALLOW 7606
#define IDS_BLOCKED_MEDIASTREAM_CAMERA_ALLOW 7607
#define IDS_BLOCKED_MEDIASTREAM_MIC_AND_CAMERA_ASK 7608
#define IDS_BLOCKED_MEDIASTREAM_MIC_ASK 7609
#define IDS_BLOCKED_MEDIASTREAM_CAMERA_ASK 7610
#define IDS_ALLOWED_MEDIASTREAM_MIC_AND_CAMERA_BLOCK 7611
#define IDS_ALLOWED_MEDIASTREAM_MIC_BLOCK 7612
#define IDS_ALLOWED_MEDIASTREAM_CAMERA_BLOCK 7613
#define IDS_BLOCKED_MEDIASTREAM_MIC_AND_CAMERA_NO_ACTION 7614
#define IDS_BLOCKED_MEDIASTREAM_MIC_NO_ACTION 7615
#define IDS_BLOCKED_MEDIASTREAM_CAMERA_NO_ACTION 7616
#define IDS_ALLOWED_MEDIASTREAM_MIC_AND_CAMERA_NO_ACTION 7617
#define IDS_ALLOWED_MEDIASTREAM_MIC_AND_CAMERA_PAN_TILT_ZOOM_NO_ACTION 7618
#define IDS_ALLOWED_MEDIASTREAM_MIC_NO_ACTION 7619
#define IDS_ALLOWED_MEDIASTREAM_CAMERA_NO_ACTION 7620
#define IDS_ALLOWED_CAMERA_PAN_TILT_ZOOM_NO_ACTION 7621
#define IDS_BLOCKED_POPUPS_EXPLANATORY_TEXT 7622
#define IDS_BLOCKED_JAVASCRIPT_TITLE 7623
#define IDS_BLOCKED_JAVASCRIPT_MESSAGE 7624
#define IDS_BLOCKED_JAVASCRIPT_UNBLOCK 7625
#define IDS_BLOCKED_JAVASCRIPT_NO_ACTION 7626
#define IDS_BLOCKED_SOUND_TITLE 7627
#define IDS_BLOCKED_SOUND_UNBLOCK 7628
#define IDS_BLOCKED_SOUND_NO_ACTION 7629
#define IDS_NOTIFICATIONS_OFF_EXPLANATORY_TEXT 7630
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_TITLE 7631
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_DESCRIPTION 7632
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_ALLOW_BUTTON 7633
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_COMPACT_ALLOW_BUTTON 7634
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_CONTINUE_BLOCKING_BUTTON 7635
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_EARLY_PROMO 7636
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_NEW_REQUEST_PROMO 7637
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_CROWD_DENY_DESCRIPTION 7638
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_ABUSIVE_DESCRIPTION 7639
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_PREDICTION_SERVICE_DESCRIPTION 7640
#define IDS_GEOLOCATION_OFF_EXPLANATORY_TEXT 7641
#define IDS_GEOLOCATION_QUIET_PERMISSION_BUBBLE_TITLE 7642
#define IDS_GEOLOCATION_QUIET_PERMISSION_BUBBLE_PREDICTION_SERVICE_DESCRIPTION 7643
#define IDS_GEOLOCATION_QUIET_PERMISSION_BUBBLE_ALLOW_BUTTON 7644
#define IDS_COOKIE_CONTROLS_DIALOG_TITLE 7645
#define IDS_COOKIE_CONTROLS_DIALOG_TITLE_ALL_BLOCKED 7646
#define IDS_COOKIE_CONTROLS_DIALOG_TITLE_OFF 7647
#define IDS_COOKIE_CONTROLS_TURN_ON_BUTTON 7648
#define IDS_COOKIE_CONTROLS_TURN_OFF_BUTTON 7649
#define IDS_COOKIE_CONTROLS_NOT_WORKING_TITLE 7650
#define IDS_COOKIE_CONTROLS_NOT_WORKING_DESCRIPTION 7651
#define IDS_COOKIE_CONTROLS_BLOCKED_MESSAGE 7652
#define IDS_COOKIE_CONTROLS_TOOLTIP 7653
#define IDS_COOKIE_CONTROLS_HELP 7654
#define IDS_CERT_SELECTOR_SUBJECT_COLUMN 7655
#define IDS_CERT_SELECTOR_ISSUER_COLUMN 7656
#define IDS_CERT_SELECTOR_PROVIDER_COLUMN 7657
#define IDS_CERT_SELECTOR_SERIAL_COLUMN 7658
#define IDS_CERT_EXPORT_TYPE_BASE64 7659
#define IDS_CERT_EXPORT_TYPE_BASE64_CHAIN 7660
#define IDS_CERT_EXPORT_TYPE_DER 7661
#define IDS_CERT_EXPORT_TYPE_PKCS7 7662
#define IDS_CERT_EXPORT_TYPE_PKCS7_CHAIN 7663
#define IDS_CERT_INFO_DIALOG_TITLE 7664
#define IDS_CERT_INFO_GENERAL_TAB_LABEL 7665
#define IDS_CERT_INFO_DETAILS_TAB_LABEL 7666
#define IDS_CERT_USAGE_SSL_CLIENT 7667
#define IDS_CERT_USAGE_SSL_SERVER 7668
#define IDS_CERT_USAGE_OBJECT_SIGNER 7669
#define IDS_CERT_USAGE_SSL_CA 7670
#define IDS_CERT_INFO_IDN_VALUE_FORMAT 7671
#define IDS_CERT_INFO_FIELD_NOT_PRESENT 7672
#define IDS_CERT_DETAILS_CERTIFICATE_HIERARCHY_LABEL 7673
#define IDS_CERT_DETAILS_CERTIFICATE_FIELDS_LABEL 7674
#define IDS_CERT_DETAILS_CERTIFICATE_FIELD_VALUE_LABEL 7675
#define IDS_CERT_DETAILS_CERTIFICATE 7676
#define IDS_CERT_DETAILS_VERSION 7677
#define IDS_CERT_DETAILS_VERSION_FORMAT 7678
#define IDS_CERT_DETAILS_SERIAL_NUMBER 7679
#define IDS_CERT_DETAILS_CERTIFICATE_SIG_ALG 7680
#define IDS_CERT_DETAILS_ISSUER 7681
#define IDS_CERT_DETAILS_VALIDITY 7682
#define IDS_CERT_DETAILS_NOT_BEFORE 7683
#define IDS_CERT_DETAILS_NOT_AFTER 7684
#define IDS_CERT_DETAILS_SUBJECT 7685
#define IDS_CERT_DETAILS_SUBJECT_KEY_INFO 7686
#define IDS_CERT_DETAILS_SUBJECT_KEY_ALG 7687
#define IDS_CERT_DETAILS_SUBJECT_KEY 7688
#define IDS_CERT_RSA_PUBLIC_KEY_DUMP_FORMAT 7689
#define IDS_CERT_DETAILS_CERTIFICATE_SIG_VALUE 7690
#define IDS_CERT_DETAILS_EXPORT_CERTIFICATE 7691
#define IDS_CERT_OID_AVA_COMMON_NAME 7692
#define IDS_CERT_OID_AVA_STATE_OR_PROVINCE 7693
#define IDS_CERT_OID_AVA_ORGANIZATION_NAME 7694
#define IDS_CERT_OID_AVA_ORGANIZATIONAL_UNIT_NAME 7695
#define IDS_CERT_OID_AVA_DN_QUALIFIER 7696
#define IDS_CERT_OID_AVA_COUNTRY_NAME 7697
#define IDS_CERT_OID_AVA_SERIAL_NUMBER 7698
#define IDS_CERT_OID_AVA_LOCALITY 7699
#define IDS_CERT_OID_AVA_DC 7700
#define IDS_CERT_OID_RFC1274_MAIL 7701
#define IDS_CERT_OID_RFC1274_UID 7702
#define IDS_CERT_OID_PKCS9_EMAIL_ADDRESS 7703
#define IDS_CERT_OID_BUSINESS_CATEGORY 7704
#define IDS_CERT_OID_EV_INCORPORATION_LOCALITY 7705
#define IDS_CERT_OID_EV_INCORPORATION_STATE 7706
#define IDS_CERT_OID_EV_INCORPORATION_COUNTRY 7707
#define IDS_CERT_OID_AVA_STREET_ADDRESS 7708
#define IDS_CERT_OID_AVA_POSTAL_CODE 7709
#define IDS_CERT_OID_PKCS1_RSA_ENCRYPTION 7710
#define IDS_CERT_OID_PKCS1_MD2_WITH_RSA_ENCRYPTION 7711
#define IDS_CERT_OID_PKCS1_MD4_WITH_RSA_ENCRYPTION 7712
#define IDS_CERT_OID_PKCS1_MD5_WITH_RSA_ENCRYPTION 7713
#define IDS_CERT_OID_PKCS1_SHA1_WITH_RSA_ENCRYPTION 7714
#define IDS_CERT_OID_PKCS1_SHA256_WITH_RSA_ENCRYPTION 7715
#define IDS_CERT_OID_PKCS1_SHA384_WITH_RSA_ENCRYPTION 7716
#define IDS_CERT_OID_PKCS1_SHA512_WITH_RSA_ENCRYPTION 7717
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA1_SIGNATURE 7718
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA256_SIGNATURE 7719
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA384_SIGNATURE 7720
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA512_SIGNATURE 7721
#define IDS_CERT_OID_ANSIX962_EC_PUBLIC_KEY 7722
#define IDS_CERT_OID_SECG_EC_SECP256R1 7723
#define IDS_CERT_OID_SECG_EC_SECP384R1 7724
#define IDS_CERT_OID_SECG_EC_SECP521R1 7725
#define IDS_CERT_EXT_NS_CERT_TYPE 7726
#define IDS_CERT_EXT_NS_CERT_TYPE_EMAIL 7727
#define IDS_CERT_EXT_NS_CERT_TYPE_EMAIL_CA 7728
#define IDS_CERT_EXT_NS_CERT_BASE_URL 7729
#define IDS_CERT_EXT_NS_CERT_REVOCATION_URL 7730
#define IDS_CERT_EXT_NS_CA_REVOCATION_URL 7731
#define IDS_CERT_EXT_NS_CERT_RENEWAL_URL 7732
#define IDS_CERT_EXT_NS_CA_POLICY_URL 7733
#define IDS_CERT_EXT_NS_SSL_SERVER_NAME 7734
#define IDS_CERT_EXT_NS_COMMENT 7735
#define IDS_CERT_EXT_NS_LOST_PASSWORD_URL 7736
#define IDS_CERT_EXT_NS_CERT_RENEWAL_TIME 7737
#define IDS_CERT_X509_SUBJECT_DIRECTORY_ATTR 7738
#define IDS_CERT_X509_SUBJECT_KEYID 7739
#define IDS_CERT_KEYID_FORMAT 7740
#define IDS_CERT_ISSUER_FORMAT 7741
#define IDS_CERT_SERIAL_NUMBER_FORMAT 7742
#define IDS_CERT_X509_KEY_USAGE 7743
#define IDS_CERT_X509_ISSUER_ALT_NAME 7744
#define IDS_CERT_X509_BASIC_CONSTRAINTS 7745
#define IDS_CERT_X509_NAME_CONSTRAINTS 7746
#define IDS_CERT_X509_CRL_DIST_POINTS 7747
#define IDS_CERT_X509_CERT_POLICIES 7748
#define IDS_CERT_X509_POLICY_MAPPINGS 7749
#define IDS_CERT_X509_POLICY_CONSTRAINTS 7750
#define IDS_CERT_X509_AUTH_KEYID 7751
#define IDS_CERT_X509_EXT_KEY_USAGE 7752
#define IDS_CERT_X509_AUTH_INFO_ACCESS 7753
#define IDS_CERT_X509_KEY_USAGE_SIGNING 7754
#define IDS_CERT_X509_KEY_USAGE_NONREP 7755
#define IDS_CERT_X509_KEY_USAGE_ENCIPHERMENT 7756
#define IDS_CERT_X509_KEY_USAGE_DATA_ENCIPHERMENT 7757
#define IDS_CERT_X509_KEY_USAGE_KEY_AGREEMENT 7758
#define IDS_CERT_X509_KEY_USAGE_CERT_SIGNER 7759
#define IDS_CERT_X509_KEY_USAGE_CRL_SIGNER 7760
#define IDS_CERT_X509_KEY_USAGE_ENCIPHER_ONLY 7761
#define IDS_CERT_X509_KEY_USAGE_DECIPHER_ONLY 7762
#define IDS_CERT_X509_BASIC_CONSTRAINT_IS_CA 7763
#define IDS_CERT_X509_BASIC_CONSTRAINT_IS_NOT_CA 7764
#define IDS_CERT_X509_BASIC_CONSTRAINT_PATH_LEN 7765
#define IDS_CERT_X509_BASIC_CONSTRAINT_PATH_LEN_UNLIMITED 7766
#define IDS_CERT_PKIX_CPS_POINTER_QUALIFIER 7767
#define IDS_CERT_PKIX_USER_NOTICE_QUALIFIER 7768
#define IDS_CERT_REVOCATION_REASON_UNUSED 7769
#define IDS_CERT_REVOCATION_REASON_KEY_COMPROMISE 7770
#define IDS_CERT_REVOCATION_REASON_CA_COMPROMISE 7771
#define IDS_CERT_REVOCATION_REASON_AFFILIATION_CHANGED 7772
#define IDS_CERT_REVOCATION_REASON_SUPERSEDED 7773
#define IDS_CERT_REVOCATION_REASON_CESSATION_OF_OPERATION 7774
#define IDS_CERT_REVOCATION_REASON_CERTIFICATE_HOLD 7775
#define IDS_CERT_OCSP_RESPONDER_FORMAT 7776
#define IDS_CERT_CA_ISSUERS_FORMAT 7777
#define IDS_CERT_UNKNOWN_OID_INFO_FORMAT 7778
#define IDS_CERT_EXT_KEY_USAGE_FORMAT 7779
#define IDS_CERT_MULTILINE_INFO_START_FORMAT 7780
#define IDS_CERT_GENERAL_NAME_RFC822_NAME 7781
#define IDS_CERT_GENERAL_NAME_DNS_NAME 7782
#define IDS_CERT_GENERAL_NAME_X400_ADDRESS 7783
#define IDS_CERT_GENERAL_NAME_DIRECTORY_NAME 7784
#define IDS_CERT_GENERAL_NAME_EDI_PARTY_NAME 7785
#define IDS_CERT_GENERAL_NAME_URI 7786
#define IDS_CERT_GENERAL_NAME_IP_ADDRESS 7787
#define IDS_CERT_GENERAL_NAME_REGISTERED_ID 7788
#define IDS_CERT_EXT_MS_CERT_TYPE 7789
#define IDS_CERT_EXT_MS_CA_VERSION 7790
#define IDS_CERT_EXT_MS_NT_PRINCIPAL_NAME 7791
#define IDS_CERT_EXT_MS_NTDS_REPLICATION 7792
#define IDS_CERT_EKU_ANY_EKU 7793
#define IDS_CERT_EKU_TLS_WEB_SERVER_AUTHENTICATION 7794
#define IDS_CERT_EKU_TLS_WEB_CLIENT_AUTHENTICATION 7795
#define IDS_CERT_EKU_CODE_SIGNING 7796
#define IDS_CERT_EKU_EMAIL_PROTECTION 7797
#define IDS_CERT_EKU_TIME_STAMPING 7798
#define IDS_CERT_EKU_OCSP_SIGNING 7799
#define IDS_CERT_EKU_MS_INDIVIDUAL_CODE_SIGNING 7800
#define IDS_CERT_EKU_MS_COMMERCIAL_CODE_SIGNING 7801
#define IDS_CERT_EKU_MS_TRUST_LIST_SIGNING 7802
#define IDS_CERT_EKU_MS_TIME_STAMPING 7803
#define IDS_CERT_EKU_MS_SERVER_GATED_CRYPTO 7804
#define IDS_CERT_EKU_MS_ENCRYPTING_FILE_SYSTEM 7805
#define IDS_CERT_EKU_MS_FILE_RECOVERY 7806
#define IDS_CERT_EKU_MS_WINDOWS_HARDWARE_DRIVER_VERIFICATION 7807
#define IDS_CERT_EKU_MS_QUALIFIED_SUBORDINATION 7808
#define IDS_CERT_EKU_MS_KEY_RECOVERY 7809
#define IDS_CERT_EKU_MS_DOCUMENT_SIGNING 7810
#define IDS_CERT_EKU_MS_LIFETIME_SIGNING 7811
#define IDS_CERT_EKU_MS_SMART_CARD_LOGON 7812
#define IDS_CERT_EKU_MS_KEY_RECOVERY_AGENT 7813
#define IDS_CERT_EKU_NETSCAPE_INTERNATIONAL_STEP_UP 7814
#define IDS_CERT_EXTENSION_CRITICAL 7815
#define IDS_CERT_EXTENSION_NON_CRITICAL 7816
#define IDS_CERT_EXTENSION_DUMP_ERROR 7817
#define IDS_CERTIFICATE_MANAGER_TITLE 7818
#define IDS_CERT_MANAGER_HARDWARE_BACKED_KEY_FORMAT 7819
#define IDS_CERT_MANAGER_HARDWARE_BACKED 7820
#define IDS_CERT_MANAGER_EXTENSION_PROVIDED_FORMAT 7821
#define IDS_DEV_TOOLS_INFOBAR_LABEL 7822
#define IDS_DEV_TOOLS_CONFIRM_ADD_FILE_SYSTEM_MESSAGE 7823
#define IDS_DEV_TOOLS_CONFIRM_ALLOW_BUTTON 7824
#define IDS_DEV_TOOLS_CONFIRM_DENY_BUTTON 7825
#define IDS_RELOAD_MENU_NORMAL_RELOAD_ITEM 270
#define IDS_RELOAD_MENU_HARD_RELOAD_ITEM 271
#define IDS_RELOAD_MENU_EMPTY_AND_HARD_RELOAD_ITEM 272
#define IDS_TAB_SHARING_INFOBAR_SHARING_CURRENT_TAB_LABEL 7826
#define IDS_TAB_SHARING_INFOBAR_SHARING_ANOTHER_UNTITLED_TAB_LABEL 7827
#define IDS_TAB_SHARING_INFOBAR_SHARING_ANOTHER_TAB_LABEL 7828
#define IDS_TAB_SHARING_INFOBAR_SHARE_BUTTON 7829
#define IDS_TAB_SHARING_INFOBAR_STOP_BUTTON 7830
#define IDS_TAB_SHARING_INFOBAR_SWITCH_TO_BUTTON 7831
#define IDS_TAB_SHARING_INFOBAR_SWITCH_TO_CAPTURER_BUTTON 7832
#define IDS_TAB_SHARING_INFOBAR_SWITCH_TO_CAPTURED_BUTTON 7833
#define IDS_TASK_MANAGER_KILL 7834
#define IDS_TASK_MANAGER_PROCESS_ID_COLUMN 7835
#define IDS_TASK_MANAGER_GDI_HANDLES_COLUMN 7836
#define IDS_TASK_MANAGER_USER_HANDLES_COLUMN 7837
#define IDS_TASK_MANAGER_NACL_DEBUG_STUB_PORT_COLUMN 7839
#define IDS_TASK_MANAGER_TASK_COLUMN 7838
#define IDS_TASK_MANAGER_NET_COLUMN 7840
#define IDS_TASK_MANAGER_CPU_COLUMN 7841
#define IDS_TASK_MANAGER_START_TIME_COLUMN 7842
#define IDS_TASK_MANAGER_CPU_TIME_COLUMN 7843
#define IDS_TASK_MANAGER_MEM_FOOTPRINT_COLUMN 7844
#define IDS_TASK_MANAGER_SWAPPED_MEM_COLUMN 7845
#define IDS_TASK_MANAGER_PROFILE_NAME_COLUMN 7846
#define IDS_TASK_MANAGER_IDLE_WAKEUPS_COLUMN 7847
#define IDS_TASK_MANAGER_HARD_FAULTS_COLUMN 7848
#define IDS_TASK_MANAGER_OPEN_FD_COUNT_COLUMN 7849
#define IDS_TASK_MANAGER_PROCESS_PRIORITY_COLUMN 7850
#define IDS_TASK_MANAGER_WEBCORE_IMAGE_CACHE_COLUMN 7851
#define IDS_TASK_MANAGER_WEBCORE_SCRIPTS_CACHE_COLUMN 7852
#define IDS_TASK_MANAGER_WEBCORE_CSS_CACHE_COLUMN 7853
#define IDS_TASK_MANAGER_VIDEO_MEMORY_COLUMN 7854
#define IDS_TASK_MANAGER_SQLITE_MEMORY_USED_COLUMN 7855
#define IDS_TASK_MANAGER_JAVASCRIPT_MEMORY_ALLOCATED_COLUMN 7856
#define IDS_TASK_MANAGER_KEEPALIVE_COUNT_COLUMN 7857
#define IDS_TASK_MANAGER_MEM_CELL_TEXT 7858
#define IDS_TASK_MANAGER_CACHE_SIZE_CELL_TEXT 7859
#define IDS_TASK_MANAGER_NA_CELL_TEXT 7860
#define IDS_TASK_MANAGER_BACKGROUNDED_TEXT 7861
#define IDS_TASK_MANAGER_FOREGROUNDED_TEXT 7862
#define IDS_TASK_MANAGER_UNKNOWN_VALUE_TEXT 7863
#define IDS_TASK_MANAGER_DISABLED_NACL_DBG_TEXT 7864
#define IDS_TASK_MANAGER_HANDLES_CELL_TEXT 7865
#define IDS_TASK_MANAGER_WEB_BROWSER_CELL_TEXT 7866
#define IDS_TASK_MANAGER_EXTENSION_PREFIX 7867
#define IDS_TASK_MANAGER_EXTENSION_INCOGNITO_PREFIX 7868
#define IDS_TASK_MANAGER_APP_PREFIX 7869
#define IDS_TASK_MANAGER_APP_INCOGNITO_PREFIX 7870
#define IDS_TASK_MANAGER_TAB_PREFIX 7871
#define IDS_TASK_MANAGER_TAB_INCOGNITO_PREFIX 7872
#define IDS_TASK_MANAGER_BACKGROUND_APP_PREFIX 7873
#define IDS_TASK_MANAGER_BACKGROUND_PREFIX 7874
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_PREFIX 7875
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_INCOGNITO_PREFIX 7876
#define IDS_TASK_MANAGER_PLUGIN_PREFIX 7877
#define IDS_TASK_MANAGER_PLUGIN_BROKER_PREFIX 7878
#define IDS_TASK_MANAGER_PRERENDER_PREFIX 7879
#define IDS_TASK_MANAGER_SPARE_RENDERER_PREFIX 7880
#define IDS_TASK_MANAGER_UNKNOWN_RENDERER_PREFIX 7881
#define IDS_TASK_MANAGER_DEDICATED_WORKER_PREFIX 7882
#define IDS_TASK_MANAGER_SHARED_WORKER_PREFIX 7883
#define IDS_TASK_MANAGER_SERVICE_WORKER_PREFIX 7884
#define IDS_TASK_MANAGER_UNKNOWN_PLUGIN_NAME 7885
#define IDS_TASK_MANAGER_UTILITY_PREFIX 7886
#define IDS_TASK_MANAGER_NACL_PREFIX 7887
#define IDS_TASK_MANAGER_NACL_BROKER_PREFIX 7888
#define IDS_TASK_MANAGER_GPU_PREFIX 7889
#define IDS_TASK_MANAGER_PRINT_PREFIX 7890
#define IDS_TASK_MANAGER_SUBFRAME_PREFIX 7891
#define IDS_TASK_MANAGER_SUBFRAME_INCOGNITO_PREFIX 7892
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_SUBFRAME_PREFIX 7893
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_INCOGNITO_SUBFRAME_PREFIX 7894
#define IDS_TASK_MANAGER_PORTAL_PREFIX 7895
#define IDS_TASK_MANAGER_PORTAL_INCOGNITO_PREFIX 7896
#define IDS_TASK_MANAGER_ARC_PREFIX 7897
#define IDS_TASK_MANAGER_TOOL_PREFIX 7898
#define IDS_TASK_MANAGER_ARC_PREFIX_BACKGROUND_SERVICE 7899
#define IDS_TASK_MANAGER_ARC_PREFIX_RECEIVER 7900
#define IDS_TASK_MANAGER_ARC_SYSTEM 7901
#define IDS_TASK_MANAGER_LINUX_VM_PREFIX 7902
#define IDS_TASK_MANAGER_PLUGIN_VM_PREFIX 7903
#define IDS_UTILITY_PROCESS_FILE_UTILITY_NAME 7905
#define IDS_UTILITY_PROCESS_PROFILE_IMPORTER_NAME 7906
#define IDS_UTILITY_PROCESS_QRCODE_GENERATOR_SERVICE_NAME 7907
#define IDS_UTILITY_PROCESS_IMAGE_WRITER_NAME 7909
#define IDS_UTILITY_PROCESS_MEDIA_GALLERY_UTILITY_NAME 7910
#define IDS_UTILITY_PROCESS_NOOP_SERVICE_NAME 7911
#define IDS_SERVICE_PROCESS_DOCUMENT_ANALYSIS_NAME 7912
#define IDS_THEME_INSTALL_INFOBAR_LABEL 7913
#define IDS_THEME_INSTALL_INFOBAR_UNDO_BUTTON 7914
#define IDS_CRITICAL_NOTIFICATION_RESTART 7915
#define IDS_EXTENSION_DISABLED_ERROR_LABEL 7916
#define IDS_EXTENSION_IS_BLOCKLISTED 7917
#define IDS_EXTENSION_DISABLED_REMOTE_INSTALL_ERROR_TITLE 7918
#define IDS_EXTENSION_DISABLED_ERROR_TITLE 7919
#define IDS_EXTENSION_BLOCKED_ACTION_BUBBLE_HEADING 7920
#define IDS_EXTENSION_BLOCKED_ACTION_BUBBLE_OK_BUTTON 7921
#define IDS_APP_UNINSTALL_PROMPT_TITLE 7922
#define IDS_ARC_APP_UNINSTALL_PROMPT_DATA_REMOVAL_WARNING 7923
#define IDS_EXTENSION_UNINSTALL_PROMPT_TITLE 7924
#define IDS_EXTENSION_CONFIRM_PERMISSIONS 7925
#define IDS_EXTENSION_DELEGATED_INSTALL_PROMPT_TITLE 7926
#define IDS_EXTENSION_INSTALL_PROMPT_TITLE 7927
#define IDS_EXTENSION_UNINSTALL_PROMPT_HEADING 7928
#define IDS_EXTENSION_REQUEST_PROMPT_TITLE 7929
#define IDS_EXTENSION_PENDING_REQUEST_PROMPT_TITLE 7930
#define IDS_EXTENSION_BLOCKED_BY_POLICY_PROMPT_TITLE 7931
#define IDS_EXTENSION_PROGRAMMATIC_UNINSTALL_PROMPT_HEADING 7932
#define IDS_EXTENSION_RE_ENABLE_PROMPT_TITLE 7933
#define IDS_EXTENSION_PERMISSIONS_PROMPT_TITLE 7934
#define IDS_EXTENSION_POST_INSTALL_PERMISSIONS_PROMPT_TITLE 7935
#define IDS_EXTENSION_REMOTE_INSTALL_PROMPT_TITLE 7936
#define IDS_EXTENSION_REPAIR_PROMPT_TITLE 7937
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_TITLE_APP 7938
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_TITLE_EXTENSION 7939
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_TITLE_THEME 7940
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ACCEPT_BUTTON_EXTENSION 7941
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ACCEPT_BUTTON_APP 7942
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ACCEPT_BUTTON_THEME 7943
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ABORT_BUTTON 7944
#define IDS_EXTENSION_ALERT_TITLE 7945
#define IDS_APP_ALERT_TITLE 7946
#define IDS_EXTENSION_AND_APP_ALERT_TITLE 7947
#define IDS_POLICY_BLOCKED_EXTENSION_ALERT_TITLE 7948
#define IDS_POLICY_BLOCKED_EXTENSIONS_ALERT_ITEM_TITLE 7949
#define IDS_BLOCKLISTED_EXTENSIONS_ALERT_ITEM 7950
#define IDS_POLICY_BLOCKED_EXTENSION_ALERT_ITEM_DETAIL 7951
#define IDS_EXTENSION_ALERT_ITEM_OK 7952
#define IDS_EXTENSION_ALERT_ITEM_DETAILS 7953
#define IDS_EXTENSION_PROMPT_APP_CONNECT_FROM_INCOGNITO 7954
#define IDS_EXTENSION_PROMPT_EXTENSION_CONNECT_FROM_INCOGNITO 7955
#define IDS_EXTENSION_PROMPT_WILL_HAVE_ACCESS_TO 7956
#define IDS_EXTENSION_PROMPT_WILL_NOW_HAVE_ACCESS_TO 7957
#define IDS_EXTENSION_PROMPT_WANTS_ACCESS_TO 7958
#define IDS_EXTENSION_PROMPT_CAN_ACCESS 7959
#define IDS_EXTENSION_NO_SPECIAL_PERMISSIONS 7960
#define IDS_EXTENSION_WITHHOLD_PERMISSIONS 7961
#define IDS_EXTENSION_PROMPT_MESSAGE_FROM_ADMIN 7962
#define IDS_EXTENSION_PERMISSION_LINE 7963
#define IDS_EXTENSION_RATING_COUNT 7964
#define IDS_EXTENSION_PROMPT_RATING_ACCESSIBLE_TEXT 7965
#define IDS_EXTENSION_PROMPT_NO_RATINGS_ACCESSIBLE_TEXT 7966
#define IDS_EXTENSION_USER_COUNT 7967
#define IDS_EXTENSION_PROMPT_STORE_LINK 7968
#define IDS_EXTENSION_PROMPT_RETAINED_FILES 7969
#define IDS_EXTENSION_PROMPT_RETAINED_DEVICES 7970
#define IDS_EXTENSION_PROMPT_WARNING_FULL_ACCESS 7971
#define IDS_EXTENSION_PROMPT_WARNING_ALL_HOSTS 7972
#define IDS_EXTENSION_PROMPT_WARNING_CURRENT_HOST 7973
#define IDS_EXTENSION_PROMPT_WARNING_ALL_HOSTS_READ_ONLY 7974
#define IDS_EXTENSION_PROMPT_WARNING_AUDIO_CAPTURE 7975
#define IDS_EXTENSION_PROMPT_WARNING_VIDEO_CAPTURE 7976
#define IDS_EXTENSION_PROMPT_WARNING_AUDIO_AND_VIDEO_CAPTURE 7977
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH 7978
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH_DEVICES 7979
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH_PRIVATE 7980
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH_SERIAL 7981
#define IDS_EXTENSION_PROMPT_WARNING_BOOKMARKS 7982
#define IDS_EXTENSION_PROMPT_WARNING_CLIPBOARD 7983
#define IDS_EXTENSION_PROMPT_WARNING_CLIPBOARD_READWRITE 364
#define IDS_EXTENSION_PROMPT_WARNING_CLIPBOARD_WRITE 7984
#define IDS_EXTENSION_PROMPT_WARNING_DEBUGGER 7985
#define IDS_EXTENSION_PROMPT_WARNING_DECLARATIVE_WEB_REQUEST 7986
#define IDS_EXTENSION_PROMPT_WARNING_DECLARATIVE_NET_REQUEST 7987
#define IDS_EXTENSION_PROMPT_WARNING_DOCUMENT_SCAN 7988
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_HARDWARE_PLATFORM 7989
#define IDS_EXTENSION_PROMPT_WARNING_FAVICON 7990
#define IDS_EXTENSION_PROMPT_WARNING_GEOLOCATION 7991
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_READ 7992
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_READ_AND_SESSIONS 7993
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_WRITE 7994
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_WRITE_AND_SESSIONS 7995
#define IDS_EXTENSION_PROMPT_WARNING_HOME_PAGE_SETTING_OVERRIDE 7996
#define IDS_EXTENSION_PROMPT_WARNING_1_HOST 7997
#define IDS_EXTENSION_PROMPT_WARNING_1_HOST_READ_ONLY 7998
#define IDS_EXTENSION_PROMPT_WARNING_2_HOSTS 7999
#define IDS_EXTENSION_PROMPT_WARNING_2_HOSTS_READ_ONLY 8000
#define IDS_EXTENSION_PROMPT_WARNING_3_HOSTS 8001
#define IDS_EXTENSION_PROMPT_WARNING_3_HOSTS_READ_ONLY 8002
#define IDS_EXTENSION_PROMPT_WARNING_HOSTS_LIST 8003
#define IDS_EXTENSION_PROMPT_WARNING_HOSTS_LIST_READ_ONLY 8004
#define IDS_EXTENSION_PROMPT_WARNING_HOST_AND_SUBDOMAIN 8005
#define IDS_EXTENSION_PROMPT_WARNING_HOST_AND_SUBDOMAIN_LIST 8006
#define IDS_EXTENSION_PROMPT_WARNING_INPUT 8007
#define IDS_EXTENSION_PROMPT_WARNING_LOGIN 8008
#define IDS_EXTENSION_PROMPT_WARNING_LOGIN_SCREEN_UI 8009
#define IDS_EXTENSION_PROMPT_WARNING_LOGIN_SCREEN_STORAGE 8010
#define IDS_EXTENSION_PROMPT_WARNING_MANAGEMENT 8011
#define IDS_EXTENSION_PROMPT_WARNING_MDNS 8012
#define IDS_EXTENSION_PROMPT_WARNING_NETWORK_STATE 8013
#define IDS_EXTENSION_PROMPT_WARNING_NETWORKING_PRIVATE 8014
#define IDS_EXTENSION_PROMPT_WARNING_PRINTING 8015
#define IDS_EXTENSION_PROMPT_WARNING_PRINTING_METRICS 8016
#define IDS_EXTENSION_PROMPT_WARNING_SEARCH_SETTINGS_OVERRIDE 8017
#define IDS_EXTENSION_PROMPT_WARNING_SERIAL 8018
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_ANY_HOST 8019
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_HOSTS_IN_DOMAIN 8020
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_HOSTS_IN_DOMAINS 8021
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_SPECIFIC_HOST 8022
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_SPECIFIC_HOSTS 8023
#define IDS_EXTENSION_PROMPT_WARNING_SPEECH_RECOGNITION 8024
#define IDS_EXTENSION_PROMPT_WARNING_START_PAGE_SETTING_OVERRIDE 8025
#define IDS_EXTENSION_PROMPT_WARNING_SYSTEM_STORAGE 8026
#define IDS_EXTENSION_PROMPT_WARNING_TAB_GROUPS 8027
#define IDS_EXTENSION_PROMPT_WARNING_TOPSITES 8028
#define IDS_EXTENSION_PROMPT_WARNING_TTS_ENGINE 8029
#define IDS_EXTENSION_PROMPT_WARNING_U2F_DEVICES 8030
#define IDS_EXTENSION_PROMPT_WARNING_NOTIFICATIONS 365
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE 8031
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_LIST 8032
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_LIST_ITEM_UNKNOWN_PRODUCT 8033
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_LIST_ITEM_UNKNOWN_VENDOR 8034
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_UNKNOWN_PRODUCT 8035
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_UNKNOWN_VENDOR 8036
#define IDS_EXTENSION_PROMPT_WARNING_VPN 8037
#define IDS_EXTENSION_PROMPT_WARNING_CONTENT_SETTINGS 8038
#define IDS_EXTENSION_PROMPT_WARNING_PRIVACY 8039
#define IDS_EXTENSION_PROMPT_WARNING_DOWNLOADS 8040
#define IDS_EXTENSION_PROMPT_WARNING_DOWNLOADS_OPEN 8041
#define IDS_EXTENSION_PROMPT_WARNING_IDENTITY_EMAIL 8042
#define IDS_EXTENSION_PROMPT_WARNING_WALLPAPER 8043
#define IDS_EXTENSION_PROMPT_WARNING_FILE_SYSTEM_DIRECTORY 8044
#define IDS_EXTENSION_PROMPT_WARNING_FILE_SYSTEM_WRITE_DIRECTORY 8045
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ 8046
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ_WRITE 8047
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ_DELETE 8048
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ_WRITE_DELETE 8049
#define IDS_EXTENSION_PROMPT_WARNING_SYNCFILESYSTEM 8050
#define IDS_EXTENSION_PROMPT_WARNING_MUSIC_MANAGER_PRIVATE 8051
#define IDS_EXTENSION_PROMPT_WARNING_NATIVE_MESSAGING 8052
#define IDS_EXTENSION_PROMPT_WARNING_SCREENLOCK_PRIVATE 8053
#define IDS_EXTENSION_PROMPT_WARNING_ACTIVITY_LOG_PRIVATE 8054
#define IDS_EXTENSION_PROMPT_WARNING_DESKTOP_CAPTURE 8055
#define IDS_EXTENSION_PROMPT_WARNING_ACCESSIBILITY_FEATURES_MODIFY 8056
#define IDS_EXTENSION_PROMPT_WARNING_ACCESSIBILITY_FEATURES_READ 8057
#define IDS_EXTENSION_PROMPT_WARNING_ACCESSIBILITY_FEATURES_READ_MODIFY 8058
#define IDS_EXTENSION_PROMPT_WARNING_PLATFORMKEYS 8059
#define IDS_EXTENSION_PROMPT_WARNING_CERTIFICATEPROVIDER 8060
#define IDS_EXTENSION_PROMPT_WARNING_SETTINGS_PRIVATE 8061
#define IDS_EXTENSION_PROMPT_WARNING_AUTOFILL_PRIVATE 8062
#define IDS_EXTENSION_PROMPT_WARNING_PASSWORDS_PRIVATE 8063
#define IDS_EXTENSION_PROMPT_WARNING_USERS_PRIVATE 8064
#define IDS_EXTENSION_PROMPT_WARNING_NEW_TAB_PAGE_OVERRIDE 8065
#define IDS_EXTENSION_PROMPT_WARNING_TRANSIENT_BACKGROUND 8066
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_DEVICE_ATTRIBUTES 8067
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_NETWORKING_ATTRIBUTES 8068
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_PLATFORMKEYS 8069
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_REPORTING_PRIVATE 8070
#define IDS_EXTENSION_PROMPT_WARNING_CHROMEOS_DIAGNOSTICS 8071
#define IDS_EXTENSION_PROMPT_WARNING_CHROMEOS_TELEMETRY 8072
#define IDS_EXTENSION_PROMPT_WARNING_CHROMEOS_TELEMETRY_SERIAL_NUMBER 8073
#define IDS_EXTENSION_CANT_DOWNGRADE_VERSION 8074
#define IDS_APP_CANT_DOWNGRADE_VERSION 8075
#define IDS_EXTENSION_MOVE_DIRECTORY_TO_PROFILE_FAILED 8078
#define IDS_EXTENSION_INSTALL_NOT_ENABLED 8079
#define IDS_EXTENSION_INSTALL_INCORRECT_APP_CONTENT_TYPE 8080
#define IDS_EXTENSION_INSTALL_INCORRECT_INSTALL_HOST 8081
#define IDS_EXTENSION_INSTALL_UNEXPECTED_ID 8082
#define IDS_EXTENSION_INSTALL_DISALLOWED_ON_SITE 8083
#define IDS_EXTENSION_INSTALL_UNEXPECTED_VERSION 8084
#define IDS_EXTENSION_INSTALL_DEPENDENCY_OLD_VERSION 8085
#define IDS_EXTENSION_INSTALL_DEPENDENCY_NOT_SHARED_MODULE 8086
#define IDS_EXTENSION_INSTALL_DEPENDENCY_NOT_ALLOWLISTED 8087
#define IDS_EXTENSION_INSTALL_GALLERY_ONLY 8088
#define IDS_EXTENSION_INSTALL_KIOSK_MODE_ONLY 8089
#define IDS_EXTENSION_OVERLAPPING_WEB_EXTENT 8090
#define IDS_EXTENSION_INVALID_IMAGE_PATH 8091
#define IDS_EXTENSION_INSTALLED_APP_INFO 8093
#define IDS_EXTENSION_INSTALLED_PAGE_ACTION_INFO 8094
#define IDS_EXTENSION_INSTALLED_PAGE_ACTION_INFO_WITH_SHORTCUT 8095
#define IDS_EXTENSION_INSTALLED_BROWSER_ACTION_INFO 8096
#define IDS_EXTENSION_INSTALLED_BROWSER_ACTION_INFO_WITH_SHORTCUT 8097
#define IDS_EXTENSION_INSTALLED_OMNIBOX_KEYWORD_INFO 8098
#define IDS_EXTENSION_INSTALLED_MANAGE_INFO 8099
#define IDS_EXTENSION_INSTALLED_MANAGE_SHORTCUTS 8100
#define IDS_EXTENSION_INSTALLED_DICE_PROMO_SYNC_MESSAGE 8101
#define IDS_EXTENSIONS_DIRECTORY_CONFIRMATION_DIALOG_TITLE 8102
#define IDS_EXTENSIONS_DIRECTORY_CONFIRMATION_DIALOG_MESSAGE_READ_ONLY 8103
#define IDS_EXTENSIONS_DIRECTORY_CONFIRMATION_DIALOG_MESSAGE_WRITABLE 8104
#define IDS_DIRECT_SOCKETS_CONNECTION_BUBBLE_TITLE_LABEL 8105
#define IDS_DIRECT_SOCKETS_CONNECTION_BUBBLE_ADDRESS_LABEL 8106
#define IDS_DIRECT_SOCKETS_CONNECTION_BUBBLE_PORT_LABEL 8107
#define IDS_EXTENSIONS_LOAD_ERROR_ALERT_HEADING 8108
#define IDS_EXTENSIONS_LOAD_ERROR_MESSAGE 8109
#define IDS_EXTENSIONS_WANTS_ACCESS_TO_SITE 8110
#define IDS_EXTENSIONS_HAS_ACCESS_TO_SITE 8111
#define IDS_EXTENSIONS_REQUEST_ACCESS_BUTTON 8112
#define IDS_EXTENSIONS_REQUEST_ACCESS_BUTTON_TOOLTIP_SINGLE_EXTENSION 8113
#define IDS_EXTENSIONS_REQUEST_ACCESS_BUTTON_TOOLTIP_MULTIPLE_EXTENSIONS 8114
#define IDS_EXTENSIONS_CONTEXT_MENU_CANT_ACCESS_PAGE 8115
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS 8116
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_ALL_EXTENSIONS_GRANTED 8117
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_ALL_EXTENSIONS_BLOCKED 8118
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_RUN_ON_CLICK 8119
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_RUN_ON_SITE 8120
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_RUN_ON_ALL_SITES 8121
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_LEARN_MORE 8122
#define IDS_EXTENSIONS_OPTIONS_MENU_ITEM 8123
#define IDS_EXTENSIONS_INSTALLED_BY_ADMIN 8124
#define IDS_EXTENSIONS_DISABLE 8125
#define IDS_EXTENSIONS_HIDE_BUTTON 8135
#define IDS_EXTENSIONS_KEEP_BUTTON_IN_TOOLBAR 8136
#define IDS_EXTENSIONS_SHOW_BUTTON_IN_TOOLBAR 8137
#define IDS_EXTENSIONS_PIN_TO_TOOLBAR 8126
#define IDS_EXTENSIONS_UNPIN_FROM_TOOLBAR 8127
#define IDS_EXTENSIONS_PINNED_BY_ADMIN 8128
#define IDS_MANAGE_EXTENSION 8129
#define IDS_MANAGE_EXTENSIONS 8130
#define IDS_EXTENSION_ACTION_INSPECT_POPUP 8131
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX_RUN_ON_CLICK 8132
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX_RUN_ON_SITE 8133
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX_RUN_ON_ALL_SITES 8134
#define IDS_EXTENSIONS_LOCKED_SUPERVISED_USER 8138
#define IDS_EXTENSION_LOAD_FROM_DIRECTORY 8139
#define IDS_EXTENSION_COMMANDS_GENERIC_ACTIVATE 8140
#define IDS_EXTENSION_PACK_DIALOG_HEADING 8141
#define IDS_EXTENSION_PACK_DIALOG_SELECT_KEY 8142
#define IDS_EXTENSION_PACK_DIALOG_KEY_FILE_TYPE_DESCRIPTION 8143
#define IDS_EXTENSION_PACK_DIALOG_ERROR_ROOT_REQUIRED 8144
#define IDS_EXTENSION_PACK_DIALOG_ERROR_ROOT_INVALID 8145
#define IDS_EXTENSION_PACK_DIALOG_ERROR_KEY_INVALID 8146
#define IDS_EXTENSION_PACK_DIALOG_SUCCESS_BODY_NEW 8147
#define IDS_EXTENSION_PACK_DIALOG_SUCCESS_BODY_UPDATE 8148
#define IDS_EXTENSION_PROMPT_INSTALL_FRICTION_CONTINUE_BUTTON 8149
#define IDS_EXTENSION_PROMPT_INSTALL_FRICTION_TITLE 8150
#define IDS_EXTENSION_PROMPT_INSTALL_FRICTION_WARNING_TEXT 8151
#define IDS_EXTENSION_PROMPT_INSTALL_BUTTON 8152
#define IDS_EXTENSION_INSTALL_PROMPT_ACCEPT_BUTTON_EXTENSION 8153
#define IDS_EXTENSION_INSTALL_PROMPT_ACCEPT_BUTTON_APP 8154
#define IDS_EXTENSION_INSTALL_PROMPT_ACCEPT_BUTTON_THEME 8155
#define IDS_EXTENSION_INSTALL_PROMPT_ASK_A_PARENT_BUTTON 8156
#define IDS_EXTENSION_INSTALL_BLOCKED_BY_PARENT_PROMPT_TITLE 8157
#define IDS_EXTENSION_ENABLE_BLOCKED_BY_PARENT_PROMPT_TITLE 8158
#define IDS_PARENT_PERMISSION_PROMPT_GO_GET_A_PARENT_FOR_EXTENSION_LABEL 8159
#define IDS_PARENT_PERMISSION_PROMPT_EXTENSION_TYPE_EXTENSION 8160
#define IDS_PARENT_PERMISSION_PROMPT_EXTENSION_TYPE_APP 8161
#define IDS_PARENT_PERMISSION_PROMPT_CHILD_WANTS_TO_INSTALL_LABEL 8162
#define IDS_PARENT_PERMISSION_PROMPT_APPROVE_BUTTON 8163
#define IDS_PARENT_PERMISSION_PROMPT_CANCEL_BUTTON 8164
#define IDS_PARENT_PERMISSION_PROMPT_SELECT_PARENT_LABEL 8165
#define IDS_PARENT_PERMISSION_PROMPT_PARENT_ACCOUNT_LABEL 8166
#define IDS_PARENT_PERMISSION_PROMPT_ENTER_PASSWORD_LABEL 8167
#define IDS_PARENT_PERMISSION_PROMPT_PASSWORD_INCORRECT_LABEL 8168
#define IDS_EXTENSION_INSTALL_PROMPT_REQUEST_BUTTON 8169
#define IDS_EXTENSION_PROMPT_UNINSTALL_BUTTON 8170
#define IDS_EXTENSION_PROMPT_UNINSTALL_REPORT_ABUSE 8171
#define IDS_EXTENSION_PROMPT_UNINSTALL_REPORT_ABUSE_FROM_EXTENSION 8172
#define IDS_EXTENSION_PROMPT_UNINSTALL_TITLE 8173
#define IDS_EXTENSION_PROMPT_UNINSTALL_APP_BUTTON 8174
#define IDS_EXTENSION_PROMPT_UNINSTALL_TRIGGERED_BY_EXTENSION 8175
#define IDS_EXTENSION_PROMPT_RE_ENABLE_BUTTON 8176
#define IDS_EXTENSION_PROMPT_PERMISSIONS_BUTTON 8177
#define IDS_EXTENSION_PROMPT_PERMISSIONS_ACCEPT_BUTTON 8178
#define IDS_EXTENSION_PROMPT_PERMISSIONS_ABORT_BUTTON 8179
#define IDS_EXTENSION_PROMPT_PERMISSIONS_CLEAR_RETAINED_FILES_BUTTON 8180
#define IDS_EXTENSION_PROMPT_PERMISSIONS_CLEAR_RETAINED_DEVICES_BUTTON 8181
#define IDS_EXTENSION_PROMPT_PERMISSIONS_CLEAR_RETAINED_FILES_AND_DEVICES_BUTTON 8182
#define IDS_EXTENSION_PROMPT_REMOTE_INSTALL_BUTTON_EXTENSION 8183
#define IDS_EXTENSION_PROMPT_REMOTE_INSTALL_BUTTON_APP 8184
#define IDS_EXTENSION_PROMPT_REPAIR_BUTTON_EXTENSION 8185
#define IDS_EXTENSION_PROMPT_REPAIR_BUTTON_APP 8186
#define IDS_EXTENSION_WEB_STORE_TITLE 256
#define IDS_EXTENSION_WEB_STORE_TITLE_SHORT 8187
#define IDS_EXTENSIONS_SHOW_DETAILS 8188
#define IDS_EXTENSIONS_HIDE_DETAILS 8189
#define IDS_WEBSTORE_DOWNLOAD_ACCESS_DENIED 8190
#define IDS_EXTENSION_WARNINGS_WRENCH_MENU_ITEM 8191
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_EXTENSION 8192
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_APP 8193
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_THEME 8194
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_TITLE 8195
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_HEADING_APP 8196
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_HEADING_EXTENSION 8197
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_HEADING_THEME 8198
#define IDS_EXTENSIONS_UNSUPPORTED_DISABLED_TITLE 8199
#define IDS_EXTENSIONS_DISABLED_AND_N_MORE 8200
#define IDS_EXTENSIONS_UNSUPPORTED_DISABLED_BUTTON 8201
#define IDS_EXTENSIONS_ADDED_WITHOUT_KNOWLEDGE 8202
#define IDS_EXTENSIONS_DISABLE_DEVELOPER_MODE_TITLE 8203
#define IDS_EXTENSIONS_DISABLE_DEVELOPER_MODE_BODY 8204
#define IDS_EXTENSION_SETTINGS_OVERRIDDEN_DIALOG_CHANGE_IT_BACK 8205
#define IDS_EXTENSION_SETTINGS_OVERRIDDEN_DIALOG_KEEP_IT 8206
#define IDS_EXTENSION_NTP_OVERRIDDEN_DIALOG_TITLE_GENERIC 8207
#define IDS_EXTENSION_NTP_OVERRIDDEN_DIALOG_TITLE_BACK_TO_GOOGLE 8208
#define IDS_EXTENSION_NTP_OVERRIDDEN_DIALOG_BODY_GENERIC 8209
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_TITLE_GENERIC 8210
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_TITLE_BACK_TO_GOOGLE 8211
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_TITLE_BACK_TO_OTHER 8212
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_BODY_GENERIC 8213
#define IDS_FORCE_INSTALLED_DEPRECATED_APPS_CONTENT 8214
#define IDS_FORCE_INSTALLED_PREINSTALLED_DEPRECATED_APPS_TITLE 8215
#define IDS_FORCE_INSTALLED_PREINSTALLED_DEPRECATED_APPS_CONTENT 8216
#define IDS_FORCE_INSTALLED_DEPRECATED_APPS_LEARN_MORE_AX_LABEL 8217
#define IDS_DEPRECATED_APPS_RENDERER_TITLE 8218
#define IDS_DEPRECATED_APPS_MONITOR_RENDERER 8219
#define IDS_DEPRECATED_APPS_LEARN_MORE 8220
#define IDS_DEPRECATED_APPS_DELETION_LINK 8221
#define IDS_DEPRECATED_APPS_OK_LABEL 8222
#define IDS_DEPRECATED_APPS_CANCEL_LABEL 8223
#define IDS_EXTENSIONS_MENU_TITLE 8224
#define IDS_EXTENSIONS_MENU_CONTEXT_MENU_TOOLTIP 8225
#define IDS_EXTENSIONS_MENU_PIN_BUTTON_TOOLTIP 8226
#define IDS_EXTENSIONS_MENU_UNPIN_BUTTON_TOOLTIP 8227
#define IDS_EXTENSIONS_MENU_ACCESSING_SITE_DATA_SHORT 8228
#define IDS_EXTENSIONS_MENU_ACCESSING_SITE_DATA 8229
#define IDS_EXTENSIONS_MENU_WANTS_TO_ACCESS_SITE_DATA_SHORT 8230
#define IDS_EXTENSIONS_MENU_WANTS_TO_ACCESS_SITE_DATA 8231
#define IDS_EXTENSIONS_MENU_CANT_ACCESS_SITE_DATA_SHORT 8232
#define IDS_EXTENSIONS_MENU_CANT_ACCESS_SITE_DATA 8233
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_TITLE 8234
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_HAS_ACCESS_SECTION_TITLE 8235
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_REQUESTS_ACCESS_SECTION_TITLE 8236
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_BLOCK_ALL_EXTENSIONS_TEXT 8237
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_NO_EXTENSIONS_HAVE_ACCESS_TEXT 8238
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_TITLE 8239
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_ALLOW_ALL_TEXT 8240
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_BLOCK_ALL_TEXT 8241
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_CUSTOMIZE_EACH_TEXT 8242
#define IDS_EXTENSIONS_MENU_EXTENSIONS_TAB_TITLE 8243
#define IDS_EXTENSIONS_MENU_EXTENSIONS_TAB_DISCOVER_MORE_TITLE 8244
#define IDS_EXTENSIONS_SETTINGS_API_TITLE_HOME_PAGE_BUBBLE 8245
#define IDS_EXTENSIONS_SETTINGS_API_TITLE_STARTUP_PAGES_BUBBLE 8246
#define IDS_EXTENSIONS_SETTINGS_API_TITLE_SEARCH_ENGINE_BUBBLE 8247
#define IDS_EXTENSIONS_NTP_CONTROLLED_TITLE_HOME_PAGE_BUBBLE 8248
#define IDS_EXTENSIONS_PROXY_CONTROLLED_TITLE_HOME_PAGE_BUBBLE 8249
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_SEARCH_ENGINE_SPECIFIC 8250
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_SEARCH_ENGINE 8251
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_HOME_PAGE_SPECIFIC 8252
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_HOME_PAGE 8253
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_SEARCH_ENGINE 8254
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_HOME_PAGE 8255
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_HOME_AND_SEARCH 8256
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_BUBBLE_TITLE 8257
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_BUBBLE_HEADING 8258
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_ALLOW 8259
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_DENY 8260
#define IDS_EXTENSIONS_NTP_CONTROLLED_FIRST_LINE 8261
#define IDS_EXTENSIONS_PROXY_CONTROLLED_FIRST_LINE 8262
#define IDS_EXTENSIONS_PROXY_CONTROLLED_FIRST_LINE_EXTENSION_SPECIFIC 8263
#define IDS_EXTENSIONS_SETTINGS_API_THIRD_LINE_CONFIRMATION 8264
#define IDS_EXTENSION_CONTROLLED_RESTORE_SETTINGS 8265
#define IDS_EXTENSION_CONTROLLED_KEEP_CHANGES 8266
#define IDS_WEB_APP_MENU_BUTTON_TOOLTIP 8267
#define IDS_WEB_APP_ENABLE_WINDOW_CONTROLS_OVERLAY_TOOLTIP 8268
#define IDS_WEB_APP_DISABLE_WINDOW_CONTROLS_OVERLAY_TOOLTIP 8269
#define IDS_WEB_APP_WINDOW_CONTROLS_OVERLAY_ENABLED_ALERT 8270
#define IDS_WEB_APP_WINDOW_CONTROLS_OVERLAY_DISABLED_ALERT 8271
#define IDS_WEB_APP_SETTINGS_TITLE 8272
#define IDS_WEB_APP_SETTINGS_LINK 8273
#define IDS_WEB_APP_SETTINGS_LINK_TOOLTIP 8274
#define IDS_COMPONENTS_TITLE 8275
#define IDS_COMPONENTS_VERSION 8276
#define IDS_COMPONENTS_NONE_INSTALLED 8277
#define IDS_COMPONENTS_NO_COMPONENTS 8278
#define IDS_COMPONENTS_CHECK_FOR_UPDATE 8279
#define IDS_COMPONENTS_STATUS_LABEL 8280
#define IDS_COMPONENTS_CHECKING_LABEL 8281
#define IDS_COMPONENTS_SVC_STATUS_NEW 8285
#define IDS_COMPONENTS_SVC_STATUS_CHECKING 8286
#define IDS_COMPONENTS_SVC_STATUS_UPDATE 8287
#define IDS_COMPONENTS_SVC_STATUS_DNL_DIFF 8288
#define IDS_COMPONENTS_SVC_STATUS_DNL 8289
#define IDS_COMPONENTS_SVC_STATUS_DOWNLOADED 8290
#define IDS_COMPONENTS_SVC_STATUS_UPDT_DIFF 8291
#define IDS_COMPONENTS_SVC_STATUS_UPDATING 8292
#define IDS_COMPONENTS_SVC_STATUS_UPDATED 8293
#define IDS_COMPONENTS_SVC_STATUS_UPTODATE 8294
#define IDS_COMPONENTS_SVC_STATUS_UPDATE_ERROR 8295
#define IDS_COMPONENTS_UNKNOWN 8296
#define IDS_COMPONENTS_EVT_STATUS_STARTED 8297
#define IDS_COMPONENTS_EVT_STATUS_SLEEPING 8298
#define IDS_COMPONENTS_EVT_STATUS_FOUND 8299
#define IDS_COMPONENTS_EVT_STATUS_READY 8300
#define IDS_COMPONENTS_EVT_STATUS_UPDATED 8301
#define IDS_COMPONENTS_EVT_STATUS_NOTUPDATED 8302
#define IDS_COMPONENTS_EVT_STATUS_UPDATE_ERROR 8303
#define IDS_COMPONENTS_EVT_STATUS_DOWNLOADING 8304
#define IDS_COMPONENTS_EVT_STATUS_UPDATING 8305
#define IDS_PASSWORD_MANAGER_ACCOUNT_CHOOSER_TITLE 8306
#define IDS_PASSWORD_MANAGER_CONFIRM_SAVED_TITLE 8307
#define IDS_PASSWORD_GENERATION_SUGGESTION 8308
#define IDS_PASSWORD_GENERATION_SUGGESTION_GPM 8309
#define IDS_PASSWORD_GENERATION_EDITING_SUGGESTION 8310
#define IDS_SAVE_PASSWORD 8311
#define IDS_SAVE_ACCOUNT 8312
#define IDS_UPDATE_PASSWORD 8313
#define IDS_SAVE_PASSWORD_DIFFERENT_DOMAINS_TITLE 8314
#define IDS_UPDATE_PASSWORD_DIFFERENT_DOMAINS_TITLE 8315
#define IDS_SAVE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION_GOOGLE_ACCOUNT 8316
#define IDS_UPDATE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION_GOOGLE_ACCOUNT 8317
#define IDS_SAVE_PASSWORD_FOOTER 8318
#define IDS_PASSWORD_BUBBLES_PASSWORD_MANAGER_LINK_TEXT_SYNCED_TO_ACCOUNT 8319
#define IDS_PASSWORD_BUBBLES_FOOTER_SYNCED_TO_ACCOUNT 8320
#define IDS_PASSWORD_BUBBLES_FOOTER_SAVING_ON_DEVICE 8321
#define IDS_PASSWORD_GENERATION_CONFIRMATION_GOOGLE_PASSWORD_MANAGER 8322
#define IDS_PASSWORD_GENERATION_PROMPT_GOOGLE_PASSWORD_MANAGER 8323
#define IDS_PASSWORD_MANAGEMENT_BUBBLE_FOOTER_ACCOUNT_STORE_USERS 8324
#define IDS_PASSWORD_MANAGER_GENERATED_PASSWORD_SAVED_MESSAGE_DESCRIPTION 8325
#define IDS_PASSWORD_MANAGER_SAVE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION 8326
#define IDS_PASSWORD_MANAGER_UPDATE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION 8327
#define IDS_PASSWORD_MANAGER_ACCOUNT_CHOOSER_SIGN_IN 8328
#define IDS_PASSWORD_MANAGER_MANAGE_PASSWORDS_BUTTON 8329
#define IDS_PASSWORD_MANAGER_ONBOARDING_TITLE_A 8330
#define IDS_PASSWORD_MANAGER_ONBOARDING_TITLE_B 8331
#define IDS_PASSWORD_MANAGER_ONBOARDING_TITLE_C 8332
#define IDS_PASSWORD_MANAGER_ONBOARDING_DETAILS_A 8333
#define IDS_PASSWORD_MANAGER_ONBOARDING_DETAILS_B 8334
#define IDS_PASSWORD_MANAGER_DICE_PROMO_SIGNIN_MESSAGE 8335
#define IDS_PASSWORD_MANAGER_DICE_PROMO_SYNC_MESSAGE 8336
#define IDS_WEBRTC_LOGS_TITLE 8337
#define IDS_WEBRTC_TEXT_LOGS_LOG_COUNT_BANNER_FORMAT 8338
#define IDS_WEBRTC_EVENT_LOGS_LOG_COUNT_BANNER_FORMAT 8339
#define IDS_WEBRTC_LOGS_LOG_HEADER_FORMAT 8340
#define IDS_WEBRTC_LOGS_LOG_LOCAL_FILE_LABEL_FORMAT 8341
#define IDS_WEBRTC_LOGS_NO_LOCAL_LOG_FILE_MESSAGE 8342
#define IDS_WEBRTC_LOGS_LOG_UPLOAD_TIME_FORMAT 8343
#define IDS_WEBRTC_LOGS_LOG_FAILED_UPLOAD_TIME_FORMAT 8344
#define IDS_WEBRTC_LOGS_LOG_REPORT_ID_FORMAT 8345
#define IDS_WEBRTC_LOGS_BUG_LINK_LABEL 8346
#define IDS_WEBRTC_LOGS_LOG_PENDING_MESSAGE 8347
#define IDS_WEBRTC_LOGS_LOG_ACTIVELY_UPLOADED_MESSAGE 8348
#define IDS_WEBRTC_LOGS_LOG_NOT_UPLOADED_MESSAGE 8349
#define IDS_WEBRTC_LOGS_EVENT_LOG_LOCAL_LOG_ID 8350
#define IDS_WEBRTC_LOGS_NO_TEXT_LOGS_MESSAGE 8351
#define IDS_WEBRTC_LOGS_NO_EVENT_LOGS_MESSAGE 8352
#define IDS_PLUGIN_HIDE 8353
#define IDS_PLUGIN_UPDATE 8354
#define IDS_PLUGIN_BLOCKED 8355
#define IDS_PLUGIN_BLOCKED_BY_POLICY 8356
#define IDS_PLUGIN_BLOCKED_NO_LOADING 8357
#define IDS_PLUGIN_OUTDATED 8359
#define IDS_PLUGIN_NOT_AUTHORIZED 8360
#define IDS_PLUGIN_DOWNLOADING 8361
#define IDS_PLUGIN_DOWNLOAD_ERROR 8362
#define IDS_PLUGIN_DOWNLOAD_ERROR_SHORT 8363
#define IDS_PLUGIN_UPDATING 8364
#define IDS_PLUGIN_DISABLED 8365
#define IDS_PLUGIN_DEPRECATED 8366
#define IDS_SESSION_CRASHED_BUBBLE_TITLE 8367
#define IDS_SESSION_CRASHED_BUBBLE_UMA_LINK_TEXT 8368
#define IDS_SETTINGS_STORAGE_PRESSURE_BUBBLE_VIEW_TITLE 8369
#define IDS_SETTINGS_STORAGE_PRESSURE_BUBBLE_VIEW_MESSAGE 8370
#define IDS_SETTINGS_STORAGE_PRESSURE_BUBBLE_VIEW_BUTTON_LABEL 8371
#define IDS_EXPERIMENTAL_LACROS_WARNING_MESSAGE 8372
#define IDS_EXPERIMENTAL_LACROS_WARNING_MESSAGE_PRIMARY 8373
#define IDS_EXPERIMENTAL_LACROS_WARNING_MESSAGE_MANAGED 8374
#define IDS_EXPERIMENTAL_LACROS_WARNING_LEARN_MORE 8375
#define IDS_BAD_FLAGS_WARNING_MESSAGE 8376
#define IDS_BAD_FEATURES_WARNING_MESSAGE 8377
#define IDS_BAD_ENVIRONMENT_VARIABLES_WARNING_MESSAGE 8378
#define IDS_BLOCKED_DISPLAYING_INSECURE_CONTENT_TITLE 8379
#define IDS_BLOCKED_DISPLAYING_INSECURE_CONTENT 8380
#define IDS_ALLOW_INSECURE_CONTENT_BUTTON 8381
#define IDS_ADD_TO_SHELF_INFOBAR_TITLE 8382
#define IDS_ADD_TO_SHELF_INFOBAR_ADD_BUTTON 8383
#define IDS_ABOUT_SYS_TITLE 8384
#define IDS_ABOUT_SYS_DESC 8385
#define IDS_ABOUT_SYS_TABLE_TITLE 8386
#define IDS_ABOUT_SYS_LOG_FILE_TABLE_TITLE 8387
#define IDS_ABOUT_SYS_EXPAND_ALL 8388
#define IDS_ABOUT_SYS_COLLAPSE_ALL 8389
#define IDS_ABOUT_SYS_EXPAND 8390
#define IDS_ABOUT_SYS_COLLAPSE 8391
#define IDS_ABOUT_SYS_PARSE_ERROR 8392
#define IDS_ABOUT_BROWSER_SWITCH_TITLE 8393
#define IDS_ABOUT_BROWSER_SWITCH_OPENING_TITLE_UNKNOWN_BROWSER 8394
#define IDS_ABOUT_BROWSER_SWITCH_OPENING_TITLE_KNOWN_BROWSER 8395
#define IDS_ABOUT_BROWSER_SWITCH_ERROR_TITLE_UNKNOWN_BROWSER 8396
#define IDS_ABOUT_BROWSER_SWITCH_ERROR_TITLE_KNOWN_BROWSER 8397
#define IDS_ABOUT_BROWSER_SWITCH_COUNTDOWN_TITLE_UNKNOWN_BROWSER 8398
#define IDS_ABOUT_BROWSER_SWITCH_COUNTDOWN_TITLE_KNOWN_BROWSER 8399
#define IDS_ABOUT_BROWSER_SWITCH_GENERIC_ERROR_UNKNOWN_BROWSER 8400
#define IDS_ABOUT_BROWSER_SWITCH_GENERIC_ERROR_KNOWN_BROWSER 8401
#define IDS_ABOUT_BROWSER_SWITCH_PROTOCOL_ERROR 8402
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_DESC 8403
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_TITLE 8404
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_NOTHING_SHOWN 8405
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_LBS_DISABLED 8406
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_URL_CHECKER_TITLE 8407
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_URL_CHECKER_DESC 8408
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_OPEN_BROWSER 8409
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_INVALID_URL 8410
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_TITLE 8411
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_DESC 8412
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SOURCE 8413
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_NOT_CONFIGURED 8414
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_NOT_FETCHED 8415
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_DOWNLOAD_BUTTON 8416
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_LAST_DOWNLOAD_DATE 8417
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_NEXT_DOWNLOAD_DATE 8418
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_TITLE 8419
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_DESCRIPTION 8420
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_FIRST_PARAGRAPH 8421
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_SECOND_PARAGRAPH 8422
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_TABLE_COLUMN_RULE 8423
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_TABLE_COLUMN_OPENS_IN 8424
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_TABLE_COLUMN_SOURCE 8425
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_TITLE 8426
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_DESCRIPTION 8427
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_FIRST_PARAGRAPH 8428
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_SECOND_PARAGRAPH 8429
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_TABLE_COLUMN_RULE 8430
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_TABLE_COLUMN_SOURCE 8431
#define IDS_NACL_APP_MISSING_ARCH_MESSAGE 8435
#define IDS_OMNIBOX_PLACEHOLDER_TEXT 8440
#define IDS_PASTE_AND_GO 8441
#define IDS_PASTE_AND_SEARCH 8442
#define IDS_PASTE_AND_GO_EMPTY 8443
#define IDS_OMNIBOX_KEYWORD_HINT 8444
#define IDS_OMNIBOX_EXTENSION_KEYWORD_HINT 8445
#define IDS_OMNIBOX_KEYWORD_HINT_KEY_ACCNAME 8446
#define IDS_OMNIBOX_KEYWORD_HINT_TOUCH 8447
#define IDS_OMNIBOX_EXTENSION_KEYWORD_HINT_TOUCH 8448
#define IDS_OMNIBOX_KEYWORD_TEXT 8449
#define IDS_OMNIBOX_KEYWORD_TEXT_MD 8450
#define IDS_OMNIBOX_CLEAR_ALL 8451
#define IDS_OMNIBOX_WHY_THIS_SUGGESTION 8453
#define IDS_OMNIBOX_REMOVE_SUGGESTION 8454
#define IDS_OMNIBOX_REMOVE_SUGGESTION_BUBBLE_TITLE 8455
#define IDS_OMNIBOX_REMOVE_SUGGESTION_BUBBLE_DESCRIPTION 8456
#define IDS_CONTEXT_MENU_SHOW_FULL_URLS 8457
#define IDS_GOOGLE_SEARCH_BOX_EMPTY_HINT 8458
#define IDS_GOOGLE_SEARCH_BOX_EMPTY_HINT_MD 8459
#define IDS_GOOGLE_SEARCH_BOX_EMPTY_HINT_SHORT 8460
#define IDS_NTP_CUSTOM_LINKS_ADD_SHORTCUT_TOOLTIP 8461
#define IDS_NTP_CUSTOM_LINKS_ADD_SHORTCUT_TITLE 8462
#define IDS_NTP_CUSTOM_LINKS_EDIT_SHORTCUT_TOOLTIP 8463
#define IDS_NTP_CUSTOM_LINKS_EDIT_SHORTCUT 8464
#define IDS_UPLOAD_IMAGE_FORMAT 8465
#define IDS_NTP_CUSTOM_LINKS_NAME 8466
#define IDS_NTP_CUSTOM_LINKS_URL 8467
#define IDS_NTP_CUSTOM_LINKS_REMOVE 8468
#define IDS_NTP_CUSTOM_LINKS_CANCEL 8469
#define IDS_NTP_CUSTOM_LINKS_DONE 8470
#define IDS_NTP_CUSTOM_LINKS_INVALID_URL 8471
#define IDS_NTP_CUSTOM_LINKS_CANT_CREATE 8472
#define IDS_NTP_CUSTOM_LINKS_CANT_EDIT 8473
#define IDS_NTP_CUSTOM_LINKS_CANT_REMOVE 8474
#define IDS_NTP_CONFIRM_MSG_SHORTCUT_REMOVED 8475
#define IDS_NTP_CONFIRM_MSG_SHORTCUT_EDITED 8476
#define IDS_NTP_CONFIRM_MSG_SHORTCUT_ADDED 8477
#define IDS_NTP_CUSTOM_LINKS_ALREADY_EXISTS 8478
#define IDS_NTP_CONFIRM_MSG_RESTORE_DEFAULTS 8479
#define IDS_NTP_CUSTOM_BG_CHROME_WALLPAPERS 8480
#define IDS_NTP_CUSTOM_BG_UPLOAD_AN_IMAGE 8481
#define IDS_NTP_CUSTOM_BG_SELECT_A_COLLECTION 8482
#define IDS_NTP_CUSTOM_BG_DAILY_REFRESH 8483
#define IDS_NTP_CUSTOM_BG_RESTORE_DEFAULT 8484
#define IDS_NTP_CUSTOM_BG_CANCEL 8485
#define IDS_NTP_CONNECTION_ERROR_NO_PERIOD 8486
#define IDS_NTP_CONNECTION_ERROR 8487
#define IDS_NTP_ERROR_MORE_INFO 8488
#define IDS_NTP_CUSTOM_BG_BACKGROUNDS_UNAVAILABLE 8489
#define IDS_NTP_CUSTOM_BG_BACK_LABEL 8490
#define IDS_NTP_CUSTOM_BG_IMAGE_SELECTED 8491
#define IDS_NTP_CUSTOM_BG_CUSTOMIZE_NTP_LABEL 8492
#define IDS_NTP_DISMISS_PROMO 8493
#define IDS_NTP_DOODLE_SHARE_LABEL 8494
#define IDS_NTP_DOODLE_SHARE_DIALOG_CLOSE_LABEL 8495
#define IDS_NTP_DOODLE_SHARE_DIALOG_FACEBOOK_LABEL 8496
#define IDS_NTP_DOODLE_SHARE_DIALOG_TWITTER_LABEL 8497
#define IDS_NTP_DOODLE_SHARE_DIALOG_MAIL_LABEL 8498
#define IDS_NTP_DOODLE_SHARE_DIALOG_COPY_LABEL 8499
#define IDS_NTP_DOODLE_SHARE_DIALOG_LINK_LABEL 8500
#define IDS_NTP_CUSTOMIZE_MENU_BACKGROUND_LABEL 8501
#define IDS_NTP_CUSTOMIZE_MENU_BACKGROUND_DISABLED_LABEL 8502
#define IDS_NTP_CUSTOMIZE_MENU_SHORTCUTS_LABEL 8503
#define IDS_NTP_CUSTOMIZE_MENU_MODULES_LABEL 8504
#define IDS_NTP_CUSTOMIZE_MENU_COLOR_LABEL 8505
#define IDS_NTP_CUSTOMIZE_NO_BACKGROUND_LABEL 8506
#define IDS_NTP_CUSTOMIZE_UPLOAD_FROM_DEVICE_LABEL 8507
#define IDS_NTP_CUSTOMIZE_HIDE_SHORTCUTS_LABEL 8508
#define IDS_NTP_CUSTOMIZE_HIDE_SHORTCUTS_DESC 8509
#define IDS_NTP_CUSTOMIZE_MY_SHORTCUTS_LABEL 8510
#define IDS_NTP_CUSTOMIZE_MOST_VISITED_LABEL 8511
#define IDS_NTP_CUSTOMIZE_MOST_VISITED_DESC 8512
#define IDS_NTP_CUSTOMIZE_MY_SHORTCUTS_DESC 8513
#define IDS_NTP_CUSTOMIZE_HIDE_ALL_CARDS_LABEL 8514
#define IDS_NTP_CUSTOMIZE_CUSTOMIZE_CARDS_LABEL 8515
#define IDS_NTP_CUSTOMIZE_3PT_THEME_DESC 8516
#define IDS_NTP_CUSTOMIZE_3PT_THEME_UNINSTALL 8517
#define IDS_NTP_CUSTOMIZE_COLOR_PICKER_LABEL 8518
#define IDS_NTP_THEME_MANAGED_DIALOG_TITLE 8519
#define IDS_NTP_THEME_MANAGED_DIALOG_BODY 8520
#define IDS_NTP_CUSTOMIZE_DEFAULT_LABEL 8521
#define IDS_NTP_COLORS_WARM_GREY 8522
#define IDS_NTP_COLORS_COOL_GREY 8523
#define IDS_NTP_COLORS_MIDNIGHT_BLUE 8524
#define IDS_NTP_COLORS_BLACK 8525
#define IDS_NTP_COLORS_BEIGE_AND_WHITE 8526
#define IDS_NTP_COLORS_YELLOW_AND_WHITE 8527
#define IDS_NTP_COLORS_GREEN_AND_WHITE 8528
#define IDS_NTP_COLORS_LIGHT_TEAL_AND_WHITE 8529
#define IDS_NTP_COLORS_LIGHT_PURPLE_AND_WHITE 8530
#define IDS_NTP_COLORS_PINK_AND_WHITE 8531
#define IDS_NTP_COLORS_BEIGE 8532
#define IDS_NTP_COLORS_ORANGE 8533
#define IDS_NTP_COLORS_LIGHT_GREEN 8534
#define IDS_NTP_COLORS_LIGHT_TEAL 8535
#define IDS_NTP_COLORS_LIGHT_BLUE 8536
#define IDS_NTP_COLORS_PINK 8537
#define IDS_NTP_COLORS_DARK_PINK_AND_RED 8538
#define IDS_NTP_COLORS_DARK_RED_AND_ORANGE 8539
#define IDS_NTP_COLORS_DARK_GREEN 8540
#define IDS_NTP_COLORS_DARK_TEAL 8541
#define IDS_NTP_COLORS_DARK_BLUE 8542
#define IDS_NTP_COLORS_DARK_PURPLE 8543
#define IDS_NTP_MODULES_INFO_BUTTON_TITLE 8544
#define IDS_NTP_MODULES_DISMISS_TOAST_MESSAGE 8545
#define IDS_NTP_MODULES_DISABLE_TOAST_MESSAGE 8546
#define IDS_NTP_MODULES_DISMISS_BUTTON_TEXT 8547
#define IDS_NTP_MODULES_DISABLE_BUTTON_TEXT 8548
#define IDS_NTP_MODULES_CUSTOMIZE_BUTTON_TEXT 8549
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_TODAY 8550
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_YESTERDAY 8551
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_PAST_WEEK 8552
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_PAST_MONTH 8553
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_PREVIOUSLY 8554
#define IDS_NTP_MODULES_SHOPPING_TASKS_SENTENCE 8555
#define IDS_NTP_MODULES_SHOPPING_TASKS_LOWER 8556
#define IDS_NTP_MODULES_RECIPE_INFO 8557
#define IDS_NTP_MODULES_RECIPE_TASKS_SENTENCE 8558
#define IDS_NTP_MODULES_RECIPE_TASKS_LOWER 8559
#define IDS_NTP_MODULES_RECIPE_TASKS_LOWER_THESE 8560
#define IDS_NTP_MODULES_RECIPE_VIEWED_TASKS_SENTENCE 8561
#define IDS_NTP_MODULES_RECIPE_VIEWED_TASKS_LOWER 8562
#define IDS_NTP_MODULES_RECIPE_VIEWED_TASKS_LOWER_THESE 8563
#define IDS_NTP_MODULES_CART_SENTENCE 8564
#define IDS_NTP_MODULES_CART_SENTENCE_V2 8565
#define IDS_NTP_MODULES_CART_LOWER 8566
#define IDS_NTP_MODULES_CART_LOWER_THESE 8567
#define IDS_NTP_MODULES_CART_LOWER_YOUR 8568
#define IDS_NTP_MODULES_CART_INFO 8569
#define IDS_NTP_MODULES_CART_ITEM_COUNT_SINGULAR 8570
#define IDS_NTP_MODULES_CART_ITEM_COUNT_MULTIPLE 8571
#define IDS_NTP_MODULES_DRIVE_SENTENCE 8572
#define IDS_NTP_MODULES_DRIVE_SENTENCE2 8573
#define IDS_NTP_MODULES_DRIVE_FILES_SENTENCE 8574
#define IDS_NTP_MODULES_DRIVE_FILES_LOWER 8575
#define IDS_NTP_MODULES_DUMMY_LOWER 8576
#define IDS_NTP_MODULES_DUMMY_TITLE 8577
#define IDS_NTP_MODULES_DUMMY2_TITLE 8578
#define IDS_NTP_MODULES_DRIVE_TITLE 8579
#define IDS_NTP_MODULES_DRIVE_INFO 8580
#define IDS_NTP_MODULES_PHOTOS_TITLE 8581
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_TITLE 8582
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_HIDE_TODAY 8583
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_HIDDEN_TODAY 8584
#define IDS_NTP_MODULES_PHOTOS_SOFT_OPT_OUT 8585
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_DISABLE 8586
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_DISABLED 8587
#define IDS_NTP_MODULES_PHOTOS_INFO 8588
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_EXPLORE 8589
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_TITLE 8590
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_PERSONALIZED_WELCOME_TITLE_TEMPLATE 8591
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_RH_WELCOME_TITLE 8592
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_FAVORITE_PEOPLE_WELCOME_TITLE 8593
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_TRIPS_WELCOME_TITLE 8594
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_TEXT 8595
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_BUTTON_OPT_IN 8596
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_BUTTON_OPT_OUT 8597
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_BUTTON_SOFT_OPT_OUT 8598
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_EXAMPLE 8599
#define IDS_NTP_MODULES_PHOTOS_NEW 8600
#define IDS_NTP_MODULES_KALEIDOSCOPE_TITLE 8601
#define IDS_NTP_MODULES_SHOPPING_TASKS_INFO_TITLE 8602
#define IDS_NTP_MODULES_TASKS_INFO 8603
#define IDS_NTP_MODULES_SHOPPING_TASKS_INFO_CLOSE 8604
#define IDS_NTP_MODULES_SHOPPING_TASKS_RELATED 8605
#define IDS_NTP_MODULES_RECIPE_TASKS_RECOMMENDED 8606
#define IDS_NTP_MODULES_CART_WARM_WELCOME 8607
#define IDS_NTP_MODULES_CART_MODULE_MENU_HIDE_TOAST_MESSAGE 8608
#define IDS_NTP_MODULES_CART_CART_MENU_HIDE_MERCHANT 8609
#define IDS_NTP_MODULES_CART_CART_MENU_HIDE_MERCHANT_TOAST_MESSAGE 8610
#define IDS_NTP_MODULES_CART_CART_MENU_REMOVE_MERCHANT 8611
#define IDS_NTP_MODULES_CART_CART_MENU_REMOVE_MERCHANT_TOAST_MESSAGE 8612
#define IDS_NTP_MODULES_CART_DISCOUNT_CHIP_AMOUNT 8613
#define IDS_NTP_MODULES_CART_DISCOUNT_CHIP_UP_TO_AMOUNT 8614
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONTENT 8615
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_ACCEPT 8616
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_ACCEPT_CONFIRMATION 8617
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_REJECT 8618
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_REJECT_CONFIRMATION 8619
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONFIRMATION_DISMISS 8620
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONTENT_V2 8621
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONTENT_V3 8622
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_WITH_MERCHANT_NAME 8623
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_WITH_TWO_MERCHANT_NAMES 8624
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_WITH_THREE_MERCHANT_NAMES 8625
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_CONTINUE 8626
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_TITLE 8627
#define IDS_NTP_MODULES_NEW_TAG_LABEL 8628
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_TITLE 8629
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_BODY_LINE_1 8630
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_BODY_LINE_2 8631
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_OPT_IN 8632
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_OPT_OUT 8633
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_OPT_OUT_TOAST 8634
#define IDS_EXTENSIONS_PROMO_PERFORMANCE 8635
#define IDS_EXTENSIONS_PROMO_PRIVACY 8636
#define IDS_EXTENSIONS_PROMO_NEUTRAL 8637
#define IDS_STAR_VIEW_MENU_ADD_BOOKMARK 8639
#define IDS_STAR_VIEW_MENU_EDIT_BOOKMARK 8640
#define IDS_STAR_VIEW_MENU_MOVE_TO_READ_LATER 8641
#define IDS_STAR_VIEW_MENU_MARK_AS_READ 8642
#define IDS_READ_LATER_TITLE 8643
#define IDS_READ_LATER_MENU_UNREAD_HEADER 8644
#define IDS_READ_LATER_MENU_READ_HEADER 8645
#define IDS_READ_LATER_MENU_TOOLTIP_MARK_AS_READ 8646
#define IDS_READ_LATER_MENU_TOOLTIP_MARK_AS_UNREAD 8647
#define IDS_READ_LATER_MENU_EMPTY_STATE_ADD_FROM_DIALOG_SUBHEADER 8648
#define IDS_READ_LATER_MENU_EMPTY_STATE_HEADER 8649
#define IDS_READ_LATER_MENU_EMPTY_STATE_SUBHEADER 8650
#define IDS_READ_LATER_ADD_CURRENT_TAB 8651
#define IDS_READ_LATER_CANT_ADD_CURRENT_TAB 8652
#define IDS_READ_LATER_REMOVE_CURRENT_TAB 8653
#define IDS_READ_LATER_CONTEXT_MENU_MARK_AS_UNREAD 8654
#define IDS_READ_LATER_CONTEXT_MENU_MARK_AS_READ 8655
#define IDS_READ_LATER_CONTEXT_MENU_DELETE 8656
#define IDS_SIDE_PANEL_TITLE 8657
#define IDS_READ_ANYTHING_TITLE 8658
#define IDS_USER_NOTE_TITLE 8659
#define IDS_ADD_NEW_USER_NOTE_PLACEHOLDER_TEXT 8660
#define IDS_FEED_TITLE 8661
#define IDS_TOOLTIP_BACK 266
#define IDS_ACCDESCRIPTION_BACK 307
#define IDS_TOOLTIP_CHROMELABS_BUTTON 8662
#define IDS_TOOLTIP_LEFT_ALIGNED_SIDE_PANEL_BUTTON 8663
#define IDS_TOOLTIP_CHROMELABS_COMBOBOX 8664
#define IDS_TOOLTIP_CHROMELABS_FEEDBACK_BUTTON 8665
#define IDS_TOOLTIP_FORWARD 267
#define IDS_ACCDESCRIPTION_FORWARD 309
#define IDS_TOOLTIP_DOWNLOAD_ICON 8666
#define IDS_TOOLTIP_HOME 269
#define IDS_TOOLTIP_RELOAD 268
#define IDS_TOOLTIP_RELOAD_WITH_MENU 8667
#define IDS_TOOLTIP_STOP 329
#define IDS_TOOLTIP_EXTENSIONS_BUTTON 8668
#define IDS_TOOLTIP_EXTENSIONS_SITE_ACCESS_BUTTON 8669
#define IDS_TOOLTIP_SIDE_PANEL_SHOW 8670
#define IDS_TOOLTIP_SIDE_PANEL_HIDE 8671
#define IDS_TOOLTIP_CLOSE_TAB 322
#define IDS_TOOLTIP_LOCATION_ICON 318
#define IDS_TOOLTIP_NEW_TAB 261
#define IDS_TOOLTIP_MIC_SEARCH 8672
#define IDS_TOOLTIP_SAVE_CREDIT_CARD 8673
#define IDS_TOOLTIP_SAVE_CREDIT_CARD_PENDING 8674
#define IDS_TOOLTIP_SAVE_CREDIT_CARD_FAILURE 8675
#define IDS_TOOLTIP_MIGRATE_LOCAL_CARD 8676
#define IDS_TOOLTIP_TRANSLATE 8677
#define IDS_TOOLTIP_ZOOM 8678
#define IDS_TOOLTIP_ZOOM_EXTENSION_ICON 8679
#define IDS_ZOOM_SET_DEFAULT 8680
#define IDS_TOOLTIP_FIND 8681
#define IDS_TOOLTIP_TAB_SEARCH 8682
#define IDS_TOOLTIP_INTENT_PICKER_ICON 8683
#define IDS_INTENT_PICKER_BUBBLE_VIEW_OPEN_WITH 8684
#define IDS_INTENT_PICKER_BUBBLE_VIEW_REMEMBER_SELECTION 8685
#define IDS_INTENT_PICKER_BUBBLE_VIEW_OPEN 8686
#define IDS_INTENT_PICKER_BUBBLE_VIEW_STAY_IN_CHROME 8687
#define IDS_INTENT_PICKER_BUBBLE_VIEW_INITIATING_ORIGIN 8688
#define IDS_INTENT_CHIP_LABEL 8689
#define IDS_INTENT_CHIP_IPH 8690
#define IDS_ACCESSIBLE_INCOGNITO_WINDOW_TITLE_FORMAT 8693
#define IDS_ACCESSIBLE_GUEST_WINDOW_TITLE_FORMAT 8694
#define IDS_ACCESSIBLE_WINDOW_TITLE_WITH_PROFILE_FORMAT 8695
#define IDS_ACCNAME_APP_UPGRADE_RECOMMENDED 8696
#define IDS_ACCNAME_FULLSCREEN 8697
#define IDS_ACCNAME_HOME 311
#define IDS_ACCNAME_RELOAD 310
#define IDS_ACCNAME_FIND 8698
#define IDS_ACCNAME_BOOKMARKS 8699
#define IDS_ACCNAME_SAVED_TAB_GROUPS 8700
#define IDS_ACCNAME_BOOKMARKS_CHEVRON 8701
#define IDS_ACCNAME_BOOKMARK_BUTTON_ROLE_DESCRIPTION 8702
#define IDS_ACCNAME_BOOKMARK_FOLDER_BUTTON_ROLE_DESCRIPTION 8703
#define IDS_ACCNAME_SAVED_TAB_GROUP_BUTTON_ROLE_DESCRIPTION 8704
#define IDS_ACCNAME_CHROMELABS_BUTTON 8705
#define IDS_ACCNAME_LEFT_ALIGNED_SIDE_PANEL_BUTTON 8706
#define IDS_ACCESSIBLE_TEXT_CHROMELABS_BUTTON_ADDED_BY_ENTERPRISE_POLICY 8707
#define IDS_ACCESSIBLE_TEXT_CHROMELABS_BUTTON_REMOVED_BY_ENTERPRISE_POLICY 8708
#define IDS_ACCNAME_CHROMELABS_COMBOBOX 8709
#define IDS_ACCNAME_CHROMELABS_COMBOBOX_MAC 8710
#define IDS_ACCNAME_SEPARATOR 8711
#define IDS_ACCNAME_EXTENSIONS 8712
#define IDS_ACCNAME_NEWTAB 264
#define IDS_ACCNAME_MINIMIZE 8713
#define IDS_ACCNAME_MAXIMIZE 8714
#define IDS_ACCNAME_RESTORE 8715
#define IDS_ACCNAME_CLOSE_TAB 323
#define IDS_ACCNAME_ZOOM_SET_DEFAULT 8716
#define IDS_ACCNAME_TAB_SEARCH 8717
#define IDS_ACCNAME_TAB_SCROLL_LEADING 8718
#define IDS_ACCNAME_TAB_SCROLL_TRAILING 8719
#define IDS_ACCNAME_MUTE_TAB 8720
#define IDS_ACCNAME_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX 8721
#define IDS_ALLOWED_CLIPBOARD_TITLE 8722
#define IDS_BLOCKED_CLIPBOARD_TITLE 8723
#define IDS_ALLOWED_CLIPBOARD_MESSAGE 8724
#define IDS_ALLOWED_CLIPBOARD_BLOCK 8725
#define IDS_ALLOWED_CLIPBOARD_NO_ACTION 8726
#define IDS_BLOCKED_CLIPBOARD_MESSAGE 8727
#define IDS_BLOCKED_CLIPBOARD_UNBLOCK 8728
#define IDS_BLOCKED_CLIPBOARD_NO_ACTION 8729
#define IDS_BOOKMARK_PROMO_0 8730
#define IDS_BOOKMARK_PROMO_1 8731
#define IDS_BOOKMARK_PROMO_2 8732
#define IDS_CHROME_TIP 8733
#define IDS_GLOBAL_MEDIA_CONTROLS_PROMO 8734
#define IDS_INCOGNITOWINDOW_PROMO_0 8735
#define IDS_INCOGNITOWINDOW_PROMO_1 8736
#define IDS_INCOGNITOWINDOW_PROMO_2 8737
#define IDS_INCOGNITOWINDOW_PROMO_3 8738
#define IDS_NEWTAB_PROMO_0 8739
#define IDS_NEWTAB_PROMO_1 8740
#define IDS_NEWTAB_PROMO_2 8741
#define IDS_READING_LIST_DISCOVERY_PROMO 8742
#define IDS_READING_LIST_ENTRY_POINT_PROMO 8743
#define IDS_REOPEN_TAB_PROMO 8744
#define IDS_DESKTOP_PWA_INSTALL_PROMO 8745
#define IDS_UPDATED_CONNECTION_SECURITY_INDICATORS_PROMO 8746
#define IDS_REOPEN_TAB_PROMO_SCREENREADER 8747
#define IDS_READING_LIST_IN_SIDE_PANEL_PROMO 8748
#define IDS_TAB_GROUPS_NEW_GROUP_PROMO 8749
#define IDS_TAB_GROUPS_UNNAMED_GROUP_TOOLTIP 8750
#define IDS_TAB_GROUPS_NAMED_GROUP_TOOLTIP 8751
#define IDS_TAB_AUDIO_MUTING_PROMO 8752
#define IDS_SHARED_HIGHLIGHTING_PROMO 8753
#define IDS_TUTORIAL_TAB_GROUP_ADD_TAB_TO_GROUP 8754
#define IDS_TUTORIAL_TAB_GROUP_EDIT_BUBBLE 8755
#define IDS_TUTORIAL_TAB_GROUP_DRAG_TAB 8756
#define IDS_TUTORIAL_TAB_GROUP_COLLAPSE 8757
#define IDS_TUTORIAL_TAB_GROUP_SUCCESS_TITLE 8758
#define IDS_TUTORIAL_TAB_GROUP_SUCCESS_DESCRIPTION 8759
#define IDS_BROWSER_HANGMONITOR_RENDERER_TITLE 8761
#define IDS_BROWSER_HANGMONITOR_RENDERER 8762
#define IDS_BROWSER_HANGMONITOR_RENDERER_INFOBAR 8763
#define IDS_BROWSER_HANGMONITOR_IFRAME_TITLE 8764
#define IDS_BROWSER_HANGMONITOR_RENDERER_INFOBAR_END 8765
#define IDS_BROWSER_HANGMONITOR_RENDERER_WAIT 8766
#define IDS_BROWSER_HANGMONITOR_RENDERER_END 8767
#define IDS_BROWSER_HANGMONITOR_PLUGIN_INFOBAR 8768
#define IDS_BROWSER_HANGMONITOR_PLUGIN_INFOBAR_KILLBUTTON 8769
#define IDS_PASSWORDS_AUTO_SIGNIN_TITLE 8770
#define IDS_PASSWORDS_AUTO_SIGNIN_DESCRIPTION 8771
#define IDS_PASSWORDS_VIA_FEDERATION 8772
#define IDS_CONFIRM_MESSAGEBOX_YES_BUTTON_LABEL 8773
#define IDS_CONFIRM_MESSAGEBOX_NO_BUTTON_LABEL 8774
#define IDS_NEAR_OOM_REDUCTION_MESSAGE_TITLE 8775
#define IDS_NEAR_OOM_REDUCTION_MESSAGE_DESCRIPTION 8776
#define IDS_TAILORED_SECURITY_UNCONSENTED_MESSAGE_TITLE 8777
#define IDS_TAILORED_SECURITY_UNCONSENTED_MESSAGE_ACCEPT 8778
#define IDS_TAILORED_SECURITY_DISPLAY_SOURCE 8779
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_NOTIFICATION_TITLE 8780
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_NOTIFICATION_ACCEPT 8781
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_NOTIFICATION_TITLE 8782
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_NOTIFICATION_DESCRIPTION 8783
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_NOTIFICATION_TURN_OFF 8784
#define IDS_PASSWORD_MANAGER_CANCEL_BUTTON 8785
#define IDS_PASSWORD_MANAGER_USERNAME_LABEL 8786
#define IDS_PASSWORD_MANAGER_PASSWORD_LABEL 8787
#define IDS_PASSWORD_MANAGER_UPDATED_BUBBLE_TITLE 8788
#define IDS_PASSWORD_MANAGER_MORE_TO_FIX_BODY_MESSAGE 8789
#define IDS_PASSWORD_MANAGER_MORE_TO_FIX_BODY_MESSAGE_GOOGLE_PASSWORD_MANAGER 8790
#define IDS_PASSWORD_MANAGER_SAFE_STATE_BODY_MESSAGE 8791
#define IDS_PASSWORD_MANAGER_SAFE_STATE_BODY_MESSAGE_GOOGLE_PASSWORD_MANAGER 8792
#define IDS_PASSWORD_MANAGER_SAFE_STATE_SETTINGS 8793
#define IDS_PASSWORD_MANAGER_CHECK_REMAINING_BUTTON 8794
#define IDS_PASSWORD_MANAGER_SAVE_BUTTON 8795
#define IDS_PASSWORD_MANAGER_SAVE_BUBBLE_OPT_IN_BUTTON 8796
#define IDS_PASSWORD_MANAGER_MOVE_BUBBLE_OK_BUTTON 8797
#define IDS_PASSWORD_MANAGER_MOVE_BUBBLE_CANCEL_BUTTON 8798
#define IDS_PASSWORD_MANAGER_UPDATE_BUTTON 8799
#define IDS_PASSWORD_MANAGER_BUBBLE_BLOCKLIST_BUTTON 8800
#define IDS_PASSWORD_MANAGER_DESTINATION_DROPDOWN_ACCESSIBLE_NAME 8801
#define IDS_PASSWORD_MANAGER_DESTINATION_DROPDOWN_SAVE_TO_ACCOUNT 8802
#define IDS_PASSWORD_MANAGER_DESTINATION_DROPDOWN_SAVE_TO_DEVICE 8803
#define IDS_PASSWORD_MANAGER_IPH_TITLE_SAVE_TO_ACCOUNT 8804
#define IDS_PASSWORD_MANAGER_IPH_BODY_SAVE_REAUTH_FAIL 8805
#define IDS_PASSWORD_MANAGER_IPH_BODY_SAVE_TO_ACCOUNT 8806
#define IDS_PASSWORD_MANAGER_TOOLTIP_SAVE 8807
#define IDS_PASSWORD_MANAGER_TOOLTIP_MANAGE 8808
#define IDS_PASSWORD_MANAGER_TOOLTIP_MOVE 8809
#define IDS_PASSWORD_MANAGER_IMPORT_BUTTON 8810
#define IDS_PASSWORD_MANAGER_IMPORT_DIALOG_TITLE 8811
#define IDS_PASSWORD_MANAGER_EXPORT_DIALOG_TITLE 8812
#define IDS_PASSWORD_MANAGER_MOVE_TITLE 8813
#define IDS_PASSWORD_MANAGER_MOVE_HINT 8814
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_TITLE 8815
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_TITLE_GPM 8816
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_DESCRIPTION 8817
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_DESCRIPTION_GPM 8818
#define IDS_PASSWORD_MANAGER_SAVE_UNSYNCED_CREDENTIALS_BUTTON 8819
#define IDS_PASSWORD_MANAGER_SAVE_UNSYNCED_CREDENTIALS_BUTTON_GPM 8820
#define IDS_PASSWORD_MANAGER_DISCARD_UNSYNCED_CREDENTIALS_BUTTON 8821
#define IDS_IMPORT_FROM_FIREFOX 8838
#define IDS_IMPORT_FROM_ICEWEASEL 8839
#define IDS_IMPORT_FROM_SAFARI 8840
#define IDS_IMPORT_FROM_BOOKMARKS_HTML_FILE 8841
#define IDS_IMPORT_SETTINGS_MENU_MAC 107
#define IDS_CONFIRM_TO_QUIT_DESCRIPTION 8842
#define IDS_CONFIRM_TO_QUIT_OPTION 112
#define IDS_IMPORTER_LOCK_TITLE 8843
#define IDS_IMPORTER_LOCK_TEXT 8844
#define IDS_IMPORTER_LOCK_OK 8845
#define IDS_FEEDBACK_REPORT_APP_TITLE 8846
#define IDS_FEEDBACK_REPORT_PAGE_TITLE 8847
#define IDS_FEEDBACK_REPORT_PAGE_TITLE_SAD_TAB_FLOW 8848
#define IDS_FEEDBACK_MINIMIZE_BUTTON_LABEL 8849
#define IDS_FEEDBACK_CLOSE_BUTTON_LABEL 8850
#define IDS_FEEDBACK_FREE_TEXT_LABEL 8851
#define IDS_FEEDBACK_REPORT_URL_LABEL 8852
#define IDS_FEEDBACK_USER_EMAIL_LABEL 8853
#define IDS_FEEDBACK_ANONYMOUS_EMAIL_OPTION 8854
#define IDS_FEEDBACK_CONSENT_CHECKBOX_LABEL 8855
#define IDS_FEEDBACK_SCREENSHOT_LABEL 8856
#define IDS_FEEDBACK_SCREENSHOT_A11Y_TEXT 8857
#define IDS_FEEDBACK_INCLUDE_PERFORMANCE_TRACE_CHECKBOX 8858
#define IDS_FEEDBACK_BLUETOOTH_LOGS_CHECKBOX 8859
#define IDS_FEEDBACK_ASSISTANT_LOGS_MESSAGE 8860
#define IDS_FEEDBACK_BLUETOOTH_LOGS_MESSAGE 8861
#define IDS_FEEDBACK_OFFLINE_DIALOG_TITLE 8862
#define IDS_FEEDBACK_OFFLINE_DIALOG_TEXT 8863
#define IDS_FEEDBACK_INCLUDE_SYSTEM_INFORMATION_CHKBOX 8864
#define IDS_FEEDBACK_INCLUDE_ASSISTANT_INFORMATION_CHKBOX 8867
#define IDS_FEEDBACK_ATTACH_FILE_NOTE 8868
#define IDS_FEEDBACK_ATTACH_FILE_LABEL 8869
#define IDS_FEEDBACK_ATTACH_FILE_TO_BIG 8870
#define IDS_FEEDBACK_IWLWIFI_DEBUG_DUMP_EXPLAINER 8871
#define IDS_FEEDBACK_PRIVACY_NOTE 8872
#define IDS_FEEDBACK_NO_DESCRIPTION 8873
#define IDS_FEEDBACK_SEND_REPORT 8874
#define IDS_FEEDBACK_SYSINFO_PAGE_TITLE 8875
#define IDS_FEEDBACK_SYSINFO_PAGE_LOADING 8876
#define IDS_FEEDBACK_ADDITIONAL_INFO_LABEL 8877
#define IDS_CLEAR_BROWSING_DATA_TITLE 8878
#define IDS_CLEAR_BROWSING_DATA_HISTORY_NOTICE 8879
#define IDS_CLEAR_BROWSING_DATA_HISTORY_NOTICE_TITLE 8880
#define IDS_CLEAR_BROWSING_DATA_HISTORY_NOTICE_OK 8881
#define IDS_CLEAR_BROWSING_DATA_PASSWORDS_NOTICE 8882
#define IDS_CLEAR_BROWSING_DATA_PASSWORDS_NOTICE_TITLE 8883
#define IDS_CLEAR_BROWSING_DATA_PASSWORDS_NOTICE_OK 8884
#define IDS_MEDIA_SELECTED_MIC_LABEL 8885
#define IDS_MEDIA_SELECTED_CAMERA_LABEL 8886
#define IDS_MEDIA_MENU_NO_DEVICE_TITLE 8887
#define IDS_ZOOMLEVELS_CHROME_ERROR_PAGES_LABEL 8888
#define IDS_PROMOTE_INFOBAR_TEXT 8889
#define IDS_PROMOTE_INFOBAR_PROMOTE_BUTTON 8890
#define IDS_PROMOTE_INFOBAR_DONT_ASK_BUTTON 8891
#define IDS_PROMOTE_AUTHENTICATION_PROMPT 8892
#define IDS_PROMOTE_PREFLIGHT_LAUNCH_ERROR 8893
#define IDS_PROMOTE_PREFLIGHT_SCRIPT_ERROR 8894
#define IDS_UPGRADE_ERROR 8895
#define IDS_UPGRADE_ERROR_DETAILS 8896
#define IDS_REPORT_AN_ISSUE 8899
#define IDS_SETTINGS_RESET_PROMPT_TITLE_SEARCH_ENGINE 8906
#define IDS_SETTINGS_RESET_PROMPT_TITLE_STARTUP_PAGE 8907
#define IDS_SETTINGS_RESET_PROMPT_TITLE_HOMEPAGE 8908
#define IDS_SETTINGS_RESET_PROMPT_ACCEPT_BUTTON_LABEL 8909
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_SEARCH_ENGINE_NO_EXTENSIONS 8910
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_STARTUP_PAGE_SINGLE_NO_EXTENSIONS 8911
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_STARTUP_PAGE_MULTIPLE_NO_EXTENSIONS 8912
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_HOMEPAGE_NO_EXTENSIONS 8913
#define IDS_REENABLE_UPDATES 8914
#define IDS_PICTURE_IN_PICTURE_TITLE_TEXT 8915
#define IDS_PICTURE_IN_PICTURE_PAUSE_CONTROL_TEXT 8916
#define IDS_PICTURE_IN_PICTURE_PLAY_CONTROL_TEXT 8917
#define IDS_PICTURE_IN_PICTURE_REPLAY_CONTROL_TEXT 8918
#define IDS_PICTURE_IN_PICTURE_BACK_TO_TAB_CONTROL_TEXT 8919
#define IDS_PICTURE_IN_PICTURE_SKIP_AD_CONTROL_TEXT 8920
#define IDS_PICTURE_IN_PICTURE_MUTE_MICROPHONE_TEXT 8921
#define IDS_PICTURE_IN_PICTURE_UNMUTE_MICROPHONE_TEXT 8922
#define IDS_PICTURE_IN_PICTURE_TURN_ON_CAMERA_TEXT 8923
#define IDS_PICTURE_IN_PICTURE_TURN_OFF_CAMERA_TEXT 8924
#define IDS_PICTURE_IN_PICTURE_HANG_UP_TEXT 8925
#define IDS_PICTURE_IN_PICTURE_CLOSE_CONTROL_TEXT 8926
#define IDS_PICTURE_IN_PICTURE_RESIZE_HANDLE_TEXT 8927
#define IDS_PICTURE_IN_PICTURE_PLAY_PAUSE_CONTROL_ACCESSIBLE_TEXT 8928
#define IDS_PICTURE_IN_PICTURE_NEXT_TRACK_CONTROL_ACCESSIBLE_TEXT 8929
#define IDS_PICTURE_IN_PICTURE_PREVIOUS_TRACK_CONTROL_ACCESSIBLE_TEXT 8930
#define IDS_LOAD_STATE_WAITING_FOR_SOCKET_SLOT 8931
#define IDS_LOAD_STATE_WAITING_FOR_DELEGATE 8932
#define IDS_LOAD_STATE_WAITING_FOR_DELEGATE_GENERIC 8933
#define IDS_LOAD_STATE_WAITING_FOR_CACHE 8934
#define IDS_LOAD_STATE_ESTABLISHING_PROXY_TUNNEL 8935
#define IDS_LOAD_STATE_RESOLVING_PROXY_FOR_URL 8936
#define IDS_LOAD_STATE_RESOLVING_HOST_IN_PAC_FILE 8937
#define IDS_LOAD_STATE_DOWNLOADING_PAC_FILE 8938
#define IDS_LOAD_STATE_RESOLVING_HOST 8939
#define IDS_LOAD_STATE_CONNECTING 8940
#define IDS_LOAD_STATE_SSL_HANDSHAKE 8941
#define IDS_LOAD_STATE_SENDING_REQUEST 8942
#define IDS_LOAD_STATE_SENDING_REQUEST_WITH_PROGRESS 8943
#define IDS_LOAD_STATE_WAITING_FOR_RESPONSE 8944
#define IDS_TAB_CXMENU_NEWTABTORIGHT 8945
#define IDS_TAB_CXMENU_NEWTABTOLEFT 8946
#define IDS_TAB_CXMENU_RELOAD 8947
#define IDS_TAB_CXMENU_DUPLICATE 8948
#define IDS_TAB_CXMENU_CLOSETAB 8949
#define IDS_TAB_CXMENU_CLOSEOTHERTABS 8950
#define IDS_TAB_CXMENU_CLOSETABSTORIGHT 8951
#define IDS_TAB_CXMENU_CLOSETABSTOLEFT 8952
#define IDS_TAB_CXMENU_FOCUS_THIS_TAB 8953
#define IDS_TAB_CXMENU_PIN_TAB 8954
#define IDS_TAB_CXMENU_UNPIN_TAB 8955
#define IDS_TAB_CXMENU_SOUND_MUTE_SITE 8956
#define IDS_TAB_CXMENU_SOUND_UNMUTE_SITE 8957
#define IDS_TAB_CXMENU_READ_LATER 8958
#define IDS_TAB_CXMENU_ADD_TAB_TO_GROUP 8959
#define IDS_TAB_CXMENU_ADD_TAB_TO_NEW_GROUP 8960
#define IDS_TAB_CXMENU_SUBMENU_NEW_GROUP 8961
#define IDS_TAB_CXMENU_REMOVE_TAB_FROM_GROUP 8962
#define IDS_TAB_CXMENU_PLACEHOLDER_GROUP_TITLE 8963
#define IDS_TAB_CXMENU_MOVE_TABS_TO_NEW_WINDOW 8964
#define IDS_TAB_CXMENU_MOVETOANOTHERWINDOW 8965
#define IDS_TAB_CXMENU_MOVETOANOTHERNEWWINDOW 8966
#define IDS_TAB_CXMENU_FOLLOW_SITE 8967
#define IDS_TAB_CXMENU_UNFOLLOW_SITE 8968
#define IDS_TAB_SEARCH_PROMO 8976
#define IDS_TAB_SEARCH_SEARCH_TABS 8977
#define IDS_TAB_SEARCH_NO_RESULTS_FOUND 8978
#define IDS_TAB_SEARCH_CLOSE_TAB 8979
#define IDS_TAB_SEARCH_SUBMIT_FEEDBACK 8980
#define IDS_TAB_SEARCH_A11Y_TAB_CLOSED 8981
#define IDS_TAB_SEARCH_A11Y_FOUND_TAB 8982
#define IDS_TAB_SEARCH_A11Y_FOUND_TABS 8983
#define IDS_TAB_SEARCH_A11Y_FOUND_TAB_FOR 8984
#define IDS_TAB_SEARCH_A11Y_FOUND_TABS_FOR 8985
#define IDS_TAB_SEARCH_A11Y_OPEN_TAB 8986
#define IDS_TAB_SEARCH_A11Y_RECENTLY_CLOSED_TAB 8987
#define IDS_TAB_SEARCH_A11Y_RECENTLY_CLOSED_TAB_GROUP 8988
#define IDS_TAB_SEARCH_MEDIA_TABS 8989
#define IDS_TAB_SEARCH_OPEN_TABS 8990
#define IDS_TAB_SEARCH_RECENTLY_CLOSED 8991
#define IDS_TAB_SEARCH_RECENTLY_CLOSED_TABS 8992
#define IDS_TAB_SEARCH_ONE_TAB 8993
#define IDS_TAB_SEARCH_TAB_COUNT 8994
#define IDS_TAB_SEARCH_EXPAND_RECENTLY_CLOSED_ITEMS 8995
#define IDS_BROWSER_WINDOW_TITLE_MENU_ENTRY 8996
#define IDS_TAB_GROUP_HEADER_CXMENU_SAVE_GROUP 8997
#define IDS_TAB_GROUP_HEADER_CXMENU_NEW_TAB_IN_GROUP 8998
#define IDS_TAB_GROUP_HEADER_CXMENU_UNGROUP 8999
#define IDS_TAB_GROUP_HEADER_CXMENU_CLOSE_GROUP 9000
#define IDS_TAB_GROUP_HEADER_CXMENU_MOVE_GROUP_TO_NEW_WINDOW 9001
#define IDS_TAB_GROUP_HEADER_CXMENU_SEND_FEEDBACK 9002
#define IDS_TAB_GROUP_HEADER_BUBBLE_TITLE_PLACEHOLDER 9003
#define IDS_APP_MENU_RELOAD 9004
#define IDS_APP_MENU_NEW_WEB_PAGE 9005
#define IDS_APP_MENU_BUTTON_UPDATE 9006
#define IDS_APP_MENU_BUTTON_ERROR 9007
#define IDS_MEDIA_SCREEN_CAPTURE_CONFIRMATION_TITLE 9008
#define IDS_MEDIA_SCREEN_CAPTURE_CONFIRMATION_TEXT 9009
#define IDS_MEDIA_SCREEN_AND_AUDIO_CAPTURE_CONFIRMATION_TEXT 9010
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_TEXT 9011
#define IDS_MEDIA_SCREEN_CAPTURE_WITH_AUDIO_NOTIFICATION_TEXT 9012
#define IDS_MEDIA_WINDOW_CAPTURE_NOTIFICATION_TEXT 9013
#define IDS_MEDIA_TAB_CAPTURE_NOTIFICATION_TEXT 9014
#define IDS_MEDIA_TAB_CAPTURE_WITH_AUDIO_NOTIFICATION_TEXT 9015
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_HIDE 9016
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_SOURCE 9017
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_STOP 9018
#define IDS_PLATFORM_KEYS_SELECT_CERT_DIALOG_TEXT 9019
#define IDS_UNSAFE_FRAME_MESSAGE 9020
#define IDS_CLIENT_CERT_DIALOG_TITLE 9021
#define IDS_CLIENT_CERT_DIALOG_TEXT 9022
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TITLE 9023
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CERT_ENROLLMENT 9024
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CLIENT_AUTH 9025
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_LIST_CERTS 9026
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CERT_IMPORT 9027
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CERT_EXPORT 9028
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_PASSWORD_FIELD 9029
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_OK_BUTTON_LABEL 9030
#define IDS_MENU_EMPTY_SUBMENU 9031
#define IDS_FR_ENABLE_LOGGING 9032
#define IDS_FIRSTRUN_DLG_MAC_COMPLETE_INSTALLATION_LABEL 9033
#define IDS_FIRSTRUN_DLG_MAC_OPTIONS_SEND_USAGE_STATS_LABEL 9034
#define IDS_FIRSTRUN_DLG_MAC_START_CHROME_BUTTON 9035
#define IDS_CRASHED_TAB_FEEDBACK_MESSAGE 9036
#define IDS_CRASHED_TAB_FEEDBACK_LINK 9037
#define IDS_KILLED_TAB_FEEDBACK_MESSAGE 9039
#define IDS_RELAUNCH_BUTTON 9041
#define IDS_TOOLBAR_INFORM_SET_HOME_PAGE 9044
#define IDS_MANAGE_EXTENSIONS_SETTING_WINDOWS_TITLE 9045
#define IDS_CONTROLLED_SETTING_POLICY 9046
#define IDS_CONTROLLED_SETTING_EXTENSION 9047
#define IDS_CONTROLLED_SETTING_EXTENSION_WITHOUT_NAME 9048
#define IDS_CONTROLLED_SETTING_RECOMMENDED 9049
#define IDS_CONTROLLED_SETTING_HAS_RECOMMENDATION 9050
#define IDS_CONTROLLED_SETTING_CHILD_RESTRICTION 9051
#define IDS_EXTENSIONS_INSTALL_LOCATION_UNKNOWN 9052
#define IDS_EXTENSIONS_INSTALL_LOCATION_3RD_PARTY 9053
#define IDS_EXTENSIONS_INSTALL_LOCATION_ENTERPRISE 9054
#define IDS_EXTENSIONS_INSTALL_LOCATION_SHARED_MODULE 9055
#define IDS_EXTENSIONS_BLOCKLISTED_MALWARE 9056
#define IDS_EXTENSIONS_BLOCKLISTED_SECURITY_VULNERABILITY 9057
#define IDS_EXTENSIONS_BLOCKLISTED_CWS_POLICY_VIOLATION 9058
#define IDS_EXTENSIONS_BLOCKLISTED_POTENTIALLY_UNWANTED 9059
#define IDS_EXTENSIONS_SAFE_BROWSING_CRX_ALLOWLIST_WARNING 9060
#define IDS_RESET_PROFILE_SETTINGS_EXPLANATION 9061
#define IDS_RESET_PROFILE_SETTINGS_EXPLANATION_IN_BULLET_POINTS 9062
#define IDS_TRIGGERED_RESET_PROFILE_SETTINGS_TITLE 9063
#define IDS_TRIGGERED_RESET_PROFILE_SETTINGS_EXPLANATION 9064
#define IDS_TRIGGERED_RESET_PROFILE_SETTINGS_DEFAULT_TOOL_NAME 9065
#define IDS_RESET_PROFILE_SETTINGS_LOCALE 9066
#define IDS_RESET_PROFILE_SETTINGS_STARTUP_URLS 9067
#define IDS_RESET_PROFILE_SETTINGS_STARTUP_TYPE 9068
#define IDS_RESET_PROFILE_SETTINGS_HOMEPAGE 9069
#define IDS_RESET_PROFILE_SETTINGS_HOMEPAGE_IS_NTP 9070
#define IDS_RESET_PROFILE_SETTINGS_YES 9071
#define IDS_RESET_PROFILE_SETTINGS_NO 9072
#define IDS_RESET_PROFILE_SETTINGS_SHOW_HOME_BUTTON 9073
#define IDS_RESET_PROFILE_SETTINGS_DSE 9074
#define IDS_RESET_PROFILE_SETTINGS_EXTENSIONS 9075
#define IDS_RESET_PROFILE_SETTINGS_SHORTCUTS 9076
#define IDS_RESET_PROFILE_SETTINGS_PROCESSING_SHORTCUTS 9077
#define IDS_AUTOFILL_DIALOG_PLACEHOLDER_EXPIRY_MONTH 9079
#define IDS_AUTOFILL_DIALOG_PLACEHOLDER_EXPIRY_YEAR 9080
#define IDS_AUTOFILL_FROM_GOOGLE_ACCOUNT 9081
#define IDS_OMNIBOX_ICON_SEND_TAB_TO_SELF 9083
#define IDS_OMNIBOX_TOOLTIP_SEND_TAB_TO_SELF 9084
#define IDS_OMNIBOX_BUBBLE_ITEM_SUBTITLE_TODAY_SEND_TAB_TO_SELF 9085
#define IDS_OMNIBOX_BUBBLE_ITEM_SUBTITLE_DAY_SEND_TAB_TO_SELF 9086
#define IDS_OMNIBOX_BUBBLE_ITEM_SUBTITLE_DAYS_SEND_TAB_TO_SELF 9087
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_FROM_DEVICE 9088
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_TITLE 9089
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_BUTTON_LABEL 9090
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_BUTTON_A11Y_NAME 9091
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_BUTTON_HINT_TEXT 9092
#define IDS_CONTEXT_MENU_SEND_TAB_TO_SELF 9093
#define IDS_SEND_TAB_TO_SELF_SENDING_ANNOUNCE 9096
#define IDS_SEND_TAB_TO_SELF_MANAGE_DEVICES_LINK 9097
#define IDS_SEND_TAB_TO_SELF_PROMO_LABEL 9098
#define IDS_CONTEXT_MENU_GENERATE_QR_CODE_PAGE 9099
#define IDS_CONTEXT_MENU_GENERATE_QR_CODE_IMAGE 9100
#define IDS_CONTEXT_MENU_GENERATE_QR_CODE_LINK 9101
#define IDS_OMNIBOX_QRCODE_GENERATOR_ICON_LABEL 9102
#define IDS_OMNIBOX_QRCODE_GENERATOR_ICON_TOOLTIP 9103
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_TITLE 9104
#define IDS_SHARING_HUB_GENERATE_QR_CODE_LABEL 9105
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_URL_TEXTFIELD_ACCESSIBLE_NAME 9106
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_TOOLTIP 9107
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_DOWNLOAD_BUTTON_LABEL 9108
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_ERROR_TOO_LONG 9109
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_ERROR_UNKNOWN 9110
#define IDS_SHARING_HUB_TITLE 9111
#define IDS_SHARING_HUB_TOOLTIP 9112
#define IDS_SHARING_HUB_SCREENSHOT_LABEL 9113
#define IDS_SHARING_HUB_COPY_LINK_LABEL 9114
#define IDS_SHARING_HUB_SAVE_PAGE_LABEL 9115
#define IDS_SHARING_HUB_MEDIA_ROUTER_LABEL 9116
#define IDS_SHARING_HUB_SHARE_LABEL 9117
#define IDS_SHARING_HUB_SHARE_LABEL_ACCESSIBILITY 9118
#define IDS_SHARING_HUB_FOLLOW_LABEL 9119
#define IDS_SHARING_HUB_FOLLOWING_LABEL 9120
#define IDS_BROWSER_SHARING_SCREENSHOT_POST_CAPTURE_TITLE 9121
#define IDS_BROWSER_SHARING_SCREENSHOT_DIALOG_DOWNLOAD_BUTTON_LABEL 9122
#define IDS_BROWSER_SHARING_SCREENSHOT_DIALOG_EDIT_BUTTON_LABEL 9123
#define IDS_BROWSER_SHARING_SCREENSHOT_DIALOG_SHARE_BUTTON_LABEL 9124
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_SELECTION 9125
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_CROP 9126
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_TEXT 9127
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ELLIPSE 9128
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_RECTANGLE 9129
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_LINE 9130
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ARROW 9131
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_BRUSH 9132
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_EMOJI 9133
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_HIGHLIGHTER 9134
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_UNDO 9135
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_REDO 9136
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ZOOM_IN 9137
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ZOOM_OUT 9138
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_COMMIT_CROP 9139
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_COMMIT_CROP 9140
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_CANCEL_CROP 9141
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_CANCEL_CROP 9142
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_BUTTON_DOWNLOAD_IMAGE 9143
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_BUTTON_COPY 9144
#define IDS_CONTEXT_MENU_SHOW_CLIPBOARD_HISTORY_MENU 9145
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_TEXT_CONTENT_UNKNOWN_DEVICE 9146
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_TEXT_CONTENT 9147
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_IMAGE_CONTENT_UNKNOWN_DEVICE 9148
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_IMAGE_CONTENT 9149
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_DESCRIPTION 9150
#define IDS_CONTENT_CONTEXT_SHARING_CLICK_TO_CALL_MULTIPLE_DEVICES 9151
#define IDS_CONTENT_CONTEXT_SHARING_CLICK_TO_CALL_SINGLE_DEVICE 9152
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_MULTIPLE_DEVICES 9153
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_SINGLE_DEVICE 9154
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_NOTIFICATION_TITLE_UNKNOWN_DEVICE 9155
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_NOTIFICATION_TITLE 9156
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_NOTIFICATION_DESCRIPTION 9157
#define IDS_OMNIBOX_TOOLTIP_SHARED_CLIPBOARD 9158
#define IDS_COLLECTED_COOKIES_DIALOG_TITLE 9159
#define IDS_COLLECTED_COOKIES_ALLOWED_COOKIES_LABEL 9160
#define IDS_COLLECTED_COOKIES_BLOCKED_COOKIES_LABEL 9161
#define IDS_COLLECTED_COOKIES_BLOCKED_THIRD_PARTY_BLOCKING_ENABLED 9162
#define IDS_COLLECTED_COOKIES_ALLOW_BUTTON 9163
#define IDS_COLLECTED_COOKIES_SESSION_ONLY_BUTTON 9164
#define IDS_COLLECTED_COOKIES_BLOCK_BUTTON 9165
#define IDS_COLLECTED_COOKIES_ALLOW_RULE_CREATED 9166
#define IDS_COLLECTED_COOKIES_BLOCK_RULE_CREATED 9167
#define IDS_COLLECTED_COOKIES_SESSION_RULE_CREATED 9168
#define IDS_COLLECTED_COOKIES_ALLOWED_COOKIES_TAB_LABEL 9169
#define IDS_COLLECTED_COOKIES_BLOCKED_COOKIES_TAB_LABEL 9170
#define IDS_COLLECTED_COOKIES_ALLOWED_AUX_TEXT 9171
#define IDS_COLLECTED_COOKIES_BLOCKED_AUX_TEXT 9172
#define IDS_COLLECTED_COOKIES_CLEAR_ON_EXIT_AUX_TEXT 9173
#define IDS_COLLECTED_COOKIES_INFOBAR_MESSAGE 9174
#define IDS_COLLECTED_COOKIES_INFOBAR_BUTTON 9175
#define IDS_COLLECTED_COOKIES_PARTITIONED_COOKIE 9176
#define IDS_ACCNAME_INFOBAR_CONTAINER 9177
#define IDS_ACCNAME_INFOBAR 9178
#define IDS_ONE_CLICK_BUBBLE_UNDO 9179
#define IDS_ONE_CLICK_SIGNIN_BUBBLE_MESSAGE 9180
#define IDS_ONE_CLICK_SIGNIN_DIALOG_OK_BUTTON 9181
#define IDS_ONE_CLICK_SIGNIN_DIALOG_UNDO_BUTTON 9182
#define IDS_ONE_CLICK_SIGNIN_DIALOG_ADVANCED 9183
#define IDS_ENTERPRISE_SIGNIN_CANCEL 9184
#define IDS_ENTERPRISE_SIGNIN_CREATE_NEW_PROFILE 9185
#define IDS_ENTERPRISE_SIGNIN_CREATE_NEW_WORK_PROFILE 9186
#define IDS_ENTERPRISE_SIGNIN_CONTINUE 9187
#define IDS_ENTERPRISE_SIGNIN_ALERT 9188
#define IDS_PROFILE_WILL_BE_DELETED_DIALOG_TITLE 9189
#define IDS_PROFILE_WILL_BE_DELETED_DIALOG_DESCRIPTION 9190
#define IDS_MANAGED_WITH_HYPERLINK 9193
#define IDS_MANAGED_BY_WITH_HYPERLINK 9194
#define IDS_COOKIES_REMOVE_LABEL 9195
#define IDS_COOKIES_COOKIE_NAME_LABEL 9196
#define IDS_COOKIES_COOKIE_CONTENT_LABEL 9197
#define IDS_COOKIES_COOKIE_DOMAIN_LABEL 9198
#define IDS_COOKIES_COOKIE_PATH_LABEL 9199
#define IDS_COOKIES_COOKIE_SENDFOR_LABEL 9200
#define IDS_COOKIES_COOKIE_CREATED_LABEL 9201
#define IDS_COOKIES_COOKIE_EXPIRES_LABEL 9202
#define IDS_COOKIES_COOKIE_EXPIRES_SESSION 9203
#define IDS_COOKIES_COOKIE_SENDFOR_ANY 9204
#define IDS_COOKIES_COOKIE_SENDFOR_SECURE 9205
#define IDS_COOKIES_COOKIE_SENDFOR_SAME_SITE 9206
#define IDS_COOKIES_COOKIE_SENDFOR_SECURE_SAME_SITE 9207
#define IDS_COOKIES_COOKIE_ACCESSIBLE_TO_SCRIPT_YES 9208
#define IDS_COOKIES_COOKIE_ACCESSIBLE_TO_SCRIPT_NO 9209
#define IDS_COOKIES_COOKIE_NONESELECTED 9210
#define IDS_COOKIES_COOKIES 9211
#define IDS_COOKIES_WEB_DATABASES 9212
#define IDS_COOKIES_LOCAL_STORAGE 9213
#define IDS_COOKIES_SESSION_STORAGE 9214
#define IDS_COOKIES_INDEXED_DBS 9215
#define IDS_COOKIES_MEDIA_LICENSE 9216
#define IDS_COOKIES_MEDIA_LICENSES 9217
#define IDS_COOKIES_FILE_SYSTEM 9218
#define IDS_COOKIES_FILE_SYSTEMS 9219
#define IDS_COOKIES_FILE_SYSTEM_USAGE_NONE 9220
#define IDS_COOKIES_SERVICE_WORKER 9221
#define IDS_COOKIES_SERVICE_WORKERS 9222
#define IDS_COOKIES_SHARED_WORKERS 9223
#define IDS_COOKIES_CACHE_STORAGE 9224
#define IDS_CLIENT_CERT_ECDSA_SIGN 9225
#define IDS_APP_DEFAULT_PAGE_NAME 9226
#define IDS_APP_LAUNCHER_TAB_TITLE 9227
#define IDS_NEW_TAB_GUEST_SESSION_HEADING 9228
#define IDS_NEW_TAB_GUEST_SESSION_DESCRIPTION 9229
#define IDS_NEW_TAB_TILE_GRID_ACCESSIBLE_DESCRIPTION 9230
#define IDS_NEW_TAB_APP_INSTALL_HINT_LABEL 9231
#define IDS_NEW_TAB_MOST_VISITED 9232
#define IDS_NEW_TAB_RESTORE_THUMBNAILS_SHORT_LINK 9233
#define IDS_NEW_TAB_ATTRIBUTION_INTRO 9234
#define IDS_NEW_TAB_THUMBNAIL_REMOVED_NOTIFICATION 9235
#define IDS_NEW_TAB_REMOVE_THUMBNAIL_TOOLTIP 9236
#define IDS_NEW_TAB_PAGE_SWITCHER_CHANGE_TITLE 9237
#define IDS_NEW_TAB_PAGE_SWITCHER_SAME_TITLE 9238
#define IDS_NEW_TAB_VOICE_AUDIO_ERROR 9239
#define IDS_NEW_TAB_VOICE_CLOSE_TOOLTIP 9240
#define IDS_NEW_TAB_VOICE_DETAILS 9241
#define IDS_NEW_TAB_VOICE_LANGUAGE_ERROR 9242
#define IDS_NEW_TAB_VOICE_LISTENING 9243
#define IDS_NEW_TAB_VOICE_NETWORK_ERROR 9244
#define IDS_NEW_TAB_VOICE_NO_TRANSLATION 9245
#define IDS_NEW_TAB_VOICE_NO_VOICE 9246
#define IDS_NEW_TAB_VOICE_OTHER_ERROR 9247
#define IDS_NEW_TAB_VOICE_PERMISSION_ERROR 9248
#define IDS_NEW_TAB_VOICE_READY 9249
#define IDS_NEW_TAB_VOICE_TRY_AGAIN 9250
#define IDS_NEW_TAB_VOICE_WAITING 9251
#define IDS_NEW_TAB_VOICE_SEARCH_CLOSED 9252
#define IDS_NEW_TAB_APP_OPTIONS 9253
#define IDS_NEW_TAB_APP_DETAILS 9254
#define IDS_NEW_TAB_APP_CREATE_SHORTCUT 9255
#define IDS_NEW_TAB_APP_INSTALL_LOCALLY 9256
#define IDS_APP_CONTEXT_MENU_SHOW_INFO 9257
#define IDS_APP_CONTEXT_MENU_OPEN_PINNED 9258
#define IDS_APP_CONTEXT_MENU_OPEN_REGULAR 9259
#define IDS_APP_CONTEXT_MENU_OPEN_WINDOW 9260
#define IDS_APP_CONTEXT_MENU_OPEN_FULLSCREEN 9261
#define IDS_APP_CONTEXT_MENU_OPEN_MAXIMIZED 9262
#define IDS_APP_CONTEXT_MENU_OPEN_TAB 9263
#define IDS_APP_CONTEXT_MENU_RUN_ON_OS_LOGIN 9268
#define IDS_APPS_PAGE_DEPRECATED_APP_TITLE 9271
#define IDS_SYNC_CONFIRMATION_TITLE 9272
#define IDS_SYNC_CONFIRMATION_SYNC_INFO_TITLE 9273
#define IDS_SYNC_CONFIRMATION_SYNC_INFO_DESC 9274
#define IDS_SYNC_CONFIRMATION_SETTINGS_INFO 9275
#define IDS_SYNC_CONFIRMATION_CONFIRM_BUTTON_LABEL 9276
#define IDS_SYNC_CONFIRMATION_SETTINGS_BUTTON_LABEL 9277
#define IDS_SYNC_CONFIRMATION_REFRESHED_SETTINGS_BUTTON_LABEL 9278
#define IDS_SYNC_DISABLED_CONFIRMATION_CHROME_SYNC_TITLE 9281
#define IDS_SYNC_DISABLED_CONFIRMATION_DETAILS 9282
#define IDS_SYNC_DISABLED_CONFIRMATION_CONFIRM_BUTTON_LABEL 9283
#define IDS_SYNC_DISABLED_CONFIRMATION_UNDO_BUTTON_LABEL 9284
#define IDS_SYNC_LOADING_CONFIRMATION_TITLE 9285
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_NEW_PROFILE_BUTTON_LABEL 9286
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_NEW_PROFILE_BUTTON_LABEL_V2 9287
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CANCEL_BUTTON_LABEL 9288
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CANCEL_SWITCH_BUTTON_LABEL 9289
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CONFIRM_SWITCH_BUTTON_LABEL 9290
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CONFIRM_SWITCH_BUTTON_LABEL_V2 9291
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_GUEST_LINK 9292
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_ENTERPRISE_BUBBLE_DESC 9293
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_SWITCH_BUBBLE_DESC_V2 9294
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_ENTERPRISE_PROFILE_NAME 9295
#define IDS_SIGNIN_ERROR_TITLE 9296
#define IDS_SIGNIN_ERROR_EMAIL_TITLE 9297
#define IDS_SIGNIN_ERROR_DICE_EMAIL_TITLE 9298
#define IDS_SIGNIN_ERROR_CLOSE_BUTTON_LABEL 9299
#define IDS_SIGNIN_ERROR_OK_BUTTON_LABEL 9300
#define IDS_SIGNIN_ERROR_SWITCH_BUTTON_LABEL 9301
#define IDS_SIGNIN_ACCESSIBLE_CLOSE_BUTTON 9302
#define IDS_SIGNIN_ACCESSIBLE_BACK_BUTTON 9303
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CREATE_PROFILE_RADIO_BUTTON_TITLE 9304
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CREATE_PROFILE_RADIO_BUTTON_SUBTITLE 9305
#define IDS_SIGNIN_EMAIL_CONFIRMATION_START_SYNC_RADIO_BUTTON_TITLE 9306
#define IDS_SIGNIN_EMAIL_CONFIRMATION_START_SYNC_RADIO_BUTTON_SUBTITLE 9307
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CLOSE_BUTTON_LABEL 9308
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CONFIRM_BUTTON_LABEL 9309
#define IDS_ACCOUNT_PASSWORDS_REAUTH_TITLE 9310
#define IDS_ACCOUNT_PASSWORDS_REAUTH_DESC 9311
#define IDS_ACCOUNT_PASSWORDS_REAUTH_DESC_ALREADY_SAVED_LOCALLY 9312
#define IDS_ACCOUNT_PASSWORDS_REAUTH_CONFIRM_BUTTON_LABEL 9313
#define IDS_ACCOUNT_PASSWORDS_REAUTH_CLOSE_BUTTON_LABEL 9314
#define IDS_ACCOUNT_PASSWORDS_REAUTH_CLOSE_BUTTON_LABEL_ALREADY_SAVED_LOCALLY 9315
#define IDS_PLUGIN_OUTDATED_PROMPT 9316
#define IDS_PLUGIN_ENABLE_TEMPORARILY 9317
#define IDS_PLUGIN_CRASHED_PROMPT 9318
#define IDS_RELOAD_PAGE_WITH_PLUGIN 9320
#define IDS_PLUGIN_INITIALIZATION_ERROR_PROMPT 9321
#define IDS_EXTERNAL_PROTOCOL_TITLE 9322
#define IDS_EXTERNAL_PROTOCOL_MESSAGE_WITH_INITIATING_ORIGIN 9323
#define IDS_EXTERNAL_PROTOCOL_MESSAGE 9324
#define IDS_EXTERNAL_PROTOCOL_OK_BUTTON_TEXT 9325
#define IDS_EXTERNAL_PROTOCOL_CANCEL_BUTTON_TEXT 9326
#define IDS_EXTERNAL_PROTOCOL_CHECKBOX_PER_ORIGIN_TEXT 9327
#define IDS_DIRECTORY_LISTING_HEADER 9329
#define IDS_DIRECTORY_LISTING_PARENT 9330
#define IDS_DIRECTORY_LISTING_NAME 9331
#define IDS_DIRECTORY_LISTING_SIZE 9332
#define IDS_DIRECTORY_LISTING_DATE_MODIFIED 9333
#define IDS_SAVE_PAGE_DESC_HTML_ONLY 9334
#define IDS_SAVE_PAGE_DESC_SINGLE_FILE 9335
#define IDS_SAVE_PAGE_DESC_COMPLETE 9336
#define IDS_SAVE_PAGE_DESC_WEB_BUNDLE_FILE 9337
#define IDS_PROFILE_ERROR_DIALOG_TITLE 9338
#define IDS_COULDNT_OPEN_PROFILE_ERROR 9339
#define IDS_OPEN_PROFILE_DATA_LOSS 9340
#define IDS_PROFILE_ERROR_DIALOG_CHECKBOX 9341
#define IDS_PROFILE_ERROR_FEEDBACK_DESCRIPTION 9342
#define IDS_COULDNT_STARTUP_PROFILE_ERROR 9343
#define IDS_REFUSE_TO_RUN_AS_ROOT 9344
#define IDS_REFUSE_TO_RUN_AS_ROOT_2 9345
#define IDS_CANT_WRITE_USER_DIRECTORY_TITLE 9347
#define IDS_RECENT_TABS_MENU 9348
#define IDS_RECENTLY_CLOSED_WINDOW 9349
#define IDS_RECENTLY_CLOSED_GROUP 9350
#define IDS_RECENTLY_CLOSED_GROUP_UNNAMED 9351
#define IDS_RECENT_TABS_NO_DEVICE_TABS 282
#define IDS_HISTORY_MENU 283
#define IDS_DEFAULT_DOWNLOAD_FILENAME 9352
#define IDS_BROWSER_WINDOW_MAC_TAB_UNTITLED 9353
#define IDS_DEFAULT_BROWSER_INFOBAR_OK_BUTTON_LABEL 9354
#define IDS_USED_EXISTING_BROWSER 9360
#define IDS_DECLINE_RECOVERY 9361
#define IDS_SYNC_ACCOUNT_SYNCING 9362
#define IDS_SYNC_ACCOUNT_SYNCING_CUSTOM_DATA_TYPES 9363
#define IDS_SIGNIN_ERROR_DISPLAY_SOURCE 9364
#define IDS_SIGNIN_ERROR_BUBBLE_VIEW_TITLE 9365
#define IDS_SYNC_ERROR_BUBBLE_VIEW_TITLE 9366
#define IDS_SYNC_ERROR_PASSWORDS_BUBBLE_VIEW_TITLE 9367
#define IDS_SYNC_ERROR_USER_MENU_TITLE 9368
#define IDS_SYNC_ERROR_PASSWORDS_USER_MENU_TITLE 9369
#define IDS_SYNC_ERROR_PASSWORDS_USER_MENU_TITLE_SIGNED_IN_ONLY 9370
#define IDS_SYNC_ERROR_RECOVERABILITY_DEGRADED_FOR_EVERYTHING_USER_MENU_TITLE 9371
#define IDS_SYNC_ERROR_RECOVERABILITY_DEGRADED_FOR_PASSWORDS_USER_MENU_TITLE 9372
#define IDS_SYNC_ERROR_USER_MENU_SIGNIN_BUTTON 9373
#define IDS_SYNC_NEEDS_VERIFICATION_BUBBLE_VIEW_TITLE 9374
#define IDS_SYNC_ERROR_USER_MENU_PASSPHRASE_BUTTON 9375
#define IDS_SYNC_ERROR_USER_MENU_RECOVERABILITY_BUTTON 9376
#define IDS_SYNC_ERROR_USER_MENU_RETRIEVE_KEYS_BUTTON 9377
#define IDS_SYNC_ERROR_USER_MENU_SIGNOUT_BUTTON 9378
#define IDS_SYNC_ERROR_USER_MENU_CONFIRM_SYNC_SETTINGS_BUTTON 9379
#define IDS_SYNC_OVERVIEW 9385
#define IDS_SYNC_START_SYNC_BUTTON_LABEL 9386
#define IDS_SIGNED_IN_WITH_SYNC_DISABLED_BY_POLICY 9387
#define IDS_SIGNED_IN_WITH_SYNC_STOPPED_VIA_DASHBOARD 9388
#define IDS_SYNC_SETTINGS_NOT_CONFIRMED 9389
#define IDS_SYNC_SETUP_IN_PROGRESS 9390
#define IDS_SYNC_STATUS_UNRECOVERABLE_ERROR 9391
#define IDS_SYNC_STATUS_UNRECOVERABLE_ERROR_NEEDS_SIGNOUT 9392
#define IDS_SYNC_STATUS_NEEDS_PASSWORD 9393
#define IDS_SYNC_STATUS_NEEDS_PASSWORD_BUTTON 9394
#define IDS_SYNC_STATUS_NEEDS_KEYS_BUTTON 9395
#define IDS_SYNC_SERVER_IS_UNREACHABLE 9396
#define IDS_SYNC_RELOGIN_ERROR 9397
#define IDS_SYNC_RELOGIN_BUTTON 9398
#define IDS_SYNC_ADVANCED_OPTIONS 9403
#define IDS_SYNC_FULL_ENCRYPTION_BODY_CUSTOM 9405
#define IDS_SYNC_FULL_ENCRYPTION_BODY_CUSTOM_WITH_DATE 9406
#define IDS_TRANSLATE_BUBBLE_BEFORE_TRANSLATE_TITLE 9407
#define IDS_TRANSLATE_BUBBLE_TRANSLATED_TITLE 9408
#define IDS_TRANSLATE_BUBBLE_COULD_NOT_TRANSLATE_TITLE 9409
#define IDS_TRANSLATE_BUBBLE_SOURCE_LANG_COMBOBOX_ACCNAME 9410
#define IDS_TRANSLATE_BUBBLE_TARGET_LANG_COMBOBOX_ACCNAME 9411
#define IDS_TRANSLATE_BUBBLE_TRANSLATION_COMPLETE_ANNOUNCEMENT 9412
#define IDS_TRANSLATE_BUBBLE_ADVANCED_BUTTON 9413
#define IDS_TRANSLATE_BUBBLE_ADVANCED_MENU_BUTTON 9414
#define IDS_TRANSLATE_BUBBLE_CHANGE_TARGET_LANGUAGE 9415
#define IDS_TRANSLATE_BUBBLE_CHANGE_SOURCE_LANGUAGE 9416
#define IDS_TRANSLATE_BUBBLE_ACCEPT 9417
#define IDS_TRANSLATE_BUBBLE_DENY 9418
#define IDS_TRANSLATE_BUBBLE_ALWAYS_TRANSLATE_LANG 9419
#define IDS_TRANSLATE_BUBBLE_NEVER_TRANSLATE_LANG 9420
#define IDS_TRANSLATE_BUBBLE_NEVER_TRANSLATE_SITE 9421
#define IDS_TRANSLATE_BUBBLE_TRANSLATING 9422
#define IDS_TRANSLATE_BUBBLE_REVERT 9423
#define IDS_TRANSLATE_BUBBLE_TRY_AGAIN 9424
#define IDS_TRANSLATE_BUBBLE_ALWAYS 9425
#define IDS_TRANSLATE_BUBBLE_OPTIONS_MENU_BUTTON 9426
#define IDS_TRANSLATE_BUBBLE_PAGE_LANGUAGE 9427
#define IDS_TRANSLATE_BUBBLE_TRANSLATION_LANGUAGE 9428
#define IDS_TRANSLATE_BUBBLE_ADVANCED_TARGET 9429
#define IDS_TRANSLATE_BUBBLE_ADVANCED_SOURCE 9430
#define IDS_TRANSLATE_BUBBLE_RESET 9431
#define IDS_NOTIFICATION_BUTTON_SETTINGS 9432
#define IDS_NOTIFICATION_BUTTON_CLOSE 9433
#define IDS_NOTIFICATION_BUTTON_MORE 9434
#define IDS_NOTIFICATION_REPLY_PLACEHOLDER 9435
#define IDS_NOTIFICATION_MUTED_MESSAGE 9436
#define IDS_NOTIFICATION_MUTED_TITLE 9437
#define IDS_NOTIFICATION_MUTED_ACTION_SHOW 9438
#define IDS_NOTIFICATION_MUTED_ACTION_SNOOZE 9439
#define IDS_GET_PROFILE_ERROR_APPLESCRIPT_MAC 9445
#define IDS_BOOKMARK_MODEL_LOAD_ERROR_APPLESCRIPT_MAC 9446
#define IDS_CREATE_BOOKMARK_FOLDER_ERROR_APPLESCRIPT_MAC 9447
#define IDS_CREATE_BOOKMARK_ITEM_ERROR_APPLESCRIPT_MAC 9448
#define IDS_INVALID_URL_APPLESCRIPT_MAC 9449
#define IDS_INITIATE_PRINTING_ERROR_APPLESCRIPT_MAC 9450
#define IDS_INVALID_SAVE_TYPE_ERROR_APPLESCRIPT_MAC 9451
#define IDS_INVALID_MODE_ERROR_APPLESCRIPT_MAC 9452
#define IDS_INVALID_TAB_INDEX_APPLESCRIPT_MAC 9453
#define IDS_SET_MODE_APPLESCRIPT_MAC 9454
#define IDS_WRONG_INDEX_ERROR_APPLESCRIPT_MAC 9455
#define IDS_JAVASCRIPT_UNSUPPORTED_ERROR_APPLESCRIPT_MAC 9456
#define IDS_FILE_MENU_MAC 114
#define IDS_EDIT_MENU_MAC 127
#define IDS_VIEW_MENU_MAC 151
#define IDS_HISTORY_MENU_MAC 166
#define IDS_WINDOW_MENU_MAC 177
#define IDS_TAB_MENU_MAC 9457
#define IDS_HELP_MENU_MAC 187
#define IDS_ABOUT_MAC 104
#define IDS_SERVICES_MAC 108
#define IDS_HIDE_APP_MAC 109
#define IDS_HIDE_OTHERS_MAC 110
#define IDS_SHOW_ALL_MAC 111
#define IDS_EXIT_MAC 113
#define IDS_NEW_MAC 331
#define IDS_NEW_TAB_MAC 115
#define IDS_NEW_WINDOW_MAC 116
#define IDS_NEW_INCOGNITO_WINDOW_MAC 117
#define IDS_REOPEN_CLOSED_TABS_MAC 118
#define IDS_OPEN_FILE_MAC 119
#define IDS_OPEN_LOCATION_MAC 120
#define IDS_CLOSE_WINDOW_MAC 121
#define IDS_CLOSE_TAB_MAC 122
#define IDS_SAVE_PAGE_MAC 123
#define IDS_EMAIL_LINK_MAC 9458
#define IDS_SHARING_MORE_MAC 9459
#define IDS_SHARE_MAC 9460
#define IDS_PRINT_USING_SYSTEM_DIALOG_MAC 126
#define IDS_EDIT_UNDO_MAC 128
#define IDS_EDIT_REDO_MAC 129
#define IDS_CUT_MAC 130
#define IDS_COPY_MAC 131
#define IDS_PASTE_MAC 132
#define IDS_PASTE_MATCH_STYLE_MAC 133
#define IDS_EDIT_DELETE_MAC 134
#define IDS_EDIT_SELECT_ALL_MAC 135
#define IDS_EDIT_FIND_SUBMENU_MAC 136
#define IDS_EDIT_SEARCH_WEB_MAC 137
#define IDS_EDIT_FIND_MAC 138
#define IDS_EDIT_FIND_NEXT_MAC 139
#define IDS_EDIT_FIND_PREVIOUS_MAC 140
#define IDS_EDIT_USE_SELECTION_MAC 141
#define IDS_EDIT_JUMP_TO_SELECTION_MAC 142
#define IDS_EDIT_SPELLING_GRAMMAR_MAC 143
#define IDS_EDIT_SHOW_SPELLING_GRAMMAR_MAC 144
#define IDS_EDIT_CHECK_DOCUMENT_MAC 145
#define IDS_EDIT_CHECK_SPELLING_TYPING_MAC 146
#define IDS_EDIT_CHECK_GRAMMAR_MAC 147
#define IDS_EDIT_SUBSTITUTIONS_MAC 9461
#define IDS_EDIT_SHOW_SUBSTITUTIONS_MAC 9462
#define IDS_EDIT_SMART_QUOTES_MAC 9463
#define IDS_EDIT_SMART_DASHES_MAC 9464
#define IDS_EDIT_TEXT_REPLACEMENT_MAC 9465
#define IDS_EDIT_TRANSFORMATIONS_MAC 9466
#define IDS_EDIT_MAKE_UPPERCASE_MAC 9467
#define IDS_EDIT_MAKE_LOWERCASE_MAC 9468
#define IDS_EDIT_CAPITALIZE_MAC 9469
#define IDS_BOOKMARK_BAR_ALWAYS_SHOW_MAC 152
#define IDS_STOP_MENU_MAC 154
#define IDS_RELOAD_MENU_MAC 155
#define IDS_RELOAD_BYPASSING_CACHE_MENU_MAC 156
#define IDS_ENTER_FULLSCREEN_MAC 157
#define IDS_EXIT_FULLSCREEN_MAC 9470
#define IDS_TOGGLE_FULLSCREEN_TOOLBAR_MAC 153
#define IDS_TEXT_BIGGER_MAC 159
#define IDS_TEXT_DEFAULT_MAC 158
#define IDS_TEXT_SMALLER_MAC 160
#define IDS_DEVELOPER_MENU_MAC 162
#define IDS_VIEW_SOURCE_MAC 163
#define IDS_DEV_TOOLS_MAC 164
#define IDS_DEV_TOOLS_CONSOLE_MAC 165
#define IDS_DEV_TOOLS_ELEMENTS_MAC 9471
#define IDS_ALLOW_JAVASCRIPT_APPLE_EVENTS_MAC 9472
#define IDS_TASK_MANAGER_MAC 185
#define IDS_HISTORY_HOME_MAC 167
#define IDS_HISTORY_BACK_MAC 168
#define IDS_HISTORY_FORWARD_MAC 169
#define IDS_HISTORY_VISITED_MAC 171
#define IDS_HISTORY_CLOSED_MAC 170
#define IDS_HISTORY_CLOSED_RESTORE_WINDOW_MAC 9473
#define IDS_MINIMIZE_WINDOW_MAC 178
#define IDS_ZOOM_WINDOW_MAC 179
#define IDS_NEXT_TAB_MAC 180
#define IDS_PREV_TAB_MAC 181
#define IDS_SHOW_DOWNLOADS_MAC 183
#define IDS_WINDOW_AUDIO_PLAYING_MAC 9474
#define IDS_WINDOW_AUDIO_MUTING_MAC 9475
#define IDS_SHOW_EXTENSIONS_MAC 184
#define IDS_ALL_WINDOWS_FRONT_MAC 186
#define IDS_DUPLICATE_TAB_MAC 9476
#define IDS_DUPLICATE_TARGET_TAB_MAC 9477
#define IDS_MUTE_SITE_MAC 9478
#define IDS_MUTE_TARGET_SITE_MAC 9479
#define IDS_PIN_TAB_MAC 9480
#define IDS_PIN_TARGET_TAB_MAC 9481
#define IDS_GROUP_TAB_MAC 9482
#define IDS_GROUP_TARGET_TAB_MAC 9483
#define IDS_FEEDBACK_MAC 188
#define IDS_HELP_MAC 189
#define IDS_ALLOWED_GEOLOCATION_TITLE 9506
#define IDS_BLOCKED_GEOLOCATION_TITLE 9507
#define IDS_ALLOWED_GEOLOCATION_MESSAGE 9508
#define IDS_ALLOWED_GEOLOCATION_BLOCK 9509
#define IDS_ALLOWED_GEOLOCATION_NO_ACTION 9510
#define IDS_BLOCKED_GEOLOCATION_MESSAGE 9511
#define IDS_BLOCKED_GEOLOCATION_UNBLOCK 9512
#define IDS_BLOCKED_GEOLOCATION_NO_ACTION 9513
#define IDS_GEOLOCATION 9514
#define IDS_GEOLOCATION_WILL_ASK_AGAIN 9515
#define IDS_GEOLOCATION_TURNED_OFF_IN_MACOS 9516
#define IDS_GEOLOCATION_TURNED_OFF_IN_MACOS_SETTINGS 9517
#define IDS_GEOLOCATION_TURNED_OFF 9518
#define IDS_ALLOWED_MIDI_SYSEX_TITLE 9519
#define IDS_BLOCKED_MIDI_SYSEX_TITLE 9520
#define IDS_ALLOWED_MIDI_SYSEX_MESSAGE 9521
#define IDS_ALLOWED_MIDI_SYSEX_BLOCK 9522
#define IDS_ALLOWED_MIDI_SYSEX_NO_ACTION 9523
#define IDS_BLOCKED_MIDI_SYSEX_MESSAGE 9524
#define IDS_BLOCKED_MIDI_SYSEX_UNBLOCK 9525
#define IDS_BLOCKED_MIDI_SYSEX_NO_ACTION 9526
#define IDS_MICROPHONE_CAMERA_ALLOWED 9527
#define IDS_MICROPHONE_CAMERA_BLOCKED 9528
#define IDS_MICROPHONE_ACCESSED 9529
#define IDS_CAMERA_ACCESSED 9530
#define IDS_MICROPHONE_BLOCKED 9531
#define IDS_CAMERA_BLOCKED 9532
#define IDS_MICROPHONE_CAMERA_ALLOWED_TITLE 9533
#define IDS_MICROPHONE_CAMERA_BLOCKED_TITLE 9534
#define IDS_MICROPHONE_ACCESSED_TITLE 9535
#define IDS_CAMERA_ACCESSED_TITLE 9536
#define IDS_MICROPHONE_BLOCKED_TITLE 9537
#define IDS_CAMERA_BLOCKED_TITLE 9538
#define IDS_MEDIASTREAM_SETTING_CHANGED_MESSAGE 9539
#define IDS_CAMERA_MIC_TURNED_OFF_IN_MACOS 9540
#define IDS_CAMERA_TURNED_OFF_IN_MACOS 9541
#define IDS_MIC_TURNED_OFF_IN_MACOS 9542
#define IDS_OPEN_PREFERENCES_LINK 9543
#define IDS_CAMERA 9544
#define IDS_MIC 9545
#define IDS_TURNED_OFF 9546
#define IDS_CAMERA_TURNED_OFF 9547
#define IDS_MIC_TURNED_OFF 9548
#define IDS_A11Y_OMNIBOX_CHIP_HINT 9549
#define IDS_MANAGE_PASSWORDS_CONFIRM_GENERATED_TEXT 9550
#define IDS_PASSWORDS_WEB_LINK 9551
#define IDS_MANAGE_PASSWORDS_LINK 9553
#define IDS_MANAGE_PASSWORDS_TITLE 9554
#define IDS_MANAGE_PASSWORDS_NO_PASSWORDS_TITLE 9555
#define IDS_MANAGE_PASSWORDS_DIFFERENT_DOMAIN_TITLE 9556
#define IDS_MANAGE_PASSWORDS_DIFFERENT_DOMAIN_NO_PASSWORDS_TITLE 9557
#define IDS_MANAGE_PASSWORDS_ACCOUNT_STORE_ICON_DESCRIPTION 9558
#define IDS_MANAGE_PASSWORDS_DELETED 9559
#define IDS_MANAGE_PASSWORDS_UNDO 9560
#define IDS_MANAGE_PASSWORDS_UNDO_TOOLTIP 9561
#define IDS_MANAGE_PASSWORDS_DELETE 9562
#define IDS_MANAGE_PASSWORDS_SHOW_PASSWORD 9563
#define IDS_MANAGE_PASSWORDS_HIDE_PASSWORD 9564
#define IDS_MANAGE_PASSWORDS_AUTO_SIGNIN_TITLE_MD 9565
#define IDS_AUTO_SIGNIN_FIRST_RUN_TITLE_MANY_DEVICES 9566
#define IDS_AUTO_SIGNIN_FIRST_RUN_TITLE_LOCAL_DEVICE 9567
#define IDS_AUTO_SIGNIN_FIRST_RUN_TEXT 9568
#define IDS_AUTO_SIGNIN_FIRST_RUN_OK 9569
#define IDS_PLEASE_RELAUNCH_BROWSER 9570
#define IDS_INSTALL_FROM_DMG_TITLE 9571
#define IDS_INSTALL_FROM_DMG_PROMPT 9572
#define IDS_INSTALL_FROM_DMG_YES 9573
#define IDS_INSTALL_FROM_DMG_NO 9574
#define IDS_INSTALL_FROM_DMG_AUTHENTICATION_PROMPT 9575
#define IDS_INSTALL_FROM_DMG_ERROR_TITLE 9576
#define IDS_INSTALL_FROM_DMG_ERROR 9577
#define IDS_FILE_SELECTION_DIALOG_INFOBAR 9578
#define IDS_IMAGE_FILES 9581
#define IDS_AUDIO_FILES 9582
#define IDS_VIDEO_FILES 9583
#define IDS_CUSTOM_FILES 9584
#define IDS_FULLSCREEN_USER_ENTERED_FULLSCREEN 9585
#define IDS_FULLSCREEN_EXTENSION_TRIGGERED_FULLSCREEN 9586
#define IDS_FULLSCREEN_UNKNOWN_EXTENSION_TRIGGERED_FULLSCREEN 9587
#define IDS_FULLSCREEN_SITE_ENTERED_FULLSCREEN 9588
#define IDS_FULLSCREEN_ENTERED_FULLSCREEN 9589
#define IDS_FULLSCREEN_SITE_ENTERED_FULLSCREEN_MOUSELOCK 9590
#define IDS_FULLSCREEN_ENTERED_FULLSCREEN_MOUSELOCK 9591
#define IDS_FULLSCREEN_SITE_ENTERED_MOUSELOCK 9592
#define IDS_FULLSCREEN_ENTERED_MOUSELOCK 9593
#define IDS_FULLSCREEN_PRESS_ESC_TO_EXIT_MOUSELOCK 9594
#define IDS_SENSORS_ALLOWED_TOOLTIP 9595
#define IDS_MOTION_SENSORS_ALLOWED_TOOLTIP 9596
#define IDS_SENSORS_BLOCKED_TOOLTIP 9597
#define IDS_MOTION_SENSORS_BLOCKED_TOOLTIP 9598
#define IDS_BLOCKED_SENSORS_UNBLOCK 9599
#define IDS_BLOCKED_SENSORS_NO_ACTION 9600
#define IDS_ALLOWED_SENSORS_TITLE 9601
#define IDS_BLOCKED_SENSORS_TITLE 9602
#define IDS_ALLOWED_SENSORS_MESSAGE 9603
#define IDS_ALLOWED_MOTION_SENSORS_MESSAGE 9604
#define IDS_BLOCKED_SENSORS_MESSAGE 9605
#define IDS_BLOCKED_MOTION_SENSORS_MESSAGE 9606
#define IDS_ALLOWED_SENSORS_NO_ACTION 9607
#define IDS_ALLOWED_SENSORS_BLOCK 9608
#define IDS_LIVE_CAPTION_PROMO 9615
#define IDS_LIVE_CAPTION_PROMO_SCREENREADER 9616
#define IDS_ENABLE_CARET_BROWSING_INFO 9617
#define IDS_CARET_BROWSING_DO_NOT_ASK 9618
#define IDS_ENABLE_CARET_BROWSING_TITLE 9619
#define IDS_ENABLE_CARET_BROWSING_TURN_ON 9620
#define IDS_MEDIA_GALLERIES_DIALOG_HEADER 9621
#define IDS_MEDIA_GALLERIES_DIALOG_SUBTEXT_READ_WRITE 9622
#define IDS_MEDIA_GALLERIES_DIALOG_SUBTEXT_READ_DELETE 9623
#define IDS_MEDIA_GALLERIES_DIALOG_SUBTEXT_READ_ONLY 9624
#define IDS_MEDIA_GALLERIES_PERMISSION_SUGGESTIONS 9625
#define IDS_MEDIA_GALLERIES_LAST_ATTACHED 9626
#define IDS_MEDIA_GALLERIES_DIALOG_ADD_GALLERY 9627
#define IDS_MEDIA_GALLERIES_DIALOG_CONFIRM 9628
#define IDS_MEDIA_GALLERIES_DIALOG_ADD_GALLERY_TITLE 9629
#define IDS_MEDIA_GALLERIES_DIALOG_DEVICE_ATTACHED 9630
#define IDS_MEDIA_GALLERIES_DIALOG_DEVICE_NOT_ATTACHED 9631
#define IDS_MEDIA_GALLERIES_DIALOG_DELETE 9632
#define IDS_CHROME_SHORTCUT_DESCRIPTION 9652
#define IDS_WEBSTORE_NAME_STORE 245
#define IDS_DESKTOP_MEDIA_PICKER_TITLE 9653
#define IDS_DESKTOP_MEDIA_PICKER_TITLE_SCREEN_ONLY 9654
#define IDS_DESKTOP_MEDIA_PICKER_TITLE_WINDOW_ONLY 9655
#define IDS_DESKTOP_MEDIA_PICKER_TEXT 9656
#define IDS_DESKTOP_MEDIA_PICKER_TEXT_DELEGATED 9657
#define IDS_DESKTOP_MEDIA_PICKER_AUDIO_SHARE_SCREEN 9658
#define IDS_DESKTOP_MEDIA_PICKER_AUDIO_SHARE_WINDOW 9659
#define IDS_DESKTOP_MEDIA_PICKER_AUDIO_SHARE_TAB 9660
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_SCREEN 9661
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_WINDOW 9662
#define IDS_DESKTOP_MEDIA_PICKER_SINGLE_SCREEN_NAME 9663
#define IDS_DESKTOP_MEDIA_PICKER_MULTIPLE_SCREEN_NAME 9664
#define IDS_DESKTOP_MEDIA_PICKER_MANAGED 9665
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_THIS_TAB 9666
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_OTHER_TAB 9667
#define IDS_DESKTOP_MEDIA_PICKER_EMPTY_PREVIEW 9668
#define IDS_DESKTOP_MEDIA_PICKER_PREVIEW_ACCESSIBLE_NAME 9669
#define IDS_DESKTOP_MEDIA_SOURCE_EMPTY_ACCESSIBLE_NAME 9670
#define IDS_TAB_CAPTURE_TERMINATED_BY_POLICY_TITLE 9671
#define IDS_TAB_CAPTURE_TERMINATED_BY_POLICY_TEXT 9672
#define IDS_TOOLTIP_TAB_ALERT_STATE_MEDIA_RECORDING 9673
#define IDS_TOOLTIP_TAB_ALERT_STATE_TAB_CAPTURING 9674
#define IDS_TOOLTIP_TAB_ALERT_STATE_AUDIO_PLAYING 9675
#define IDS_TOOLTIP_TAB_ALERT_STATE_AUDIO_MUTING 9676
#define IDS_TOOLTIP_TAB_ALERT_STATE_BLUETOOTH_CONNECTED 9677
#define IDS_TOOLTIP_TAB_ALERT_STATE_BLUETOOTH_SCAN_ACTIVE 9678
#define IDS_TOOLTIP_TAB_ALERT_STATE_USB_CONNECTED 9679
#define IDS_TOOLTIP_TAB_ALERT_STATE_HID_CONNECTED 9680
#define IDS_TOOLTIP_TAB_ALERT_STATE_SERIAL_CONNECTED 9681
#define IDS_TOOLTIP_TAB_ALERT_STATE_PIP_PLAYING 9682
#define IDS_TOOLTIP_TAB_ALERT_STATE_DESKTOP_CAPTURING 9683
#define IDS_TOOLTIP_TAB_ALERT_STATE_VR_PRESENTING 9684
#define IDS_TAB_AX_LABEL_MEDIA_RECORDING_FORMAT 9685
#define IDS_TAB_AX_LABEL_TAB_CAPTURING_FORMAT 9686
#define IDS_TAB_AX_LABEL_PIP_PLAYING_FORMAT 9687
#define IDS_TAB_AX_LABEL_AUDIO_PLAYING_FORMAT 9688
#define IDS_TAB_AX_LABEL_AUDIO_MUTING_FORMAT 9689
#define IDS_TAB_AX_LABEL_BLUETOOTH_CONNECTED_FORMAT 9690
#define IDS_TAB_AX_LABEL_BLUETOOTH_SCAN_ACTIVE_FORMAT 9691
#define IDS_TAB_AX_LABEL_USB_CONNECTED_FORMAT 9692
#define IDS_TAB_AX_LABEL_HID_CONNECTED_FORMAT 9693
#define IDS_TAB_AX_LABEL_SERIAL_CONNECTED_FORMAT 9694
#define IDS_TAB_AX_LABEL_NETWORK_ERROR_FORMAT 9695
#define IDS_TAB_AX_LABEL_CRASHED_FORMAT 9696
#define IDS_TAB_AX_LABEL_DESKTOP_CAPTURING_FORMAT 9697
#define IDS_TAB_AX_LABEL_VR_PRESENTING 9698
#define IDS_TAB_AX_LABEL_UNNAMED_GROUP_FORMAT 9699
#define IDS_TAB_AX_LABEL_NAMED_GROUP_FORMAT 9700
#define IDS_TAB_AX_LABEL_PERMISSION_REQUESTED_FORMAT 9701
#define IDS_GROUP_AX_LABEL_UNNAMED_GROUP_FORMAT 9702
#define IDS_GROUP_AX_LABEL_NAMED_GROUP_FORMAT 9703
#define IDS_GROUP_AX_LABEL_COLLAPSED 9704
#define IDS_GROUP_AX_LABEL_EXPANDED 9705
#define IDS_TAB_AX_ANNOUNCE_MOVED_RIGHT 9706
#define IDS_TAB_AX_ANNOUNCE_MOVED_LEFT 9707
#define IDS_TAB_AX_ANNOUNCE_MOVED_FIRST 9708
#define IDS_TAB_AX_ANNOUNCE_MOVED_LAST 9709
#define IDS_TAB_AX_ANNOUNCE_TAB_ADDED_TO_UNNAMED_GROUP 9710
#define IDS_TAB_AX_ANNOUNCE_TAB_ADDED_TO_NAMED_GROUP 9711
#define IDS_TAB_AX_ANNOUNCE_TAB_REMOVED_FROM_UNNAMED_GROUP 9712
#define IDS_TAB_AX_ANNOUNCE_TAB_REMOVED_FROM_NAMED_GROUP 9713
#define IDS_PAGE_LOADING_AX_TITLE_FORMAT 9714
#define IDS_PROFILE_IN_USE_LINUX_QUIT 9715
#define IDS_PROFILE_IN_USE_LINUX_RELAUNCH 9716
#define IDS_DESKTOP_MEDIA_PICKER_SHARE 9727
#define IDS_PUSH_MESSAGING_GENERIC_NOTIFICATION_BODY 9728
#define IDS_DEVICE_PERMISSIONS_DIALOG_SELECT 9729
#define IDS_DEVICE_PERMISSIONS_DIALOG_LOADING_LABEL 9730
#define IDS_DEVICE_PERMISSIONS_DIALOG_LOADING_LABEL_TOOLTIP 9731
#define IDS_DEVICE_LOG_TITLE 9732
#define IDS_DEVICE_AUTO_REFRESH 9733
#define IDS_DEVICE_LOG_REFRESH 9734
#define IDS_DEVICE_LOG_CLEAR 9735
#define IDS_DEVICE_LOG_CLEAR_TYPES 9736
#define IDS_DEVICE_LOG_NO_ENTRIES 9737
#define IDS_DEVICE_LOG_LEVEL_LABEL 9738
#define IDS_DEVICE_LOG_LEVEL_ERROR 9739
#define IDS_DEVICE_LOG_LEVEL_USER 9740
#define IDS_DEVICE_LOG_LEVEL_EVENT 9741
#define IDS_DEVICE_LOG_LEVEL_DEBUG 9742
#define IDS_DEVICE_LOG_TYPE_LOGIN 9743
#define IDS_DEVICE_LOG_TYPE_NETWORK 9744
#define IDS_DEVICE_LOG_TYPE_POWER 9745
#define IDS_DEVICE_LOG_TYPE_BLUETOOTH 9746
#define IDS_DEVICE_LOG_TYPE_USB 9747
#define IDS_DEVICE_LOG_TYPE_HID 9748
#define IDS_DEVICE_LOG_TYPE_PRINTER 9749
#define IDS_DEVICE_LOG_TYPE_FIDO 9750
#define IDS_DEVICE_LOG_TYPE_SERIAL 9751
#define IDS_DEVICE_LOG_TYPE_CAMERA 9752
#define IDS_DEVICE_LOG_FILEINFO 9753
#define IDS_DEVICE_LOG_TIME_DETAIL 9754
#define IDS_DEVICE_LOG_ENTRY 9755
#define IDS_WEBUSB_DEVICE_DETECTED_NOTIFICATION 9756
#define IDS_WEBUSB_DEVICE_DETECTED_NOTIFICATION_TITLE 9757
#define IDS_DEFAULT_AUDIO_DEVICE_NAME 9766
#define IDS_BLUETOOTH_DEVICE_CHOOSER_PROMPT_EXTENSION_NAME 9768
#define IDS_BLUETOOTH_DEVICE_CHOOSER_TURN_ADAPTER_OFF 9769
#define IDS_BLUETOOTH_DEVICE_CHOOSER_TURN_ON_BLUETOOTH_LINK_TEXT 9770
#define IDS_BLUETOOTH_DEVICE_CHOOSER_AUTHORIZE_BLUETOOTH_LINK_TEXT 9771
#define IDS_BLUETOOTH_DEVICE_CHOOSER_RE_SCAN 9772
#define IDS_BLUETOOTH_DEVICE_CHOOSER_RE_SCAN_TOOLTIP 9773
#define IDS_DEVICE_CHOOSER_GET_HELP_LINK_WITH_SCANNING_STATUS 9774
#define IDS_DEVICE_CHOOSER_GET_HELP_LINK_WITH_RE_SCAN_LINK 9775
#define IDS_DEVICE_CHOOSER_PAIRED_STATUS_TEXT 9776
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_AND_PAIRED_STATUS_TEXT 9777
#define IDS_USB_DEVICE_CHOOSER_PROMPT_EXTENSION_NAME 9778
#define IDS_DEVICE_CHOOSER_ACCNAME_COMPATIBLE_DEVICES_LIST 9779
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_UNKNOWN_DEVICE_WITH_VENDOR_NAME 9780
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_UNKNOWN_DEVICE_WITH_VENDOR_ID_AND_PRODUCT_ID 9781
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_PRODUCT_ID_AND_VENDOR_NAME 9782
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_PRODUCT_ID_AND_VENDOR_ID 9783
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_VENDOR_ID 9784
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_VENDOR_NAME 9785
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_ANY_VENDOR 9786
#define IDS_SERIAL_PORT_CHOOSER_PROMPT_ORIGIN 9787
#define IDS_SERIAL_PORT_CHOOSER_PROMPT_EXTENSION_NAME 9788
#define IDS_SERIAL_PORT_CHOOSER_NAME_WITH_PATH 9789
#define IDS_SERIAL_PORT_CHOOSER_PATH_ONLY 9790
#define IDS_SERIAL_PORT_CHOOSER_CONNECT_BUTTON_TEXT 9791
#define IDS_SERIAL_PORT_CHOOSER_LOADING_LABEL 9792
#define IDS_SERIAL_PORT_CHOOSER_LOADING_LABEL_TOOLTIP 9793
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_PRODUCT_ID_AND_VENDOR_NAME 9794
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_PRODUCT_ID_AND_VENDOR_ID 9795
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_VENDOR_ID 9796
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_VENDOR_NAME 9797
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_ANY_PORT 9798
#define IDS_HID_CHOOSER_PROMPT_ORIGIN 9799
#define IDS_HID_CHOOSER_PROMPT_EXTENSION_NAME 9800
#define IDS_HID_CHOOSER_ITEM_WITHOUT_NAME 9801
#define IDS_HID_CHOOSER_LOADING_LABEL 9802
#define IDS_HID_CHOOSER_LOADING_LABEL_TOOLTIP 9803
#define IDS_HID_POLICY_DESCRIPTION_FOR_VENDOR_ID_AND_PRODUCT_ID 9804
#define IDS_HID_POLICY_DESCRIPTION_FOR_VENDOR_ID 9805
#define IDS_HID_POLICY_DESCRIPTION_FOR_USAGE_AND_USAGE_PAGE 9806
#define IDS_HID_POLICY_DESCRIPTION_FOR_USAGE_PAGE 9807
#define IDS_HID_POLICY_DESCRIPTION_FOR_ANY_DEVICE 9808
#define IDS_WEB_APP_FILE_HANDLING_LIST_SEPARATOR 9809
#define IDS_WEB_APP_FILE_HANDLING_DIALOG_QUESTION 9810
#define IDS_WEB_APP_FILE_HANDLING_DIALOG_QUESTION_MULTIPLE 9811
#define IDS_WEB_APP_FILE_HANDLING_DIALOG_STICKY_CHOICE 9812
#define IDS_WEB_APP_FILE_HANDLING_POSITIVE_BUTTON 9813
#define IDS_WEB_APP_FILE_HANDLING_NEGATIVE_BUTTON 9814
#define IDS_WEB_APP_PERMISSION_NEGATIVE_BUTTON 9815
#define IDS_PROTOCOL_HANDLER_INTENT_PICKER_QUESTION 9816
#define IDS_FONT_ACCESS_CHOOSER_PROMPT_ORIGIN 9817
#define IDS_FONT_ACCESS_CHOOSER_NO_FONTS_FOUND_PROMPT 9818
#define IDS_FONT_ACCESS_CHOOSER_IMPORT_BUTTON_TEXT 9819
#define IDS_FONT_ACCESS_CHOOSER_CANCEL_BUTTON_TEXT 9820
#define IDS_FONT_ACCESS_CHOOSER_SELECT_ALL_CHECKBOX_TEXT 9821
#define IDS_FONT_ACCESS_CHOOSER_LOADING_LABEL 9822
#define IDS_FONT_ACCESS_CHOOSER_LOADING_LABEL_TOOLTIP 9823
#define IDS_IME_API_ACTIVATED_WARNING 9824
#define IDS_IME_API_NEVER_SHOW 9825
#define IDS_BLOCKED_ADS_PROMPT_TOOLTIP 9826
#define IDS_REDIRECT_BLOCKED_MESSAGE 9830
#define IDS_REDIRECT_BLOCKED_TITLE 9831
#define IDS_REDIRECT_BLOCKED_TOOLTIP 9832
#define IDS_CONTROLLED_BY_AUTOMATION 9845
#define IDS_DOWNLOAD_OPEN_CONFIRMATION_DIALOG_TITLE 9847
#define IDS_DOWNLOAD_OPEN_CONFIRMATION_DIALOG_MESSAGE 9848
#define IDS_CONFIRM_FILE_UPLOAD_TITLE 9849
#define IDS_CONFIRM_FILE_UPLOAD_TEXT 9850
#define IDS_CONFIRM_FILE_UPLOAD_OK_BUTTON 9851
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_TITLE 9852
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_ALLOW_TEXT 9853
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_WRITE_PERMISSION_FILE_TEXT 9854
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_WRITE_PERMISSION_DIRECTORY_TEXT 9855
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_READ_PERMISSION_FILE_TEXT 9856
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_READ_PERMISSION_DIRECTORY_TEXT 9857
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_FILE_TEXT 9858
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_DIRECTORY_TEXT 9859
#define IDS_FILE_SYSTEM_ACCESS_READ_PERMISSION_FILE_TEXT 9860
#define IDS_FILE_SYSTEM_ACCESS_READ_PERMISSION_DIRECTORY_TEXT 9861
#define IDS_FILE_SYSTEM_ACCESS_EDIT_FILE_PERMISSION_TITLE 9862
#define IDS_FILE_SYSTEM_ACCESS_EDIT_DIRECTORY_PERMISSION_TITLE 9863
#define IDS_FILE_SYSTEM_ACCESS_READ_FILE_PERMISSION_TITLE 9864
#define IDS_FILE_SYSTEM_ACCESS_READ_DIRECTORY_PERMISSION_TITLE 9865
#define IDS_FILE_SYSTEM_ACCESS_EDIT_FILE_PERMISSION_ALLOW_TEXT 9866
#define IDS_FILE_SYSTEM_ACCESS_EDIT_DIRECTORY_PERMISSION_ALLOW_TEXT 9867
#define IDS_FILE_SYSTEM_ACCESS_VIEW_FILE_PERMISSION_ALLOW_TEXT 9868
#define IDS_FILE_SYSTEM_ACCESS_VIEW_DIRECTORY_PERMISSION_ALLOW_TEXT 9869
#define IDS_FILE_SYSTEM_ACCESS_WRITE_USAGE_TOOLTIP 9870
#define IDS_FILE_SYSTEM_ACCESS_DIRECTORY_USAGE_TOOLTIP 9871
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_WRITABLE_FILE_TEXT 9872
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_WRITABLE_FILES_TEXT 9873
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_WRITABLE_DIRECTORY_TEXT 9874
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_WRITABLE_DIRECTORIES_TEXT 9875
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_WRITABLE_FILES_AND_DIRECTORIES_TEXT 9876
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_READABLE_FILE_TEXT 9877
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READABLE_FILES_TEXT 9878
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_READABLE_DIRECTORY_TEXT 9879
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READABLE_DIRECTORIES_TEXT 9880
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READABLE_FILES_AND_DIRECTORIES_TEXT 9881
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READ_AND_WRITE 9882
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SAVE_CHANGES 9883
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_VIEW_CHANGES 9884
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_FILES_TEXT 9885
#define IDS_FILE_SYSTEM_ACCESS_USAGE_EXPAND 9886
#define IDS_FILE_SYSTEM_ACCESS_USAGE_COLLAPSE 9887
#define IDS_FILE_SYSTEM_ACCESS_USAGE_REMOVE_ACCESS 9888
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_DIRECTORY_TITLE 9889
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_DIRECTORY_TEXT 9890
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_DIRECTORY_BUTTON 9891
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_FILE_TITLE 9892
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_FILE_TEXT 9893
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_FILE_BUTTON 9894
#define IDS_RELAUNCH_ACCEPT_BUTTON 9895
#define IDS_RELAUNCH_REQUIRED_CANCEL_BUTTON 9896
#define IDS_WEBAUTHN_GENERIC_TITLE 9897
#define IDS_WEBAUTHN_CONTINUE 9898
#define IDS_WEBAUTHN_TRANSPORT_SELECTION_TITLE 9899
#define IDS_WEBAUTHN_TRANSPORT_SELECTION_DESCRIPTION 9900
#define IDS_WEBAUTHN_TRANSPORT_USB 9901
#define IDS_WEBAUTHN_TRANSPORT_INTERNAL 9902
#define IDS_WEBAUTHN_TRANSPORT_CABLE 9903
#define IDS_WEBAUTHN_TRANSPORT_AOA 9904
#define IDS_WEBAUTHN_TRANSPORT_POPUP_DIFFERENT_AUTHENTICATOR_WIN 9905
#define IDS_WEBAUTHN_USB_ACTIVATE_DESCRIPTION 9906
#define IDS_WEBAUTHN_ERROR_GENERIC_TITLE 9907
#define IDS_WEBAUTHN_ERROR_WRONG_KEY_TITLE 9908
#define IDS_WEBAUTHN_ERROR_WRONG_KEY_REGISTER_DESCRIPTION 9909
#define IDS_WEBAUTHN_ERROR_WRONG_KEY_SIGN_DESCRIPTION 9910
#define IDS_WEBAUTHN_ERROR_TIMEOUT_DESCRIPTION 9911
#define IDS_WEBAUTHN_ERROR_INTERNAL_UNRECOGNIZED_TITLE 9912
#define IDS_WEBAUTHN_ERROR_INTERNAL_UNRECOGNIZED_DESCRIPTION 9913
#define IDS_WEBAUTHN_ERROR_NO_TRANSPORTS_TITLE 9914
#define IDS_WEBAUTHN_ERROR_NO_TRANSPORTS_DESCRIPTION 9915
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_AUTO_TITLE 9916
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_AUTO_DESCRIPTION 9917
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_AUTO_NEXT 9918
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_MANUAL_TITLE 9919
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_MANUAL_DESCRIPTION 9920
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_MANUAL_NEXT 9921
#define IDS_WEBAUTHN_TRANSPORT_POPUP_LABEL 9922
#define IDS_WEBAUTHN_TRANSPORT_POPUP_USB 9923
#define IDS_WEBAUTHN_TRANSPORT_POPUP_PAIR_PHONE 9924
#define IDS_WEBAUTHN_TRANSPORT_POPUP_INTERNAL 9925
#define IDS_WEBAUTHN_TRANSPORT_POPUP_CABLE 9926
#define IDS_WEBAUTHN_TRANSPORT_POPUP_AOA 9927
#define IDS_WEBAUTHN_CABLE_ACTIVATE_TITLE 9928
#define IDS_WEBAUTHN_CABLE_ACTIVATE_TITLE_ALT 9929
#define IDS_WEBAUTHN_CABLE_ACTIVATE_DESCRIPTION 9930
#define IDS_WEBAUTHN_CABLEV2_SERVERLINK_DESCRIPTION 9931
#define IDS_WEBAUTHN_CABLEV2_SERVERLINK_TROUBLE 9932
#define IDS_WEBAUTHN_CABLEV2_SERVERLINK_TROUBLE_ALT 9933
#define IDS_WEBAUTHN_CABLEV2_AOA_TITLE 9934
#define IDS_WEBAUTHN_CABLEV2_AOA_DESCRIPTION 9935
#define IDS_WEBAUTHN_CABLEV2_AOA_REQUEST_DESCRIPTION 9936
#define IDS_WEBAUTHN_CABLEV2_2ND_FACTOR_DESCRIPTION 9937
#define IDS_WEBAUTHN_CABLEV2_ADD_PHONE 9938
#define IDS_WEBAUTHN_CABLE_ACTIVATE_DESCRIPTION_SHORT 9939
#define IDS_WEBAUTHN_CABLE_V2_ACTIVATE_TITLE 9940
#define IDS_WEBAUTHN_CABLE_V2_ACTIVATE_DESCRIPTION_SHORT 9941
#define IDS_WEBAUTHN_PIN_ENTRY_TITLE 9942
#define IDS_WEBAUTHN_PIN_ENTRY_DESCRIPTION 9943
#define IDS_WEBAUTHN_PIN_ENTRY_PIN_LABEL 9944
#define IDS_WEBAUTHN_PIN_ENTRY_NEXT 9945
#define IDS_WEBAUTHN_PIN_SETUP_DESCRIPTION 9946
#define IDS_WEBAUTHN_PIN_SETUP_CONFIRMATION_LABEL 9947
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_INVALID_CHARACTERS 9948
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_TOO_SHORT 9949
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_FAILED_RETRIES 9950
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_FAILED 9951
#define IDS_WEBAUTHN_PIN_SETUP_ERROR_FAILED 9952
#define IDS_WEBAUTHN_PIN_TAP_AGAIN_DESCRIPTION 9953
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_MISMATCH 9954
#define IDS_WEBAUTHN_CLIENT_PIN_SOFT_BLOCK_DESCRIPTION 9955
#define IDS_WEBAUTHN_CLIENT_PIN_HARD_BLOCK_DESCRIPTION 9956
#define IDS_WEBAUTHN_CLIENT_PIN_AUTHENTICATOR_REMOVED_DESCRIPTION 9957
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_SAME_AS_CURRENT 9958
#define IDS_WEBAUTHN_INLINE_ENROLLMENT_CANCEL_LABEL 9959
#define IDS_WEBAUTHN_UV_RETRY_TITLE 9960
#define IDS_WEBAUTHN_UV_RETRY_DESCRIPTION 9961
#define IDS_WEBAUTHN_UV_RETRY_ERROR_FAILED_RETRIES 9962
#define IDS_WEBAUTHN_UV_ERROR_LOCKED 9963
#define IDS_WEBAUTHN_FORCE_PIN_CHANGE 9964
#define IDS_WEBAUTHN_SELECT_ACCOUNT 9965
#define IDS_WEBAUTHN_UNKNOWN_ACCOUNT 9966
#define IDS_WEBAUTHN_RESIDENT_KEY_PRIVACY 9967
#define IDS_WEBAUTHN_RESIDENT_KEY_PREFERRED_PRIVACY 9968
#define IDS_WEBAUTHN_ERROR_MISSING_CAPABILITY_TITLE 9969
#define IDS_WEBAUTHN_ERROR_MISSING_CAPABILITY_DESC 9970
#define IDS_WEBAUTHN_STORAGE_FULL_DESC 9971
#define IDS_WEBAUTHN_REQUEST_ATTESTATION_PERMISSION_TITLE 9972
#define IDS_WEBAUTHN_REQUEST_ATTESTATION_PERMISSION_DESC 9973
#define IDS_WEBAUTHN_REQUEST_ENTERPRISE_ATTESTATION_PERMISSION_TITLE 9974
#define IDS_WEBAUTHN_REQUEST_ENTERPRISE_ATTESTATION_PERMISSION_DESC 9975
#define IDS_WEBAUTHN_ALLOW_ATTESTATION 9976
#define IDS_WEBAUTHN_DENY_ATTESTATION 9977
#define IDS_WEBAUTHN_RETRY 9978
#define IDS_WEBAUTHN_PLATFORM_AUTHENTICATOR_OFF_THE_RECORD_INTERSTITIAL_TITLE 9979
#define IDS_WEBAUTHN_PLATFORM_AUTHENTICATOR_OFF_THE_RECORD_INTERSTITIAL_DESCRIPTION 9980
#define IDS_WEBAUTHN_PLATFORM_AUTHENTICATOR_OFF_THE_RECORD_INTERSTITIAL_DENY 9981
#define IDS_WEBAUTHN_MANAGE_DEVICES 9982
#define IDS_ACCNAME_SIDE_SEARCH_TOOL 9983
#define IDS_ACCNAME_SIDE_SEARCH_TOOLBAR_BUTTON_ACTIVATED 9984
#define IDS_ACCNAME_SIDE_SEARCH_TOOLBAR_BUTTON_NOT_ACTIVATED 9985
#define IDS_ACCNAME_SIDE_SEARCH_CLOSE_BUTTON 9986
#define IDS_ACCNAME_SIDE_SEARCH_FEEDBACK_BUTTON 9987
#define IDS_SIDE_SEARCH_PROMO 9988
#define IDS_TOOLTIP_SIDE_SEARCH_TOOLBAR_BUTTON_ACTIVATED 9989
#define IDS_TOOLTIP_SIDE_SEARCH_TOOLBAR_BUTTON_NOT_ACTIVATED 9990
#define IDS_TOOLTIP_SIDE_SEARCH_CLOSE_BUTTON 9991
#define IDS_TOOLTIP_SIDE_SEARCH_FEEDBACK_BUTTON 9992
#define IDS_CABLEV2_MAKE_CREDENTIAL_NOTIFICATION_TITLE 9993
#define IDS_CABLEV2_GET_ASSERTION_NOTIFICATION_TITLE 9994
#define IDS_INCOGNITO_PROFILE_MENU_TITLE 9995
#define IDS_INCOGNITO_WINDOW_COUNT_MESSAGE 9996
#define IDS_INCOGNITO_PROFILE_MENU_CLOSE_BUTTON 9997
#define IDS_INCOGNITO_PROFILE_MENU_CLOSE_BUTTON_NEW 9998
#define IDS_INCOGNITO_CLEAR_BROWSING_DATA_DIALOG_PRIMARY_TEXT 9999
#define IDS_INCOGNITO_CLEAR_BROWSING_DATA_DIALOG_SECONDARY_TEXT 10000
#define IDS_INCOGNITO_CLEAR_BROWSING_DATA_DIALOG_CLOSE_WINDOWS_BUTTON 10001
#define IDS_INCOGNITO_HISTORY_BUBBLE_PRIMARY_TEXT 10002
#define IDS_INCOGNITO_HISTORY_BUBBLE_SECONDARY_TEXT 10003
#define IDS_INCOGNITO_HISTORY_BUBBLE_CLOSE_INCOGNITO_BUTTON_TEXT 10004
#define IDS_INCOGNITO_HISTORY_BUBBLE_CANCEL_BUTTON_TEXT 10005
#define IDS_GUEST_WINDOW_COUNT_MESSAGE 10006
#define IDS_GUEST_PROFILE_MENU_CLOSE_BUTTON 10007
#define IDS_HATS_BUBBLE_OK_LABEL 10008
#define IDS_HATS_BUBBLE_TEXT 10009
#define IDS_NOTIFICATION_DEFAULT_HELPFUL_BUTTON_TEXT 10010
#define IDS_NOTIFICATION_DEFAULT_UNHELPFUL_BUTTON_TEXT 10011
#define IDS_DEEP_SCANNING_DIALOG_TITLE 10012
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_PENDING_MESSAGE 10013
#define IDS_DEEP_SCANNING_DIALOG_PRINT_PENDING_MESSAGE 10014
#define IDS_DEEP_SCANNING_DIALOG_SUCCESS_MESSAGE 10015
#define IDS_DEEP_SCANNING_DIALOG_PRINT_SUCCESS_MESSAGE 10016
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_FAILURE_MESSAGE 10017
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_WARNING_MESSAGE 10018
#define IDS_DEEP_SCANNING_DIALOG_PRINT_WARNING_MESSAGE 10019
#define IDS_DEEP_SCANNING_DIALOG_TIMEOUT_MESSAGE 10020
#define IDS_DEEP_SCANNING_DIALOG_PROCEED_BUTTON 10021
#define IDS_DEEP_SCANNING_DIALOG_CANCEL_WARNING_BUTTON 10022
#define IDS_DEEP_SCANNING_DIALOG_CANCEL_UPLOAD_BUTTON 10023
#define IDS_DEEP_SCANNING_DIALOG_LARGE_FILE_FAILURE_MESSAGE 10024
#define IDS_DEEP_SCANNING_DIALOG_LARGE_PRINT_FAILURE_MESSAGE 10025
#define IDS_DEEP_SCANNING_DIALOG_ENCRYPTED_FILE_FAILURE_MESSAGE 10026
#define IDS_DEEP_SCANNING_DIALOG_CUSTOM_MESSAGE 10027
#define IDS_DEEP_SCANNING_DIALOG_DOWNLOADS_CUSTOM_MESSAGE 10028
#define IDS_DEEP_SCANNING_DIALOG_CUSTOM_MESSAGE_LEARN_MORE_LINK 10029
#define IDS_DEEP_SCANNING_DIALOG_DOWNLOADS_DISCARD_FILE_BUTTON 10030
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_BYPASS_JUSTIFICATION_LABEL 10031
#define IDS_DEEP_SCANNING_DIALOG_BYPASS_JUSTIFICATION_TEXT_LIMIT_LABEL 10032
#define IDS_DEEP_SCANNING_DIALOG_OPEN_NOW_TITLE 10033
#define IDS_DEEP_SCANNING_DIALOG_OPEN_NOW_MESSAGE 10034
#define IDS_DEEP_SCANNING_DIALOG_OPEN_NOW_ACCEPT_BUTTON 10035
#define IDS_DEEP_SCANNING_INFO_DIALOG_TITLE 10036
#define IDS_APP_DEEP_SCANNING_INFO_DIALOG_MESSAGE 10037
#define IDS_DEEP_SCANNING_INFO_DIALOG_MESSAGE 10038
#define IDS_DEEP_SCANNING_INFO_DIALOG_ACCEPT_BUTTON 10039
#define IDS_DEEP_SCANNING_INFO_DIALOG_CANCEL_BUTTON 10040
#define IDS_DEEP_SCANNING_INFO_DIALOG_OPEN_NOW_BUTTON 10041
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_TITLE 10042
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_MESSAGE 10043
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_ACCEPT_BUTTON 10044
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_CANCEL_BUTTON 10045
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_OPEN_NOW_BUTTON 10046
#define IDS_TAILORED_SECURITY_UNCONSENTED_MODAL_TITLE 10047
#define IDS_TAILORED_SECURITY_UNCONSENTED_ACCEPT_BUTTON 10048
#define IDS_TAILORED_SECURITY_UNCONSENTED_CANCEL_BUTTON 10049
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_MESSAGE_TITLE 10050
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_MESSAGE_DESCRIPTION 10051
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_MESSAGE_TITLE 10052
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_MESSAGE_DESCRIPTION 10053
#define IDS_TAILORED_SECURITY_CONSENTED_MESSAGE_OK_BUTTON 10054
#define IDS_FILE_SYSTEM_CONNECTOR_BOX 10055
#define IDS_FILE_SYSTEM_CONNECTOR_GOOGLE_DRIVE 10056
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_TITLE 10057
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_MESSAGE 10058
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_CANCEL_BUTTON 10059
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_ACCEPT_BUTTON 10060
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_TITLE 10061
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_MESSAGE 10062
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_CANCEL_BUTTON 10063
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_ACCEPT_BUTTON 10064
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_DIALOG_TITLE 10065
#define IDS_PROMPT_APP_UNINSTALL_TITLE 10066
#define IDS_APP_UNINSTALL_PROMPT_REMOVE_DATA_CHECKBOX_FOR_NON_GOOGLE 10067
#define IDS_APP_UNINSTALL_PROMPT_REMOVE_DATA_CHECKBOX_FOR_GOOGLE 10068
#define IDS_APP_UNINSTALL_PROMPT_LEARN_MORE 10069
#define IDS_UTILITY_PROCESS_SPEECH_RECOGNITION_SERVICE_NAME 10070
#define IDS_APP_PAUSE_PROMPT_TITLE 10071
#define IDS_APP_PAUSE_HEADING 10072
#define IDS_APP_PAUSE_HEADING_FOR_WEB_APPS 10073
#define IDS_APP_BLOCK_PROMPT_TITLE 10074
#define IDS_APP_BLOCK_HEADING_FOR_CHILD 10075
#define IDS_APP_BLOCK_HEADING 10076
#define IDS_ENTERPRISE_EXTENSION_REQUEST_APPROVED_TITLE 10077
#define IDS_ENTERPRISE_EXTENSION_REQUEST_REJECTED_TITLE 10078
#define IDS_ENTERPRISE_EXTENSION_REQUEST_FORCE_INSTALLED_TITLE 10079
#define IDS_ENTERPRISE_EXTENSION_REQUEST_CLICK_TO_INSTALL 10080
#define IDS_ENTERPRISE_EXTENSION_REQUEST_CLICK_TO_VIEW 10081
#define IDS_ENTERPRISE_EXTENSION_REQUEST_JUSTIFICATION 10082
#define IDS_ENTERPRISE_EXTENSION_REQUEST_JUSTIFICATION_PLACEHOLDER 10083
#define IDS_ENTERPRISE_EXTENSION_REQUEST_JUSTIFICATION_LENGTH_LIMIT 10084
#define IDS_NAME_WINDOW_PROMPT_TITLE 10085
#define IDS_QUICK_COMMANDS_LABEL 10086
#define IDS_QUICK_COMMANDS_PLACEHOLDER 10087
#define IDS_QUICK_COMMANDS_NO_RESULTS 10088
#define IDS_CHROMELABS_RELAUNCH_BUTTON_LABEL 10089
#define IDS_WINDOW_TITLE_EXPERIMENTS 10090
#define IDS_CHROMELABS_SEND_FEEDBACK 10091
#define IDS_CHROMELABS_SEND_FEEDBACK_DESCRIPTION_PLACEHOLDER 10092
#define IDS_CHROMELABS_DEFAULT 10093
#define IDS_CHROMELABS_ENABLED 10094
#define IDS_CHROMELABS_DISABLED 10095
#define IDS_CHROMELABS_ENABLED_WITH_VARIATION_NAME 10096
#define IDS_TAB_SEARCH_MEDIA_TABS_EXPERIMENT_NAME 10097
#define IDS_TAB_SEARCH_MEDIA_TABS_EXPERIMENT_DESCRIPTION 10098
#define IDS_MEDIA_TABS_ALSO_SHOWN_IN_OPEN_TABS_SECTION 10099
#define IDS_SIDE_PANEL_EXPERIMENT_NAME 10100
#define IDS_SIDE_PANEL_EXPERIMENT_DESCRIPTION 10101
#define IDS_TAB_SCROLLING_EXPERIMENT_NAME 10102
#define IDS_TAB_SCROLLING_EXPERIMENT_DESCRIPTION 10103
#define IDS_TABS_SHRINK_TO_PINNED_TAB_WIDTH 10104
#define IDS_TABS_SHRINK_TO_MEDIUM_WIDTH 10105
#define IDS_TABS_SHRINK_TO_LARGE_WIDTH 10106
#define IDS_TABS_DO_NOT_SHRINK 10107
#define IDS_TAB_SEARCH_EXPERIMENT_NAME 10108
#define IDS_TAB_SEARCH_EXPERIMENT_DESCRIPTION 10109
#define IDS_LENS_REGION_SEARCH_EXPERIMENT_NAME 10110
#define IDS_LENS_REGION_SEARCH_EXPERIMENT_DESCRIPTION 10111
#define IDS_LENS_REGION_SEARCH_BUBBLE_TEXT 10112
#define IDS_THUMBNAIL_TAB_STRIP_EXPERIMENT_NAME 10113
#define IDS_THUMBNAIL_TAB_STRIP_EXPERIMENT_DESCRIPTION 10114
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_NOTIFICATION_DESCRIPTION 10117
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_NOTIFICATION_ACCEPT 10118
#define IDS_ACCOUNT_SELECTION_SHEET_TITLE_EXPLICIT 10119
#define IDS_ACCOUNT_SELECTION_CONTINUE 10120
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT_NO_PP_OR_TOS 10121
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT_NO_PP 10122
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT_NO_TOS 10123
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT 10124
#define IDS_VERIFY_SHEET_TITLE 10125
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_TITLE 10143
#define IDS_PRIVACY_SANDBOX_BUBBLE_NOTICE_TITLE 10144
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_HEADER_1 10145
#define IDS_PRIVACY_SANDBOX_BUBBLE_NOTICE_DESCRIPTION 10146
#define IDS_PRIVACY_SANDBOX_BUBBLE_NOTICE_DESCRIPTION_ESTIMATES_INTERESTS_LINK 10147
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_DESCRIPTION_1 10148
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_HEADER_2 10149
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_HEADER 10150
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_BULLET_POINT_3 10151
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_ACCEPT_BUTTON 10152
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_DECLINE_BUTTON 10153
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_HEADER_1 10154
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_DESCRIPTION_1 10155
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_HEADER_2 10156
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_ACKNOWLEDGE_BUTTON 10157
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_OPEN_SETTINGS_BUTTON 10158

// ---------------------------------------------------------------------------
// From locale_settings.h:

#define IDS_SPELLCHECK_DICTIONARY 237
#define IDS_EDITBOOKMARK_DIALOG_WIDTH_CHARS 12000
#define IDS_EDITBOOKMARK_DIALOG_HEIGHT_LINES 12001
#define IDS_THEMES_GALLERY_URL 12002
#define IDS_WEBSTORE_URL 255
#define IDS_MEDIA_GALLERIES_DIALOG_CONTENT_WIDTH_CHARS 12003
#define IDS_SETTINGS_CLEAR_DATA_MYACTIVITY_URL_IN_DIALOG 12005
#define IDS_SETTINGS_CLEAR_DATA_MYACTIVITY_URL_IN_HISTORY 12006

// ---------------------------------------------------------------------------
// From omnibox_resources.h:

#define IDR_OMNIBOX_PEDAL_CONCEPTS 27640

// ---------------------------------------------------------------------------
// From platform_locale_settings.h:

#define IDS_STANDARD_FONT_FAMILY 211
#define IDS_FIXED_FONT_FAMILY 212
#define IDS_SERIF_FONT_FAMILY 213
#define IDS_NTP_FONT_FAMILY 12100
#define IDS_SANS_SERIF_FONT_FAMILY 214
#define IDS_CURSIVE_FONT_FAMILY 215
#define IDS_FANTASY_FONT_FAMILY 216
#define IDS_MATH_FONT_FAMILY 368
#define IDS_STANDARD_FONT_FAMILY_JAPANESE 218
#define IDS_FIXED_FONT_FAMILY_JAPANESE 219
#define IDS_SERIF_FONT_FAMILY_JAPANESE 220
#define IDS_SANS_SERIF_FONT_FAMILY_JAPANESE 221
#define IDS_STANDARD_FONT_FAMILY_KOREAN 222
#define IDS_SERIF_FONT_FAMILY_KOREAN 223
#define IDS_SANS_SERIF_FONT_FAMILY_KOREAN 224
#define IDS_STANDARD_FONT_FAMILY_SIMPLIFIED_HAN 225
#define IDS_SERIF_FONT_FAMILY_SIMPLIFIED_HAN 226
#define IDS_SANS_SERIF_FONT_FAMILY_SIMPLIFIED_HAN 227
#define IDS_CURSIVE_FONT_FAMILY_SIMPLIFIED_HAN 231
#define IDS_STANDARD_FONT_FAMILY_TRADITIONAL_HAN 228
#define IDS_SERIF_FONT_FAMILY_TRADITIONAL_HAN 229
#define IDS_SANS_SERIF_FONT_FAMILY_TRADITIONAL_HAN 230
#define IDS_CURSIVE_FONT_FAMILY_TRADITIONAL_HAN 232
#define IDS_MINIMUM_FONT_SIZE 235
#define IDS_MINIMUM_LOGICAL_FONT_SIZE 236

// ---------------------------------------------------------------------------
// From services_strings.h:

#define IDS_PROXY_RESOLVER_DISPLAY_NAME 31400
#define IDS_WINDOWS_SYSTEM_PROXY_RESOLVER_DISPLAY_NAME 31401

// ---------------------------------------------------------------------------
// From ui_strings.h:

#define IDS_TIME_SECS 35920
#define IDS_TIME_LONG_SECS 35921
#define IDS_TIME_LONG_SECS_2ND 35922
#define IDS_TIME_MINS 35923
#define IDS_TIME_LONG_MINS 35924
#define IDS_TIME_LONG_MINS_1ST 35925
#define IDS_TIME_LONG_MINS_2ND 35926
#define IDS_TIME_HOURS 35927
#define IDS_TIME_HOURS_1ST 35928
#define IDS_TIME_HOURS_2ND 35929
#define IDS_TIME_DAYS 35930
#define IDS_TIME_DAYS_1ST 35931
#define IDS_TIME_MONTHS 35932
#define IDS_TIME_YEARS 35933
#define IDS_TIME_REMAINING_SECS 35934
#define IDS_TIME_REMAINING_LONG_SECS 35935
#define IDS_TIME_REMAINING_MINS 35936
#define IDS_TIME_REMAINING_LONG_MINS 35937
#define IDS_TIME_REMAINING_HOURS 35938
#define IDS_TIME_REMAINING_DAYS 35939
#define IDS_TIME_REMAINING_MONTHS 35940
#define IDS_TIME_REMAINING_YEARS 35941
#define IDS_TIME_ELAPSED_SECS 35942
#define IDS_TIME_ELAPSED_LONG_SECS 35943
#define IDS_TIME_ELAPSED_MINS 35944
#define IDS_TIME_ELAPSED_LONG_MINS 35945
#define IDS_TIME_ELAPSED_HOURS 35946
#define IDS_TIME_ELAPSED_DAYS 35947
#define IDS_TIME_ELAPSED_MONTHS 35948
#define IDS_TIME_ELAPSED_YEARS 35949
#define IDS_PAST_TIME_TODAY 35950
#define IDS_PAST_TIME_YESTERDAY 35951
#define IDS_APP_MENU_EMPTY_SUBMENU 35952
#define IDS_CLIPBOARD_MENU_HTML 35953
#define IDS_CLIPBOARD_MENU_IMAGE 35954
#define IDS_CLIPBOARD_MENU_RTF_CONTENT 35955
#define IDS_CLIPBOARD_MENU_WEB_SMART_PASTE 35956
#define IDS_CLIPBOARD_MENU_CLIPBOARD 35957
#define IDS_CLIPBOARD_MENU_DELETE_ALL 35958
#define IDS_EXTENSION_PINNED 35959
#define IDS_EXTENSION_UNPINNED 35960
#define IDS_NEW_BADGE 35961
#define IDS_NEW_BADGE_SCREEN_READER_MESSAGE 35962
#define IDS_SENTENCE_END 35963
#define IDS_CONCAT_TWO_STRINGS_WITH_COMMA 35964
#define IDS_CONCAT_TWO_STRINGS_WITH_PERIODS 35965
#define IDS_APP_SAVEAS_ALL_FILES 35967
#define IDS_APP_SAVEAS_EXTENSION_FORMAT 35968
#define IDS_SELECT_UPLOAD_FOLDER_DIALOG_TITLE 35969
#define IDS_SAVE_PAGE_FILE_FORMAT_PROMPT_MAC 35970
#define IDS_SELECT_FOLDER_BUTTON_TITLE 35971
#define IDS_SELECT_UPLOAD_FOLDER_BUTTON_TITLE 35972
#define IDS_SPEECH_MAC 148
#define IDS_SPEECH_START_SPEAKING_MAC 149
#define IDS_SPEECH_STOP_SPEAKING_MAC 150
#define IDS_CONTENT_CONTEXT_LOOK_UP 35973
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_MENU 35974
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_DEFAULT 35975
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_LTR 35976
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_RTL 35977
#define IDS_SELECT_FOLDER_DIALOG_TITLE 35978
#define IDS_SAVE_AS_DIALOG_TITLE 35979
#define IDS_OPEN_FILE_DIALOG_TITLE 35980
#define IDS_OPEN_FILES_DIALOG_TITLE 35981
#define IDS_SAVEAS_ALL_FILES 35982
#define IDS_SELECT_UPLOAD_FOLDER_DIALOG_UPLOAD_BUTTON 35983
#define IDS_APP_ACCNAME_BACK 35984
#define IDS_APP_ACCNAME_CENTER 35985
#define IDS_APP_ACCNAME_CLOSE 35986
#define IDS_APP_ACCNAME_MINIMIZE 35987
#define IDS_APP_ACCNAME_MAXIMIZE 35988
#define IDS_APP_ACCNAME_RESTORE 35989
#define IDS_APP_ACCNAME_MENU 35990
#define IDS_APP_ACCNAME_FLOAT 35991
#define IDS_APP_ACCNAME_COLOR_CHOOSER_HEX_INPUT 35992
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLHERE 35993
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLLEFTEDGE 35994
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLRIGHTEDGE 35995
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLHOME 35996
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLEND 35997
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLPAGEUP 35998
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLPAGEDOWN 35999
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLLEFT 36000
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLRIGHT 36001
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLUP 36002
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLDOWN 36003
#define IDS_APP_TABLE_COLUMN_NOT_SORTED_ACCNAME 36004
#define IDS_APP_TABLE_COLUMN_SORTED_ASC_ACCNAME 36005
#define IDS_APP_TABLE_COLUMN_SORTED_DESC_ACCNAME 36006
#define IDS_APP_UNDO 36007
#define IDS_APP_CUT 36008
#define IDS_APP_COPY 36009
#define IDS_APP_PASTE 36010
#define IDS_APP_DELETE 36011
#define IDS_APP_SELECT_ALL 36012
#define IDS_CONTENT_CONTEXT_EMOJI 36013
#define IDS_APP_OK 36015
#define IDS_APP_CANCEL 36016
#define IDS_APP_CLOSE 36017
#define IDS_APP_ESC_KEY 36018
#define IDS_APP_TAB_KEY 36019
#define IDS_APP_INSERT_KEY 36020
#define IDS_APP_HOME_KEY 36021
#define IDS_APP_DELETE_KEY 36022
#define IDS_APP_END_KEY 36023
#define IDS_APP_PAGEUP_KEY 36024
#define IDS_APP_PAGEDOWN_KEY 36025
#define IDS_APP_LEFT_ARROW_KEY 36026
#define IDS_APP_RIGHT_ARROW_KEY 36027
#define IDS_APP_UP_ARROW_KEY 36028
#define IDS_APP_DOWN_ARROW_KEY 36029
#define IDS_APP_ENTER_KEY 36030
#define IDS_APP_SPACE_KEY 36031
#define IDS_APP_F1_KEY 36032
#define IDS_APP_F5_KEY 36033
#define IDS_APP_F6_KEY 36034
#define IDS_APP_F11_KEY 36035
#define IDS_APP_BACKSPACE_KEY 36036
#define IDS_APP_COMMA_KEY 36037
#define IDS_APP_PERIOD_KEY 36038
#define IDS_APP_MEDIA_NEXT_TRACK_KEY 36039
#define IDS_APP_MEDIA_PLAY_PAUSE_KEY 36040
#define IDS_APP_MEDIA_PREV_TRACK_KEY 36041
#define IDS_APP_MEDIA_STOP_KEY 36042
#define IDS_APP_ALT_KEY 36043
#define IDS_APP_COMMAND_KEY 36044
#define IDS_APP_CTRL_KEY 36045
#define IDS_APP_SEARCH_KEY 36046
#define IDS_APP_SHIFT_KEY 36047
#define IDS_APP_ACCELERATOR_WITH_MODIFIER 36052
#define IDS_APP_BYTES 36053
#define IDS_APP_KIBIBYTES 36054
#define IDS_APP_MEBIBYTES 36055
#define IDS_APP_GIBIBYTES 36056
#define IDS_APP_TEBIBYTES 36057
#define IDS_APP_PEBIBYTES 36058
#define IDS_APP_BYTES_PER_SECOND 36059
#define IDS_APP_KIBIBYTES_PER_SECOND 36060
#define IDS_APP_MEBIBYTES_PER_SECOND 36061
#define IDS_APP_GIBIBYTES_PER_SECOND 36062
#define IDS_APP_TEBIBYTES_PER_SECOND 36063
#define IDS_APP_PEBIBYTES_PER_SECOND 36064
#define IDS_MESSAGE_CENTER_ACCESSIBLE_NAME 36065
#define IDS_MESSAGE_CENTER_NOTIFICATION_ACCESSIBLE_NAME 36066
#define IDS_MESSAGE_CENTER_NOTIFICATION_ACCESSIBLE_NAME_PLURAL 36067
#define IDS_MESSAGE_CENTER_EXPAND_NOTIFICATION 36068
#define IDS_MESSAGE_CENTER_COLLAPSE_NOTIFICATION 36069
#define IDS_MESSAGE_CENTER_LIST_NOTIFICATION_MESSAGE_WITH_DIVIDER 36070
#define IDS_MESSAGE_CENTER_LIST_NOTIFICATION_HEADER_OVERFLOW_INDICATOR 36071
#define IDS_MESSAGE_CENTER_NOTIFICATION_PROGRESS_PERCENTAGE 36072
#define IDS_MESSAGE_CENTER_NOTIFICATION_CHROMEOS_SYSTEM 36073
#define IDS_MESSAGE_CENTER_NOTIFICATION_INLINE_REPLY_PLACEHOLDER 36074
#define IDS_MESSAGE_NOTIFICATION_NOW_STRING_SHORTEST 36075
#define IDS_MESSAGE_NOTIFICATION_DURATION_MINUTES_SHORTEST 36076
#define IDS_MESSAGE_NOTIFICATION_DURATION_HOURS_SHORTEST 36077
#define IDS_MESSAGE_NOTIFICATION_DURATION_DAYS_SHORTEST 36078
#define IDS_MESSAGE_NOTIFICATION_DURATION_YEARS_SHORTEST 36079
#define IDS_MESSAGE_NOTIFICATION_DURATION_MINUTES_SHORTEST_FUTURE 36080
#define IDS_MESSAGE_NOTIFICATION_DURATION_HOURS_SHORTEST_FUTURE 36081
#define IDS_MESSAGE_NOTIFICATION_DURATION_DAYS_SHORTEST_FUTURE 36082
#define IDS_MESSAGE_NOTIFICATION_DURATION_YEARS_SHORTEST_FUTURE 36083
#define IDS_MESSAGE_CENTER_BLOCK_ALL_NOTIFICATIONS_SITE 36084
#define IDS_MESSAGE_CENTER_BLOCK_ALL_NOTIFICATIONS_APP 36085
#define IDS_MESSAGE_CENTER_BLOCK_ALL_NOTIFICATIONS 36086
#define IDS_MESSAGE_CENTER_DONT_BLOCK_NOTIFICATIONS 36087
#define IDS_MESSAGE_CENTER_SETTINGS_DONE 36088
#define IDS_MESSAGE_CENTER_CLOSE_NOTIFICATION_BUTTON_ACCESSIBLE_NAME 36089
#define IDS_MESSAGE_CENTER_CLOSE_NOTIFICATION_BUTTON_TOOLTIP 36090
#define IDS_MESSAGE_CENTER_NOTIFICATION_SNOOZE_BUTTON_TOOLTIP 36091
#define IDS_MESSAGE_NOTIFICATION_SETTINGS_BUTTON_ACCESSIBLE_NAME 36092
#define IDS_MESSAGE_NOTIFICATION_ACCESSIBLE_NAME 36093
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_DEVICE_INFO 36094
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_CONFIRMATION_SUCCESS 36095
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_CONFIRMATION_FAILURE_TITLE 36096
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_CONFIRMATION_FAILURE_MESSAGE 36097
#define IDS_CLIPBOARD_HISTORY_MENU_PNG_IMAGE 36099
#define IDS_CLIPBOARD_HISTORY_MENU_HTML_IMAGE 36100
#define IDS_CLIPBOARD_HISTORY_MENU_TITLE 36101
#define IDS_CLIPBOARD_HISTORY_DELETE_BUTTON 36102
#define IDS_CLIPBOARD_HISTORY_ITEM_DELETION 36103
#define IDS_DISPLAY_TOUCH_CALIBRATION_EXIT_LABEL 36104
#define IDS_DISPLAY_TOUCH_CALIBRATION_HINT_LABEL_TEXT 36105
#define IDS_DISPLAY_TOUCH_CALIBRATION_HINT_SUBLABEL_TEXT 36106
#define IDS_DISPLAY_TOUCH_CALIBRATION_TAP_HERE_LABEL 36107
#define IDS_DISPLAY_TOUCH_CALIBRATION_FINISH_LABEL 36108
#define IDS_DISPLAY_NAME_UNKNOWN 36109
#define IDS_DISPLAY_NAME_INTERNAL 36110
#define IDS_CROSTINI_USE_LOW_DENSITY 36111
#define IDS_CROSTINI_USE_HIGH_DENSITY 36112
#define IDS_CROSTINI_APP_RESTART_BODY 36113
#define IDS_SATURATED_BADGE_CONTENT 36114
#define IDS_BADGE_UNREAD_NOTIFICATIONS_SATURATED 36115
#define IDS_BADGE_UNREAD_NOTIFICATIONS_UNSPECIFIED 36116
#define IDS_BADGE_UNREAD_NOTIFICATIONS 36117
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_TITLE_LABEL 36118
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_TITLE_NO_DEVICES 36119
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_CALL_BUTTON_LABEL 36120
#define IDS_BROWSER_SHARING_OMNIBOX_SENDING_LABEL 36121
#define IDS_BROWSER_SHARING_DIALOG_DEVICE_SUBTITLE_LAST_ACTIVE_DAYS 36122
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_INITIATING_ORIGIN 36123
#define IDS_BROWSER_SHARING_CONTENT_TYPE_TEXT 36124
#define IDS_BROWSER_SHARING_CONTENT_TYPE_NUMBER 36125
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TITLE_GENERIC_ERROR 36126
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TITLE_INTERNAL_ERROR 36127
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_NETWORK_ERROR 36128
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_DEVICE_ACK_TIMEOUT 36129
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_INTERNAL_ERROR 36130
#define IDS_BROWSER_SHARING_SHARED_CLIPBOARD_ERROR_DIALOG_TITLE_PAYLOAD_TOO_LARGE 36131
#define IDS_BROWSER_SHARING_SHARED_CLIPBOARD_ERROR_DIALOG_TEXT_PAYLOAD_TOO_LARGE 36132
#define IDS_SETTINGS_PASSWORD_SHOW 36136
#define IDS_SETTINGS_PASSWORD_HIDE 36137
#define IDS_TABLE_VIEW_AX_ANNOUNCE_ROW_SELECTED 36138

#endif  // CEF_INCLUDE_CEF_PACK_STRINGS_H_
