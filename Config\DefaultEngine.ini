

[/Script/EngineSettings.GameMapsSettings]
EditorStartupMap=/Game/Framework/Maps/main.main
LocalMapOptions=
TransitionMap=None
bUseSplitscreen=True
TwoPlayerSplitscreenLayout=Horizontal
ThreePlayerSplitscreenLayout=FavorTop
FourPlayerSplitscreenLayout=Grid
bOffsetPlayerGamepadIds=False
GameInstanceClass=/Game/Framework/Blueprints/BP_VisualizationGameInstance.BP_VisualizationGameInstance_C
GameDefaultMap=/Game/Framework/Maps/main.main
ServerDefaultMap=/Engine/Maps/Entry.Entry
GlobalDefaultGameMode=/Script/Engine.GameModeBase
GlobalDefaultServerGameMode=None

[/Script/WindowsTargetPlatform.WindowsTargetSettings]
DefaultGraphicsRHI=DefaultGraphicsRHI_DX12
-D3D12TargetedShaderFormats=PCD3D_SM5
+D3D12TargetedShaderFormats=PCD3D_SM6
-D3D11TargetedShaderFormats=PCD3D_SM5
+D3D11TargetedShaderFormats=PCD3D_SM5
Compiler=Default
AudioSampleRate=48000
AudioCallbackBufferFrameSize=1024
AudioNumBuffersToEnqueue=1
AudioMaxChannels=0
AudioNumSourceWorkers=4
SpatializationPlugin=
SourceDataOverridePlugin=
ReverbPlugin=
OcclusionPlugin=
CompressionOverrides=(bOverrideCompressionTimes=False,DurationThreshold=5.000000,MaxNumRandomBranches=0,SoundCueQualityIndex=0)
CacheSizeKB=65536
MaxChunkSizeOverrideKB=0
bResampleForDevice=False
MaxSampleRate=48000.000000
HighSampleRate=32000.000000
MedSampleRate=24000.000000
LowSampleRate=12000.000000
MinSampleRate=8000.000000
CompressionQualityModifier=1.000000
AutoStreamingThreshold=0.000000
SoundCueCookQualityIndex=-1


[/Script/HardwareTargeting.HardwareTargetingSettings]
TargetedHardwareClass=Desktop
AppliedTargetedHardwareClass=Desktop
DefaultGraphicsPerformance=Maximum
AppliedDefaultGraphicsPerformance=Maximum

[/Script/Engine.RendererSettings]
r.Mobile.ShadingPath=0
r.Mobile.SupportGPUScene=False
r.Mobile.AntiAliasing=1
r.Mobile.FloatPrecisionMode=0
r.Mobile.AllowDitheredLODTransition=False
r.Mobile.VirtualTextures=False
r.DiscardUnusedQuality=False
r.Shaders.CompressionFormat=2
r.AllowOcclusionQueries=True
r.MinScreenRadiusForLights=0.030000
r.MinScreenRadiusForDepthPrepass=0.030000
r.MinScreenRadiusForCSMDepth=0.010000
r.PrecomputedVisibilityWarning=False
r.TextureStreaming=True
Compat.UseDXT5NormalMaps=False
r.VirtualTextures=False
r.VT.EnableAutoImport=True
r.VirtualTexturedLightmaps=False
r.VT.AnisotropicFiltering=False
bEnableVirtualTextureOpacityMask=False
r.VT.TileSize=128
r.VT.TileBorderSize=4
r.vt.FeedbackFactor=16
WorkingColorSpaceChoice=sRGB
RedChromaticityCoordinate=(X=0.640000,Y=0.330000)
GreenChromaticityCoordinate=(X=0.300000,Y=0.600000)
BlueChromaticityCoordinate=(X=0.150000,Y=0.060000)
WhiteChromaticityCoordinate=(X=0.312700,Y=0.329000)
r.ClearCoatNormal=False
r.DynamicGlobalIlluminationMethod=1
r.ReflectionMethod=1
r.ReflectionCaptureResolution=512
r.ReflectionEnvironmentLightmapMixBasedOnRoughness=True
r.Lumen.HardwareRayTracing=False
r.Lumen.HardwareRayTracing.LightingMode=0
r.Lumen.TranslucencyReflections.FrontLayer.EnableForProject=False
r.Lumen.TraceMeshSDFs=0
r.Shadow.Virtual.Enable=1
r.RayTracing=False
r.RayTracing.Shadows=False
r.RayTracing.Skylight=False
r.RayTracing.UseTextureLod=False
r.PathTracing=True
r.GenerateMeshDistanceFields=True
r.DistanceFields.DefaultVoxelDensity=0.200000
r.Nanite.ProjectEnabled=True
r.AllowStaticLighting=True
r.NormalMapsForStaticLighting=False
r.ForwardShading=False
r.VertexFoggingForOpaque=True
r.SeparateTranslucency=True
r.TranslucentSortPolicy=0
TranslucentSortAxis=(X=0.000000,Y=-1.000000,Z=0.000000)
vr.VRS.HMDFixedFoveationLevel=0
vr.VRS.HMDFixedFoveationDynamic=False
r.CustomDepth=1
r.CustomDepthTemporalAAJitter=True
r.PostProcessing.PropagateAlpha=0
r.DefaultFeature.Bloom=True
r.DefaultFeature.AmbientOcclusion=True
r.DefaultFeature.AmbientOcclusionStaticFraction=True
r.DefaultFeature.AutoExposure=True
r.DefaultFeature.AutoExposure.Method=0
r.DefaultFeature.AutoExposure.Bias=1.000000
r.DefaultFeature.AutoExposure.ExtendDefaultLuminanceRange=True
r.DefaultFeature.MotionBlur=True
r.DefaultFeature.LensFlare=False
r.TemporalAA.Upsampling=True
r.AntiAliasingMethod=4
r.MSAACount=4
r.DefaultFeature.LightUnits=1
r.DefaultBackBufferPixelFormat=4
r.Shadow.UnbuiltPreviewInGame=True
r.StencilForLODDither=False
r.EarlyZPass=3
r.EarlyZPassOnlyMaterialMasking=False
r.Shadow.CSMCaching=False
r.DBuffer=True
r.ClearSceneMethod=1
r.VelocityOutputPass=1
r.Velocity.EnableVertexDeformation=2
r.SelectiveBasePassOutputs=False
bDefaultParticleCutouts=False
fx.GPUSimulationTextureSizeX=1024
fx.GPUSimulationTextureSizeY=1024
r.AllowGlobalClipPlane=False
r.GBufferFormat=1
r.MorphTarget.Mode=True
r.GPUCrashDebugging=False
vr.InstancedStereo=False
r.MobileHDR=True
vr.MobileMultiView=False
r.Mobile.UseHWsRGBEncoding=False
vr.RoundRobinOcclusion=True
r.MeshStreaming=False
r.HeterogeneousVolumes=True
r.WireframeCullThreshold=5.000000
r.SupportStationarySkylight=True
r.SupportLowQualityLightmaps=True
r.SupportPointLightWholeSceneShadows=True
r.SupportSkyAtmosphere=True
r.SupportSkyAtmosphereAffectsHeightFog=True
r.SupportCloudShadowOnForwardLitTranslucent=False
r.Shadow.TranslucentPerObject.ProjectEnabled=False
r.Water.SingleLayerWater.SupportCloudShadow=False
r.Substrate=False
r.Substrate.OpaqueMaterialRoughRefraction=False
r.Substrate.Debug.AdvancedVisualizationShaders=False
r.Material.RoughDiffuse=False
r.Material.EnergyConservation=False
r.OIT.SortedPixels=False
r.SkinCache.CompileShaders=False
r.SkinCache.SkipCompilingGPUSkinVF=False
r.SkinCache.DefaultBehavior=1
r.SkinCache.SceneMemoryLimitInMB=128.000000
r.Mobile.EnableStaticAndCSMShadowReceivers=True
r.Mobile.EnableMovableLightCSMShaderCulling=True
r.Mobile.Forward.EnableLocalLights=True
r.Mobile.Forward.EnableClusteredReflections=False
r.Mobile.EnableNoPrecomputedLightingCSMShader=False
r.Mobile.AllowDistanceFieldShadows=True
r.Mobile.AllowMovableDirectionalLights=True
r.Mobile.EnableMovableSpotlightsShadow=False
r.GPUSkin.Support16BitBoneIndex=False
r.GPUSkin.Limit2BoneInfluences=False
r.SupportDepthOnlyIndexBuffers=True
r.SupportReversedIndexBuffers=True
r.Mobile.AmbientOcclusion=False
r.GPUSkin.UnlimitedBoneInfluences=False
r.GPUSkin.UnlimitedBoneInfluencesThreshold=8
DefaultBoneInfluenceLimit=(Default=0,PerPlatform=())
MaxSkinBones=(Default=65536,PerPlatform=(("Mobile", 256)))
r.Mobile.PlanarReflectionMode=0
r.Mobile.SupportsGen4TAA=True
bStreamSkeletalMeshLODs=(Default=False,PerPlatform=())
bDiscardSkeletalMeshOptionalLODs=(Default=False,PerPlatform=())
VisualizeCalibrationColorMaterialPath=/Engine/EngineMaterials/PPM_DefaultCalibrationColor.PPM_DefaultCalibrationColor
VisualizeCalibrationCustomMaterialPath=None
VisualizeCalibrationGrayscaleMaterialPath=/Engine/EngineMaterials/PPM_DefaultCalibrationGrayscale.PPM_DefaultCalibrationGrayscale

[/Script/WorldPartitionEditor.WorldPartitionEditorSettings]
CommandletClass=Class'/Script/UnrealEd.WorldPartitionConvertCommandlet'

[/Script/Engine.UserInterfaceSettings]
bAuthorizeAutomaticWidgetVariableCreation=False

[/Script/Engine.Engine]
+ActiveGameNameRedirects=(OldGameName="TP_BlankBP",NewGameName="/Script/qzs")
+ActiveGameNameRedirects=(OldGameName="/Script/TP_BlankBP",NewGameName="/Script/qzs")
AssetManagerClassName=/Script/qzs.BIMAssetManager

[/Script/AndroidFileServerEditor.AndroidFileServerRuntimeSettings]
bEnablePlugin=True
bAllowNetworkConnection=True
SecurityToken=7DD15B024930FAA9FF190395B7EE37B3
bIncludeInShipping=False
bAllowExternalStartInShipping=False
bCompileAFSProject=False
bUseCompression=False
bLogFiles=False
bReportStats=False
ConnectionType=USBOnly
bUseManualIPAddress=False
ManualIPAddress=

[/Script/Slate.SlateSettings]
bExplicitCanvasChildZOrder=False

