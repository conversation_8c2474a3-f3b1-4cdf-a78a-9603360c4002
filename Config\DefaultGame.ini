

[/Script/EngineSettings.GeneralProjectSettings]
ProjectID=F4467D75409EFE0C96462DB1E4BE9038

[WebView]
#cefdebug=18080   # CEF调试js端口
#gpuid=0
#touch=true

[/Script/UnrealEd.ProjectPackagingSettings]
Build=IfProjectHasCode
BuildConfiguration=PPBC_Development
BuildTarget=
FullRebuild=False
ForDistribution=False
IncludeDebugFiles=False
BlueprintNativizationMethod=Disabled
bIncludeNativizedAssetsInProjectGeneration=False
bExcludeMonolithicEngineHeadersInNativizedCode=False
UsePakFile=True
bUseIoStore=True
bUseZenStore=False
bMakeBinaryConfig=False
bGenerateChunks=False
bGenerateNoChunks=False
bChunkHardReferencesOnly=False
bForceOneChunkPerFile=False
MaxChunkSize=0
bBuildHttpChunkInstallData=False
HttpChunkInstallDataDirectory=(Path="")
WriteBackMetadataToAssetRegistry=Disabled
bCompressed=True
PackageCompressionFormat=Oodle
bForceUseProjectCompressionFormatIgnoreHardwareOverride=False
PackageAdditionalCompressionOptions=
PackageCompressionMethod=Kraken
PackageCompressionLevel_DebugDevelopment=4
PackageCompressionLevel_TestShipping=5
PackageCompressionLevel_Distribution=7
PackageCompressionMinBytesSaved=1024
PackageCompressionMinPercentSaved=5
bPackageCompressionEnableDDC=False
PackageCompressionMinSizeToConsiderDDC=0
HttpChunkInstallDataVersion=
IncludePrerequisites=True
IncludeAppLocalPrerequisites=False
bShareMaterialShaderCode=True
bDeterministicShaderCodeOrder=False
bSharedMaterialNativeLibraries=True
ApplocalPrerequisitesDirectory=(Path="")
IncludeCrashReporter=False
InternationalizationPreset=English
-CulturesToStage=en
+CulturesToStage=en
LocalizationTargetCatchAllChunkId=0
bCookAll=False
bCookMapsOnly=False
bSkipEditorContent=False
bSkipMovies=False
-IniKeyDenylist=KeyStorePassword
-IniKeyDenylist=KeyPassword
-IniKeyDenylist=rsa.privateexp
-IniKeyDenylist=rsa.modulus
-IniKeyDenylist=rsa.publicexp
-IniKeyDenylist=aes.key
-IniKeyDenylist=SigningPublicExponent
-IniKeyDenylist=SigningModulus
-IniKeyDenylist=SigningPrivateExponent
-IniKeyDenylist=EncryptionKey
-IniKeyDenylist=DevCenterUsername
-IniKeyDenylist=DevCenterPassword
-IniKeyDenylist=IOSTeamID
-IniKeyDenylist=SigningCertificate
-IniKeyDenylist=MobileProvision
-IniKeyDenylist=IniKeyDenylist
-IniKeyDenylist=IniSectionDenylist
+IniKeyDenylist=KeyStorePassword
+IniKeyDenylist=KeyPassword
+IniKeyDenylist=rsa.privateexp
+IniKeyDenylist=rsa.modulus
+IniKeyDenylist=rsa.publicexp
+IniKeyDenylist=aes.key
+IniKeyDenylist=SigningPublicExponent
+IniKeyDenylist=SigningModulus
+IniKeyDenylist=SigningPrivateExponent
+IniKeyDenylist=EncryptionKey
+IniKeyDenylist=DevCenterUsername
+IniKeyDenylist=DevCenterPassword
+IniKeyDenylist=IOSTeamID
+IniKeyDenylist=SigningCertificate
+IniKeyDenylist=MobileProvision
+IniKeyDenylist=IniKeyDenylist
+IniKeyDenylist=IniSectionDenylist
-IniSectionDenylist=HordeStorageServers
-IniSectionDenylist=StorageServers
+IniSectionDenylist=HordeStorageServers
+IniSectionDenylist=StorageServers
+MapsToCook=(FilePath="/Game/StarterContent/Maps/Map_BGMODEL_V2")
+MapsToCook=(FilePath="/Game/StarterContent/Maps/Map_BGMODEL_V3")
+MapsToCook=(FilePath="/Game/StarterContent/Maps/Map_BGMODEL_V4")
+MapsToCook=(FilePath="/Game/StarterContent/Maps/Map_BGMODEL_V5")
+DirectoriesToAlwaysCook=(Path="/DatasmithRuntime/Materials")
+DirectoriesToAlwaysCook=(Path="/Game/Framework/Movies")
+DirectoriesToAlwaysCook=(Path="/Game/Maps")
+DirectoriesToAlwaysCook=(Path="/ProjectAssetsDLC/ProjectAssets/Materials")
+DirectoriesToAlwaysCook=(Path="/ProjectAssetsDLC/ProjectAssets/Models/mesh")
+DirectoriesToAlwaysCook=(Path="/Game/StarterContent")
+DirectoriesToAlwaysStageAsUFS=(Path="license")
+DirectoriesToAlwaysStageAsUFS=(Path="Framework/Movies")

[/Script/AsyncLoadingScreen.LoadingScreenSettings]
bPreloadBackgroundImages=False
StartupLoadingScreen=(MinimumLoadingScreenDisplayTime=-1.000000,bAutoCompleteWhenLoadingCompletes=True,bMoviesAreSkippable=False,bWaitForManualStop=False,bAllowInEarlyStartup=False,bAllowEngineTick=False,PlaybackType=MT_Looped,MoviePaths=,bShuffle=False,bSetDisplayMovieIndexManually=False,bShowWidgetOverlay=False,bShowLoadingCompleteText=False,LoadingCompleteTextSettings=(LoadingCompleteText="",Appearance=(ColorAndOpacity=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),Font=(FontObject=/Script/Engine.Font'"/Engine/EngineFonts/Roboto.Roboto"',FontMaterial=None,OutlineSettings=(OutlineSize=0,bSeparateFillAlpha=False,bApplyOutlineToDropShadows=False,OutlineMaterial=None,OutlineColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)),TypefaceFontName="Normal",Size=24,LetterSpacing=0,SkewAmount=0.000000),ShadowOffset=(X=0.000000,Y=0.000000),ShadowColorAndOpacity=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),Justification=Left),Alignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),Padding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),bFadeInOutAnim=True,AnimationSpeed=1.000000),Background=(Images=,ImageStretch=ScaleToFit,Padding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),BackgroundColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000),bSetDisplayBackgroundManually=False),TipWidget=(TipText=,Appearance=(ColorAndOpacity=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),Font=(FontObject=/Script/Engine.Font'"/Engine/EngineFonts/Roboto.Roboto"',FontMaterial=None,OutlineSettings=(OutlineSize=0,bSeparateFillAlpha=False,bApplyOutlineToDropShadows=False,OutlineMaterial=None,OutlineColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)),TypefaceFontName="Normal",Size=20,LetterSpacing=0,SkewAmount=0.000000),ShadowOffset=(X=0.000000,Y=0.000000),ShadowColorAndOpacity=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),Justification=Left),TipWrapAt=1000.000000,bSetDisplayTipTextManually=False),LoadingWidget=(LoadingIconType=LIT_CircularThrobber,LoadingWidgetType=LWT_Horizontal,TransformTranslation=(X=0.000000,Y=0.000000),TransformScale=(X=1.000000,Y=1.000000),TransformPivot=(X=0.500000,Y=0.500000),LoadingText=NSLOCTEXT("AsyncLoadingScreen", "Loading", "LOADING"),bLoadingTextRightPosition=True,bLoadingTextTopPosition=True,Appearance=(ColorAndOpacity=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),Font=(FontObject=/Script/Engine.Font'"/Engine/EngineFonts/Roboto.Roboto"',FontMaterial=None,OutlineSettings=(OutlineSize=0,bSeparateFillAlpha=False,bApplyOutlineToDropShadows=False,OutlineMaterial=None,OutlineColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)),TypefaceFontName="Bold",Size=32,LetterSpacing=0,SkewAmount=0.000000),ShadowOffset=(X=0.000000,Y=0.000000),ShadowColorAndOpacity=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),Justification=Left),ThrobberSettings=(NumberOfPieces=3,bAnimateHorizontally=True,bAnimateVertically=True,bAnimateOpacity=True,Image=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False))),CircularThrobberSettings=(NumberOfPieces=6,Period=0.750000,Radius=64.000000,Image=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False))),ImageSequenceSettings=(Images=,Scale=(X=1.000000,Y=1.000000),Interval=0.050000,bPlayReverse=False),TextAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),LoadingIconAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),Space=1.000000,bHideLoadingWidgetWhenCompletes=False),Layout=ALSL_Classic)
DefaultLoadingScreen=(MinimumLoadingScreenDisplayTime=-1.000000,bAutoCompleteWhenLoadingCompletes=True,bMoviesAreSkippable=False,bWaitForManualStop=False,bAllowInEarlyStartup=False,bAllowEngineTick=False,PlaybackType=MT_Looped,MoviePaths=("cons","ops"),bShuffle=False,bSetDisplayMovieIndexManually=True,bShowWidgetOverlay=True,bShowLoadingCompleteText=False,LoadingCompleteTextSettings=(LoadingCompleteText="",Appearance=(ColorAndOpacity=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),Font=(FontObject=/Script/Engine.Font'"/Engine/EngineFonts/Roboto.Roboto"',FontMaterial=None,OutlineSettings=(OutlineSize=0,bSeparateFillAlpha=False,bApplyOutlineToDropShadows=False,OutlineMaterial=None,OutlineColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)),TypefaceFontName="Normal",Size=24,LetterSpacing=0,SkewAmount=0.000000),ShadowOffset=(X=0.000000,Y=0.000000),ShadowColorAndOpacity=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),Justification=Left),Alignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),Padding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),bFadeInOutAnim=True,AnimationSpeed=1.000000),Background=(Images=,ImageStretch=ScaleToFit,Padding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),BackgroundColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000),bSetDisplayBackgroundManually=False),TipWidget=(TipText=,Appearance=(ColorAndOpacity=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),Font=(FontObject=/Script/Engine.Font'"/Engine/EngineFonts/Roboto.Roboto"',FontMaterial=None,OutlineSettings=(OutlineSize=0,bSeparateFillAlpha=False,bApplyOutlineToDropShadows=False,OutlineMaterial=None,OutlineColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)),TypefaceFontName="Normal",Size=20,LetterSpacing=0,SkewAmount=0.000000),ShadowOffset=(X=0.000000,Y=0.000000),ShadowColorAndOpacity=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),Justification=Left),TipWrapAt=1000.000000,bSetDisplayTipTextManually=False),LoadingWidget=(LoadingIconType=LIT_ImageSequence,LoadingWidgetType=LWT_Horizontal,TransformTranslation=(X=0.000000,Y=0.000000),TransformScale=(X=1.000000,Y=1.000000),TransformPivot=(X=0.500000,Y=0.500000),LoadingText=NSLOCTEXT("[/Script/AsyncLoadingScreen]", "3E14CB614B4FDFFC3EE725885AD01CEC", "加载中"),bLoadingTextRightPosition=False,bLoadingTextTopPosition=True,Appearance=(ColorAndOpacity=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),Font=(FontObject=/Script/Engine.Font'"/Engine/EngineFonts/Roboto.Roboto"',FontMaterial=None,OutlineSettings=(OutlineSize=0,bSeparateFillAlpha=False,bApplyOutlineToDropShadows=False,OutlineMaterial=None,OutlineColor=(R=0.000000,G=0.000000,B=0.000000,A=1.000000)),TypefaceFontName="Bold",Size=32,LetterSpacing=0,SkewAmount=0.000000),ShadowOffset=(X=0.000000,Y=0.000000),ShadowColorAndOpacity=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),Justification=Left),ThrobberSettings=(NumberOfPieces=3,bAnimateHorizontally=True,bAnimateVertically=True,bAnimateOpacity=True,Image=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False))),CircularThrobberSettings=(NumberOfPieces=6,Period=0.750000,Radius=64.000000,Image=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False))),ImageSequenceSettings=(Images=(/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-1.T_jiazaiA-1"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-2.T_jiazaiA-2"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-3.T_jiazaiA-3"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-4.T_jiazaiA-4"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-5.T_jiazaiA-5"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-6.T_jiazaiA-6"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-7.T_jiazaiA-7"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-8.T_jiazaiA-8"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-9.T_jiazaiA-9"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-10.T_jiazaiA-10"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-11.T_jiazaiA-11"',/Script/Engine.Texture2D'"/Game/images/T_jiazaiA-12.T_jiazaiA-12"'),Scale=(X=0.200000,Y=0.200000),Interval=0.050000,bPlayReverse=False),TextAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),LoadingIconAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),Space=1.000000,bHideLoadingWidgetWhenCompletes=False),Layout=ALSL_Classic)
Classic=(bIsWidgetAtBottom=True,bIsLoadingWidgetAtLeft=False,Space=1.000000,TipAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),BorderHorizontalAlignment=HAlign_Fill,BorderPadding=(Left=0.000000,Top=0.000000,Right=100.000000,Bottom=100.000000),BorderBackground=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=0.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False)))
Center=(bIsTipAtBottom=True,TipAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),BorderHorizontalAlignment=HAlign_Fill,BorderVerticalOffset=0.000000,BorderPadding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),BorderBackground=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False)))
Letterbox=(bIsLoadingWidgetAtTop=True,TipAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),LoadingWidgetAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),TopBorderHorizontalAlignment=HAlign_Fill,BottomBorderHorizontalAlignment=HAlign_Fill,TopBorderPadding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),BottomBorderPadding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TopBorderBackground=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False)),BottomBorderBackground=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False)))
Sidebar=(bIsWidgetAtRight=True,bIsLoadingWidgetAtTop=True,Space=1.000000,VerticalAlignment=VAlign_Center,LoadingWidgetAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),TipAlignment=(HorizontalAlignment=HAlign_Center,VerticalAlignment=VAlign_Center),BorderVerticalAlignment=VAlign_Fill,BorderHorizontalOffset=0.000000,BorderPadding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),BorderBackground=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False)))
DualSidebar=(bIsLoadingWidgetAtRight=True,LeftVerticalAlignment=VAlign_Center,RightVerticalAlignment=VAlign_Center,LeftBorderVerticalAlignment=VAlign_Fill,RightBorderVerticalAlignment=VAlign_Fill,LeftBorderPadding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),RightBorderPadding=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),LeftBorderBackground=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False)),RightBorderBackground=(bIsDynamicallyLoaded=False,DrawAs=Image,Tiling=NoTile,Mirroring=NoMirror,ImageType=NoImage,ImageSize=(X=32.000000,Y=32.000000),Margin=(Left=0.000000,Top=0.000000,Right=0.000000,Bottom=0.000000),TintColor=(SpecifiedColor=(R=1.000000,G=1.000000,B=1.000000,A=1.000000),ColorUseRule=UseColor_Specified),OutlineSettings=(CornerRadii=(X=0.000000,Y=0.000000,Z=0.000000,W=1.000000),Color=(SpecifiedColor=(R=0.000000,G=0.000000,B=0.000000,A=0.000000),ColorUseRule=UseColor_Specified),Width=0.000000,RoundingType=HalfHeightRadius,bUseBrushTransparency=False),ResourceObject=None,ResourceName="",UVRegion=(Min=(X=0.000000,Y=0.000000),Max=(X=0.000000,Y=0.000000),bIsValid=False)))

