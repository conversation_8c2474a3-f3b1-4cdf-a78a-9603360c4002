/************************************************************************************
 *																					*
 * Copyright (C) 2020 Truong Bui.													*
 * Website:	https://github.com/truong-bui/AsyncLoadingScreen						*
 * Licensed under the MIT License. See 'LICENSE' file for full license information. *
 *																					*
 ************************************************************************************/

#include "AsyncLoadingScreen.h"
#include "MoviePlayer.h"
#include "LoadingScreenSettings.h"
#include "SCenterLayout.h"
#include "SClassicLayout.h"
#include "SLetterboxLayout.h"
#include "SSidebarLayout.h"
#include "SDualSidebarLayout.h"
#include "Framework/Application/SlateApplication.h"
#include "AsyncLoadingScreenLibrary.h"
#include "Engine/Texture2D.h"

#define LOCTEXT_NAMESPACE "FAsyncLoadingScreenModule"

void FAsyncLoadingScreenModule::StartupModule()
{
	// This code will execute after your module is loaded into memory; the exact timing is specified in the .uplugin file per-module
	if (!IsRunningDedicatedServer() && FSlateApplication::IsInitialized())
	{
		const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();
				
		if (IsMoviePlayerEnabled())
		{
			GetMoviePlayer()->OnPrepareLoadingScreen().AddRaw(this, &FAsyncLoadingScreenModule::PreSetupLoadingScreen);				
		}		
		
		// If PreloadBackgroundImages option is check, load all background images into memory
		if (Settings->bPreloadBackgroundImages)
		{
			LoadBackgroundImages();
		}

		// Prepare the startup screen, the PreSetupLoadingScreen callback won't be called
		// if we've already explicitly setup the loading screen
		// bIsStartupLoadingScreen = true;
		// SetupLoadingScreen(Settings->StartupLoadingScreen);
	}	
}

void FAsyncLoadingScreenModule::ShutdownModule()
{
	// This function may be called during shutdown to clean up your module.  For modules that support dynamic reloading,
	// we call this function before unloading the module.
	if (!IsRunningDedicatedServer())
	{
		// TODO: Unregister later
		GetMoviePlayer()->OnPrepareLoadingScreen().RemoveAll(this);
	}
}

bool FAsyncLoadingScreenModule::IsGameModule() const
{
	return true;
}

TArray<UTexture2D*> FAsyncLoadingScreenModule::GetBackgroundImages()
{
    // 由于启动屏幕的设置被跳过，bIsStartupLoadingScreen 可能一直为 false
    // return bIsStartupLoadingScreen ? StartupBackgroundImages : DefaultBackgroundImages;
     return DefaultBackgroundImages; // 直接返回默认图片列表可能更符合当前逻辑
}

// TArray<UTexture2D*> FAsyncLoadingScreenModule::GetBackgroundImages()
// {
// 	return bIsStartupLoadingScreen ? StartupBackgroundImages : DefaultBackgroundImages;
// }

void FAsyncLoadingScreenModule::PreSetupLoadingScreen()
{

	// 获取当前世界和地图名称，用于日志区分（可能在某些早期阶段返回 nullptr）
	UWorld* CurrentWorld = nullptr;
	// 尝试获取 GWorld，这在很多情况下可用，但不是所有时候都保证有效
	if (GEngine && GEngine->GameViewport && GEngine->GameViewport->GetWorld())
	{
		CurrentWorld = GEngine->GameViewport->GetWorld();
	}
	// 备选方案：有时需要通过迭代 UObject 来查找 World，但这里我们先用简单方法
	FString MapName = CurrentWorld ? CurrentWorld->GetMapName() : TEXT("Unknown");
	// 移除路径前缀，只保留地图名
	MapName.RemoveFromStart(CurrentWorld ? CurrentWorld->StreamingLevelsPrefix : TEXT(""));

	// 打印详细日志，包含标志状态和地图名
	UE_LOG(LogTemp, Warning, TEXT("PreSetupLoadingScreen called. bInitialLoadSequenceHandled = %s. Map = %s"),
		bInitialLoadSequenceHandled ? TEXT("true") : TEXT("false"),
		*MapName);

   

    // --- 对于后续的关卡加载 ---
    UE_LOG(LogTemp, Warning, TEXT("PreSetupLoadingScreen (Subsequent Level Load)")); // 可以修改日志帮助区分

    // 检查全局的加载屏幕启用状态 (可通过蓝图或C++控制)
    const bool bIsEnableLoadingScreen = UAsyncLoadingScreenLibrary::GetIsEnableLoadingScreen();
    // if (MapName == TEXT("Unknown") || MapName == TEXT("Untitled_0")) // 不受
    // {
    //     // 获取设置并使用 DefaultLoadingScreen 配置加载屏幕
    //     const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();
    //     // bIsStartupLoadingScreen 在这里的意义不大了，但设置无害
    //     bIsStartupLoadingScreen = false;
    //     SetupLoadingScreen(Settings->StartupLoadingScreen);
    //     UE_LOG(LogTemp, Log, TEXT("AsyncLoadingScreen: StartupLoadingScreen"));
    // }
	// if (MapName == TEXT("main"))
    // {
	// 	const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();
	// 	// bIsStartupLoadingScreen 在这里的意义不大了，但设置无害
	// 	bIsStartupLoadingScreen = false;
	// 	SetupLoadingScreen(Settings->DefaultLoadingScreen);
	// 	UE_LOG(LogTemp, Log, TEXT("AsyncLoadingScreen: DefaultLoadingScreen"));
    // }
	// if (MapName == TEXT("ConsMain") || MapName == TEXT("OpsMain_WP"))
	if (bIsEnableLoadingScreen)
	{
		// 获取设置并使用 DefaultLoadingScreen 配置加载屏幕
		const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();
		// bIsStartupLoadingScreen 在这里的意义不大了，但设置无害
		bIsStartupLoadingScreen = false;
		SetupLoadingScreen(Settings->StartupLoadingScreen);
	} else {
		// 获取设置并使用 DefaultLoadingScreen 配置加载屏幕
		const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();
		// bIsStartupLoadingScreen 在这里的意义不大了，但设置无害
		bIsStartupLoadingScreen = false;
		SetupLoadingScreen(Settings->DefaultLoadingScreen);
	}
}

// void FAsyncLoadingScreenModule::PreSetupLoadingScreen()
// {	
// 	UE_LOG(LogTemp, Warning, TEXT("PreSetupLoadingScreen"));
// 	const bool bIsEnableLoadingScreen = UAsyncLoadingScreenLibrary::GetIsEnableLoadingScreen();
// 	if (bIsEnableLoadingScreen)
// 	{
// 		const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();
// 		bIsStartupLoadingScreen = false;
// 		SetupLoadingScreen(Settings->DefaultLoadingScreen);
// 	}	
// }

void FAsyncLoadingScreenModule::SetupLoadingScreen(const FALoadingScreenSettings& LoadingScreenSettings)
{
	TArray<FString> MoviesList = LoadingScreenSettings.MoviePaths;

	// Shuffle the movies list
	if (LoadingScreenSettings.bShuffle == true)
	{
		ShuffleMovies(MoviesList);
	}
		
	if (LoadingScreenSettings.bSetDisplayMovieIndexManually == true)
	{
		MoviesList.Empty();

		// Show specific movie if valid otherwise show original movies list
		if (LoadingScreenSettings.MoviePaths.IsValidIndex(UAsyncLoadingScreenLibrary::GetDisplayMovieIndex()))
		{
			MoviesList.Add(LoadingScreenSettings.MoviePaths[UAsyncLoadingScreenLibrary::GetDisplayMovieIndex()]);
		}
		else
		{
			MoviesList = LoadingScreenSettings.MoviePaths;
		}
	}

	FLoadingScreenAttributes LoadingScreen;
	LoadingScreen.MinimumLoadingScreenDisplayTime = LoadingScreenSettings.MinimumLoadingScreenDisplayTime;
	LoadingScreen.bAutoCompleteWhenLoadingCompletes = LoadingScreenSettings.bAutoCompleteWhenLoadingCompletes;
	LoadingScreen.bMoviesAreSkippable = LoadingScreenSettings.bMoviesAreSkippable;
	LoadingScreen.bWaitForManualStop = LoadingScreenSettings.bWaitForManualStop;
	LoadingScreen.bAllowInEarlyStartup = LoadingScreenSettings.bAllowInEarlyStartup;
	LoadingScreen.bAllowEngineTick = LoadingScreenSettings.bAllowEngineTick;
	LoadingScreen.MoviePaths = MoviesList;
	LoadingScreen.PlaybackType = LoadingScreenSettings.PlaybackType;

	if (LoadingScreenSettings.bShowWidgetOverlay)
	{
		const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();

		switch (LoadingScreenSettings.Layout)
		{
		case EAsyncLoadingScreenLayout::ALSL_Classic:
			LoadingScreen.WidgetLoadingScreen = SNew(SClassicLayout, LoadingScreenSettings, Settings->Classic);
			break;
		case EAsyncLoadingScreenLayout::ALSL_Center:
			LoadingScreen.WidgetLoadingScreen = SNew(SCenterLayout, LoadingScreenSettings, Settings->Center);
			break;
		case EAsyncLoadingScreenLayout::ALSL_Letterbox:
			LoadingScreen.WidgetLoadingScreen = SNew(SLetterboxLayout, LoadingScreenSettings, Settings->Letterbox);
			break;
		case EAsyncLoadingScreenLayout::ALSL_Sidebar:
			LoadingScreen.WidgetLoadingScreen = SNew(SSidebarLayout, LoadingScreenSettings, Settings->Sidebar);
			break;
		case EAsyncLoadingScreenLayout::ALSL_DualSidebar:
			LoadingScreen.WidgetLoadingScreen = SNew(SDualSidebarLayout, LoadingScreenSettings, Settings->DualSidebar);
			break;
		}
		
	}

	GetMoviePlayer()->SetupLoadingScreen(LoadingScreen);
}

void FAsyncLoadingScreenModule::ShuffleMovies(TArray<FString>& MoviesList)
{
	if (MoviesList.Num() > 0)
	{
		int32 LastIndex = MoviesList.Num() - 1;
		for (int32 i = 0; i <= LastIndex; ++i)
		{
			int32 Index = FMath::RandRange(i, LastIndex);
			if (i != Index)
			{
				MoviesList.Swap(i, Index);
			}
		}
	}
}

void FAsyncLoadingScreenModule::LoadBackgroundImages()
{
	// Empty all background images array
	RemoveAllBackgroundImages();

	const ULoadingScreenSettings* Settings = GetDefault<ULoadingScreenSettings>();
	
	// Preload startup background images
	for (auto& Image : Settings->StartupLoadingScreen.Background.Images)
	{
		UTexture2D* LoadedImage = Cast<UTexture2D>(Image.TryLoad());
		if (LoadedImage)
		{
			StartupBackgroundImages.Add(LoadedImage);
		}
	}

	// Preload default background images
	for (auto& Image : Settings->DefaultLoadingScreen.Background.Images)
	{
		UTexture2D* LoadedImage = Cast<UTexture2D> (Image.TryLoad());
		if (LoadedImage)
		{
			DefaultBackgroundImages.Add(LoadedImage);
		}		
	}
}

void FAsyncLoadingScreenModule::RemoveAllBackgroundImages()
{
	StartupBackgroundImages.Empty();
	DefaultBackgroundImages.Empty();
}

bool FAsyncLoadingScreenModule::IsPreloadBackgroundImagesEnabled()
{	
	return GetDefault<ULoadingScreenSettings>()->bPreloadBackgroundImages;
}

#undef LOCTEXT_NAMESPACE
	
IMPLEMENT_MODULE(FAsyncLoadingScreenModule, AsyncLoadingScreen)