// Copyright (c) 2022 Marshall <PERSON>. All rights reserved.
//
// Redistribution and use in source and binary forms, with or without
// modification, are permitted provided that the following conditions are
// met:
//
//    * Redistributions of source code must retain the above copyright
// notice, this list of conditions and the following disclaimer.
//    * Redistributions in binary form must reproduce the above
// copyright notice, this list of conditions and the following disclaimer
// in the documentation and/or other materials provided with the
// distribution.
//    * Neither the name of Google Inc. nor the name Chromium Embedded
// Framework nor the names of its contributors may be used to endorse
// or promote products derived from this software without specific prior
// written permission.
//
// THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
// "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
// LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
// A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT
// OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
// SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT
// LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE,
// DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY
// THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT
// (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
// OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
//
// ---------------------------------------------------------------------------
//
// This file is generated by the make_pack_header.py tool.
//

#ifndef CEF_INCLUDE_CEF_PACK_STRINGS_H_
#define CEF_INCLUDE_CEF_PACK_STRINGS_H_
#pragma once

// ---------------------------------------------------------------------------
// From blink_accessibility_strings.h:

#define IDS_AX_UNLABELED_IMAGE_ROLE_DESCRIPTION 31520
#define IDS_AX_IMAGE_ELIGIBLE_FOR_ANNOTATION 31523
#define IDS_AX_IMAGE_ANNOTATION_PENDING 31524
#define IDS_AX_IMAGE_ANNOTATION_ADULT 31525
#define IDS_AX_IMAGE_ANNOTATION_NO_DESCRIPTION 31526
#define IDS_AX_IMAGE_ANNOTATION_OCR_CONTEXT 31527
#define IDS_AX_IMAGE_ANNOTATION_DESCRIPTION_CONTEXT 31528
#define IDS_AX_IMAGE_ANNOTATION_ICON_PLUS 31529
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_BACKWARD 31530
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_FORWARD 31531
#define IDS_AX_IMAGE_ANNOTATION_ICON_CALL 31532
#define IDS_AX_IMAGE_ANNOTATION_ICON_CHAT 31533
#define IDS_AX_IMAGE_ANNOTATION_ICON_CHECK 31534
#define IDS_AX_IMAGE_ANNOTATION_ICON_X 31535
#define IDS_AX_IMAGE_ANNOTATION_ICON_DELETE 31536
#define IDS_AX_IMAGE_ANNOTATION_ICON_EDIT 31537
#define IDS_AX_IMAGE_ANNOTATION_ICON_EMOJI 31538
#define IDS_AX_IMAGE_ANNOTATION_ICON_END_CALL 31539
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_DOWNWARD 31540
#define IDS_AX_IMAGE_ANNOTATION_ICON_HEART 31541
#define IDS_AX_IMAGE_ANNOTATION_ICON_HOME 31542
#define IDS_AX_IMAGE_ANNOTATION_ICON_INFO 31543
#define IDS_AX_IMAGE_ANNOTATION_ICON_LAUNCH_APPS 31544
#define IDS_AX_IMAGE_ANNOTATION_ICON_THUMBS_UP 31545
#define IDS_AX_IMAGE_ANNOTATION_ICON_THREE_BARS 31546
#define IDS_AX_IMAGE_ANNOTATION_ICON_THREE_DOTS 31547
#define IDS_AX_IMAGE_ANNOTATION_ICON_NOTIFICATIONS 31548
#define IDS_AX_IMAGE_ANNOTATION_ICON_PAUSE 31549
#define IDS_AX_IMAGE_ANNOTATION_ICON_PLAY 31550
#define IDS_AX_IMAGE_ANNOTATION_ICON_REFRESH 31551
#define IDS_AX_IMAGE_ANNOTATION_ICON_MAGNIFYING_GLASS 31552
#define IDS_AX_IMAGE_ANNOTATION_ICON_SEND 31553
#define IDS_AX_IMAGE_ANNOTATION_ICON_SETTINGS 31554
#define IDS_AX_IMAGE_ANNOTATION_ICON_SHARE 31555
#define IDS_AX_IMAGE_ANNOTATION_ICON_STAR 31556
#define IDS_AX_IMAGE_ANNOTATION_ICON_TAKE_PHOTO 31557
#define IDS_AX_IMAGE_ANNOTATION_ICON_TIME 31558
#define IDS_AX_IMAGE_ANNOTATION_ICON_VIDEOCAM 31559
#define IDS_AX_IMAGE_ANNOTATION_ICON_EXPAND 31560
#define IDS_AX_IMAGE_ANNOTATION_ICON_CONTRACT 31561
#define IDS_AX_IMAGE_ANNOTATION_ICON_GOOGLE 31562
#define IDS_AX_IMAGE_ANNOTATION_ICON_TWITTER 31563
#define IDS_AX_IMAGE_ANNOTATION_ICON_FACEBOOK 31564
#define IDS_AX_IMAGE_ANNOTATION_ICON_ASSISTANT 31565
#define IDS_AX_IMAGE_ANNOTATION_ICON_WEATHER 31566
#define IDS_AX_IMAGE_ANNOTATION_ICON_SHOPPING_CART 31567
#define IDS_AX_IMAGE_ANNOTATION_ICON_UPLOAD 31568
#define IDS_AX_IMAGE_ANNOTATION_ICON_QUESTION 31569
#define IDS_AX_IMAGE_ANNOTATION_ICON_MIC 31570
#define IDS_AX_IMAGE_ANNOTATION_ICON_MIC_MUTE 31571
#define IDS_AX_IMAGE_ANNOTATION_ICON_GALLERY 31572
#define IDS_AX_IMAGE_ANNOTATION_ICON_COMPASS 31573
#define IDS_AX_IMAGE_ANNOTATION_ICON_PEOPLE 31574
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_UPWARD 31575
#define IDS_AX_IMAGE_ANNOTATION_ICON_ENVELOPE 31576
#define IDS_AX_IMAGE_ANNOTATION_ICON_EMOJI_FACE 31577
#define IDS_AX_IMAGE_ANNOTATION_ICON_PAPERCLIP 31578
#define IDS_AX_IMAGE_ANNOTATION_ICON_CAST 31579
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_UP 31580
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_DOWN 31581
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_STATE 31582
#define IDS_AX_IMAGE_ANNOTATION_ICON_VOLUME_MUTE 31583
#define IDS_AX_IMAGE_ANNOTATION_ICON_STOP 31584
#define IDS_AX_IMAGE_ANNOTATION_ICON_SHOPPING_BAG 31585
#define IDS_AX_IMAGE_ANNOTATION_ICON_LIST 31586
#define IDS_AX_IMAGE_ANNOTATION_ICON_LOCATION 31587
#define IDS_AX_IMAGE_ANNOTATION_ICON_CALENDAR 31588
#define IDS_AX_IMAGE_ANNOTATION_ICON_THUMBS_DOWN 31589
#define IDS_AX_IMAGE_ANNOTATION_ICON_HEADSET 31590
#define IDS_AX_IMAGE_ANNOTATION_ICON_REDO 31591
#define IDS_AX_IMAGE_ANNOTATION_ICON_UNDO 31592
#define IDS_AX_IMAGE_ANNOTATION_ICON_DOWNLOAD 31593
#define IDS_AX_IMAGE_ANNOTATION_ICON_ARROW_DOWNWARD 31594
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_UPWARD 31595
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_FORWARD 31596
#define IDS_AX_IMAGE_ANNOTATION_ICON_V_BACKWARD 31597
#define IDS_AX_IMAGE_ANNOTATION_ICON_HISTORY 31598
#define IDS_AX_IMAGE_ANNOTATION_ICON_PERSON 31599
#define IDS_AX_IMAGE_ANNOTATION_ICON_HAPPY_FACE 31600
#define IDS_AX_IMAGE_ANNOTATION_ICON_SAD_FACE 31601
#define IDS_AX_IMAGE_ANNOTATION_ICON_MOON 31602
#define IDS_AX_IMAGE_ANNOTATION_ICON_CLOUD 31603
#define IDS_AX_IMAGE_ANNOTATION_ICON_SUN 31604

// ---------------------------------------------------------------------------
// From blink_strings.h:

#define IDS_DETAILS_WITHOUT_SUMMARY_LABEL 31620
#define IDS_FORM_CALENDAR_CLEAR 31621
#define IDS_FORM_CALENDAR_TODAY 31622
#define IDS_FORM_SUBMIT_LABEL 31623
#define IDS_FORM_INPUT_ALT 31624
#define IDS_FORM_RESET_LABEL 31625
#define IDS_FORM_FILE_BUTTON_LABEL 31626
#define IDS_FORM_MULTIPLE_FILES_BUTTON_LABEL 31627
#define IDS_FORM_FILE_NO_FILE_LABEL 31628
#define IDS_FORM_FILE_MULTIPLE_UPLOAD 31629
#define IDS_FORM_OTHER_COLOR_LABEL 31630
#define IDS_FORM_OTHER_DATE_LABEL 31631
#define IDS_FORM_OTHER_MONTH_LABEL 31632
#define IDS_FORM_OTHER_WEEK_LABEL 31633
#define IDS_FORM_PLACEHOLDER_FOR_DAY_OF_MONTH_FIELD 31634
#define IDS_FORM_PLACEHOLDER_FOR_MONTH_FIELD 31635
#define IDS_FORM_PLACEHOLDER_FOR_YEAR_FIELD 31636
#define IDS_FORM_SELECT_MENU_LIST_TEXT 31637
#define IDS_FORM_THIS_MONTH_LABEL 31638
#define IDS_FORM_THIS_WEEK_LABEL 31639
#define IDS_FORM_WEEK_NUMBER_LABEL 31640
#define IDS_AX_CALENDAR_SHOW_DATE_PICKER 31641
#define IDS_AX_CALENDAR_SHOW_DATE_TIME_LOCAL_PICKER 31642
#define IDS_AX_CALENDAR_SHOW_MONTH_PICKER 31643
#define IDS_AX_CALENDAR_SHOW_TIME_PICKER 31644
#define IDS_AX_CALENDAR_SHOW_WEEK_PICKER 31645
#define IDS_AX_CALENDAR_SHOW_MONTH_SELECTOR 31646
#define IDS_AX_CALENDAR_SHOW_NEXT_MONTH 31647
#define IDS_AX_CALENDAR_SHOW_PREVIOUS_MONTH 31648
#define IDS_AX_CALENDAR_WEEK_DESCRIPTION 31649
#define IDS_AX_COLOR_EDIT_BLUE 31650
#define IDS_AX_COLOR_EDIT_GREEN 31651
#define IDS_AX_COLOR_EDIT_HEXADECIMAL 31652
#define IDS_AX_COLOR_EDIT_HUE 31653
#define IDS_AX_COLOR_EDIT_LIGHTNESS 31654
#define IDS_AX_COLOR_EDIT_RED 31655
#define IDS_AX_COLOR_EDIT_SATURATION 31656
#define IDS_AX_COLOR_EYEDROPPER 31657
#define IDS_AX_COLOR_FORMAT_TOGGLER 31658
#define IDS_AX_COLOR_HUE_SLIDER 31659
#define IDS_AX_COLOR_WELL 31660
#define IDS_AX_COLOR_WELL_ROLEDESCRIPTION 31661
#define IDS_SYSTEM_COLOR_CHOOSER 31662
#define IDS_AX_AM_PM_FIELD_TEXT 31684
#define IDS_AX_DAY_OF_MONTH_FIELD_TEXT 31685
#define IDS_AX_HOUR_FIELD_TEXT 31686
#define IDS_AX_MEDIA_DEFAULT 31687
#define IDS_AX_MEDIA_AUDIO_ELEMENT 31688
#define IDS_AX_MEDIA_VIDEO_ELEMENT 31689
#define IDS_AX_MEDIA_MUTE_BUTTON 31690
#define IDS_AX_MEDIA_UNMUTE_BUTTON 31691
#define IDS_AX_MEDIA_PLAY_BUTTON 31692
#define IDS_AX_MEDIA_PAUSE_BUTTON 31693
#define IDS_AX_MEDIA_CURRENT_TIME_DISPLAY 31694
#define IDS_AX_MEDIA_TIME_REMAINING_DISPLAY 31695
#define IDS_AX_MEDIA_ENTER_FULL_SCREEN_BUTTON 31696
#define IDS_AX_MEDIA_EXIT_FULL_SCREEN_BUTTON 31697
#define IDS_AX_MEDIA_DISPLAY_CUT_OUT_FULL_SCREEN_BUTTON 31698
#define IDS_AX_MEDIA_ENTER_PICTURE_IN_PICTURE_BUTTON 31699
#define IDS_AX_MEDIA_EXIT_PICTURE_IN_PICTURE_BUTTON 31700
#define IDS_AX_MEDIA_LOADING_PANEL 31701
#define IDS_AX_MEDIA_SHOW_CLOSED_CAPTIONS_MENU_BUTTON 31702
#define IDS_AX_MEDIA_HIDE_CLOSED_CAPTIONS_MENU_BUTTON 31703
#define IDS_AX_MEDIA_SHOW_PLAYBACK_SPEED_MENU_BUTTON 31704
#define IDS_AX_MEDIA_HIDE_PLAYBACK_SPEED_MENU_BUTTON 31705
#define IDS_AX_MEDIA_CAST_OFF_BUTTON 31706
#define IDS_AX_MEDIA_CAST_ON_BUTTON 31707
#define IDS_AX_MEDIA_DOWNLOAD_BUTTON 31708
#define IDS_AX_MEDIA_OVERFLOW_BUTTON 31709
#define IDS_AX_MEDIA_AUDIO_ELEMENT_HELP 31710
#define IDS_AX_MEDIA_VIDEO_ELEMENT_HELP 31711
#define IDS_AX_MEDIA_AUDIO_SLIDER_HELP 31712
#define IDS_AX_MEDIA_VIDEO_SLIDER_HELP 31713
#define IDS_AX_MEDIA_VOLUME_SLIDER_HELP 31714
#define IDS_AX_MEDIA_CURRENT_TIME_DISPLAY_HELP 31715
#define IDS_AX_MEDIA_TIME_REMAINING_DISPLAY_HELP 31716
#define IDS_AX_MEDIA_OVERFLOW_BUTTON_HELP 31717
#define IDS_AX_MILLISECOND_FIELD_TEXT 31718
#define IDS_AX_MINUTE_FIELD_TEXT 31719
#define IDS_AX_MONTH_FIELD_TEXT 31720
#define IDS_AX_SECOND_FIELD_TEXT 31721
#define IDS_AX_WEEK_OF_YEAR_FIELD_TEXT 31722
#define IDS_AX_YEAR_FIELD_TEXT 31723
#define IDS_AX_OBJECT_SELECTED 31724
#define IDS_AX_OBJECT_NOT_SELECTED 31725
#define IDS_VIEW_SOURCE_LINE_WRAP 31726
#define IDS_FORM_INPUT_WEEK_TEMPLATE 31727
#define IDS_FORM_VALIDATION_VALUE_MISSING_MULTIPLE_FILE 31728
#define IDS_FORM_VALIDATION_TYPE_MISMATCH 31729
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY 31730
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY_DOMAIN 31731
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_EMPTY_LOCAL 31732
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_DOMAIN 31733
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_DOTS 31734
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_INVALID_LOCAL 31735
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL_NO_AT_SIGN 31736
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_MULTIPLE_EMAIL 31737
#define IDS_FORM_VALIDATION_VALUE_NOT_EQUAL 31738
#define IDS_FORM_VALIDATION_VALUE_NOT_EQUAL_DATETIME 31739
#define IDS_FORM_VALIDATION_RANGE_UNDERFLOW 31740
#define IDS_FORM_VALIDATION_RANGE_UNDERFLOW_DATETIME 31741
#define IDS_FORM_VALIDATION_RANGE_OVERFLOW 31742
#define IDS_FORM_VALIDATION_RANGE_OVERFLOW_DATETIME 31743
#define IDS_FORM_VALIDATION_REVERSED_RANGE_OUT_OF_RANGE_TIME 31744
#define IDS_FORM_VALIDATION_RANGE_INVALID_DATETIME 31745
#define IDS_FORM_VALIDATION_BAD_INPUT_DATETIME 31746
#define IDS_FORM_VALIDATION_BAD_INPUT_NUMBER 31747
#define IDS_FORM_VALIDATION_VALUE_MISSING 31748
#define IDS_FORM_VALIDATION_VALUE_MISSING_CHECKBOX 31749
#define IDS_FORM_VALIDATION_VALUE_MISSING_FILE 31750
#define IDS_FORM_VALIDATION_VALUE_MISSING_RADIO 31751
#define IDS_FORM_VALIDATION_VALUE_MISSING_SELECT 31752
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_EMAIL 31753
#define IDS_FORM_VALIDATION_TYPE_MISMATCH_URL 31754
#define IDS_FORM_VALIDATION_PATTERN_MISMATCH 31755
#define IDS_FORM_VALIDATION_STEP_MISMATCH 31756
#define IDS_FORM_VALIDATION_STEP_MISMATCH_CLOSE_TO_LIMIT 31757
#define IDS_FORM_VALIDATION_TOO_LONG 31758
#define IDS_FORM_VALIDATION_TOO_SHORT 31759
#define IDS_FORM_VALIDATION_TOO_SHORT_PLURAL 31760
#define IDS_MEDIA_SESSION_FILE_SOURCE 31761
#define IDS_MEDIA_OVERFLOW_MENU_CLOSED_CAPTIONS 31762
#define IDS_MEDIA_OVERFLOW_MENU_CLOSED_CAPTIONS_SUBMENU_TITLE 31763
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED 31764
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_SUBMENU_TITLE 31765
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_0_25X_TITLE 31766
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_0_5X_TITLE 31767
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_0_75X_TITLE 31768
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_NORMAL_TITLE 31769
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_1_25X_TITLE 31770
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_1_5X_TITLE 31771
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_1_75X_TITLE 31772
#define IDS_MEDIA_OVERFLOW_MENU_PLAYBACK_SPEED_2X_TITLE 31773
#define IDS_MEDIA_OVERFLOW_MENU_CAST 31774
#define IDS_MEDIA_OVERFLOW_MENU_ENTER_FULLSCREEN 31775
#define IDS_MEDIA_OVERFLOW_MENU_EXIT_FULLSCREEN 31776
#define IDS_MEDIA_OVERFLOW_MENU_MUTE 31777
#define IDS_MEDIA_OVERFLOW_MENU_UNMUTE 31778
#define IDS_MEDIA_OVERFLOW_MENU_PLAY 31779
#define IDS_MEDIA_OVERFLOW_MENU_PAUSE 31780
#define IDS_MEDIA_OVERFLOW_MENU_DOWNLOAD 31781
#define IDS_MEDIA_OVERFLOW_MENU_ENTER_PICTURE_IN_PICTURE 31782
#define IDS_MEDIA_OVERFLOW_MENU_EXIT_PICTURE_IN_PICTURE 31783
#define IDS_MEDIA_PICTURE_IN_PICTURE_INTERSTITIAL_TEXT 31784
#define IDS_MEDIA_REMOTING_CAST_TEXT 31785
#define IDS_MEDIA_REMOTING_CAST_TO_UNKNOWN_DEVICE_TEXT 31786
#define IDS_MEDIA_REMOTING_STOP_TEXT 31787
#define IDS_MEDIA_REMOTING_STOP_BY_PLAYBACK_QUALITY_TEXT 31788
#define IDS_MEDIA_REMOTING_STOP_BY_ERROR_TEXT 31789
#define IDS_MEDIA_SCRUBBING_MESSAGE_TEXT 31790
#define IDS_MEDIA_TRACKS_NO_LABEL 31791
#define IDS_MEDIA_TRACKS_OFF 31792
#define IDS_PLUGIN_INITIALIZATION_ERROR 31793
#define IDS_MEDIA_PLAYBACK_ERROR 31794
#define IDS_UNITS_KIBIBYTES 31795
#define IDS_UNITS_MEBIBYTES 31796
#define IDS_UNITS_GIBIBYTES 31797
#define IDS_UNITS_TEBIBYTES 31798
#define IDS_UNITS_PEBIBYTES 31799
#define CONTENT_INVALID_TRUE 31800
#define CONTENT_INVALID_SPELLING 31801
#define CONTENT_INVALID_GRAMMAR 31802

// ---------------------------------------------------------------------------
// From cef_strings.h:

#define IDS_TEXT_FILES 64000
#define IDS_CONTENT_CONTEXT_NO_SPELLING_SUGGESTIONS 64001

// ---------------------------------------------------------------------------
// From chromium_strings.h:

#define IDS_RELAUNCH_CONFIRMATION_DIALOG_TITLE 400
#define IDS_SETTINGS_ABOUT_PROGRAM 401
#define IDS_SETTINGS_GET_HELP_USING_CHROME 402
#define IDS_SETTINGS_UPGRADE_UPDATING 403
#define IDS_SETTINGS_UPGRADE_UPDATING_PERCENT 404
#define IDS_SETTINGS_UPGRADE_SUCCESSFUL_RELAUNCH 405
#define IDS_SETTINGS_UPGRADE_UP_TO_DATE 406
#define IDS_SETTINGS_GOOGLE_PAYMENTS_CACHED 411
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_OFFLINE 412
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_SIGNED_OUT 413
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_NO_PASSWORDS 414
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_QUOTA_LIMIT_GOOGLE_ACCOUNT 415
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_QUOTA_LIMIT 416
#define IDS_SETTINGS_CHECK_PASSWORDS_ERROR_GENERIC 417
#define IDS_SETTINGS_NO_COMPROMISED_CREDENTIALS_LABEL 418
#define IDS_SETTINGS_SIGNED_OUT_USER_LABEL 419
#define IDS_SETTINGS_SIGNED_OUT_USER_HAS_COMPROMISED_CREDENTIALS_LABEL 420
#define IDS_SETTINGS_WEAK_PASSWORDS_DESCRIPTION_GENERATION 421
#define IDS_SETTINGS_COMPROMISED_EDIT_DISCLAIMER_DESCRIPTION 422
#define IDS_SETTINGS_DEFAULT_BROWSER_DEFAULT 423
#define IDS_SETTINGS_DEFAULT_BROWSER_MAKE_DEFAULT 424
#define IDS_SETTINGS_DEFAULT_BROWSER_ERROR 425
#define IDS_SETTINGS_DEFAULT_BROWSER_SECONDARY 426
#define IDS_SETTINGS_SPELLING_PREF_DESC 428
#define IDS_SETTINGS_RESTART_TO_APPLY_CHANGES 429
#define IDS_SETTINGS_SIGNIN_ALLOWED 430
#define IDS_SETTINGS_SIGNIN_ALLOWED_DESC 431
#define IDS_SETTINGS_SITE_SETTINGS_PDFS_BLOCKED 432
#define IDS_SETTINGS_PRIVACY_GUIDE_PROMO_BODY 433
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_FEATURE_DESCRIPTION1 434
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_FEATURE_DESCRIPTION2 435
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_FEATURE_DESCRIPTION3 436
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_STANDARD_PROTECTION_FEATURE_DESCRIPTION2 437
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_STANDARD_PROTECTION_PRIVACY_DESCRIPTION1 438
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_PRIVACY_SANDBOX_SUB_LABEL 439
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_WAA_SUB_LABEL 440
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_PRIVACY_DESCRIPTION2 441
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_TITLE 442
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_DATA_TYPES 443
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_DATA_USAGE 444
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TOPICS_DATA_MANAGEMENT 445
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_DATA_TYPES 446
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_DATA_USAGE 447
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_DESCRIPTION 448
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_DESCRIPTION_TRIALS_OFF 449
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_DESCRIPTION_LISTS_EMPTY 450
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_TITLE 451
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_LEARN_MORE_1 452
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_LEARN_MORE_2 453
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_LEARN_MORE_3 454
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_LEARN_MORE_1 455
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_DESCRIPTION 456
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_DESCRIPTION_TRIALS_OFF 457
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_BEFORE 458
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_FAILED_OFFLINE 459
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_FAILED 460
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_UNKNOWN 461
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_SIGNED_OUT 462
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_DISABLED 463
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_TWO 464
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_BULLET_TWO 465
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE_WARNING_WITH_COUNTS_SINGULAR 466
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE_WARNING_WITH_COUNTS_PLURAL 467
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE_WARNING_WITHOUT_COUNTS 468
#define IDS_SETTINGS_CUSTOMIZE_YOUR_CHROME_PROFILE 469
#define IDS_SETTING_NAME_YOUR_PROFILE 470
#define IDS_SETTINGS_PEOPLE_SIGN_IN_PROMPT_SECONDARY_WITH_ACCOUNT 471
#define IDS_SETTINGS_SYNC_DATA_ENCRYPTED_TEXT 473
#define IDS_SETTINGS_SYNC_DISCONNECT_TITLE 474
#define IDS_DRIVE_SUGGEST_PREF_DESC 475
#define IDS_SETTINGS_SYNC_SIGN_IN_PROMPT_WITH_NO_ACCOUNT 476
#define IDS_SETTINGS_LANGUAGES_IS_DISPLAYED_IN_THIS_LANGUAGE 477
#define IDS_SETTINGS_LANGUAGES_DISPLAY_IN_THIS_LANGUAGE 478
#define IDS_SETTINGS_SYSTEM_BACKGROUND_APPS_LABEL 479
#define IDS_SETTINGS_RESET_PROFILE_FEEDBACK 480
#define IDS_PRODUCT_NAME 101
#define IDS_SHORT_PRODUCT_NAME 481
#define IDS_SXS_SHORTCUT_NAME 482
#define IDS_SHORTCUT_NAME_BETA 483
#define IDS_SHORTCUT_NAME_DEV 484
#define IDS_PRODUCT_DESCRIPTION 485
#define IDS_WELCOME_TO_CHROME 486
#define IDS_PRODUCT_LOGO_ENTERPRISE_ALT_TEXT 491
#define IDS_SHORTCUT_NEW_WINDOW 492
#define IDS_TASK_MANAGER_TITLE 493
#define IDS_SESSION_CRASHED_VIEW_UMA_OPTIN 494
#define IDS_BROWSER_WINDOW_TITLE_FORMAT 285
#define IDS_CAPTIVE_PORTAL_BROWSER_WINDOW_TITLE_FORMAT 495
#define IDS_ACCESSIBLE_BROWSER_WINDOW_TITLE_FORMAT 295
#define IDS_ACCESSIBLE_BETA_BROWSER_WINDOW_TITLE_FORMAT 496
#define IDS_ACCESSIBLE_DEV_BROWSER_WINDOW_TITLE_FORMAT 497
#define IDS_ACCESSIBLE_CANARY_BROWSER_WINDOW_TITLE_FORMAT 498
#define IDS_ABOUT_VERSION_COMPANY_NAME 499
#define IDS_ABOUT_VERSION_COPYRIGHT 500
#define IDS_ABOUT_TERMS_OF_SERVICE 504
#define IDS_WIN_XP_VISTA_OBSOLETE 507
#define IDS_ACCNAME_APP 273
#define IDS_BROWSER_HUNGBROWSER_MESSAGE 509
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_HELP_TEXT_NO_DEVICES 510
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_HELP_TEXT_NO_DEVICES_ORIGIN 511
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_DEVICE_NOT_FOUND 512
#define IDS_UNINSTALL_CLOSE_APP 513
#define IDS_UNINSTALL_VERIFY 514
#define IDS_UNINSTALL_CHROME 515
#define IDS_FR_CUSTOMIZE_DEFAULT_BROWSER 516
#define IDS_STATUS_TRAY_KEEP_CHROME_RUNNING_IN_BACKGROUND 517
#define IDS_CANT_WRITE_USER_DIRECTORY_SUMMARY 518
#define IDS_PROFILE_TOO_NEW_ERROR 520
#define IDS_PREFERENCES_UNREADABLE_ERROR 521
#define IDS_PREFERENCES_CORRUPT_ERROR 522
#define IDS_CRASH_RECOVERY_TITLE 166
#define IDS_CRASH_RECOVERY_CONTENT 167
#define IDS_PASSWORD_GENERATION_PROMPT 525
#define IDS_PASSWORD_MANAGER_ONBOARDING_DETAILS_C 526
#define IDS_PASSWORD_MANAGER_TITLE_BRAND 527
#define IDS_PASSWORD_BUBBLES_PASSWORD_MANAGER_LINK_TEXT_SAVING_ON_DEVICE 528
#define IDS_PASSWORD_MANAGER_SAVE_PASSWORD_SIGNED_OUT_MESSAGE_DESCRIPTION 529
#define IDS_PASSWORD_MANAGER_UPDATE_PASSWORD_SIGNED_OUT_MESSAGE_DESCRIPTION 530
#define IDS_PASSWORDS_PAGE_AUTHENTICATION_PROMPT 531
#define IDS_PASSWORDS_PAGE_COPY_AUTHENTICATION_PROMPT 532
#define IDS_PASSWORDS_PAGE_EDIT_AUTHENTICATION_PROMPT 533
#define IDS_PASSWORDS_PAGE_EXPORT_AUTHENTICATION_PROMPT 534
#define IDS_INSTALL_HIGHER_VERSION 535
#define IDS_INSTALL_FAILED 536
#define IDS_SAME_VERSION_REPAIR_FAILED 537
#define IDS_SETUP_PATCH_FAILED 538
#define IDS_INSTALL_OS_NOT_SUPPORTED 539
#define IDS_INSTALL_OS_ERROR 540
#define IDS_INSTALL_SINGLETON_ACQUISITION_FAILED 541
#define IDS_INSTALL_TEMP_DIR_FAILED 542
#define IDS_INSTALL_UNCOMPRESSION_FAILED 543
#define IDS_INSTALL_INVALID_ARCHIVE 544
#define IDS_INSTALL_INSUFFICIENT_RIGHTS 545
#define IDS_INSTALL_EXISTING_VERSION_LAUNCHED 546
#define IDS_SHORTCUT_TOOLTIP 547
#define IDS_UNINSTALL_DELETE_PROFILE 548
#define IDS_UNINSTALL_SET_DEFAULT_BROWSER 549
#define IDS_UNINSTALL_BUTTON_TEXT 550
#define IDS_DEFAULT_BROWSER_INFOBAR_TEXT 301
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_NOTIFICATION_DESCRIPTION 551
#define IDS_TRY_TOAST_HEADING 552
#define IDS_TRY_TOAST_HEADING2 553
#define IDS_TRY_TOAST_HEADING3 554
#define IDS_TRY_TOAST_HEADING4 555
#define IDS_TRY_TOAST_HEADING_SKYPE 556
#define IDS_DOWNLOAD_BUBBLE_DANGEROUS_FILE 557
#define IDS_DOWNLOAD_BUBBLE_MALICIOUS_URL_BLOCKED 558
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_UNKNOWN_SOURCE 559
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_ENCRYPTED 560
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_MALWARE 561
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_TOO_BIG 562
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_DEEP_SCANNING_PROMPT 563
#define IDS_DOWNLOAD_STATUS_CRX_INSTALL_RUNNING 564
#define IDS_PROMPT_DOWNLOAD_CHANGES_SETTINGS 565
#define IDS_PROMPT_MALICIOUS_DOWNLOAD_URL 566
#define IDS_PROMPT_MALICIOUS_DOWNLOAD_CONTENT 567
#define IDS_BLOCK_REASON_DANGEROUS_DOWNLOAD 568
#define IDS_BLOCK_REASON_UNWANTED_DOWNLOAD 569
#define IDS_ABANDON_DOWNLOAD_DIALOG_BROWSER_MESSAGE 570
#define IDS_MISSING_GOOGLE_API_KEYS 572
#define IDS_EXTENSION_INSTALLED_HEADING 573
#define IDS_EXTENSION_UNINSTALL_PROMPT_REMOVE_DATA_CHECKBOX 574
#define IDS_EXTENSION_ALERT_ITEM_BLOCKLISTED_MALWARE 575
#define IDS_EXTENSIONS_ALERT_ITEM_BLOCKLISTED_MALWARE_TITLE 576
#define IDS_EXTENSIONS_INCOGNITO_WARNING 577
#define IDS_EXTENSIONS_UNINSTALL 578
#define IDS_EXTENSIONS_SHORTCUT_SCOPE_IN_CHROME 579
#define IDS_EXTENSIONS_MULTIPLE_UNSUPPORTED_DISABLED_BODY 580
#define IDS_EXTENSIONS_SINGLE_UNSUPPORTED_DISABLED_BODY 581
#define IDS_APPMENU_TOOLTIP 274
#define IDS_APPMENU_TOOLTIP_UPDATE_AVAILABLE 582
#define IDS_APPMENU_TOOLTIP_ALERT 583
#define IDS_OPEN_IN_CHROME 584
#define IDS_ABOUT 349
#define IDS_RELAUNCH_TO_UPDATE 585
#define IDS_CHROME_SIGNIN_TITLE 589
#define IDS_PROFILES_DICE_SYNC_PROMO 590
#define IDS_ONE_CLICK_SIGNIN_DIALOG_TITLE_NEW 591
#define IDS_ONE_CLICK_SIGNIN_DIALOG_MESSAGE_NEW 592
#define IDS_SYNC_WRONG_EMAIL 593
#define IDS_SYNC_USED_PROFILE_ERROR 594
#define IDS_ENTERPRISE_SIGNIN_TITLE 595
#define IDS_ENTERPRISE_SIGNIN_EXPLANATION_WITHOUT_PROFILE_CREATION 596
#define IDS_ENTERPRISE_SIGNIN_EXPLANATION_WITH_PROFILE_CREATION 597
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_TITLE 598
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_CREATION 599
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_ISOLATION_NOTICE 600
#define IDS_ENTERPRISE_SIGNIN_WORK_PROFILE_EXPLANATION 601
#define IDS_ABOUT_BROWSER_SWITCH_DESCRIPTION_UNKNOWN_BROWSER 602
#define IDS_ABOUT_BROWSER_SWITCH_DESCRIPTION_KNOWN_BROWSER 603
#define IDS_NTP_CUSTOMIZE_BUTTON_LABEL 604
#define IDS_SIGNIN_EMAIL_CONFIRMATION_TITLE 605
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_CREATE_BUBBLE_TITLE 606
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_SWITCH_BUBBLE_TITLE 607
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_SWITCH_BUBBLE_DESC 608
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_CONSUMER_BUBBLE_DESC 609
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_CONSUMER_BUBBLE_DESC_MANAGED_DEVICE 610
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_ENTERPRISE_BUBBLE_DESC_MANAGED_DEVICE 611
#define IDS_PROFILE_CUSTOMIZATION_TEXT 612
#define IDS_CHROME_CLEANUP_PROMPT_EXPLANATION 613
#define IDS_APP_SHORTCUTS_SUBDIR_NAME 619
#define IDS_APP_SHORTCUTS_SUBDIR_NAME_CANARY 620
#define IDS_APP_SHORTCUTS_SUBDIR_NAME_BETA 621
#define IDS_APP_SHORTCUTS_SUBDIR_NAME_DEV 622
#define IDS_MEDIA_STREAM_STATUS_TRAY_TEXT_AUDIO_AND_VIDEO 624
#define IDS_MEDIA_STREAM_STATUS_TRAY_TEXT_AUDIO_ONLY 625
#define IDS_MEDIA_STREAM_STATUS_TRAY_TEXT_VIDEO_ONLY 626
#define IDS_LOGIN_POD_USER_REMOVE_WARNING_SYNC 629
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_INTRO_TITLE 630
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_INTRO_TEXT 631
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_YOUR_CHROME_TITLE 632
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_YOUR_CHROME_TEXT 633
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_GUEST_TEXT 634
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_FRIENDS_TEXT 635
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_OUTRO_TEXT 636
#define IDS_USER_MANAGER_TUTORIAL_SLIDE_OUTRO_ADD_USER 637
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_START_PAGES_SPECIFIC 638
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_START_PAGES 639
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_START_PAGES 640
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_START_AND_HOME 641
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_START_AND_SEARCH 642
#define IDS_WEBSTORE_APP_DESCRIPTION 153
#define IDS_INBOUND_MDNS_RULE_NAME 643
#define IDS_INBOUND_MDNS_RULE_NAME_BETA 644
#define IDS_INBOUND_MDNS_RULE_NAME_CANARY 645
#define IDS_INBOUND_MDNS_RULE_NAME_DEV 646
#define IDS_INBOUND_MDNS_RULE_DESCRIPTION 647
#define IDS_INBOUND_MDNS_RULE_DESCRIPTION_BETA 648
#define IDS_INBOUND_MDNS_RULE_DESCRIPTION_CANARY 649
#define IDS_INBOUND_MDNS_RULE_DESCRIPTION_DEV 650
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_TEXT 651
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_TEXT_ONCE 652
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_TEXT 653
#define IDS_CONTENT_CONTEXT_OPENLINKNEWTAB_INAPP 654
#define IDS_CONTENT_CONTEXT_OPENLINKOFFTHERECORD_INAPP 655
#define IDS_UPDATE_RECOMMENDED_DIALOG_TITLE 656
#define IDS_UPDATE_RECOMMENDED 657
#define IDS_RELAUNCH_AND_UPDATE 658
#define IDS_REINSTALL_APP 659
#define IDS_UPGRADE_BUBBLE_MENU_ITEM 660
#define IDS_UPGRADE_BUBBLE_TITLE 661
#define IDS_UPGRADE_BUBBLE_TEXT 662
#define IDS_SYNC_ERROR_USER_MENU_UPGRADE_BUTTON 663
#define IDS_SYNC_UPGRADE_CLIENT 664
#define IDS_SYNC_UPGRADE_CLIENT_BUTTON 665
#define IDS_RECOVERY_BUBBLE_TITLE 666
#define IDS_RUN_RECOVERY 667
#define IDS_RECOVERY_BUBBLE_TEXT 668
#define IDS_CRITICAL_NOTIFICATION_TITLE 669
#define IDS_CRITICAL_NOTIFICATION_TITLE_ALTERNATE 670
#define IDS_CRITICAL_NOTIFICATION_TEXT 671
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_TAB 672
#define IDS_WELCOME_HEADER 688
#define IDS_WIN_NOTIFICATION_SETTINGS_CONTEXT_MENU_ITEM_NAME 689
#define IDS_RELAUNCH_RECOMMENDED_TITLE 690
#define IDS_RELAUNCH_RECOMMENDED_BODY 691
#define IDS_RELAUNCH_REQUIRED_TITLE_DAYS 692
#define IDS_RELAUNCH_REQUIRED_TITLE_HOURS 693
#define IDS_RELAUNCH_REQUIRED_TITLE_MINUTES 694
#define IDS_RELAUNCH_REQUIRED_TITLE_SECONDS 695
#define IDS_RELAUNCH_REQUIRED_BODY 696
#define IDS_ENTERPRISE_STARTUP_CLOUD_POLICY_ENROLLMENT_TOOLTIP 697
#define IDS_ENTERPRISE_STARTUP_CLOUD_POLICY_ENROLLMENT_ERROR 698
#define IDS_ENTERPRISE_STARTUP_RELAUNCH_BUTTON 699
#define IDS_DESKTOP_MEDIA_PICKER_TITLE_WEB_CONTENTS_ONLY 700
#define IDS_HATS_BUBBLE_TITLE 701
#define IDS_PROFILE_PICKER_MAIN_VIEW_TITLE 707
#define IDS_PROFILE_PICKER_MAIN_VIEW_TITLE_V2 708
#define IDS_PROFILE_PICKER_MAIN_VIEW_SUBTITLE 709
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_PROFILE_TYPE_CHOICE_SUBTITLE 710
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_PROFILE_TYPE_CHOICE_TITLE 711
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_TITLE 712
#define IDS_PROFILE_PICKER_IPH_FOR_PROFILES_TEXT 713
#define IDS_PROFILE_PICKER_IPH_FOR_ADD_PROFILE_TEXT 714
#define IDS_PROFILE_PICKER_PROFILE_SWITCH_TITLE 715
#define IDS_PROFILE_PICKER_PROFILE_SWITCH_SUBTITLE 716
#define IDS_PROFILE_SWITCH_PROMO 717
#define IDS_PROFILE_SWITCH_PROMO_SCREENREADER 718
#define IDS_CHROMELABS_RELAUNCH_FOOTER_MESSAGE 720
#define IDS_BLUETOOTH_DEVICE_CHOOSER_AUTHORIZE_BLUETOOTH 721
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_NOTIFICATION_TITLE 722
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_MESSAGE_TITLE 723
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_MESSAGE_DESCRIPTION 724
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_MESSAGE_ACCEPT 725
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_SUBTITLE 726
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_DESCRIPTION_2 727
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_LABEL 728
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_HEADER 729
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_BULLET_POINT_1 730
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_BULLET_POINT_2 731
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_1_BULLET_POINT_3 732
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_BULLET_POINT_1 733
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_BULLET_POINT_2 734
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BOTTOM_SUMMARY 735
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_SUBTITLE 736
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_DESCRIPTION_2 737
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BOTTOM_SUMMARY 738
#define IDS_FRIENDLY_COMPANY_NAME 739
#define IDS_NO_UPDATE_RESPONSE 740
#define IDS_INSTALL_UPDATER_FAILED 741
#define IDS_INSTALLER_DISPLAY_NAME 742
#define IDS_CLOSE_BUTTON 743
#define IDS_MINIMIZE_BUTTON 744
#define IDS_INITIALIZING 745
#define IDS_WAITING_TO_CONNECT 746
#define IDS_DOWNLOADING_SHORT 747
#define IDS_DOWNLOADING_LONG 748
#define IDS_DOWNLOADING_VERY_LONG 749
#define IDS_DOWNLOADING_COMPLETED 750
#define IDS_DOWNLOADING 751
#define IDS_WAITING_TO_INSTALL 752
#define IDS_INSTALLING 753
#define IDS_CANCELING 754
#define IDS_TEXT_RESTART_BROWSER 755
#define IDS_TEXT_RESTART_ALL_BROWSERS 756
#define IDS_TEXT_RESTART_COMPUTER 757
#define IDS_UPDATER_CLOSE 758
#define IDS_RESTART_NOW 759
#define IDS_RESTART_LATER 760
#define IDS_GET_HELP_TEXT 761
#define IDS_INSTALLATION_STOPPED_WINDOW_TITLE 762
#define IDS_INSTALL_STOPPED 763
#define IDS_RESUME_INSTALLATION 764
#define IDS_CANCEL_INSTALLATION 765
#define IDS_SPLASH_SCREEN_MESSAGE 766
#define IDS_BUNDLE_INSTALLED_SUCCESSFULLY 767

// ---------------------------------------------------------------------------
// From components_chromium_strings.h:

#define IDS_ERRORPAGES_SUGGESTION_NETWORK_PREDICTION_BODY 24030
#define IDS_ERRORPAGES_SUGGESTION_FIREWALL_CONFIG_HEADER 24031
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_CLIENT 24032
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_ENROLLMENT_CHECK_PENDING 24033
#define IDS_ERRORPAGES_SUGGESTION_PROXY_DISABLE_PLATFORM 24034
#define IDS_FLAGS_UI_RELAUNCH_NOTICE 24035
#define IDS_DEPRECATED_FEATURES_RELAUNCH_NOTICE 24036
#define IDS_CRASH_DISABLED_MESSAGE 24037
#define IDS_SHORT_PRODUCT_LOGO_ALT_TEXT 24038
#define IDS_VERSION_UI_LICENSE 24039
#define IDS_PAGE_INFO_INTERNAL_PAGE 24040
#define IDS_SESSION_CRASHED_VIEW_MESSAGE 24041

// ---------------------------------------------------------------------------
// From components_strings.h:

#define IDS_ASH_ARC_APP_COMPAT_DISABLED_COMPAT_MODE_BUTTON_TOOLTIP_PHONE 24070
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_TITLE 24071
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_BODY 24072
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_ACCEPT 24073
#define IDS_ASH_ARC_APP_COMPAT_RESIZE_CONFIRM_DONT_ASK_ME 24074
#define IDS_ASH_ARC_NEARBY_SHARE_FILE_PREPARATION_PROGRESS 24075
#define IDS_ASH_ARC_NEARBY_SHARE_FILES_PREPARATION_PROGRESS 24076
#define IDS_ASH_ARC_NEARBY_SHARE_ERROR_DIALOG_MESSAGE 24077
#define IDS_ASH_ARC_NEARBY_SHARE_LOW_DISK_SPACE_DIALOG_TITLE 24078
#define IDS_ASH_ARC_NEARBY_SHARE_LOW_DISK_SPACE_DIALOG_MESSAGE 24079
#define IDS_ASH_ARC_NEARBY_SHARE_LOW_DISK_SPACE_DIALOG_STORAGE_BUTTON 24080
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_PHONE 24081
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_TABLET 24082
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_RESIZABLE 24083
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_RESIZE_SETTINGS 24084
#define IDS_ARC_COMPAT_MODE_RESIZE_TOGGLE_MENU_TITLE 24085
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_TITLE 24086
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_BODY 24087
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_BODY_UNRESIZABLE 24088
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_CLOSE 24089
#define IDS_ARC_COMPAT_MODE_SPLASH_SCREEN_LINK 24090
#define IDS_ARC_COMPAT_MODE_DISABLE_RESIZE_LOCK_TOAST 24091
#define IDS_ARC_GHOST_WINDOW_APP_LAUNCHING_ICON 24092
#define IDS_ARC_GHOST_WINDOW_APP_LAUNCHING_THROBBER 24093
#define IDS_AUTOFILL_ASSISTANT_PAYMENT_INFO_CONFIRM 24094
#define IDS_AUTOFILL_ASSISTANT_PAYMENT_INFORMATION_MISSING 24095
#define IDS_AUTOFILL_ASSISTANT_DEFAULT_ERROR 24096
#define IDS_AUTOFILL_ASSISTANT_LOADING 24097
#define IDS_AUTOFILL_ASSISTANT_GIVE_UP 24098
#define IDS_AUTOFILL_ASSISTANT_MAYBE_GIVE_UP 24099
#define IDS_AUTOFILL_ASSISTANT_DETAILS_DIFFER 24100
#define IDS_AUTOFILL_ASSISTANT_CONTINUE_BUTTON 24101
#define IDS_AUTOFILL_ASSISTANT_STOPPED 24102
#define IDS_AUTOFILL_ASSISTANT_SEND_FEEDBACK 24103
#define IDS_AUTOFILL_NO_THANKS_DESKTOP_LOCAL_SAVE 24104
#define IDS_AUTOFILL_NO_THANKS_DESKTOP_UPLOAD_SAVE 24105
#define IDS_AUTOFILL_FIELD_LABEL_PHONE 24112
#define IDS_AUTOFILL_FIELD_LABEL_BILLING_ADDRESS 24113
#define IDS_AUTOFILL_SAVE_CARD_BUBBLE_LOCAL_SAVE_ACCEPT 24115
#define IDS_AUTOFILL_SAVE_CARD_BUBBLE_UPLOAD_SAVE_ACCEPT 24116
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_CONTINUE 24123
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_LOCAL 24124
#define IDS_AUTOFILL_FIX_FLOW_PROMPT_SAVE_CARD_LABEL 24125
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_TO_CLOUD 24126
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_TO_CLOUD_V3 24127
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_TITLE_TO_CLOUD_V4 24128
#define IDS_AUTOFILL_CARD_SAVED 24129
#define IDS_AUTOFILL_MANAGE_CARDS 24130
#define IDS_AUTOFILL_DONE 24131
#define IDS_AUTOFILL_FAILURE_BUBBLE_TITLE 24132
#define IDS_AUTOFILL_FAILURE_BUBBLE_EXPLANATION 24133
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION 24134
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_V3 24135
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_V3_WITH_NAME 24136
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_CARDHOLDER_NAME 24137
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_CARDHOLDER_NAME_TOOLTIP 24138
#define IDS_AUTOFILL_SAVE_CARD_CARDHOLDER_NAME_FIX_FLOW_HEADER 24139
#define IDS_AUTOFILL_SAVE_CARD_UPDATE_EXPIRATION_DATE_TITLE 24140
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_TOOLTIP 24143
#define IDS_AUTOFILL_SAVE_CARD_PROMPT_UPLOAD_EXPLANATION_AND_CARDHOLDER_NAME_TOOLTIP 24144
#define IDS_AUTOFILL_GOOGLE_PAY_LOGO_ACCESSIBLE_NAME 24145
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_ANIMATION_LABEL 24146
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_BUBBLE_TITLE 24147
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_BUBBLE_BUTTON_LABEL 24148
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_BUBBLE_BODY_TEXT 24149
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TITLE_OFFER 24150
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TITLE_DONE 24151
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TITLE_FIX 24152
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_CHECKBOX_UNCHECK_WARNING 24153
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_OFFER 24154
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_DONE 24155
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_ERROR 24156
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_FIX 24157
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_MESSAGE_INVALID_CARD_REMOVED 24158
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_SAVE 24159
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_CANCEL 24160
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_DONE 24161
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_BUTTON_LABEL_VIEW_CARDS 24162
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_LABEL_INVALID_CARDS 24163
#define IDS_AUTOFILL_LOCAL_CARD_MIGRATION_DIALOG_TRASH_CAN_BUTTON_TOOLTIP 24164
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_TRY_AGAIN_CVC 24165
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_TRY_AGAIN_CVC_AND_EXPIRATION_V2 24166
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_PERMANENT 24171
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_ERROR_NETWORK 24172
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_TITLE 24173
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_TITLE_V2 24174
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_EXPIRED_TITLE 24175
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS 24176
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS_LOCAL_CARD 24177
#define IDS_AUTOFILL_CARD_UNMASK_PROMPT_INSTRUCTIONS_V2 24178
#define IDS_AUTOFILL_CARD_UNMASK_CVC_IMAGE_DESCRIPTION 24179
#define IDS_AUTOFILL_CARD_UNMASK_CONFIRM_BUTTON 24184
#define IDS_AUTOFILL_CARD_UNMASK_VERIFY_BUTTON 24185
#define IDS_AUTOFILL_CARD_UNMASK_EXPIRATION_MONTH 24186
#define IDS_AUTOFILL_CARD_UNMASK_EXPIRATION_YEAR 24187
#define IDS_AUTOFILL_CARD_UNMASK_VERIFICATION_IN_PROGRESS 24188
#define IDS_AUTOFILL_CARD_UNMASK_VERIFICATION_SUCCESS 24189
#define IDS_AUTOFILL_CARD_UNMASK_INVALID_EXPIRATION_DATE 24190
#define IDS_AUTOFILL_EXPIRATION_DATE_SEPARATOR 24191
#define IDS_AUTOFILL_CARD_UNMASK_NEW_CARD_LINK 24192
#define IDS_AUTOFILL_DIALOG_PLACEHOLDER_CVC 24193
#define IDS_AUTOFILL_CARD_UNMASK_PROGRESS_BAR_MESSAGE 24194
#define IDS_AUTOFILL_CARD_UNMASK_CANCEL_BUTTON_LABEL 24195
#define IDS_AUTOFILL_CARD_UNMASK_CONFIRMATION_MESSAGE 24196
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_TITLE 24197
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_TITLE_ERROR 24198
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_INSTRUCTION 24199
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_OK_BUTTON_LABEL 24200
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_INSTRUCTION_ERROR 24201
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_CANCEL_BUTTON_LABEL 24202
#define IDS_AUTOFILL_WEBAUTHN_OPT_IN_DIALOG_CANCEL_BUTTON_LABEL_ERROR 24203
#define IDS_AUTOFILL_WEBAUTHN_VERIFY_PENDING_DIALOG_TITLE 24204
#define IDS_AUTOFILL_WEBAUTHN_VERIFY_PENDING_DIALOG_CANCEL_BUTTON_LABEL 24205
#define IDS_AUTOFILL_WALLET_MANAGEMENT_LINK_TEXT 24206
#define IDS_AUTOFILL_FROM_GOOGLE_ACCOUNT_LONG 24207
#define IDS_AUTOFILL_CLOUD_TOKEN_DROPDOWN_OPTION_LABEL 24208
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_FALLBACK_ICON_TOOLTIP 24209
#define IDS_AUTOFILL_VIRTUAL_CARD_ENTRY_PREFIX 24210
#define IDS_AUTOFILL_VIRTUAL_CARD_ENTRY_PREFIX_TWO 24211
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_CONTENT_TITLE 24212
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_CONTENT_EXPLANATION 24213
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_OK_BUTTON_LABEL 24214
#define IDS_AUTOFILL_VIRTUAL_CARD_SELECTION_DIALOG_CANCEL_BUTTON_LABEL 24215
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_TITLE 24216
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_EDUCATIONAL_BODY_LABEL 24217
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_LEARN_MORE_LINK_LABEL 24218
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_CARD_NUMBER_LABEL 24219
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_EXP_DATE_LABEL 24220
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_CARDHOLDER_NAME_LABEL 24221
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_CVC_LABEL 24222
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_ICON_TOOLTIP 24223
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_BUTTON_TOOLTIP_NORMAL 24224
#define IDS_AUTOFILL_VIRTUAL_CARD_MANUAL_FALLBACK_BUBBLE_BUTTON_TOOLTIP_CLICKED 24225
#define IDS_AUTOFILL_VIRTUAL_CARD_SUGGESTION_IPH_BUBBLE_LABEL 24226
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_ACCEPT_BUTTON_LABEL 24227
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DIALOG_TITLE_LABEL 24228
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DIALOG_CONTENT_LABEL 24229
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DECLINE_BUTTON_LABEL_SKIP 24230
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_DECLINE_BUTTON_LABEL_NO_THANKS 24231
#define IDS_AUTOFILL_VIRTUAL_CARD_ENROLLMENT_LEARN_MORE_LINK_LABEL 24232
#define IDS_AUTOFILL_VIRTUAL_CARD_SUGGESTION_OPTION_VALUE 24233
#define IDS_AUTOFILL_VIRTUAL_CARD_TEMPORARY_ERROR_TITLE 24234
#define IDS_AUTOFILL_VIRTUAL_CARD_PERMANENT_ERROR_TITLE 24235
#define IDS_AUTOFILL_VIRTUAL_CARD_TEMPORARY_ERROR_DESCRIPTION 24236
#define IDS_AUTOFILL_VIRTUAL_CARD_PERMANENT_ERROR_DESCRIPTION 24237
#define IDS_AUTOFILL_VIRTUAL_CARD_NOT_ELIGIBLE_ERROR_TITLE 24238
#define IDS_AUTOFILL_VIRTUAL_CARD_NOT_ELIGIBLE_ERROR_DESCRIPTION 24239
#define IDS_AUTOFILL_ERROR_DIALOG_NEGATIVE_BUTTON_LABEL 24240
#define IDS_AUTOFILL_VIRTUAL_CARD_NUMBER_SWITCH_LABEL 24241
#define IDS_AUTOFILL_CARD_UNMASK_AUTHENTICATION_SELECTION_DIALOG_ISSUER_CONFIRMATION_TEXT 24244
#define IDS_AUTOFILL_AUTHENTICATION_MODE_TEXT_MESSAGE_LABEL 24245
#define IDS_AUTOFILL_CARD_UNMASK_AUTHENTICATION_SELECTION_DIALOG_CURRENT_INFO_NOT_SEEN_TEXT 24246
#define IDS_AUTOFILL_CARD_UNMASK_AUTHENTICATION_SELECTION_DIALOG_OK_BUTTON_LABEL 24247
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_TITLE 24248
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_FOOTER_MESSAGE 24249
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_NEW_CODE_MESSAGE 24250
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_TEXTFIELD_PLACEHOLDER_MESSAGE 24251
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_PENDING_MESSAGE 24252
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_VERIFICATION_CODE_EXPIRED_LABEL 24253
#define IDS_AUTOFILL_CARD_UNMASK_OTP_INPUT_DIALOG_ENTER_CORRECT_CODE_LABEL 24254
#define IDS_AUTOFILL_SAVE_UPI_PROMPT_TITLE 24255
#define IDS_AUTOFILL_SAVE_UPI_PROMPT_ACCEPT 24256
#define IDS_AUTOFILL_SAVE_UPI_PROMPT_REJECT 24257
#define IDS_AUTOFILL_OFFERS_CASHBACK 24258
#define IDS_AUTOFILL_CARD_LINKED_OFFER_REMINDER_TITLE 24259
#define IDS_AUTOFILL_OFFERS_REMINDER_POSITIVE_BUTTON_LABEL 24260
#define IDS_AUTOFILL_OFFERS_REMINDER_DESCRIPTION_TEXT 24261
#define IDS_AUTOFILL_OFFERS_REMINDER_ICON_TOOLTIP_TEXT 24262
#define IDS_AUTOFILL_PROMO_CODE_OFFERS_REMINDER_TITLE 24264
#define IDS_AUTOFILL_PROMO_CODE_OFFER_BUTTON_TOOLTIP_NORMAL 24265
#define IDS_AUTOFILL_PROMO_CODE_OFFER_BUTTON_TOOLTIP_CLICKED 24266
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_TITLE 24267
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_CANT_FIND_CODE_MESSAGE 24268
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_OTP_INPUT_HINT 24269
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_POSITIVE_BUTTON_LABEL 24270
#define IDS_AUTOFILL_PAYMENTS_OTP_VERIFICATION_DIALOG_NEGATIVE_BUTTON_LABEL 24271
#define IDS_AUTOFILL_CLEAR_FORM_MENU_ITEM 24274
#define IDS_AUTOFILL_WARNING_INSECURE_CONNECTION 24276
#define IDS_AUTOFILL_WARNING_MIXED_FORM 24277
#define IDS_AUTOFILL_CREDIT_CARD_SIGNIN_PROMO 24278
#define IDS_AUTOFILL_DELETE_AUTOCOMPLETE_SUGGESTION_CONFIRMATION_BODY 24279
#define IDS_AUTOFILL_DELETE_CREDIT_CARD_SUGGESTION_CONFIRMATION_BODY 24280
#define IDS_AUTOFILL_DELETE_PROFILE_SUGGESTION_CONFIRMATION_BODY 24281
#define IDS_AUTOFILL_CC_AMEX 24282
#define IDS_AUTOFILL_CC_AMEX_SHORT 24283
#define IDS_AUTOFILL_CC_DINERS 24284
#define IDS_AUTOFILL_CC_DISCOVER 24285
#define IDS_AUTOFILL_CC_ELO 24286
#define IDS_AUTOFILL_CC_GOOGLE_PAY 24287
#define IDS_AUTOFILL_CC_JCB 24288
#define IDS_AUTOFILL_CC_MASTERCARD 24289
#define IDS_AUTOFILL_CC_MIR 24290
#define IDS_AUTOFILL_CC_TROY 24291
#define IDS_AUTOFILL_CC_UNION_PAY 24292
#define IDS_AUTOFILL_CC_VISA 170
#define IDS_AUTOFILL_CC_GENERIC 24293
#define IDS_AUTOFILL_ADDRESS_SUMMARY_SEPARATOR 169
#define IDS_AUTOFILL_FIELD_LABEL_STATE 24294
#define IDS_AUTOFILL_FIELD_LABEL_AREA 24295
#define IDS_AUTOFILL_FIELD_LABEL_COUNTY 24296
#define IDS_AUTOFILL_FIELD_LABEL_DEPARTMENT 24297
#define IDS_AUTOFILL_FIELD_LABEL_DISTRICT 24298
#define IDS_AUTOFILL_FIELD_LABEL_EMIRATE 24299
#define IDS_AUTOFILL_FIELD_LABEL_ISLAND 24300
#define IDS_AUTOFILL_FIELD_LABEL_PARISH 24301
#define IDS_AUTOFILL_FIELD_LABEL_PREFECTURE 24302
#define IDS_AUTOFILL_FIELD_LABEL_PROVINCE 24303
#define IDS_AUTOFILL_FIELD_LABEL_ZIP_CODE 24304
#define IDS_AUTOFILL_FIELD_LABEL_POSTAL_CODE 24305
#define IDS_AUTOFILL_HIDE_SUGGESTIONS 24306
#define IDS_AUTOFILL_MANAGE 24307
#define IDS_AUTOFILL_MANAGE_ADDRESSES 24308
#define IDS_AUTOFILL_MANAGE_PAYMENT_METHODS 24309
#define IDS_AUTOFILL_MANAGE_PASSWORDS 24310
#define IDS_AUTOFILL_SCAN_CREDIT_CARD 24311
#define IDS_AUTOFILL_SHOW_ALL_SAVED_FALLBACK 24312
#define IDS_AUTOFILL_SHOW_ACCOUNT_CARDS 24313
#define IDS_AUTOFILL_POPUP_ACCESSIBLE_NODE_DATA 24314
#define IDS_AUTOFILL_SUGGESTION_LABEL_SEPARATOR 24315
#define IDS_AUTOFILL_CREDIT_CARD_EXPIRATION_DATE_ABBR 24316
#define IDS_AUTOFILL_CREDIT_CARD_EXPIRATION_DATE_ABBR_V2 24317
#define IDS_AUTOFILL_CREDIT_CARD_TWO_LINE_LABEL_FROM_NAME 24318
#define IDS_AUTOFILL_CREDIT_CARD_TWO_LINE_LABEL_FROM_CARD_NUMBER 24319
#define IDS_AUTOFILL_LOADING_REGIONS 24320
#define IDS_AUTOFILL_SELECT 24321
#define IDS_AUTOFILL_NO_SAVED_ADDRESS 24322
#define IDS_AUTOFILL_ADDRESSES 24323
#define IDS_AUTOFILL_ENABLE_PROFILES_TOGGLE_SUBLABEL 24324
#define IDS_AUTOFILL_ENABLE_CREDIT_CARDS_TOGGLE_SUBLABEL 24325
#define IDS_AUTOFILL_ADDRESSES_SETTINGS_TITLE 24326
#define IDS_AUTOFILL_PAYMENT_METHODS 24327
#define IDS_AUTOFILL_ENABLE_PROFILES_TOGGLE_LABEL 24328
#define IDS_AUTOFILL_ENABLE_CREDIT_CARDS_TOGGLE_LABEL 24329
#define IDS_ENABLE_CREDIT_CARD_FIDO_AUTH_LABEL 24330
#define IDS_ENABLE_CREDIT_CARD_FIDO_AUTH_SUBLABEL 24331
#define IDS_AUTOFILL_ENABLE_PAYMENTS_INTEGRATION_CHECKBOX_LABEL 24332
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_OK_BUTTON_LABEL 24338
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_OK_BUTTON_LABEL 24339
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_NEW_VALUES_SECTION_LABEL 24340
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_OLD_VALUES_SECTION_LABEL 24341
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_TITLE 24342
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_CANCEL_BUTTON_LABEL 24353
#define IDS_AUTOFILL_SAVE_ADDRESS_PROMPT_EDIT_BUTTON_TOOLTIP 24345
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_TITLE 24343
#define IDS_AUTOFILL_UPDATE_ADDRESS_PROMPT_CANCEL_BUTTON_LABEL 24354
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_TITLE 24346
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_OK_BUTTON_LABEL_SAVE 24355
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_OK_BUTTON_LABEL_UPDATE 24356
#define IDS_AUTOFILL_EDIT_ADDRESS_DIALOG_CANCEL_BUTTON_LABEL 24357
#define IDS_BOOKMARK_BAR_FOLDER_NAME 148
#define IDS_BOOKMARK_BAR_MOBILE_FOLDER_NAME 150
#define IDS_BOOKMARK_BAR_OTHER_FOLDER_NAME 149
#define IDS_BOOKMARK_BAR_MANAGED_FOLDER_DOMAIN_NAME 24358
#define IDS_BOOKMARK_BAR_MANAGED_FOLDER_DEFAULT_NAME 168
#define IDS_BOOKMARK_EDITOR_TITLE 24359
#define IDS_BOOKMARK_EDITOR_NEW_FOLDER_NAME 24360
#define IDS_BOOKMARK_BUBBLE_REMOVE_BOOKMARK 24361
#define IDS_BOOKMARK_MANAGER_NAME_INPUT_PLACE_HOLDER 24362
#define IDS_BOOKMARK_MANAGER_URL_INPUT_PLACE_HOLDER 24363
#define IDS_TOOLTIP_STAR 24365
#define IDS_CLEAR_BROWSING_DATA_CALCULATING 24368
#define IDS_DEL_BROWSING_HISTORY_COUNTER 24369
#define IDS_DEL_BROWSING_HISTORY_COUNTER_SYNCED 24370
#define IDS_DEL_CACHE_COUNTER_UPPER_ESTIMATE 24371
#define IDS_DEL_CACHE_COUNTER_ALMOST_EMPTY 24372
#define IDS_DEL_CACHE_COUNTER_BASIC 24373
#define IDS_DEL_CACHE_COUNTER_UPPER_ESTIMATE_BASIC 24374
#define IDS_DEL_CACHE_COUNTER_ALMOST_EMPTY_BASIC 24375
#define IDS_DEL_PASSWORDS_COUNTER 24376
#define IDS_DEL_PASSWORDS_COUNTER_SYNCED 24377
#define IDS_DEL_ACCOUNT_PASSWORDS_COUNTER 24378
#define IDS_DEL_PASSWORDS_DOMAINS_DISPLAY 24379
#define IDS_DEL_PASSWORDS_COUNTER_AND_X_MORE 24380
#define IDS_DEL_SIGNIN_DATA_COUNTER 24381
#define IDS_DEL_PASSWORDS_AND_SIGNIN_DATA_COUNTER_NONE 24382
#define IDS_DEL_PASSWORDS_AND_SIGNIN_DATA_COUNTER_COMBINATION 24383
#define IDS_DEL_SITE_SETTINGS_COUNTER 24384
#define IDS_DEL_AUTOFILL_COUNTER_EMPTY 24385
#define IDS_DEL_AUTOFILL_COUNTER_CREDIT_CARDS 24386
#define IDS_DEL_AUTOFILL_COUNTER_ADDRESSES 24387
#define IDS_DEL_AUTOFILL_COUNTER_SUGGESTIONS 24388
#define IDS_DEL_AUTOFILL_COUNTER_SUGGESTIONS_LONG 24389
#define IDS_DEL_AUTOFILL_COUNTER_SUGGESTIONS_SHORT 24390
#define IDS_DEL_AUTOFILL_COUNTER_ONE_TYPE_SYNCED 24391
#define IDS_DEL_AUTOFILL_COUNTER_TWO_TYPES 24392
#define IDS_DEL_AUTOFILL_COUNTER_TWO_TYPES_SYNCED 24393
#define IDS_DEL_AUTOFILL_COUNTER_THREE_TYPES 24394
#define IDS_DEL_AUTOFILL_COUNTER_THREE_TYPES_SYNCED 24395
#define IDS_DEL_COOKIES_COUNTER 24396
#define IDS_DEL_COOKIES_COUNTER_ADVANCED 24397
#define IDS_DEL_COOKIES_COUNTER_ADVANCED_WITH_EXCEPTION 24398
#define IDS_DEL_DOWNLOADS_COUNTER 24399
#define IDS_DEL_HOSTED_APPS_COUNTER 24400
#define IDS_DEL_HOSTED_APPS_COUNTER_AND_X_MORE 24401
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_TITLE 24402
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_CONTENT 24403
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_NO_THANKS 24404
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_GET_DISCOUNTS 24405
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_ACCEPTED_CONFIRMATION_TITLE 24406
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_ACCEPTED_CONFIRMATION_CONTENT 24407
#define IDS_DISCOUNT_CONTEXTUAL_CONSENT_ACCEPTED_CONFIRMATION_DONE 24408
#define IDS_NATIVE_NTP_CART_DISCOUNT_CONSENT_ACCEPT_BUTTON 24409
#define IDS_NATIVE_NTP_CART_DISCOUNT_CONSENT_TITLE 24410
#define IDS_NATIVE_NTP_CART_DISCOUNT_CONSENT_BODY 24411
#define IDS_LIVE_CAPTION_BUBBLE_TITLE 24412
#define IDS_LIVE_CAPTION_BUBBLE_CLOSE 24413
#define IDS_LIVE_CAPTION_BUBBLE_EXPAND 24414
#define IDS_LIVE_CAPTION_BUBBLE_COLLAPSE 24415
#define IDS_LIVE_CAPTION_BUBBLE_BACK_TO_TAB 24416
#define IDS_LIVE_CAPTION_BUBBLE_ERROR 24417
#define IDS_LIVE_CAPTION_BUBBLE_CONTENT_SETTINGS 24418
#define IDS_LIVE_CAPTION_BUBBLE_MEDIA_FOUNDATION_RENDERER_ERROR 24419
#define IDS_LIVE_CAPTION_BUBBLE_MEDIA_FOUNDATION_RENDERER_ERROR_CHECKBOX 24420
#define IDS_LIVE_CAPTION_BUBBLE_APPEAR_SCREENREADER_ANNOUNCEMENT 24421
#define IDS_SETTINGS_TITLE 24422
#define IDS_SETTINGS_HIDE_ADVANCED_SETTINGS 24423
#define IDS_SETTINGS_SHOW_ADVANCED_SETTINGS 24424
#define IDS_NETWORK_PREDICTION_ENABLED_DESCRIPTION 24425
#define IDS_OPTIONS_PROXIES_CONFIGURE_BUTTON 24426
#define IDS_CRASH_TITLE 24447
#define IDS_CRASH_CRASH_COUNT_BANNER_FORMAT 24448
#define IDS_CRASH_SHOW_DEVELOPER_DETAILS 24449
#define IDS_CRASH_CAPTURE_TIME_FORMAT 24450
#define IDS_CRASH_REPORT_STATUS 24451
#define IDS_CRASH_REPORT_STATUS_NOT_UPLOADED 24452
#define IDS_CRASH_REPORT_STATUS_PENDING 24453
#define IDS_CRASH_REPORT_STATUS_PENDING_USER_REQUESTED 24454
#define IDS_CRASH_REPORT_STATUS_UPLOADED 24455
#define IDS_CRASH_REPORT_UPLOADED_ID 24456
#define IDS_CRASH_REPORT_UPLOADED_TIME 24457
#define IDS_CRASH_REPORT_LOCAL_ID 24458
#define IDS_CRASH_REPORT_FILE_SIZE 24459
#define IDS_CRASH_BUG_LINK_LABEL 24460
#define IDS_CRASH_NO_CRASHES_MESSAGE 24461
#define IDS_CRASH_DISABLED_HEADER 24462
#define IDS_CRASH_UPLOAD_MESSAGE 24463
#define IDS_CRASH_UPLOAD_NOW_LINK_TEXT 24464
#define IDS_HTTP_POST_WARNING_TITLE 24465
#define IDS_HTTP_POST_WARNING 24466
#define IDS_HTTP_POST_WARNING_RESEND 24467
#define IDS_DOM_DISTILLER_JAVASCRIPT_DISABLED_CONTENT 24468
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_URL 24469
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_ADD 24470
#define IDS_DOM_DISTILLER_WEBUI_ENTRY_ADD_FAILED 24471
#define IDS_DOM_DISTILLER_WEBUI_VIEW_URL 24472
#define IDS_DOM_DISTILLER_WEBUI_VIEW_URL_FAILED 24473
#define IDS_DOM_DISTILLER_WEBUI_REFRESH 24474
#define IDS_DOM_DISTILLER_WEBUI_FETCHING_ENTRIES 24475
#define IDS_DOM_DISTILLER_VIEWER_FAILED_TO_FIND_ARTICLE_TITLE 24476
#define IDS_DOM_DISTILLER_VIEWER_FAILED_TO_FIND_ARTICLE_CONTENT 24477
#define IDS_DOM_DISTILLER_VIEWER_LOADING_TITLE 24478
#define IDS_DOM_DISTILLER_VIEWER_CUSTOMIZE_APPEARANCE 24479
#define IDS_DOM_DISTILLER_VIEWER_FONT_STYLE 24480
#define IDS_DOM_DISTILLER_VIEWER_SANS_SERIF_FONT 24481
#define IDS_DOM_DISTILLER_VIEWER_SERIF_FONT 24482
#define IDS_DOM_DISTILLER_VIEWER_MONOSPACE_FONT 24483
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR 24484
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR_LIGHT 24485
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR_SEPIA 24486
#define IDS_DOM_DISTILLER_VIEWER_PAGE_COLOR_DARK 24487
#define IDS_DOM_DISTILLER_VIEWER_FONT_SIZE 24488
#define IDS_DOM_DISTILLER_VIEWER_FONT_SIZE_SMALL 24489
#define IDS_DOM_DISTILLER_VIEWER_FONT_SIZE_LARGE 24490
#define IDS_DOM_DISTILLER_VIEWER_CLOSE 24491
#define IDS_DOM_DISTILLER_VIEWER_NO_DATA_CONTENT 24492
#define IDS_DOM_DISTILLER_WEBUI_TITLE 24493
#define IDS_DOM_DISTILLER_VIEWER_TITLE_SUFFIX 24494
#define IDS_ENTERPRISE_COPY_PREVENTION_MISSING_LIST_ERROR 24495
#define IDS_ENTERPRISE_COPY_PREVENTION_DISABLE_CONTAINS_WILDCARD_ERROR 24496
#define IDS_ENTERPRISE_COPY_PREVENTION_WARNING_MESSAGE 24497
#define IDS_ERRORPAGE_NET_BUTTON_DETAILS 24498
#define IDS_ERRORPAGE_NET_BUTTON_HIDE_DETAILS 24499
#define IDS_ERRORPAGES_BUTTON_RELOAD 24500
#define IDS_ERRORPAGES_BUTTON_SHOW_SAVED_COPY 24501
#define IDS_ERRORPAGE_FUN_DISABLED 24502
#define IDS_ERRORPAGES_SUGGESTION_RELOAD_REPOST_SUMMARY 24512
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_HEADER 24513
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_BODY 24514
#define IDS_ERRORPAGES_SUGGESTION_SECURE_DNS_CONFIG_HEADER 24515
#define IDS_ERRORPAGES_SUGGESTION_SECURE_DNS_CONFIG_BODY 24516
#define IDS_ERRORPAGES_SUGGESTION_DNS_CONFIG_HEADER 24517
#define IDS_ERRORPAGES_SUGGESTION_DNS_CONFIG_BODY 24518
#define IDS_ERRORPAGES_SUGGESTION_NETWORK_PREDICTION_HEADER 24519
#define IDS_ERRORPAGES_SUGGESTION_FIREWALL_CONFIG_BODY 24520
#define IDS_ERRORPAGES_SUGGESTION_PROXY_CONFIG_HEADER 24521
#define IDS_ERRORPAGES_SUGGESTION_PROXY_CONFIG_BODY 24522
#define IDS_ERRORPAGES_SUGGESTION_VIEW_POLICIES_HEADER 24524
#define IDS_ERRORPAGES_SUGGESTION_VIEW_POLICIES_BODY 24525
#define IDS_ERRORPAGES_SUGGESTION_UNSUPPORTED_CIPHER_HEADER 24526
#define IDS_ERRORPAGES_SUGGESTION_UNSUPPORTED_CIPHER_BODY 24527
#define IDS_ERRORPAGES_SUGGESTION_NAVIGATE_TO_ORIGIN 24528
#define IDS_ERRORPAGES_HEADING_NOT_AVAILABLE 24529
#define IDS_ERRORPAGES_HEADING_NETWORK_ACCESS_DENIED 24530
#define IDS_ERRORPAGES_HEADING_INTERNET_DISCONNECTED 24531
#define IDS_ERRORPAGES_HEADING_CACHE_READ_FAILURE 24532
#define IDS_ERRORPAGES_HEADING_CONNECTION_INTERRUPTED 24533
#define IDS_ERRORPAGES_HEADING_NOT_FOUND 24534
#define IDS_ERRORPAGES_HEADING_FILE_NOT_FOUND 24535
#define IDS_ERRORPAGES_HEADING_BLOCKED 24536
#define IDS_ERRORPAGES_HEADING_BLOCKED_SCHEME 24537
#define IDS_ERRORPAGES_SUMMARY_NOT_AVAILABLE 24538
#define IDS_ERRORPAGES_SUMMARY_TIMED_OUT 24539
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_RESET 24540
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_CLOSED 24541
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_FAILED 24542
#define IDS_ERRORPAGES_SUMMARY_NETWORK_CHANGED 24543
#define IDS_ERRORPAGES_SUMMARY_CONNECTION_REFUSED 24544
#define IDS_ERRORPAGES_SUMMARY_NAME_NOT_RESOLVED 24545
#define IDS_ERRORPAGES_SUMMARY_ICANN_NAME_COLLISION 24546
#define IDS_ERRORPAGES_SUMMARY_ADDRESS_UNREACHABLE 24547
#define IDS_ERRORPAGES_SUMMARY_FILE_ACCESS_DENIED 24548
#define IDS_ERRORPAGES_SUMMARY_NETWORK_ACCESS_DENIED 24549
#define IDS_ERRORPAGES_SUMMARY_PROXY_CONNECTION_FAILED 24550
#define IDS_ERRORPAGES_SUMMARY_CACHE_READ_FAILURE 24551
#define IDS_ERRORPAGES_SUMMARY_NETWORK_IO_SUSPENDED 24552
#define IDS_ERRORPAGES_SUMMARY_NOT_FOUND 24553
#define IDS_ERRORPAGES_SUMMARY_FILE_NOT_FOUND 24554
#define IDS_ERRORPAGES_SUMMARY_TOO_MANY_REDIRECTS 24555
#define IDS_ERRORPAGES_SUMMARY_EMPTY_RESPONSE 24556
#define IDS_ERRORPAGES_SUMMARY_INVALID_RESPONSE 24557
#define IDS_ERRORPAGES_SUMMARY_DNS_PROBE_RUNNING 24558
#define IDS_ERRORPAGES_HEADING_ACCESS_DENIED 24559
#define IDS_ERRORPAGES_HEADING_FILE_ACCESS_DENIED 24560
#define IDS_ERRORPAGES_SUMMARY_FORBIDDEN 24561
#define IDS_ERRORPAGES_SUMMARY_GONE 24562
#define IDS_ERRORPAGES_HEADING_PAGE_NOT_WORKING 24563
#define IDS_ERRORPAGES_SUMMARY_CONTACT_SITE_OWNER 24564
#define IDS_ERRORPAGES_SUMMARY_WEBSITE_CANNOT_HANDLE_REQUEST 24565
#define IDS_ERRORPAGES_SUMMARY_GATEWAY_TIMEOUT 24566
#define IDS_ERRORPAGES_SUMMARY_SSL_SECURITY_ERROR 24567
#define IDS_ERRORPAGES_SUMMARY_SSL_VERSION_OR_CIPHER_MISMATCH 24568
#define IDS_ERRORPAGES_HEADING_INSECURE_CONNECTION 24569
#define IDS_ERRORPAGES_SUMMARY_BAD_SSL_CLIENT_AUTH_CERT 24570
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_EXTENSION 24571
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_ADMINISTRATOR 24572
#define IDS_ERRORPAGES_SUMMARY_BLOCKED_BY_SECURITY 24573
#define IDS_ERRORPAGES_HTTP_POST_WARNING 24574
#define IDS_ERRORPAGES_SUGGESTION_LIST_HEADER 24575
#define IDS_ERRORPAGES_SUGGESTION_CHECK_CONNECTION_SUMMARY 24576
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_FIREWALL_DNS_SUMMARY 24578
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_FIREWALL_SECURE_DNS_SUMMARY 24579
#define IDS_ERRORPAGES_SUGGESTION_CHECK_FIREWALL_ANTIVIRUS_SUMMARY 24580
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_FIREWALL_SUMMARY 24581
#define IDS_ERRORPAGES_SUGGESTION_CHECK_PROXY_ADDRESS_SUMMARY 24582
#define IDS_ERRORPAGES_SUGGESTION_CONTACT_ADMIN_SUMMARY 24583
#define IDS_ERRORPAGES_SUGGESTION_CONTACT_ADMIN_SUMMARY_STANDALONE 24584
#define IDS_ERRORPAGES_SUGGESTION_LEARNMORE_SUMMARY 24585
#define IDS_ERRORPAGES_SUGGESTION_LEARNMORE_SUMMARY_STANDALONE 24586
#define IDS_ERRORPAGES_SUGGESTION_CLEAR_COOKIES_SUMMARY 24587
#define IDS_ERRORPAGES_SUGGESTION_CHECK_HARDWARE_SUMMARY 24591
#define IDS_ERRORPAGES_SUGGESTION_CHECK_WIFI_SUMMARY 24592
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE_CONNECTION_SUMMARY 24593
#define IDS_ERRORPAGES_SUGGESTION_COMPLETE_SETUP_SUMMARY 24594
#define IDS_ERRORPAGES_SUGGESTION_DISABLE_EXTENSION_SUMMARY 24595
#define IDS_ERRORPAGES_CHECK_TYPO_SUMMARY 24596
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE 24597
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE_STANDALONE 24598
#define IDS_ERRORPAGES_SUGGESTION_DIAGNOSE_CHECK_TYPO_STANDALONE 24599
#define IDS_ERRORPAGES_GAME_INSTRUCTIONS 24600
#define IDS_ERRORPAGE_DINO_GAME_DESCRIPTION 24601
#define IDS_ERRORPAGE_DINO_ARIA_LABEL 24602
#define IDS_ERRORPAGE_DINO_GAME_START 24603
#define IDS_ERRORPAGE_DINO_GAME_OVER 24604
#define IDS_ERRORPAGE_DINO_HIGH_SCORE 24605
#define IDS_ERRORPAGE_DINO_JUMP 24606
#define IDS_ERRORPAGE_DINO_SLOW_SPEED_TOGGLE 24607
#define IDS_FIND_IN_PAGE_ACCESSIBLE_TITLE 24608
#define IDS_FIND_IN_PAGE_COUNT 24609
#define IDS_ACCESSIBLE_FIND_IN_PAGE_COUNT 24610
#define IDS_ACCESSIBLE_FIND_IN_PAGE_NO_RESULTS 24611
#define IDS_FIND_IN_PAGE_PREVIOUS_TOOLTIP 24612
#define IDS_FIND_IN_PAGE_NEXT_TOOLTIP 24613
#define IDS_FIND_IN_PAGE_CLOSE_TOOLTIP 24614
#define IDS_FLAGS_UI_SEARCH_PLACEHOLDER 24615
#define IDS_FLAGS_UI_SEARCH_LABEL 24616
#define IDS_FLAGS_UI_TITLE 24617
#define IDS_FLAGS_UI_PAGE_RESET 24618
#define IDS_FLAGS_UI_PAGE_WARNING 24619
#define IDS_FLAGS_UI_PAGE_WARNING_EXPLANATION 24620
#define IDS_FLAGS_UI_OWNER_WARNING 24621
#define IDS_FLAGS_UI_AVAILABLE_FEATURE 24622
#define IDS_FLAGS_UI_UNAVAILABLE_FEATURE 24623
#define IDS_FLAGS_UI_ENABLED_FEATURE 24624
#define IDS_FLAGS_UI_DISABLED_FEATURE 24625
#define IDS_FLAGS_UI_NO_RESULTS 24626
#define IDS_FLAGS_UI_NOT_AVAILABLE_ON_PLATFORM 24627
#define IDS_FLAGS_UI_RELAUNCH 24628
#define IDS_FLAGS_UI_CLEAR_SEARCH 24632
#define IDS_FLAGS_UI_RESET_ACKNOWLEDGED 24633
#define IDS_FLAGS_UI_EXPERIMENT_ENABLED 24634
#define IDS_FLAGS_UI_SEARCH_RESULTS_SINGULAR 24635
#define IDS_FLAGS_UI_SEARCH_RESULTS_PLURAL 24636
#define IDS_DEPRECATED_FEATURES_PAGE_RESET 24637
#define IDS_DEPRECATED_FEATURES_OWNER_WARNING 24638
#define IDS_DEPRECATED_FEATURES_AVAILABLE_FEATURE 24639
#define IDS_DEPRECATED_FEATURES_UNAVAILABLE_FEATURE 24640
#define IDS_DEPRECATED_FEATURES_ENABLED_FEATURE 24641
#define IDS_DEPRECATED_FEATURES_DISABLED_FEATURE 24642
#define IDS_DEPRECATED_FEATURES_NOT_AVAILABLE_ON_PLATFORM 24643
#define IDS_DEPRECATED_FEATURES_RELAUNCH 24644
#define IDS_DEPRECATED_FEATURES_SEARCH_PLACEHOLDER 24648
#define IDS_DEPRECATED_FEATURES_TITLE 24649
#define IDS_DEPRECATED_FEATURES_HEADING 24650
#define IDS_DEPRECATED_FEATURES_PAGE_WARNING_EXPLANATION 24651
#define IDS_DEPRECATED_FEATURES_NO_RESULTS 24652
#define IDS_DEPRECATED_UI_CLEAR_SEARCH 24653
#define IDS_DEPRECATED_UI_RESET_ACKNOWLEDGED 24654
#define IDS_DEPRECATED_UI_EXPERIMENT_ENABLED 24655
#define IDS_ENTERPRISE_UI_SEARCH_RESULTS_SINGULAR 24656
#define IDS_ENTERPRISE_UI_SEARCH_RESULTS_PLURAL 24657
#define IDS_EXIT_FULLSCREEN_MODE 24658
#define IDS_FULLSCREEN_HOLD_TO_EXIT_FULLSCREEN 24659
#define IDS_FULLSCREEN_PRESS_TO_EXIT_FULLSCREEN 24660
#define IDS_FULLSCREEN_PRESS_TO_EXIT_FULLSCREEN_TWO_KEYS 24661
#define IDS_PRESS_TO_EXIT_MOUSELOCK 24662
#define IDS_PRESS_TO_EXIT_MOUSELOCK_TWO_KEYS 24663
#define IDS_GLOBAL_MEDIA_CONTROLS_BACK_TO_TAB 24664
#define IDS_GLOBAL_MEDIA_CONTROLS_DISMISS_ICON_TOOLTIP_TEXT 24665
#define IDS_HEAVY_AD_INTERVENTION_BUTTON_DETAILS 24666
#define IDS_HEAVY_AD_INTERVENTION_HEADING 24667
#define IDS_HEAVY_AD_INTERVENTION_SUMMARY 24668
#define IDS_HEAVY_AD_INTERVENTION_BUTTON_RELOAD 24669
#define IDS_HISTORY_ACTION_MENU_DESCRIPTION 24670
#define IDS_HISTORY_ARIA_ROLE_DESCRIPTION 24671
#define IDS_HISTORY_CANCEL_EDITING_BUTTON 24672
#define IDS_HISTORY_DATE_WITH_RELATIVE_TIME 24673
#define IDS_HISTORY_DELETE_PRIOR_VISITS_CONFIRM_BUTTON 24674
#define IDS_HISTORY_DELETE_PRIOR_VISITS_WARNING 24675
#define IDS_HISTORY_DELETE_SELECTED_ENTRIES_BUTTON 24676
#define IDS_HISTORY_ENTRY_ACCESSIBILITY_DELETE 24677
#define IDS_HISTORY_ENTRY_ACCESSIBILITY_LABEL 24678
#define IDS_HISTORY_ENTRY_BOOKMARKED 24679
#define IDS_HISTORY_ENTRY_SUMMARY 24680
#define IDS_HISTORY_FOUND_SEARCH_RESULTS 24681
#define IDS_HISTORY_OTHER_FORMS_OF_HISTORY 24682
#define IDS_HISTORY_LOADING 24683
#define IDS_HISTORY_MORE_FROM_SITE 24684
#define IDS_HISTORY_NO_RESULTS 24685
#define IDS_HISTORY_NO_SEARCH_RESULTS 24686
#define IDS_HISTORY_OPEN_CLEAR_BROWSING_DATA_DIALOG 24687
#define IDS_HISTORY_OTHER_SESSIONS_COLLAPSE_SESSION 24688
#define IDS_HISTORY_OTHER_SESSIONS_EXPAND_SESSION 24689
#define IDS_HISTORY_OTHER_SESSIONS_HIDE_FOR_NOW 24690
#define IDS_HISTORY_OTHER_SESSIONS_OPEN_ALL 24691
#define IDS_HISTORY_REMOVE_BOOKMARK 24692
#define IDS_HISTORY_REMOVE_PAGE 24693
#define IDS_HISTORY_REMOVE_PAGE_SUCCESS 24694
#define IDS_HISTORY_REMOVE_SELECTED_ITEMS 24695
#define IDS_HISTORY_SEARCH_BUTTON 24696
#define IDS_HISTORY_SEARCH_RESULT 24697
#define IDS_HISTORY_SEARCH_RESULTS 24698
#define IDS_HISTORY_SHOW_HISTORY 318
#define IDS_HISTORY_SHOWFULLHISTORY_LINK 24699
#define IDS_HISTORY_START_EDITING_BUTTON 24700
#define IDS_HISTORY_TITLE 24701
#define IDS_HISTORY_UNKNOWN_DEVICE 24702
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE 24703
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_IFRAME 24704
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_NONSTANDARD_URL 24705
#define IDS_JAVASCRIPT_MESSAGEBOX_TITLE_NONSTANDARD_URL_IFRAME 24706
#define IDS_JAVASCRIPT_MESSAGEBOX_SUPPRESS_OPTION 24707
#define IDS_BEFOREUNLOAD_MESSAGEBOX_TITLE 24708
#define IDS_BEFOREUNLOAD_APP_MESSAGEBOX_TITLE 24709
#define IDS_BEFOREUNLOAD_MESSAGEBOX_OK_BUTTON_LABEL 24710
#define IDS_BEFOREUNLOAD_MESSAGEBOX_MESSAGE 24711
#define IDS_BEFORERELOAD_MESSAGEBOX_TITLE 24712
#define IDS_BEFORERELOAD_APP_MESSAGEBOX_TITLE 24713
#define IDS_BEFORERELOAD_MESSAGEBOX_OK_BUTTON_LABEL 24714
#define IDS_LOGIN_DIALOG_TITLE 24715
#define IDS_LOGIN_DIALOG_OK_BUTTON_LABEL 24716
#define IDS_LOGIN_DIALOG_AUTHORITY 24717
#define IDS_LOGIN_DIALOG_PROXY_AUTHORITY 24718
#define IDS_LOGIN_DIALOG_NOT_PRIVATE 24719
#define IDS_LOGIN_DIALOG_USERNAME_FIELD 24720
#define IDS_LOGIN_DIALOG_PASSWORD_FIELD 24721
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_PREVIOUS_TRACK 24722
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_SEEK_BACKWARD 24723
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_PLAY 24724
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_PAUSE 24725
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_SEEK_FORWARD 24726
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_NEXT_TRACK 24727
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_ENTER_PIP 24728
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_EXIT_PIP 24729
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_MUTE 24730
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACTION_UNMUTE 24731
#define IDS_MEDIA_MESSAGE_CENTER_MEDIA_NOTIFICATION_ACCESSIBLE_NAME 24732
#define IDS_DEFAULT_TAB_TITLE 284
#define IDS_DOWNLOAD_TAB_TITLE 24733
#define IDS_SAD_TAB_TITLE 303
#define IDS_SAD_TAB_MESSAGE 304
#define IDS_SAD_TAB_HELP_MESSAGE 24734
#define IDS_SAD_TAB_HELP_LINK 24735
#define IDS_SAD_TAB_RELOAD_LABEL 306
#define IDS_SAD_TAB_OOM_TITLE 24737
#define IDS_SAD_TAB_RELOAD_TITLE 307
#define IDS_SAD_TAB_OOM_MESSAGE_TABS 24738
#define IDS_SAD_TAB_OOM_MESSAGE_NOTABS 24739
#define IDS_SAD_TAB_RELOAD_TRY 308
#define IDS_SAD_TAB_RELOAD_INCOGNITO 309
#define IDS_SAD_TAB_RELOAD_RESTART_BROWSER 310
#define IDS_SAD_TAB_RELOAD_RESTART_DEVICE 311
#define IDS_SAD_TAB_ERROR_CODE 305
#define IDS_NEW_TAB_TITLE 179
#define IDS_NEW_INCOGNITO_TAB_TITLE 24743
#define IDS_NEW_TAB_OTR_HEADING 24744
#define IDS_NEW_TAB_OTR_DESCRIPTION 24745
#define IDS_NEW_TAB_OTR_LEARN_MORE_LINK 24746
#define IDS_NEW_TAB_OTR_MESSAGE_WARNING 24747
#define IDS_NEW_TAB_UNDO_THUMBNAIL_REMOVE 176
#define IDS_NEW_TAB_OTR_TITLE 24748
#define IDS_NEW_TAB_OTR_SUBTITLE 24749
#define IDS_NEW_TAB_OTR_SUBTITLE_WITH_READING_LIST 24750
#define IDS_NEW_TAB_OTR_NOT_SAVED 24751
#define IDS_NEW_TAB_OTR_VISIBLE 24752
#define IDS_NEW_TAB_OTR_COOKIE_CONTROLS_CONTROLLED_TOOLTIP_TEXT 24753
#define IDS_NEW_TAB_OTR_THIRD_PARTY_COOKIE 24754
#define IDS_NEW_TAB_OTR_THIRD_PARTY_COOKIE_SUBLABEL 24755
#define IDS_REVAMPED_INCOGNITO_NTP_TITLE 24756
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_HEADER 24757
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_DESCRIPTION 24758
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_NOT_HEADER 24759
#define IDS_REVAMPED_INCOGNITO_NTP_DOES_NOT_DESCRIPTION 24760
#define IDS_REVAMPED_INCOGNITO_NTP_LEARN_MORE 24761
#define IDS_REVAMPED_INCOGNITO_NTP_OTR_THIRD_PARTY_COOKIE 24762
#define IDS_REVAMPED_INCOGNITO_NTP_OTR_THIRD_PARTY_COOKIE_SUBLABEL 24763
#define IDS_NTP_ARTICLE_SUGGESTIONS_NOT_AVAILABLE 24765
#define IDS_NTP_ARTICLE_SUGGESTIONS_SECTION_HEADER 24766
#define IDS_NTP_ARTICLE_SUGGESTIONS_SECTION_EMPTY 24767
#define IDS_NTP_READING_LIST_SUGGESTIONS_SECTION_HEADER 24768
#define IDS_NTP_READING_LIST_SUGGESTIONS_SECTION_EMPTY 24769
#define IDS_NTP_RECENT_TAB_SUGGESTIONS_SECTION_HEADER 24770
#define IDS_NTP_RECENT_TAB_SUGGESTIONS_SECTION_EMPTY 24771
#define IDS_NTP_NOTIFICATIONS_READ_THIS_STORY_AND_MORE 24772
#define IDS_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_HINT 24773
#define IDS_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_SUGGESTION_CONTENTS 24774
#define IDS_ACC_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_SUFFIX 24775
#define IDS_ACC_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA 24776
#define IDS_OMNIBOX_PEDAL_MANAGE_PASSWORDS_HINT 24777
#define IDS_OMNIBOX_PEDAL_MANAGE_PASSWORDS_SUGGESTION_CONTENTS 24778
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_PASSWORDS_SUFFIX 24779
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_PASSWORDS 24780
#define IDS_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_HINT 24781
#define IDS_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_SUGGESTION_CONTENTS 24782
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_SUFFIX 24783
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD 24784
#define IDS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_HINT 24785
#define IDS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUGGESTION_CONTENTS 24786
#define IDS_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUFFIX 24787
#define IDS_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO 24788
#define IDS_OMNIBOX_PEDAL_TRANSLATE_HINT 24789
#define IDS_OMNIBOX_PEDAL_TRANSLATE_SUGGESTION_CONTENTS 24790
#define IDS_ACC_OMNIBOX_PEDAL_TRANSLATE_SUFFIX 24791
#define IDS_ACC_OMNIBOX_PEDAL_TRANSLATE 24792
#define IDS_OMNIBOX_PEDAL_UPDATE_CHROME_HINT 24793
#define IDS_OMNIBOX_PEDAL_UPDATE_CHROME_SUGGESTION_CONTENTS 24794
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CHROME_SUFFIX 24795
#define IDS_ACC_OMNIBOX_PEDAL_UPDATE_CHROME 24796
#define IDS_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_HINT 24797
#define IDS_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_SUGGESTION_CONTENTS 24798
#define IDS_ACC_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_SUFFIX 24799
#define IDS_ACC_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK 24800
#define IDS_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS_HINT 24801
#define IDS_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS_SUGGESTION_CONTENTS 24802
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS_SUFFIX 24803
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SECURITY_SETTINGS 24804
#define IDS_OMNIBOX_PEDAL_MANAGE_COOKIES_HINT 24805
#define IDS_OMNIBOX_PEDAL_MANAGE_COOKIES_SUGGESTION_CONTENTS 24806
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_COOKIES_SUFFIX 24807
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_COOKIES 24808
#define IDS_OMNIBOX_PEDAL_MANAGE_ADDRESSES_HINT 24809
#define IDS_OMNIBOX_PEDAL_MANAGE_ADDRESSES_SUGGESTION_CONTENTS 24810
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_ADDRESSES_SUFFIX 24811
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_ADDRESSES 24812
#define IDS_OMNIBOX_PEDAL_MANAGE_SYNC_HINT 24813
#define IDS_OMNIBOX_PEDAL_MANAGE_SYNC_SUGGESTION_CONTENTS 24814
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SYNC_SUFFIX 24815
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SYNC 24816
#define IDS_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS_HINT 24817
#define IDS_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS_SUGGESTION_CONTENTS 24818
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS_SUFFIX 24819
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_SITE_SETTINGS 24820
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC_HINT 24821
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC_SUGGESTION_CONTENTS 24822
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC_SUFFIX 24823
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_DOC 24824
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET_HINT 24825
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET_SUGGESTION_CONTENTS 24826
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET_SUFFIX 24827
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SHEET 24828
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE_HINT 24829
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE_SUGGESTION_CONTENTS 24830
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE_SUFFIX 24831
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SLIDE 24832
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT_HINT 24833
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT_SUGGESTION_CONTENTS 24834
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT_SUFFIX 24835
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_CALENDAR_EVENT 24836
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE_HINT 24837
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE_SUGGESTION_CONTENTS 24838
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE_SUFFIX 24839
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_SITE 24840
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE_HINT 24841
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE_SUGGESTION_CONTENTS 24842
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE_SUFFIX 24843
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_KEEP_NOTE 24844
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM_HINT 24845
#define IDS_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM_SUGGESTION_CONTENTS 24846
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM_SUFFIX 24847
#define IDS_ACC_OMNIBOX_PEDAL_CREATE_GOOGLE_FORM 24848
#define IDS_OMNIBOX_PEDAL_SEE_CHROME_TIPS_HINT 24849
#define IDS_OMNIBOX_PEDAL_SEE_CHROME_TIPS_SUGGESTION_CONTENTS 24850
#define IDS_ACC_OMNIBOX_PEDAL_SEE_CHROME_TIPS_SUFFIX 24851
#define IDS_ACC_OMNIBOX_PEDAL_SEE_CHROME_TIPS 24852
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT_HINT 24853
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT_SUGGESTION_CONTENTS 24854
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT_SUFFIX 24855
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_ACCOUNT 24856
#define IDS_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD_HINT 24857
#define IDS_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD_SUGGESTION_CONTENTS 24858
#define IDS_ACC_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD_SUFFIX 24859
#define IDS_ACC_OMNIBOX_PEDAL_CHANGE_GOOGLE_PASSWORD 24860
#define IDS_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS_HINT 24861
#define IDS_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS_SUGGESTION_CONTENTS 24862
#define IDS_ACC_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS_SUFFIX 24863
#define IDS_ACC_OMNIBOX_PEDAL_CLOSE_INCOGNITO_WINDOWS 24864
#define IDS_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_HINT 24865
#define IDS_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_SUGGESTION_CONTENTS 24866
#define IDS_ACC_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_SUFFIX 24867
#define IDS_ACC_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME 24868
#define IDS_OMNIBOX_PEDAL_FIND_MY_PHONE_HINT 24869
#define IDS_OMNIBOX_PEDAL_FIND_MY_PHONE_SUGGESTION_CONTENTS 24870
#define IDS_ACC_OMNIBOX_PEDAL_FIND_MY_PHONE_SUFFIX 24871
#define IDS_ACC_OMNIBOX_PEDAL_FIND_MY_PHONE 24872
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY_HINT 24873
#define IDS_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY_SUGGESTION_CONTENTS 24874
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY_SUFFIX 24875
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_GOOGLE_PRIVACY 24876
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_HINT 24877
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_SUGGESTION_CONTENTS 24878
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_SUFFIX 24879
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS 24880
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS_HINT 24881
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS_SUGGESTION_CONTENTS 24882
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS_SUFFIX 24883
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_DOWNLOADS 24884
#define IDS_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_HINT 24885
#define IDS_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_SUGGESTION_CONTENTS 24886
#define IDS_ACC_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_SUFFIX 24887
#define IDS_ACC_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY 24888
#define IDS_OMNIBOX_PEDAL_SHARE_THIS_PAGE_HINT 24889
#define IDS_OMNIBOX_PEDAL_SHARE_THIS_PAGE_SUGGESTION_CONTENTS 24890
#define IDS_ACC_OMNIBOX_PEDAL_SHARE_THIS_PAGE_SUFFIX 24891
#define IDS_ACC_OMNIBOX_PEDAL_SHARE_THIS_PAGE 24892
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY_HINT 24893
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY_SUGGESTION_CONTENTS 24894
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY_SUFFIX 24895
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_ACCESSIBILITY 24896
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS_HINT 24897
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS_SUGGESTION_CONTENTS 24898
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS_SUFFIX 24899
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_CHROME_FONTS 24900
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES_HINT 24901
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES_SUGGESTION_CONTENTS 24902
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES_SUFFIX 24903
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROME_THEMES 24904
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES_HINT 24905
#define IDS_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES_SUGGESTION_CONTENTS 24906
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES_SUFFIX 24907
#define IDS_ACC_OMNIBOX_PEDAL_CUSTOMIZE_SEARCH_ENGINES 24908
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY_HINT 24909
#define IDS_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY_SUGGESTION_CONTENTS 24910
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY_SUFFIX 24911
#define IDS_ACC_OMNIBOX_PEDAL_MANAGE_CHROMEOS_ACCESSIBILITY 24912
#define IDS_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_HINT 24913
#define IDS_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_SUGGESTION_CONTENTS 24914
#define IDS_ACC_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_SUFFIX 24915
#define IDS_ACC_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER 24916
#define IDS_ANDROID_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_HINT 24917
#define IDS_ANDROID_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_HINT 24918
#define IDS_ANDROID_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUGGESTION_CONTENTS 24919
#define IDS_ANDROID_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUFFIX 24920
#define IDS_ANDROID_ACC_OMNIBOX_PEDAL_LAUNCH_INCOGNITO 24921
#define IDS_IOS_OMNIBOX_PEDAL_CLEAR_BROWSING_DATA_HINT 24922
#define IDS_IOS_OMNIBOX_PEDAL_SET_CHROME_AS_DEFAULT_BROWSER_HINT 24923
#define IDS_IOS_OMNIBOX_PEDAL_UPDATE_CREDIT_CARD_HINT 24924
#define IDS_IOS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_HINT 24925
#define IDS_IOS_OMNIBOX_PEDAL_RUN_CHROME_SAFETY_CHECK_HINT 24926
#define IDS_IOS_OMNIBOX_PEDAL_MANAGE_CHROME_SETTINGS_HINT 24927
#define IDS_IOS_OMNIBOX_PEDAL_VIEW_CHROME_HISTORY_HINT 24928
#define IDS_IOS_OMNIBOX_PEDAL_PLAY_CHROME_DINO_GAME_HINT 24929
#define IDS_IOS_OMNIBOX_PEDAL_LAUNCH_INCOGNITO_SUGGESTION_CONTENTS 24930
#define IDS_IOS_OMNIBOX_PEDAL_MANAGE_PASSWORDS_HINT 24931
#define IDS_AUTOCOMPLETE_SEARCH_DESCRIPTION 24932
#define IDS_EMPTY_KEYWORD_VALUE 283
#define IDS_LINK_FROM_CLIPBOARD 24933
#define IDS_TEXT_FROM_CLIPBOARD 24934
#define IDS_IMAGE_FROM_CLIPBOARD 24935
#define IDS_SECURE_CONNECTION_EV 24936
#define IDS_SECURE_VERBOSE_STATE 24937
#define IDS_NOT_SECURE_VERBOSE_STATE 24938
#define IDS_DANGEROUS_VERBOSE_STATE 24939
#define IDS_OFFLINE_VERBOSE_STATE 24940
#define IDS_OMNIBOX_TAB_SUGGEST_HINT 24942
#define IDS_OMNIBOX_FILE 24943
#define IDS_OMNIBOX_READER_MODE 24944
#define IDS_DRIVE_SUGGESTION_DOCUMENT 24945
#define IDS_DRIVE_SUGGESTION_FORM 24946
#define IDS_DRIVE_SUGGESTION_SPREADSHEET 24947
#define IDS_DRIVE_SUGGESTION_PRESENTATION 24948
#define IDS_DRIVE_SUGGESTION_GENERAL 24949
#define IDS_DRIVE_SUGGESTION_DESCRIPTION_TEMPLATE 24950
#define IDS_DRIVE_SUGGESTION_DESCRIPTION_TEMPLATE_WITHOUT_DATE 24951
#define IDS_DRIVE_SUGGESTION_DESCRIPTION_TEMPLATE_WITHOUT_OWNER 24952
#define IDS_ACCURACY_CHECK_VERBOSE_STATE 24953
#define IDS_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH_HINT 24954
#define IDS_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH_SUGGESTION_CONTENTS 24955
#define IDS_ACC_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH_SUFFIX 24956
#define IDS_ACC_OMNIBOX_ACTION_HISTORY_CLUSTERS_SEARCH 24957
#define IDS_ACC_AUTOCOMPLETE_HISTORY 24958
#define IDS_ACC_AUTOCOMPLETE_SEARCH_HISTORY 24959
#define IDS_ACC_AUTOCOMPLETE_SEARCH 24960
#define IDS_ACC_AUTOCOMPLETE_SUGGESTED_SEARCH 24961
#define IDS_ACC_AUTOCOMPLETE_SUGGESTED_SEARCH_ENTITY 24962
#define IDS_ACC_AUTOCOMPLETE_QUICK_ANSWER 24963
#define IDS_ACC_AUTOCOMPLETE_BOOKMARK 24964
#define IDS_ACC_AUTOCOMPLETE_CLIPBOARD_IMAGE 24965
#define IDS_ACC_AUTOCOMPLETE_CLIPBOARD_TEXT 24966
#define IDS_ACC_AUTOCOMPLETE_CLIPBOARD_URL 24967
#define IDS_ACC_SEARCH_ICON 24968
#define IDS_ACC_AUTOCOMPLETE_N_OF_M 24969
#define IDS_ACC_TAB_SWITCH_SUFFIX 24970
#define IDS_ACC_TAB_SWITCH_BUTTON_FOCUSED_PREFIX 24971
#define IDS_ACC_TAB_SWITCH_BUTTON 24972
#define IDS_ACC_MULTIPLE_ACTIONS_SUFFIX 24973
#define IDS_ACC_KEYWORD_SUFFIX 24974
#define IDS_ACC_KEYWORD_MODE 24975
#define IDS_ACC_REMOVE_SUGGESTION_SUFFIX 24976
#define IDS_ACC_REMOVE_SUGGESTION_FOCUSED_PREFIX 24977
#define IDS_ACC_REMOVE_SUGGESTION_BUTTON 24978
#define IDS_TOOLTIP_HEADER_SHOW_SUGGESTIONS_BUTTON 236
#define IDS_TOOLTIP_HEADER_HIDE_SUGGESTIONS_BUTTON 235
#define IDS_ACC_HEADER_SHOW_SUGGESTIONS_BUTTON 238
#define IDS_ACC_HEADER_HIDE_SUGGESTIONS_BUTTON 237
#define IDS_ACC_HEADER_SECTION_SHOWN 24979
#define IDS_ACC_HEADER_SECTION_HIDDEN 24980
#define IDS_SEARCH_ENGINES_STARTER_PACK_BOOKMARKS_NAME 24981
#define IDS_SEARCH_ENGINES_STARTER_PACK_BOOKMARKS_KEYWORD 24982
#define IDS_SEARCH_ENGINES_STARTER_PACK_HISTORY_NAME 24983
#define IDS_SEARCH_ENGINES_STARTER_PACK_HISTORY_KEYWORD 24984
#define IDS_SEARCH_ENGINES_STARTER_PACK_SETTINGS_NAME 24985
#define IDS_SEARCH_ENGINES_STARTER_PACK_SETTINGS_KEYWORD 24986
#define IDS_PAGE_INFO_SECURE_SUMMARY 24987
#define IDS_PAGE_INFO_MIXED_CONTENT_SUMMARY 24988
#define IDS_PAGE_INFO_MIXED_CONTENT_SUMMARY_SHORT 24989
#define IDS_PAGE_INFO_NOT_SECURE_SUMMARY 24990
#define IDS_PAGE_INFO_NOT_SECURE_SUMMARY_SHORT 24991
#define IDS_PAGE_INFO_MALWARE_SUMMARY 24992
#define IDS_PAGE_INFO_SOCIAL_ENGINEERING_SUMMARY 24993
#define IDS_PAGE_INFO_UNWANTED_SOFTWARE_SUMMARY 24994
#define IDS_PAGE_INFO_EXTENSION_PAGE 24995
#define IDS_PAGE_INFO_VIEW_SOURCE_PAGE 24996
#define IDS_PAGE_INFO_DEVTOOLS_PAGE 24997
#define IDS_PAGE_INFO_READER_MODE_PAGE_SECURE 24998
#define IDS_PAGE_INFO_READER_MODE_PAGE 24999
#define IDS_PAGE_INFO_SAFETY_TIP_BAD_REPUTATION_TITLE 25000
#define IDS_PAGE_INFO_SAFETY_TIP_BAD_REPUTATION_LEAVE_BUTTON 25001
#define IDS_PAGE_INFO_SAFETY_TIP_MORE_INFO_LINK 25002
#define IDS_PAGE_INFO_SAFETY_TIP_LOOKALIKE_LEAVE_BUTTON 25003
#define IDS_PAGE_INFO_SAFETY_TIP_LOOKALIKE_TITLE 25004
#define IDS_PAGE_INFO_SAFETY_TIP_DESCRIPTION 25005
#define IDS_PAGE_INFO_ACCURACY_TIP_TITLE 25006
#define IDS_PAGE_INFO_ACCURACY_TIP_LEARN_MORE_BUTTON 25007
#define IDS_PAGE_INFO_ACCURACY_TIP_IGNORE_BUTTON 25008
#define IDS_PAGE_INFO_ACCURACY_TIP_OPT_OUT_BUTTON 25009
#define IDS_PAGE_INFO_ACCURACY_TIP_BODY_LINE_1 25010
#define IDS_PAGE_INFO_ACCURACY_TIP_BODY_LINE_2 25011
#define IDS_PAGE_INFO_ACCURACY_TIP_BODY_LINE_3 25012
#define IDS_PAGE_INFO_FILE_PAGE 25013
#define IDS_PAGE_INFO_SECURE_DETAILS 25014
#define IDS_PAGE_INFO_SECURE_DETAILS_V2 25015
#define IDS_PAGE_INFO_ADMIN_PROVIDED_CERT_DETAILS 25016
#define IDS_PAGE_INFO_MIXED_CONTENT_DETAILS 25017
#define IDS_PAGE_INFO_LEGACY_TLS_DETAILS 25018
#define IDS_PAGE_INFO_NOT_SECURE_DETAILS 25019
#define IDS_PAGE_INFO_MALWARE_DETAILS 25020
#define IDS_PAGE_INFO_SOCIAL_ENGINEERING_DETAILS 25021
#define IDS_PAGE_INFO_UNWANTED_SOFTWARE_DETAILS 25022
#define IDS_PAGE_INFO_SECURITY_TAB_INSECURE_IDENTITY 25023
#define IDS_PAGE_INFO_CHANGE_PASSWORD_SUMMARY 25024
#define IDS_PAGE_INFO_CHANGE_PASSWORD_SAVED_PASSWORD_SUMMARY 25025
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_SAVED 25026
#define IDS_PAGE_INFO_CHECK_PASSWORDS_BUTTON 25027
#define IDS_PAGE_INFO_INVALID_CERTIFICATE_DESCRIPTION 25028
#define IDS_PAGE_INFO_RESET_INVALID_CERTIFICATE_DECISIONS_BUTTON 25029
#define IDS_PAGE_INFO_HELP_CENTER_LINK 25030
#define IDS_PAGE_INFO_SECURITY_TAB_DEPRECATED_SIGNATURE_ALGORITHM 25031
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_CONNECTION_TEXT 25032
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_INSECURE_CONTENT_ERROR 25033
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_INSECURE_CONTENT_WARNING 25034
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_INSECURE_FORM_WARNING 25035
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTED_SENTENCE_LINK 25036
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTION_DETAILS 25037
#define IDS_PAGE_INFO_SECURITY_TAB_ENCRYPTION_DETAILS_AEAD 25038
#define IDS_PAGE_INFO_SECURITY_TAB_NON_UNIQUE_NAME 25039
#define IDS_PAGE_INFO_SECURITY_TAB_NOT_ENCRYPTED_CONNECTION_TEXT 25040
#define IDS_PAGE_INFO_SECURITY_TAB_SSL_VERSION 25041
#define IDS_PAGE_INFO_SECURITY_TAB_UNKNOWN_PARTY 25042
#define IDS_PAGE_INFO_SECURITY_TAB_WEAK_ENCRYPTION_CONNECTION_TEXT 25043
#define IDS_PAGE_INFO_CERT_INFO_BUTTON 25044
#define IDS_PAGE_INFO_ADDRESS 25045
#define IDS_PAGE_INFO_PARTIAL_ADDRESS 25046
#define IDS_PAGE_INFO_SECURITY_TAB_SECURE_IDENTITY_EV_VERIFIED 25047
#define IDS_PAGE_INFO_CERTIFICATE 25048
#define IDS_PAGE_INFO_CERTIFICATE_VALID_LINK 25049
#define IDS_PAGE_INFO_CERTIFICATE_INVALID_LINK 25050
#define IDS_PAGE_INFO_CERTIFICATE_BUTTON_TEXT 25051
#define IDS_PAGE_INFO_CERTIFICATE_VALID_PARENTHESIZED 25052
#define IDS_PAGE_INFO_CERTIFICATE_INVALID_PARENTHESIZED 25053
#define IDS_PAGE_INFO_CERTIFICATE_IS_VALID 25054
#define IDS_PAGE_INFO_CERTIFICATE_IS_NOT_VALID 25055
#define IDS_PAGE_INFO_CERTIFICATE_VALID_LINK_TOOLTIP 25056
#define IDS_PAGE_INFO_CERTIFICATE_INVALID_LINK_TOOLTIP 25057
#define IDS_PAGE_INFO_COOKIES 25059
#define IDS_PAGE_INFO_COOKIES_BUTTON_TEXT 25060
#define IDS_PAGE_INFO_NUM_COOKIES_PARENTHESIZED 25061
#define IDS_PAGE_INFO_NUM_COOKIES 25062
#define IDS_PAGE_INFO_COOKIES_TOOLTIP 25063
#define IDS_PAGE_INFO_BUTTON_TEXT_ALLOWED_BY_USER 25064
#define IDS_PAGE_INFO_BUTTON_TEXT_ALLOWED_ONCE_BY_USER 25065
#define IDS_PAGE_INFO_BUTTON_TEXT_BLOCKED_BY_USER 25066
#define IDS_PAGE_INFO_BUTTON_TEXT_MUTED_BY_USER 25067
#define IDS_PAGE_INFO_BUTTON_TEXT_ASK_BY_USER 25068
#define IDS_PAGE_INFO_BUTTON_TEXT_DETECT_IMPORTANT_CONTENT_BY_USER 25069
#define IDS_PAGE_INFO_BUTTON_TEXT_ALLOWED_BY_DEFAULT 25070
#define IDS_PAGE_INFO_BUTTON_TEXT_BLOCKED_BY_DEFAULT 25071
#define IDS_PAGE_INFO_BUTTON_TEXT_ASK_BY_DEFAULT 25072
#define IDS_PAGE_INFO_BUTTON_TEXT_AUTOMATIC_BY_DEFAULT 25073
#define IDS_PAGE_INFO_BUTTON_TEXT_MUTED_BY_DEFAULT 25074
#define IDS_PAGE_INFO_BUTTON_TEXT_DETECT_IMPORTANT_CONTENT_BY_DEFAULT 25075
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_ALLOW 25076
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_BLOCK 25077
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_ASK 25078
#define IDS_PAGE_INFO_MENU_ITEM_DEFAULT_DETECT_IMPORTANT_CONTENT 25079
#define IDS_PAGE_INFO_MENU_ITEM_ALLOW 25080
#define IDS_PAGE_INFO_MENU_ITEM_BLOCK 25081
#define IDS_PAGE_INFO_MENU_ITEM_ASK 25082
#define IDS_PAGE_INFO_MENU_ITEM_DETECT_IMPORTANT_CONTENT 25083
#define IDS_PAGE_INFO_MENU_ITEM_ADS_BLOCK 25084
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED 25085
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED_ONCE_ONE_TAB 25086
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED_ONCE_MULTIPLE_TAB 25087
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED 25088
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED_IN_INCOGNITO 25089
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED_INSECURE 25090
#define IDS_PAGE_INFO_STATE_TEXT_MUTED 25091
#define IDS_PAGE_INFO_STATE_TEXT_ALLOWED_BY_DEFAULT 25092
#define IDS_PAGE_INFO_STATE_TEXT_NOT_ALLOWED_BY_DEFAULT 25093
#define IDS_PAGE_INFO_SELECTOR_TOOLTIP 25094
#define IDS_PAGE_INFO_STATE_TEXT_AR_ASK 25095
#define IDS_PAGE_INFO_STATE_TEXT_AUTOMATIC_DOWNLOADS_ASK 25096
#define IDS_PAGE_INFO_STATE_TEXT_BLUETOOTH_DEVICES_ASK 25097
#define IDS_PAGE_INFO_STATE_TEXT_CAMERA_ASK 25098
#define IDS_PAGE_INFO_STATE_TEXT_CAMERA_PAN_TILT_ZOOM_ASK 25099
#define IDS_PAGE_INFO_STATE_TEXT_CLIPBOARD_ASK 25100
#define IDS_PAGE_INFO_STATE_TEXT_FILE_SYSTEM_WRITE_ASK 25101
#define IDS_PAGE_INFO_STATE_TEXT_HID_DEVICES_ASK 25102
#define IDS_PAGE_INFO_STATE_TEXT_LOCATION_ASK 25103
#define IDS_PAGE_INFO_STATE_TEXT_MIC_ASK 25104
#define IDS_PAGE_INFO_STATE_TEXT_MIDI_ASK 25105
#define IDS_PAGE_INFO_STATE_TEXT_NOTIFICATIONS_ASK 25106
#define IDS_PAGE_INFO_STATE_TEXT_USB_ASK 25107
#define IDS_PAGE_INFO_STATE_TEXT_VR_ASK 25108
#define IDS_PAGE_INFO_STATE_TEXT_FONT_ACCESS_ASK 25109
#define IDS_PAGE_INFO_STATE_TEXT_SERIAL_ASK 25110
#define IDS_PAGE_INFO_STATE_TEXT_IDLE_DETECTION_ASK 25111
#define IDS_PAGE_INFO_STATE_TEXT_WINDOW_PLACEMENT_ASK 25112
#define IDS_PAGE_INFO_STATE_TEXT_BLUETOOTH_SCANNING_ASK 25113
#define IDS_PAGE_INFO_USB_DEVICE_SECONDARY_LABEL 25114
#define IDS_PAGE_INFO_USB_DEVICE_ALLOWED_BY_POLICY_LABEL 25115
#define IDS_PAGE_INFO_DELETE_USB_DEVICE 25116
#define IDS_PAGE_INFO_SERIAL_PORT_SECONDARY_LABEL 25117
#define IDS_PAGE_INFO_SERIAL_PORT_ALLOWED_BY_POLICY_LABEL 25118
#define IDS_PAGE_INFO_DELETE_SERIAL_PORT 25119
#define IDS_PAGE_INFO_BLUETOOTH_DEVICE_SECONDARY_LABEL 25120
#define IDS_PAGE_INFO_DELETE_BLUETOOTH_DEVICE 25121
#define IDS_PAGE_INFO_HID_DEVICE_SECONDARY_LABEL 25122
#define IDS_PAGE_INFO_HID_DEVICE_ALLOWED_BY_POLICY_LABEL 25123
#define IDS_PAGE_INFO_DELETE_HID_DEVICE 25124
#define IDS_PAGE_INFO_SITE_SETTINGS_LINK 25125
#define IDS_PAGE_INFO_SITE_SETTINGS_TOOLTIP 25126
#define IDS_PAGE_INFO_PERMISSION_ALLOWED_BY_POLICY 25127
#define IDS_PAGE_INFO_PERMISSION_BLOCKED_BY_POLICY 25128
#define IDS_PAGE_INFO_PERMISSION_ASK_BY_POLICY 25129
#define IDS_PAGE_INFO_PERMISSION_ALLOWED_BY_EXTENSION 25130
#define IDS_PAGE_INFO_PERMISSION_BLOCKED_BY_EXTENSION 25131
#define IDS_PAGE_INFO_PERMISSION_ASK_BY_EXTENSION 25132
#define IDS_PAGE_INFO_PERMISSION_AUTOMATICALLY_BLOCKED 25133
#define IDS_PAGE_INFO_PERMISSION_ADS_SUBTITLE 25134
#define IDS_PAGE_INFO_PERMISSION_MANAGED_BY_POLICY 25135
#define IDS_PAGE_INFO_PERMISSION_MANAGED_BY_EXTENSION 25136
#define IDS_PAGE_INFO_INFOBAR_TEXT 25137
#define IDS_PAGE_INFO_INFOBAR_BUTTON 25138
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS 25139
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_SYNC 25140
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_SIGNED_IN_NON_SYNC 25141
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_ENTERPRISE 25142
#define IDS_PAGE_INFO_CHANGE_PASSWORD_DETAILS_ENTERPRISE_WITH_ORG_NAME 25143
#define IDS_PAGE_INFO_CHANGE_PASSWORD_BUTTON 25144
#define IDS_PAGE_INFO_PROTECT_ACCOUNT_BUTTON 25145
#define IDS_PAGE_INFO_IGNORE_PASSWORD_WARNING_BUTTON 25146
#define IDS_PAGE_INFO_ALLOWLIST_PASSWORD_REUSE_BUTTON 25147
#define IDS_PAGE_INFO_BILLING_SUMMARY 25148
#define IDS_PAGE_INFO_BILLING_DETAILS 25149
#define IDS_PAGE_INFO_VR_PRESENTING_TEXT 25150
#define IDS_PAGE_INFO_VR_TURN_OFF_BUTTON_TEXT 25151
#define IDS_CERT_INFO_SUBJECT_GROUP 25152
#define IDS_CERT_INFO_ISSUER_GROUP 25153
#define IDS_CERT_INFO_COMMON_NAME_LABEL 25154
#define IDS_CERT_INFO_ORGANIZATION_LABEL 25155
#define IDS_CERT_INFO_ORGANIZATIONAL_UNIT_LABEL 25156
#define IDS_CERT_INFO_SERIAL_NUMBER_LABEL 25157
#define IDS_CERT_INFO_VALIDITY_GROUP 25158
#define IDS_CERT_INFO_ISSUED_ON_LABEL 25159
#define IDS_CERT_INFO_EXPIRES_ON_LABEL 25160
#define IDS_CERT_INFO_FINGERPRINTS_GROUP 25161
#define IDS_CERT_INFO_SHA256_FINGERPRINT_LABEL 25162
#define IDS_CERT_INFO_SHA1_FINGERPRINT_LABEL 25163
#define IDS_CERT_DETAILS_EXTENSIONS 25164
#define IDS_CERT_X509_SUBJECT_ALT_NAME 25165
#define IDS_PAGE_INFO_SECURITY_SUBPAGE_BUTTON 25166
#define IDS_PAGE_INFO_SECURITY_SUBPAGE_HEADER 25167
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_BUTTON_TOOLTIP 25168
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_MANAGE_BUTTON 25169
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_MANAGE_BUTTON_TOOLTIP 25170
#define IDS_PAGE_INFO_PERMISSIONS_SUBPAGE_REMEMBER_THIS_SETTING 25171
#define IDS_PAGE_INFO_RESET_PERMISSIONS 25172
#define IDS_PAGE_INFO_ABOUT_THIS_SITE_HEADER 25173
#define IDS_PAGE_INFO_ABOUT_THIS_SITE_TOOLTIP 25174
#define IDS_PAGE_INFO_ABOUT_THIS_SITE_SUBPAGE_FROM_LABEL 25175
#define IDS_PAGE_INFO_MORE_ABOUT_THIS_PAGE 25176
#define IDS_PAGE_INFO_MORE_ABOUT_THIS_PAGE_TOOLTIP 25177
#define IDS_PAGE_INFO_HISTORY 25178
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_TODAY 25179
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_YESTERDAY 25180
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_DAYS 25181
#define IDS_PAGE_INFO_HISTORY_LAST_VISIT_DATE 25182
#define IDS_PAGE_INFO_AD_PERSONALIZATION_HEADER 25183
#define IDS_PAGE_INFO_AD_PERSONALIZATION_TOOLTIP 25184
#define IDS_PAGE_INFO_AD_PERSONALIZATION_TOPICS_AND_INTEREST_GROUP_DESCRIPTION 25185
#define IDS_PAGE_INFO_AD_PERSONALIZATION_TOPICS_DESCRIPTION 25186
#define IDS_PAGE_INFO_AD_PERSONALIZATION_INTEREST_GROUP_DESCRIPTION 25187
#define IDS_PAGE_INFO_AD_PERSONALIZATION_SUBPAGE_MANAGE_BUTTON 25188
#define IDS_PAINT_PREVIEW_COMPOSITOR_SERVICE_DISPLAY_NAME 25189
#define IDS_LEAK_CHECK_CREDENTIALS 25190
#define IDS_CREDENTIAL_LEAK_CHANGE_AUTOMATICALLY 25191
#define IDS_CREDENTIAL_LEAK_TITLE_CHANGE 25192
#define IDS_CREDENTIAL_LEAK_TITLE_CHANGE_AUTOMATICALLY 25193
#define IDS_CREDENTIAL_LEAK_TITLE_CHECK 25194
#define IDS_CREDENTIAL_LEAK_TITLE_CHECK_GPM 25195
#define IDS_CREDENTIAL_LEAK_CHECK_PASSWORDS_MESSAGE 25196
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_MESSAGE 25197
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_AUTOMATICALLY_MESSAGE 25198
#define IDS_CREDENTIAL_LEAK_CHANGE_AND_CHECK_PASSWORDS_MESSAGE 25199
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_AUTOMATICALLY_MESSAGE_GPM 25200
#define IDS_CREDENTIAL_LEAK_CHECK_PASSWORDS_MESSAGE_GPM_BRANDED 25201
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_MESSAGE_GPM_BRANDED 25202
#define IDS_CREDENTIAL_LEAK_CHANGE_AND_CHECK_PASSWORDS_MESSAGE_GPM_BRANDED 25203
#define IDS_CREDENTIAL_LEAK_CHECK_PASSWORDS_MESSAGE_GPM_NON_BRANDED 25204
#define IDS_CREDENTIAL_LEAK_CHANGE_PASSWORD_MESSAGE_GPM_NON_BRANDED 25205
#define IDS_CREDENTIAL_LEAK_CHANGE_AND_CHECK_PASSWORDS_MESSAGE_GPM_NON_BRANDED 25206
#define IDS_PASSWORD_MANAGER_LEAK_HELP_MESSAGE 25208
#define IDS_MANAGE_PASSWORDS_AUTO_SIGNIN_TITLE 25209
#define IDS_PASSWORD_MANAGER_EMPTY_LOGIN 25210
#define IDS_PASSWORD_MANAGER_OPT_INTO_ACCOUNT_STORE 25211
#define IDS_PASSWORD_MANAGER_RE_SIGNIN_ACCOUNT_STORE 25212
#define IDS_PASSWORD_MANAGER_NO_ACCOUNT_STORE_MATCHES 25213
#define IDS_PASSWORD_MANAGER_MANAGE_PASSWORDS 25214
#define IDS_PASSWORD_MANAGER_GENERATE_PASSWORD 25215
#define IDS_PASSWORD_MANAGER_EXCEPTIONS_TAB_TITLE 25216
#define IDS_PASSWORD_MANAGER_SHOW_PASSWORDS_TAB_TITLE 25217
#define IDS_PASSWORD_MANAGER_SMART_LOCK 25218
#define IDS_PASSWORD_MANAGER_DEFAULT_EXPORT_FILENAME 25219
#define IDS_PASSWORD_MANAGER_PASSWORD_FOR_ACCOUNT 25220
#define IDS_PAYMENTS_TITLE 25221
#define IDS_PAYMENTS_ERROR_MESSAGE_DIALOG_TITLE 25222
#define IDS_PAYMENTS_METHOD_OF_PAYMENT_LABEL 25223
#define IDS_PAYMENTS_CONTACT_DETAILS_LABEL 25224
#define IDS_PAYMENTS_ADD_CONTACT_DETAILS_LABEL 25225
#define IDS_PAYMENTS_EDIT_CONTACT_DETAILS_LABEL 25226
#define IDS_PAYMENTS_ADD_CARD_LABEL 25227
#define IDS_PAYMENTS_ADD_BILLING_ADDRESS 25228
#define IDS_PAYMENTS_ADD_NAME_ON_CARD 25229
#define IDS_PAYMENTS_ADD_VALID_CARD_NUMBER 25230
#define IDS_PAYMENTS_ADD_MORE_INFORMATION 25231
#define IDS_PAYMENTS_EDIT_CARD 25232
#define IDS_PAYMENTS_ADD_PHONE_NUMBER 25233
#define IDS_PAYMENTS_ADD_RECIPIENT 25234
#define IDS_PAYMENTS_ADD_VALID_ADDRESS 25235
#define IDS_PAYMENTS_ADD_EMAIL 25236
#define IDS_PAYMENTS_ADD_NAME 25237
#define IDS_PAYMENTS_ORDER_SUMMARY_LABEL 25238
#define IDS_PAYMENT_REQUEST_PAYMENT_METHOD_SECTION_NAME 25239
#define IDS_PAYMENT_REQUEST_CONTACT_INFO_SECTION_NAME 25240
#define IDS_PAYMENTS_SHIPPING_SUMMARY_LABEL 25241
#define IDS_PAYMENTS_SHIPPING_ADDRESS_LABEL 25242
#define IDS_PAYMENTS_SHIPPING_OPTION_LABEL 25243
#define IDS_PAYMENTS_DELIVERY_SUMMARY_LABEL 25244
#define IDS_PAYMENTS_DELIVERY_ADDRESS_LABEL 25245
#define IDS_PAYMENTS_DELIVERY_OPTION_LABEL 25246
#define IDS_PAYMENTS_PICKUP_SUMMARY_LABEL 25247
#define IDS_PAYMENTS_PICKUP_ADDRESS_LABEL 25248
#define IDS_PAYMENTS_PICKUP_OPTION_LABEL 25249
#define IDS_PAYMENTS_EDIT_BUTTON 25250
#define IDS_PAYMENTS_PAY_BUTTON 25251
#define IDS_PAYMENTS_CONTINUE_BUTTON 25252
#define IDS_PAYMENTS_ADD_CONTACT 25253
#define IDS_PAYMENTS_ADD_CARD 25254
#define IDS_PAYMENTS_ADD_ADDRESS 25255
#define IDS_PAYMENTS_EDIT_ADDRESS 25256
#define IDS_PAYMENTS_CANCEL_PAYMENT 25257
#define IDS_PAYMENTS_NAME_FIELD_IN_CONTACT_DETAILS 25258
#define IDS_PAYMENTS_PHONE_FIELD_IN_CONTACT_DETAILS 25259
#define IDS_PAYMENTS_EMAIL_FIELD_IN_CONTACT_DETAILS 25260
#define IDS_PAYMENTS_SAVE_CARD_TO_DEVICE_CHECKBOX 25261
#define IDS_PAYMENTS_ACCEPTED_CARDS_LABEL 25262
#define IDS_PAYMENTS_CREDIT_CARD_EXPIRATION_DATE_ABBR 25263
#define IDS_PAYMENTS_LOADING_MESSAGE 25264
#define IDS_PAYMENTS_PROCESSING_MESSAGE 25265
#define IDS_PAYMENTS_CHECKING_OPTION 25266
#define IDS_PAYMENTS_UPDATED_LABEL 25267
#define IDS_PAYMENTS_ERROR_MESSAGE 25269
#define IDS_PAYMENTS_CARD_AND_ADDRESS_SETTINGS 25270
#define IDS_PAYMENTS_CARD_AND_ADDRESS_SETTINGS_SIGNED_IN 25271
#define IDS_PAYMENTS_CARD_AND_ADDRESS_SETTINGS_SIGNED_OUT 25272
#define IDS_SETTINGS_CAN_MAKE_PAYMENT_TOGGLE_LABEL 25273
#define IDS_PAYMENTS_REQUIRED_FIELD_MESSAGE 25292
#define IDS_PAYMENTS_VALIDATION_INVALID_NAME 25293
#define IDS_PAYMENTS_VALIDATION_INVALID_CREDIT_CARD_EXPIRATION_YEAR 25294
#define IDS_PAYMENTS_VALIDATION_INVALID_CREDIT_CARD_EXPIRATION_MONTH 25295
#define IDS_PAYMENTS_VALIDATION_INVALID_CREDIT_CARD_EXPIRED 25296
#define IDS_PAYMENTS_VALIDATION_UNSUPPORTED_CREDIT_CARD_TYPE 25297
#define IDS_PAYMENTS_PHONE_INVALID_VALIDATION_MESSAGE 25298
#define IDS_PAYMENTS_EMAIL_INVALID_VALIDATION_MESSAGE 25299
#define IDS_PAYMENTS_CARD_NUMBER_INVALID_VALIDATION_MESSAGE 25300
#define IDS_PAYMENTS_CARD_EXPIRATION_INVALID_VALIDATION_MESSAGE 25301
#define IDS_PAYMENTS_INVALID_ADDRESS 25302
#define IDS_PAYMENTS_BILLING_ADDRESS_REQUIRED 25303
#define IDS_PAYMENTS_NAME_ON_CARD_REQUIRED 25304
#define IDS_PAYMENTS_CARD_BILLING_ADDRESS_REQUIRED 25305
#define IDS_PAYMENTS_MORE_INFORMATION_REQUIRED 25306
#define IDS_PAYMENTS_PHONE_NUMBER_REQUIRED 25307
#define IDS_PAYMENTS_RECIPIENT_REQUIRED 25308
#define IDS_PAYMENTS_EMAIL_REQUIRED 25309
#define IDS_PAYMENTS_NAME_REQUIRED 25310
#define IDS_PREF_EDIT_DIALOG_FIELD_REQUIRED_VALIDATION_MESSAGE 25311
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_SECTION_TOTAL_FORMAT 25312
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_SHEET_TOTAL_FORMAT 25313
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_MORE_ITEMS 25314
#define IDS_PAYMENT_REQUEST_ORDER_SUMMARY_MULTIPLE_CURRENCY_INDICATOR 25315
#define IDS_PAYMENTS_SELECT_SHIPPING_ADDRESS_FOR_SHIPPING_METHODS 25316
#define IDS_PAYMENTS_UNSUPPORTED_SHIPPING_ADDRESS 25317
#define IDS_PAYMENTS_UNSUPPORTED_SHIPPING_OPTION 25318
#define IDS_PAYMENTS_SELECT_DELIVERY_ADDRESS_FOR_DELIVERY_METHODS 25319
#define IDS_PAYMENTS_UNSUPPORTED_DELIVERY_ADDRESS 25320
#define IDS_PAYMENTS_UNSUPPORTED_DELIVERY_OPTION 25321
#define IDS_PAYMENTS_SELECT_PICKUP_ADDRESS_FOR_PICKUP_METHODS 25322
#define IDS_PAYMENTS_UNSUPPORTED_PICKUP_ADDRESS 25323
#define IDS_PAYMENTS_UNSUPPORTED_PICKUP_OPTION 25324
#define IDS_PAYMENTS_ANDROID_APP_ERROR 25325
#define IDS_UTILITY_PROCESS_PAYMENT_MANIFEST_PARSER_NAME 25326
#define IDS_PAYMENT_REQUEST_PAYMENT_METHODS_PREVIEW 25328
#define IDS_PAYMENT_REQUEST_SHIPPING_ADDRESSES_PREVIEW 25329
#define IDS_PAYMENT_REQUEST_SHIPPING_OPTIONS_PREVIEW 25330
#define IDS_PAYMENT_REQUEST_CONTACTS_PREVIEW 25331
#define IDS_PAYMENTS_BACK 25332
#define IDS_PAYMENTS_EDIT 25333
#define IDS_PAYMENTS_ROW_ACCESSIBLE_NAME_FORMAT 25334
#define IDS_PAYMENTS_ROW_ACCESSIBLE_NAME_SELECTED_FORMAT 25335
#define IDS_PAYMENTS_PROFILE_LABELS_ACCESSIBLE_FORMAT 25336
#define IDS_PAYMENTS_ACCESSIBLE_LABEL_WITH_ERROR 25337
#define IDS_PAYMENTS_ORDER_SUMMARY_ACCESSIBLE_LABEL 25338
#define IDS_PAYMENT_HANDLER_SHEET_DESCRIPTION 25339
#define IDS_PAYMENT_HANDLER_SHEET_OPENED_HALF 25340
#define IDS_PAYMENT_HANDLER_SHEET_OPENED_FULL 25341
#define IDS_PAYMENT_HANDLER_SHEET_CLOSED 25342
#define IDS_SECURE_PAYMENT_CONFIRMATION_VERIFY_PURCHASE 25343
#define IDS_SECURE_PAYMENT_CONFIRMATION_STORE_LABEL 25344
#define IDS_SECURE_PAYMENT_CONFIRMATION_TOTAL_LABEL 25345
#define IDS_SECURE_PAYMENT_CONFIRMATION_VERIFY_BUTTON_LABEL 25346
#define IDS_NO_MATCHING_CREDENTIAL_DESCRIPTION 25347
#define IDS_PDF_DOWNLOAD_ORIGINAL 25354
#define IDS_PDF_DOWNLOAD_EDITED 25355
#define IDS_PDF_PRESENT 25356
#define IDS_PDF_NEED_PASSWORD 25357
#define IDS_PDF_PASSWORD_DIALOG_TITLE 25358
#define IDS_PDF_PASSWORD_SUBMIT 25359
#define IDS_PDF_PASSWORD_INVALID 25360
#define IDS_PDF_PAGE_LOADING 25361
#define IDS_PDF_ERROR_DIALOG_TITLE 25362
#define IDS_PDF_PAGE_LOAD_FAILED 25363
#define IDS_PDF_PAGE_RELOAD_BUTTON 25364
#define IDS_PDF_BOOKMARKS 25365
#define IDS_PDF_BOOKMARK_EXPAND_ICON_ARIA_LABEL 25366
#define IDS_PDF_PROPERTIES_DIALOG_TITLE 25367
#define IDS_PDF_PROPERTIES_FILE_NAME 25368
#define IDS_PDF_PROPERTIES_FILE_SIZE 25369
#define IDS_PDF_PROPERTIES_TITLE 25370
#define IDS_PDF_PROPERTIES_AUTHOR 25371
#define IDS_PDF_PROPERTIES_SUBJECT 25372
#define IDS_PDF_PROPERTIES_KEYWORDS 25373
#define IDS_PDF_PROPERTIES_CREATED 25374
#define IDS_PDF_PROPERTIES_MODIFIED 25375
#define IDS_PDF_PROPERTIES_APPLICATION 25376
#define IDS_PDF_PROPERTIES_PDF_PRODUCER 25377
#define IDS_PDF_PROPERTIES_PDF_VERSION 25378
#define IDS_PDF_PROPERTIES_PAGE_COUNT 25379
#define IDS_PDF_PROPERTIES_PAGE_SIZE 25380
#define IDS_PDF_PROPERTIES_PAGE_SIZE_VALUE_INCH 25381
#define IDS_PDF_PROPERTIES_PAGE_SIZE_VALUE_MM 25382
#define IDS_PDF_PROPERTIES_PAGE_SIZE_PORTRAIT 25383
#define IDS_PDF_PROPERTIES_PAGE_SIZE_LANDSCAPE 25384
#define IDS_PDF_PROPERTIES_PAGE_SIZE_SQUARE 25385
#define IDS_PDF_PROPERTIES_PAGE_SIZE_VARIABLE 25386
#define IDS_PDF_PROPERTIES_FAST_WEB_VIEW 25387
#define IDS_PDF_PROPERTIES_FAST_WEB_VIEW_NO 25388
#define IDS_PDF_PROPERTIES_FAST_WEB_VIEW_YES 25389
#define IDS_PDF_TOOLTIP_ROTATE_CCW 25390
#define IDS_PDF_TOOLTIP_DOWNLOAD 25391
#define IDS_PDF_TOOLTIP_PRINT 25392
#define IDS_PDF_TOOLTIP_FIT_PAGE 25393
#define IDS_PDF_TOOLTIP_FIT_WIDTH 25394
#define IDS_PDF_TWO_UP_VIEW_ENABLE 25395
#define IDS_PDF_ANNOTATIONS_SHOW_TOGGLE 25396
#define IDS_PDF_ZOOM_TEXT_INPUT_ARIA_LABEL 25397
#define IDS_PDF_TOOLTIP_ZOOM_IN 25398
#define IDS_PDF_TOOLTIP_ZOOM_OUT 25399
#define IDS_PDF_TOOLTIP_THUMBNAILS 25400
#define IDS_PDF_TOOLTIP_DOCUMENT_OUTLINE 25401
#define IDS_PDF_LABEL_PAGE_NUMBER 25402
#define IDS_PDF_PAGE_INDEX 25403
#define IDS_PDF_DOCUMENT_PAGE_COUNT 25404
#define IDS_PDF_THUMBNAIL_PAGE_ARIA_LABEL 25405
#define IDS_AX_ROLE_DESCRIPTION_PDF_HIGHLIGHT 25457
#define IDS_AX_ROLE_DESCRIPTION_PDF_POPUP_NOTE 25458
#define IDS_GEOLOCATION_INFOBAR_TEXT 25459
#define IDS_PROTECTED_MEDIA_IDENTIFIER_PERMISSION_FRAGMENT 25483
#define IDS_PROTECTED_MEDIA_IDENTIFIER_PER_ORIGIN_PROVISIONING_INFOBAR_TEXT 25484
#define IDS_PROTECTED_MEDIA_IDENTIFIER_PER_DEVICE_PROVISIONING_INFOBAR_TEXT 25485
#define IDS_GEOLOCATION_INFOBAR_PERMISSION_FRAGMENT 25486
#define IDS_NOTIFICATION_PERMISSIONS_FRAGMENT 25487
#define IDS_MIDI_SYSEX_PERMISSION_FRAGMENT 25488
#define IDS_MEDIA_CAPTURE_AUDIO_ONLY_PERMISSION_FRAGMENT 25489
#define IDS_MEDIA_CAPTURE_VIDEO_ONLY_PERMISSION_FRAGMENT 25490
#define IDS_MEDIA_CAPTURE_CAMERA_PAN_TILT_ZOOM_PERMISSION_FRAGMENT 25491
#define IDS_ACCESSIBILITY_EVENTS_PERMISSION_FRAGMENT 25492
#define IDS_CLIPBOARD_PERMISSION_FRAGMENT 25493
#define IDS_VR_PERMISSION_FRAGMENT 25494
#define IDS_AR_PERMISSION_FRAGMENT 25495
#define IDS_STORAGE_ACCESS_PERMISSION_FRAGMENT 25496
#define IDS_STORAGE_ACCESS_PERMISSION_EXPLANATION 25497
#define IDS_WINDOW_PLACEMENT_PERMISSION_FRAGMENT 25498
#define IDS_FONT_ACCESS_PERMISSION_FRAGMENT 25499
#define IDS_IDLE_DETECTION_PERMISSION_FRAGMENT 25500
#define IDS_MULTI_DOWNLOAD_PERMISSION_FRAGMENT 25501
#define IDS_SECURITY_KEY_ATTESTATION_PERMISSION_FRAGMENT 25502
#define IDS_U2F_API_PERMISSION_FRAGMENT 25503
#define IDS_U2F_API_PERMISSION_EXPLANATION 25504
#define IDS_PERMISSION_ALLOW 25505
#define IDS_PERMISSION_DENY 25506
#define IDS_PERMISSION_ALLOW_ONCE 25507
#define IDS_PERMISSION_ALLOW_ALWAYS 25508
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ONE_TIME 25509
#define IDS_BLUETOOTH_DEVICE_CHOOSER_PROMPT_ORIGIN 25510
#define IDS_BLUETOOTH_DEVICE_CHOOSER_NO_DEVICES_FOUND_PROMPT 25511
#define IDS_BLUETOOTH_DEVICE_CHOOSER_PAIR_BUTTON_TEXT 25512
#define IDS_BLUETOOTH_DEVICE_CHOOSER_SCANNING_LABEL 25513
#define IDS_BLUETOOTH_DEVICE_CHOOSER_SCANNING_LABEL_TOOLTIP 25514
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_WITH_ID 25515
#define IDS_BLUETOOTH_SCANNING_PROMPT_ORIGIN 25516
#define IDS_BLUETOOTH_SCANNING_DEVICE_UNKNOWN 25517
#define IDS_BLUETOOTH_SCANNING_PROMPT_NO_DEVICES_FOUND_PROMPT 25518
#define IDS_BLUETOOTH_SCANNING_PROMPT_ALLOW_BUTTON_TEXT 25519
#define IDS_BLUETOOTH_SCANNING_PROMPT_BLOCK_BUTTON_TEXT 25520
#define IDS_DEVICE_CHOOSER_CANCEL_BUTTON_TEXT 25521
#define IDS_DEVICE_CHOOSER_NO_DEVICES_FOUND_PROMPT 25522
#define IDS_USB_DEVICE_CHOOSER_PROMPT_ORIGIN 25523
#define IDS_USB_DEVICE_CHOOSER_CONNECT_BUTTON_TEXT 25524
#define IDS_USB_DEVICE_CHOOSER_LOADING_LABEL 25525
#define IDS_USB_DEVICE_CHOOSER_LOADING_LABEL_TOOLTIP 25526
#define IDS_GEOLOCATION_PERMISSION_CHIP 25527
#define IDS_NOTIFICATION_PERMISSIONS_CHIP 25528
#define IDS_MIDI_SYSEX_PERMISSION_CHIP 25529
#define IDS_MEDIA_CAPTURE_AUDIO_ONLY_PERMISSION_CHIP 25530
#define IDS_MEDIA_CAPTURE_VIDEO_ONLY_PERMISSION_CHIP 25531
#define IDS_MEDIA_CAPTURE_VIDEO_AND_AUDIO_PERMISSION_CHIP 25532
#define IDS_CLIPBOARD_PERMISSION_CHIP 25533
#define IDS_VR_PERMISSION_CHIP 25534
#define IDS_AR_PERMISSION_CHIP 25535
#define IDS_IDLE_DETECTION_PERMISSION_CHIP 25536
#define IDS_GEOLOCATION_PERMISSION_BLOCKED_CHIP 25537
#define IDS_NOTIFICATION_PERMISSIONS_BLOCKED_CHIP 25538
#define IDS_REQUEST_QUOTA_INFOBAR_TEXT 25539
#define IDS_REQUEST_LARGE_QUOTA_INFOBAR_TEXT 25540
#define IDS_REQUEST_QUOTA_PERMISSION_FRAGMENT 25541
#define IDS_POLICY_DM_STATUS_SUCCESS 25542
#define IDS_POLICY_DM_STATUS_REQUEST_INVALID 25543
#define IDS_POLICY_DM_STATUS_REQUEST_FAILED 25544
#define IDS_POLICY_DM_STATUS_TEMPORARY_UNAVAILABLE 25545
#define IDS_POLICY_DM_STATUS_HTTP_STATUS_ERROR 25546
#define IDS_POLICY_DM_STATUS_RESPONSE_DECODING_ERROR 25547
#define IDS_POLICY_DM_STATUS_SERVICE_MANAGEMENT_NOT_SUPPORTED 25548
#define IDS_POLICY_DM_STATUS_SERVICE_DEVICE_NOT_FOUND 25549
#define IDS_POLICY_DM_STATUS_SERVICE_MANAGEMENT_TOKEN_INVALID 25550
#define IDS_POLICY_DM_STATUS_SERVICE_ACTIVATION_PENDING 25551
#define IDS_POLICY_DM_STATUS_SERVICE_INVALID_SERIAL_NUMBER 25552
#define IDS_POLICY_DM_STATUS_SERVICE_DEVICE_ID_CONFLICT 25553
#define IDS_POLICY_DM_STATUS_SERVICE_MISSING_LICENSES 25554
#define IDS_POLICY_DM_STATUS_SERVICE_DEPROVISIONED 25555
#define IDS_POLICY_DM_STATUS_SERVICE_POLICY_NOT_FOUND 25556
#define IDS_POLICY_DM_STATUS_UNKNOWN_ERROR 25557
#define IDS_POLICY_DM_STATUS_SERVICE_DOMAIN_MISMATCH 25558
#define IDS_POLICY_DM_STATUS_CANNOT_SIGN_REQUEST 25559
#define IDS_POLICY_DM_STATUS_REQUEST_TOO_LARGE 25560
#define IDS_POLICY_DM_STATUS_SERVICE_TOO_MANY_REQUESTS 25561
#define IDS_POLICY_DM_STATUS_CONSUMER_ACCOUNT_WITH_PACKAGED_LICENSE 25562
#define IDS_POLICY_DM_STATUS_ENTERPRISE_ACCOUNT_IS_NOT_ELIGIBLE_TO_ENROLL 25563
#define IDS_POLICY_VALIDATION_OK 25564
#define IDS_POLICY_VALIDATION_BAD_INITIAL_SIGNATURE 25565
#define IDS_POLICY_VALIDATION_BAD_SIGNATURE 25566
#define IDS_POLICY_VALIDATION_ERROR_CODE_PRESENT 25567
#define IDS_POLICY_VALIDATION_PAYLOAD_PARSE_ERROR 25568
#define IDS_POLICY_VALIDATION_WRONG_POLICY_TYPE 25569
#define IDS_POLICY_VALIDATION_WRONG_SETTINGS_ENTITY_ID 25570
#define IDS_POLICY_VALIDATION_BAD_TIMESTAMP 25571
#define IDS_POLICY_VALIDATION_BAD_DM_TOKEN 25572
#define IDS_POLICY_VALIDATION_BAD_DEVICE_ID 25573
#define IDS_POLICY_VALIDATION_BAD_USER 25574
#define IDS_POLICY_VALIDATION_POLICY_PARSE_ERROR 25575
#define IDS_POLICY_VALIDATION_BAD_KEY_VERIFICATION_SIGNATURE 25576
#define IDS_POLICY_VALIDATION_VALUE_WARNING 25577
#define IDS_POLICY_VALIDATION_VALUE_ERROR 25578
#define IDS_POLICY_VALIDATION_UNKNOWN_ERROR 25579
#define IDS_POLICY_STORE_STATUS_OK 25580
#define IDS_POLICY_STORE_STATUS_LOAD_ERROR 25581
#define IDS_POLICY_STORE_STATUS_STORE_ERROR 25582
#define IDS_POLICY_STORE_STATUS_PARSE_ERROR 25583
#define IDS_POLICY_STORE_STATUS_SERIALIZE_ERROR 25584
#define IDS_POLICY_STORE_STATUS_VALIDATION_ERROR 25585
#define IDS_POLICY_STORE_STATUS_BAD_STATE 25586
#define IDS_POLICY_STORE_STATUS_UNKNOWN_ERROR 25587
#define IDS_POLICY_ASSOCIATION_STATE_ACTIVE 25588
#define IDS_POLICY_ASSOCIATION_STATE_UNMANAGED 25589
#define IDS_POLICY_ASSOCIATION_STATE_DEPROVISIONED 25590
#define IDS_POLICY_TYPE_ERROR 25591
#define IDS_POLICY_OUT_OF_RANGE_ERROR 25592
#define IDS_POLICY_VALUE_FORMAT_ERROR 25593
#define IDS_POLICY_CLOUD_SOURCE_ONLY_ERROR 25594
#define IDS_POLICY_CLOUD_USER_ONLY_ERROR 25595
#define IDS_POLICY_CLOUD_MANAGEMENT_ENROLLMENT_ONLY_ERROR 25596
#define IDS_POLICY_DEFAULT_SEARCH_DISABLED 25597
#define IDS_POLICY_NOT_SPECIFIED_ERROR 25598
#define IDS_POLICY_EXTENSION_SETTINGS_ORIGIN_LIMIT_WARNING 25599
#define IDS_POLICY_URL_ALLOW_BLOCK_LIST_MAX_FILTERS_LIMIT_WARNING 25600
#define IDS_POLICY_SUBKEY_ERROR 25601
#define IDS_POLICY_LIST_ENTRY_ERROR 25602
#define IDS_POLICY_SCHEMA_VALIDATION_ERROR 25603
#define IDS_POLICY_INVALID_JSON_ERROR 25604
#define IDS_POLICY_INVALID_SEARCH_URL_ERROR 25605
#define IDS_POLICY_INVALID_SECURE_DNS_MODE_ERROR 25606
#define IDS_POLICY_SECURE_DNS_TEMPLATES_INVALID_ERROR 25607
#define IDS_POLICY_SECURE_DNS_TEMPLATES_IRRELEVANT_MODE_ERROR 25608
#define IDS_POLICY_SECURE_DNS_TEMPLATES_INVALID_MODE_ERROR 25609
#define IDS_POLICY_SECURE_DNS_TEMPLATES_UNSET_MODE_ERROR 25610
#define IDS_POLICY_SECURE_DNS_TEMPLATES_NOT_SPECIFIED_ERROR 25611
#define IDS_POLICY_INVALID_PROXY_MODE_ERROR 25612
#define IDS_POLICY_INVALID_UPDATE_URL_ERROR 25613
#define IDS_POLICY_OFF_CWS_URL_ERROR 25615
#define IDS_POLICY_HOMEPAGE_LOCATION_ERROR 25616
#define IDS_POLICY_PROXY_MODE_DISABLED_ERROR 25617
#define IDS_POLICY_PROXY_MODE_AUTO_DETECT_ERROR 25618
#define IDS_POLICY_PROXY_MODE_PAC_URL_ERROR 25619
#define IDS_POLICY_PROXY_MODE_FIXED_SERVERS_ERROR 25620
#define IDS_POLICY_PROXY_MODE_SYSTEM_ERROR 25621
#define IDS_POLICY_PROXY_BOTH_SPECIFIED_ERROR 25622
#define IDS_POLICY_PROXY_NEITHER_SPECIFIED_ERROR 25623
#define IDS_POLICY_OVERRIDDEN 147
#define IDS_POLICY_VALUE_DEPRECATED 25624
#define IDS_POLICY_DEPENDENCY_ERROR 25625
#define IDS_POLICY_USER_IS_NOT_AFFILIATED_ERROR 25626
#define IDS_POLICY_LEVEL_ERROR 25631
#define IDS_POLICY_OK 25632
#define IDS_POLICY_UNSET 25633
#define IDS_POLICY_UNKNOWN 25634
#define IDS_POLICY_PROTO_PARSING_ERROR 25635
#define IDS_POLICY_HEX_COLOR_ERROR 25636
#define IDS_POLICY_TITLE 25637
#define IDS_POLICY_FILTER_PLACEHOLDER 25638
#define IDS_POLICY_RELOAD_POLICIES 25639
#define IDS_POLICY_LOADING_POLICIES 25640
#define IDS_POLICY_LOAD_POLICIES_DONE 25641
#define IDS_EXPORT_POLICIES_JSON 25642
#define IDS_POLICY_STATUS 25643
#define IDS_POLICY_STATUS_DEVICE 25644
#define IDS_POLICY_STATUS_USER 25645
#define IDS_POLICY_STATUS_MACHINE 25646
#define IDS_POLICY_STATUS_UPDATER 25647
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_DOMAIN 25648
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_TOKEN 25649
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_DEVICE_ID 25650
#define IDS_POLICY_LABEL_MACHINE_ENROLLMENT_MACHINE_NAME 25651
#define IDS_POLICY_LABEL_USERNAME 25652
#define IDS_POLICY_LABEL_GAIA_ID 25653
#define IDS_POLICY_LABEL_CLIENT_ID 25654
#define IDS_POLICY_LABEL_ASSET_ID 25655
#define IDS_POLICY_LABEL_LOCATION 25656
#define IDS_POLICY_LABEL_DIRECTORY_API_ID 25657
#define IDS_POLICY_LABEL_MANAGED_BY 25658
#define IDS_POLICY_LABEL_TIME_SINCE_LAST_FETCH_ATTEMPT 25659
#define IDS_POLICY_LABEL_TIME_SINCE_LAST_REFRESH 25660
#define IDS_POLICY_LABEL_LAST_CLOUD_REPORT_SENT_TIMESTAMP 25661
#define IDS_POLICY_NOT_SPECIFIED 25662
#define IDS_POLICY_LABEL_PUSH_POLICIES 25663
#define IDS_POLICY_PUSH_POLICIES_ON 25664
#define IDS_POLICY_PUSH_POLICIES_OFF 25665
#define IDS_POLICY_NEVER_FETCHED 25666
#define IDS_POLICY_LABEL_REFRESH_INTERVAL 25667
#define IDS_POLICY_LABEL_CONFLICT 25668
#define IDS_POLICY_LABEL_SUPERSEDING 25669
#define IDS_POLICY_LABEL_CONFLICT_VALUE 25670
#define IDS_POLICY_LABEL_SUPERSEDED_VALUE 25671
#define IDS_POLICY_LABEL_ERROR 25672
#define IDS_POLICY_LABEL_DEPRECATED 25673
#define IDS_POLICY_LABEL_FUTURE 25674
#define IDS_POLICY_LABEL_IGNORED 25675
#define IDS_POLICY_LABEL_VALUE 25676
#define IDS_POLICY_LABEL_VERSION 25677
#define IDS_POLICY_LABEL_STATUS 25678
#define IDS_POLICY_LABEL_INFO 25679
#define IDS_POLICY_LABEL_PRECEDENCE 25680
#define IDS_POLICY_SHOW_UNSET 25681
#define IDS_POLICY_NO_POLICIES_SET 25682
#define IDS_POLICY_HEADER_SCOPE 25683
#define IDS_POLICY_HEADER_LEVEL 25684
#define IDS_POLICY_HEADER_NAME 25685
#define IDS_POLICY_HEADER_VALUE 25686
#define IDS_POLICY_HEADER_STATUS 25687
#define IDS_POLICY_HEADER_SOURCE 25688
#define IDS_POLICY_HEADER_WARNING 25689
#define IDS_POLICY_SHOW_MORE 25690
#define IDS_POLICY_SHOW_LESS 25691
#define IDS_POLICY_LEARN_MORE 25692
#define IDS_POLICY_SCOPE_USER 25693
#define IDS_POLICY_SCOPE_DEVICE 25694
#define IDS_POLICY_LEVEL_RECOMMENDED 25695
#define IDS_POLICY_LEVEL_MANDATORY 25696
#define IDS_POLICY_SOURCE_ENTERPRISE_DEFAULT 25697
#define IDS_POLICY_SOURCE_DEFAULT 25698
#define IDS_POLICY_SOURCE_COMMAND_LINE 25699
#define IDS_POLICY_SOURCE_CLOUD 25700
#define IDS_POLICY_SOURCE_MERGED 25701
#define IDS_POLICY_SOURCE_CLOUD_FROM_ASH 25702
#define IDS_POLICY_SOURCE_RESTRICTED_MANAGED_GUEST_SESSION_OVERRIDE 25703
#define IDS_POLICY_SOURCE_ACTIVE_DIRECTORY 25704
#define IDS_POLICY_SOURCE_PLATFORM 25705
#define IDS_POLICY_SOURCE_DEVICE_LOCAL_ACCOUNT_OVERRIDE 25706
#define IDS_POLICY_RISK_TAG_FULL_ADMIN_ACCESS 25707
#define IDS_POLICY_RISK_TAG_SYSTEM_SECURITY 25708
#define IDS_POLICY_RISK_TAG_WEBSITE_SHARING 25709
#define IDS_POLICY_RISK_TAG_ADMIN_SHARING 25710
#define IDS_POLICY_RISK_TAG_FILTERING 25711
#define IDS_POLICY_RISK_TAG_LOCAL_DATA_ACCESS 25712
#define IDS_POLICY_RISK_TAG_GOOGLE_SHARING 25713
#define IDS_POLICY_SHOW_EXPANDED_STATUS 25714
#define IDS_POLICY_HIDE_EXPANDED_STATUS 25715
#define IDS_POLICY_LIST_MERGING_WRONG_POLICY_TYPE_SPECIFIED 25716
#define IDS_POLICY_DICTIONARY_MERGING_WRONG_POLICY_TYPE_SPECIFIED 25717
#define IDS_POLICY_DICTIONARY_MERGING_POLICY_NOT_ALLOWED 25718
#define IDS_POLICY_CONFLICT_SAME_VALUE 25719
#define IDS_POLICY_CONFLICT_DIFF_VALUE 25720
#define IDS_POLICY_MIGRATED_OLD_POLICY 25721
#define IDS_POLICY_MIGRATED_NEW_POLICY 25722
#define IDS_POLICY_BLOCKED 25723
#define IDS_POLICY_INVALID 25724
#define IDS_POLICY_IGNORED_BY_GROUP_MERGING 25725
#define IDS_POLICY_INVALID_VALUE 25726
#define IDS_POLICY_IGNORED_MANDATORY_REPORTING_POLICY 25727
#define IDS_POLICY_IGNORED_CHROME_PROFILE 25728
#define IDS_POLICY_PRECEDENCE_PLATFORM_MACHINE 25729
#define IDS_POLICY_PRECEDENCE_PLATFORM_USER 25730
#define IDS_POLICY_PRECEDENCE_CLOUD_MACHINE 25731
#define IDS_POLICY_PRECEDENCE_CLOUD_USER 25732
#define IDS_POLICY_SPELLCHECK_UNKNOWN_LANGUAGE 25733
#define IDS_POLICY_SPELLCHECK_BLOCKLIST_IGNORE 25734
#define IDS_POLICY_LABEL_IS_AFFILIATED 25735
#define IDS_POLICY_IS_AFFILIATED_YES 25736
#define IDS_POLICY_IS_AFFILIATED_NO 25737
#define IDS_POLICY_LABEL_IS_OFFHOURS_ACTIVE 25738
#define IDS_POLICY_OFFHOURS_ACTIVE 25739
#define IDS_POLICY_OFFHOURS_NOT_ACTIVE 25740
#define IDS_POLICY_SIGNIN_PROFILE 25741
#define IDS_POLICY_COPY_VALUE 25742
#define IDS_COPY_POLICIES_JSON 25743
#define IDS_CHROME_URLS_DISABLED_PAGE_HEADER 25744
#define IDS_CHROME_URLS_DISABLED_PAGE_TITLE 25745
#define IDS_CHROME_URLS_DISABLED_PAGE_MESSAGE 25746
#define IDS_POLICY_DLP_CLIPBOARD_BLOCKED_ON_PASTE 25747
#define IDS_POLICY_DLP_CLIPBOARD_BLOCKED_ON_COPY_VM 25748
#define IDS_POLICY_DLP_CLIPBOARD_BLOCK_TOAST_BUTTON 25749
#define IDS_POLICY_DLP_ANDROID_APPS 25750
#define IDS_POLICY_DLP_CLIPBOARD_BLOCK_DISMISS_BUTTON 25751
#define IDS_POLICY_DLP_CLIPBOARD_WARN_ON_PASTE 25752
#define IDS_POLICY_DLP_CLIPBOARD_WARN_PROCEED_BUTTON 25753
#define IDS_POLICY_DLP_CLIPBOARD_WARN_DISMISS_BUTTON 25754
#define IDS_POLICY_DLP_CLIPBOARD_WARN_ON_COPY_VM 25755
#define IDS_POLICY_DLP_PRINTING_BLOCKED_TITLE 25756
#define IDS_POLICY_DLP_PRINTING_BLOCKED_MESSAGE 25757
#define IDS_POLICY_DLP_PRINTING_WARN_TITLE 25758
#define IDS_POLICY_DLP_PRINTING_WARN_MESSAGE 25759
#define IDS_POLICY_DLP_PRINTING_WARN_CONTINUE_BUTTON 25760
#define IDS_POLICY_DLP_PRINTING_WARN_CANCEL_BUTTON 25761
#define IDS_POLICY_DLP_SCREEN_SHARE_BLOCKED_TITLE 25762
#define IDS_POLICY_DLP_SCREEN_SHARE_BLOCKED_MESSAGE 25763
#define IDS_POLICY_DLP_SCREEN_SHARE_PAUSED_TITLE 25764
#define IDS_POLICY_DLP_SCREEN_SHARE_PAUSED_MESSAGE 25765
#define IDS_POLICY_DLP_SCREEN_SHARE_RESUMED_TITLE 25766
#define IDS_POLICY_DLP_SCREEN_SHARE_RESUMED_MESSAGE 25767
#define IDS_POLICY_DLP_SCREEN_CAPTURE_DISABLED_TITLE 25768
#define IDS_POLICY_DLP_SCREEN_CAPTURE_DISABLED_MESSAGE 25769
#define IDS_POLICY_DLP_VIDEO_CAPTURE_STOPPED_TITLE 25770
#define IDS_POLICY_DLP_VIDEO_CAPTURE_STOPPED_MESSAGE 25771
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_TITLE 25772
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_MESSAGE 25773
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_CONTINUE_BUTTON 25774
#define IDS_POLICY_DLP_SCREEN_SHARE_WARN_CANCEL_BUTTON 25775
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_TITLE 25776
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_MESSAGE 25777
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_CONTINUE_BUTTON 25778
#define IDS_POLICY_DLP_SCREEN_CAPTURE_WARN_CANCEL_BUTTON 25779
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_TITLE 25780
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_MESSAGE 25781
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_CONTINUE_BUTTON 25782
#define IDS_POLICY_DLP_VIDEO_CAPTURE_WARN_CANCEL_BUTTON 25783
#define IDS_POLICY_DLP_CLIPBOARD_BUBBLE_MESSAGE 25784
#define IDS_POLICY_DEVICE_SCHEDULED_REBOOT_TITLE 25785
#define IDS_POLICY_DEVICE_SCHEDULED_REBOOT_MESSAGE 25786
#define IDS_POLICY_REBOOT_BUTTON 25787
#define IDS_POLICY_DEVICE_SCHEDULED_REBOOT_DIALOG_MESSAGE 25788
#define IDS_REBOOT_SCHEDULED_TITLE_MINUTES 25789
#define IDS_REBOOT_SCHEDULED_TITLE_SECONDS 25790
#define IDS_POLICY_DEVICE_POST_REBOOT_TITLE 25791
#define PRINT_PREVIEW_MEDIA_ASME_F_28X40IN 25792
#define PRINT_PREVIEW_MEDIA_ISO_2A0_1189X1682MM 25793
#define PRINT_PREVIEW_MEDIA_ISO_A0_841X1189MM 25794
#define PRINT_PREVIEW_MEDIA_ISO_A10_26X37MM 25795
#define PRINT_PREVIEW_MEDIA_ISO_A1_594X841MM 25796
#define PRINT_PREVIEW_MEDIA_ISO_A2_420X594MM 25797
#define PRINT_PREVIEW_MEDIA_ISO_A3_297X420MM 25798
#define PRINT_PREVIEW_MEDIA_ISO_A4_210X297MM 25799
#define PRINT_PREVIEW_MEDIA_ISO_A4_EXTRA_235_5X322_3MM 25800
#define PRINT_PREVIEW_MEDIA_ISO_A4_TAB_225X297MM 25801
#define PRINT_PREVIEW_MEDIA_ISO_A5_148X210MM 25802
#define PRINT_PREVIEW_MEDIA_ISO_A5_EXTRA_174X235MM 25803
#define PRINT_PREVIEW_MEDIA_ISO_A6_105X148MM 25804
#define PRINT_PREVIEW_MEDIA_ISO_A7_74X105MM 25805
#define PRINT_PREVIEW_MEDIA_ISO_A8_52X74MM 25806
#define PRINT_PREVIEW_MEDIA_ISO_A9_37X52MM 25807
#define PRINT_PREVIEW_MEDIA_ISO_B0_1000X1414MM 25808
#define PRINT_PREVIEW_MEDIA_ISO_B10_31X44MM 25809
#define PRINT_PREVIEW_MEDIA_ISO_B1_707X1000MM 25810
#define PRINT_PREVIEW_MEDIA_ISO_B2_500X707MM 25811
#define PRINT_PREVIEW_MEDIA_ISO_B3_353X500MM 25812
#define PRINT_PREVIEW_MEDIA_ISO_B4_250X353MM 25813
#define PRINT_PREVIEW_MEDIA_ISO_B5_176X250MM 25814
#define PRINT_PREVIEW_MEDIA_ISO_B5_EXTRA_201X276MM 25815
#define PRINT_PREVIEW_MEDIA_ISO_B6C4_125X324MM 25816
#define PRINT_PREVIEW_MEDIA_ISO_B6_125X176MM 25817
#define PRINT_PREVIEW_MEDIA_ISO_B7_88X125MM 25818
#define PRINT_PREVIEW_MEDIA_ISO_B8_62X88MM 25819
#define PRINT_PREVIEW_MEDIA_ISO_B9_44X62MM 25820
#define PRINT_PREVIEW_MEDIA_ISO_C0_917X1297MM 25821
#define PRINT_PREVIEW_MEDIA_ISO_C10_28X40MM 25822
#define PRINT_PREVIEW_MEDIA_ISO_C1_648X917MM 25823
#define PRINT_PREVIEW_MEDIA_ISO_C2_458X648MM 25824
#define PRINT_PREVIEW_MEDIA_ISO_C3_324X458MM 25825
#define PRINT_PREVIEW_MEDIA_ISO_C4_229X324MM 25826
#define PRINT_PREVIEW_MEDIA_ISO_C5_162X229MM 25827
#define PRINT_PREVIEW_MEDIA_ISO_C6C5_114X229MM 25828
#define PRINT_PREVIEW_MEDIA_ISO_C6_114X162MM 25829
#define PRINT_PREVIEW_MEDIA_ISO_C7C6_81X162MM 25830
#define PRINT_PREVIEW_MEDIA_ISO_C7_81X114MM 25831
#define PRINT_PREVIEW_MEDIA_ISO_C8_57X81MM 25832
#define PRINT_PREVIEW_MEDIA_ISO_C9_40X57MM 25833
#define PRINT_PREVIEW_MEDIA_ISO_DL_110X220MM 25834
#define PRINT_PREVIEW_MEDIA_JIS_EXEC_216X330MM 25835
#define PRINT_PREVIEW_MEDIA_JPN_CHOU2_111_1X146MM 25836
#define PRINT_PREVIEW_MEDIA_JPN_CHOU3_120X235MM 25837
#define PRINT_PREVIEW_MEDIA_JPN_CHOU4_90X205MM 25838
#define PRINT_PREVIEW_MEDIA_JPN_HAGAKI_100X148MM 25839
#define PRINT_PREVIEW_MEDIA_JPN_KAHU_240X322_1MM 25840
#define PRINT_PREVIEW_MEDIA_JPN_KAKU2_240X332MM 25841
#define PRINT_PREVIEW_MEDIA_JPN_OUFUKU_148X200MM 25842
#define PRINT_PREVIEW_MEDIA_JPN_YOU4_105X235MM 25843
#define PRINT_PREVIEW_MEDIA_NA_10X11_10X11IN 25844
#define PRINT_PREVIEW_MEDIA_NA_10X13_10X13IN 25845
#define PRINT_PREVIEW_MEDIA_NA_10X14_10X14IN 25846
#define PRINT_PREVIEW_MEDIA_NA_10X15_10X15IN 25847
#define PRINT_PREVIEW_MEDIA_NA_11X12_11X12IN 25848
#define PRINT_PREVIEW_MEDIA_NA_11X15_11X15IN 25849
#define PRINT_PREVIEW_MEDIA_NA_12X19_12X19IN 25850
#define PRINT_PREVIEW_MEDIA_NA_5X7_5X7IN 25851
#define PRINT_PREVIEW_MEDIA_NA_6X9_6X9IN 25852
#define PRINT_PREVIEW_MEDIA_NA_7X9_7X9IN 25853
#define PRINT_PREVIEW_MEDIA_NA_9X11_9X11IN 25854
#define PRINT_PREVIEW_MEDIA_NA_A2_4_375X5_75IN 25855
#define PRINT_PREVIEW_MEDIA_NA_ARCH_A_9X12IN 25856
#define PRINT_PREVIEW_MEDIA_NA_ARCH_B_12X18IN 25857
#define PRINT_PREVIEW_MEDIA_NA_ARCH_C_18X24IN 25858
#define PRINT_PREVIEW_MEDIA_NA_ARCH_D_24X36IN 25859
#define PRINT_PREVIEW_MEDIA_NA_ARCH_E_36X48IN 25860
#define PRINT_PREVIEW_MEDIA_NA_B_PLUS_12X19_17IN 25861
#define PRINT_PREVIEW_MEDIA_NA_C5_6_5X9_5IN 25862
#define PRINT_PREVIEW_MEDIA_NA_C_17X22IN 25863
#define PRINT_PREVIEW_MEDIA_NA_D_22X34IN 25864
#define PRINT_PREVIEW_MEDIA_NA_EDP_11X14IN 25865
#define PRINT_PREVIEW_MEDIA_NA_EUR_EDP_12X14IN 25866
#define PRINT_PREVIEW_MEDIA_NA_E_34X44IN 25867
#define PRINT_PREVIEW_MEDIA_NA_FANFOLD_EUR_8_5X12IN 25868
#define PRINT_PREVIEW_MEDIA_NA_FANFOLD_US_11X14_875IN 25869
#define PRINT_PREVIEW_MEDIA_NA_FOOLSCAP_8_5X13IN 25870
#define PRINT_PREVIEW_MEDIA_NA_F_44X68IN 25871
#define PRINT_PREVIEW_MEDIA_NA_GOVT_LEGAL_8X13IN 25872
#define PRINT_PREVIEW_MEDIA_NA_GOVT_LETTER_8X10IN 25873
#define PRINT_PREVIEW_MEDIA_NA_INDEX_3X5_3X5IN 25874
#define PRINT_PREVIEW_MEDIA_NA_INDEX_4X6_4X6IN 25875
#define PRINT_PREVIEW_MEDIA_NA_INDEX_4X6_EXT_6X8IN 25876
#define PRINT_PREVIEW_MEDIA_NA_INDEX_5X8_5X8IN 25877
#define PRINT_PREVIEW_MEDIA_NA_INVOICE_5_5X8_5IN 25878
#define PRINT_PREVIEW_MEDIA_NA_LEDGER_11X17IN 25879
#define PRINT_PREVIEW_MEDIA_NA_LEGAL_8_5X14IN 25880
#define PRINT_PREVIEW_MEDIA_NA_LEGAL_EXTRA_9_5X15IN 25881
#define PRINT_PREVIEW_MEDIA_NA_LETTER_8_5X11IN 25882
#define PRINT_PREVIEW_MEDIA_NA_LETTER_EXTRA_9_5X12IN 25883
#define PRINT_PREVIEW_MEDIA_NA_LETTER_PLUS_8_5X12_69IN 25884
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_10_4_125X9_5IN 25885
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_11_4_5X10_375IN 25886
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_12_4_75X11IN 25887
#define PRINT_PREVIEW_MEDIA_NA_NUMBER_14_5X11_5IN 25888
#define PRINT_PREVIEW_MEDIA_NA_PERSONAL_3_625X6_5IN 25889
#define PRINT_PREVIEW_MEDIA_NA_SUPER_A_8_94X14IN 25890
#define PRINT_PREVIEW_MEDIA_NA_SUPER_B_13X19IN 25891
#define PRINT_PREVIEW_MEDIA_NA_WIDE_FORMAT_30X42IN 25892
#define PRINT_PREVIEW_MEDIA_OM_DAI_PA_KAI_275X395MM 25893
#define PRINT_PREVIEW_MEDIA_OM_FOLIO_SP_215X315MM 25894
#define PRINT_PREVIEW_MEDIA_OM_INVITE_220X220MM 25895
#define PRINT_PREVIEW_MEDIA_OM_ITALIAN_110X230MM 25896
#define PRINT_PREVIEW_MEDIA_OM_JUURO_KU_KAI_198X275MM 25897
#define PRINT_PREVIEW_MEDIA_OM_LARGE_PHOTO_200X300 25898
#define PRINT_PREVIEW_MEDIA_OM_PA_KAI_267X389MM 25899
#define PRINT_PREVIEW_MEDIA_OM_POSTFIX_114X229MM 25900
#define PRINT_PREVIEW_MEDIA_OM_SMALL_PHOTO_100X150MM 25901
#define PRINT_PREVIEW_MEDIA_PRC_10_324X458MM 25902
#define PRINT_PREVIEW_MEDIA_PRC_16K_146X215MM 25903
#define PRINT_PREVIEW_MEDIA_PRC_1_102X165MM 25904
#define PRINT_PREVIEW_MEDIA_PRC_2_102X176MM 25905
#define PRINT_PREVIEW_MEDIA_PRC_32K_97X151MM 25906
#define PRINT_PREVIEW_MEDIA_PRC_3_125X176MM 25907
#define PRINT_PREVIEW_MEDIA_PRC_4_110X208MM 25908
#define PRINT_PREVIEW_MEDIA_PRC_5_110X220MM 25909
#define PRINT_PREVIEW_MEDIA_PRC_6_120X320MM 25910
#define PRINT_PREVIEW_MEDIA_PRC_7_160X230MM 25911
#define PRINT_PREVIEW_MEDIA_PRC_8_120X309MM 25912
#define PRINT_PREVIEW_MEDIA_ROC_16K_7_75X10_75IN 25913
#define PRINT_PREVIEW_MEDIA_ROC_8K_10_75X15_5IN 25914
#define PRINT_PREVIEW_MEDIA_JIS_B0_1030X1456MM 25915
#define PRINT_PREVIEW_MEDIA_JIS_B1_728X1030MM 25916
#define PRINT_PREVIEW_MEDIA_JIS_B2_515X728MM 25917
#define PRINT_PREVIEW_MEDIA_JIS_B3_364X515MM 25918
#define PRINT_PREVIEW_MEDIA_JIS_B4_257X364MM 25919
#define PRINT_PREVIEW_MEDIA_JIS_B5_182X257MM 25920
#define PRINT_PREVIEW_MEDIA_JIS_B6_128X182MM 25921
#define PRINT_PREVIEW_MEDIA_JIS_B7_91X128MM 25922
#define PRINT_PREVIEW_MEDIA_JIS_B8_64X91MM 25923
#define PRINT_PREVIEW_MEDIA_JIS_B9_45X64MM 25924
#define PRINT_PREVIEW_MEDIA_JIS_B10_32X45MM 25925
#define IDS_PRINT_PREVIEW_FRIENDLY_WIN_NETWORK_PRINTER_NAME 25926
#define IDS_PRINT_COMPOSITOR_SERVICE_DISPLAY_NAME 25927
#define IDS_PRIVACY_SANDBOX_FLOC_INVALID 26204
#define IDS_PRIVACY_SANDBOX_FLOC_DESCRIPTION 26205
#define IDS_PRIVACY_SANDBOX_FLOC_TIME_TO_NEXT_COMPUTE 26206
#define IDS_PRIVACY_SANDBOX_FLOC_TIME_TO_NEXT_COMPUTE_INVALID 26207
#define IDS_PRIVACY_SANDBOX_FLOC_RESET_EXPLANATION 26208
#define IDS_PRIVACY_SANDBOX_FLOC_STATUS_ACTIVE 26209
#define IDS_PRIVACY_SANDBOX_FLOC_STATUS_ELIGIBLE_NOT_ACTIVE 26210
#define IDS_PRIVACY_SANDBOX_FLOC_STATUS_NOT_ACTIVE 26211
#define IDS_PRIVACY_SANDBOX_TOPICS_INVALID_TOPIC 26212
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_1 26213
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_2 26214
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_3 26215
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_4 26216
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_5 26217
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_6 26218
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_7 26219
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_8 26220
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_9 26221
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_10 26222
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_11 26223
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_12 26224
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_13 26225
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_14 26226
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_15 26227
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_16 26228
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_17 26229
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_18 26230
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_19 26231
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_20 26232
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_21 26233
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_22 26234
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_23 26235
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_24 26236
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_25 26237
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_26 26238
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_27 26239
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_28 26240
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_29 26241
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_30 26242
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_31 26243
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_32 26244
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_33 26245
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_34 26246
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_35 26247
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_36 26248
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_37 26249
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_38 26250
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_39 26251
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_40 26252
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_41 26253
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_42 26254
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_43 26255
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_44 26256
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_45 26257
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_46 26258
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_47 26259
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_48 26260
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_49 26261
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_50 26262
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_51 26263
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_52 26264
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_53 26265
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_54 26266
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_55 26267
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_56 26268
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_57 26269
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_58 26270
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_59 26271
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_60 26272
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_61 26273
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_62 26274
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_63 26275
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_64 26276
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_65 26277
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_66 26278
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_67 26279
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_68 26280
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_69 26281
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_70 26282
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_71 26283
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_72 26284
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_73 26285
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_74 26286
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_75 26287
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_76 26288
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_77 26289
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_78 26290
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_79 26291
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_80 26292
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_81 26293
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_82 26294
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_83 26295
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_84 26296
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_85 26297
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_86 26298
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_87 26299
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_88 26300
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_89 26301
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_90 26302
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_91 26303
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_92 26304
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_93 26305
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_94 26306
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_95 26307
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_96 26308
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_97 26309
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_98 26310
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_99 26311
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_100 26312
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_101 26313
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_102 26314
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_103 26315
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_104 26316
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_105 26317
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_106 26318
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_107 26319
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_108 26320
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_109 26321
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_110 26322
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_111 26323
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_112 26324
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_113 26325
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_114 26326
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_115 26327
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_116 26328
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_117 26329
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_118 26330
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_119 26331
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_120 26332
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_121 26333
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_122 26334
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_123 26335
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_124 26336
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_125 26337
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_126 26338
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_127 26339
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_128 26340
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_129 26341
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_130 26342
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_131 26343
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_132 26344
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_133 26345
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_134 26346
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_135 26347
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_136 26348
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_137 26349
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_138 26350
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_139 26351
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_140 26352
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_141 26353
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_142 26354
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_143 26355
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_144 26356
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_145 26357
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_146 26358
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_147 26359
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_148 26360
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_149 26361
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_150 26362
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_151 26363
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_152 26364
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_153 26365
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_154 26366
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_155 26367
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_156 26368
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_157 26369
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_158 26370
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_159 26371
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_160 26372
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_161 26373
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_162 26374
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_163 26375
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_164 26376
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_165 26377
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_166 26378
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_167 26379
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_168 26380
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_169 26381
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_170 26382
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_171 26383
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_172 26384
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_173 26385
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_174 26386
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_175 26387
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_176 26388
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_177 26389
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_178 26390
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_179 26391
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_180 26392
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_181 26393
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_182 26394
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_183 26395
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_184 26396
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_185 26397
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_186 26398
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_187 26399
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_188 26400
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_189 26401
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_190 26402
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_191 26403
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_192 26404
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_193 26405
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_194 26406
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_195 26407
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_196 26408
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_197 26409
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_198 26410
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_199 26411
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_200 26412
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_201 26413
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_202 26414
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_203 26415
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_204 26416
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_205 26417
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_206 26418
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_207 26419
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_208 26420
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_209 26421
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_210 26422
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_211 26423
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_212 26424
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_213 26425
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_214 26426
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_215 26427
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_216 26428
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_217 26429
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_218 26430
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_219 26431
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_220 26432
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_221 26433
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_222 26434
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_223 26435
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_224 26436
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_225 26437
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_226 26438
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_227 26439
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_228 26440
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_229 26441
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_230 26442
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_231 26443
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_232 26444
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_233 26445
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_234 26446
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_235 26447
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_236 26448
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_237 26449
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_238 26450
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_239 26451
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_240 26452
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_241 26453
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_242 26454
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_243 26455
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_244 26456
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_245 26457
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_246 26458
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_247 26459
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_248 26460
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_249 26461
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_250 26462
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_251 26463
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_252 26464
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_253 26465
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_254 26466
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_255 26467
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_256 26468
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_257 26469
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_258 26470
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_259 26471
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_260 26472
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_261 26473
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_262 26474
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_263 26475
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_264 26476
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_265 26477
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_266 26478
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_267 26479
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_268 26480
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_269 26481
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_270 26482
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_271 26483
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_272 26484
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_273 26485
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_274 26486
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_275 26487
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_276 26488
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_277 26489
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_278 26490
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_279 26491
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_280 26492
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_281 26493
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_282 26494
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_283 26495
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_284 26496
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_285 26497
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_286 26498
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_287 26499
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_288 26500
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_289 26501
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_290 26502
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_291 26503
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_292 26504
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_293 26505
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_294 26506
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_295 26507
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_296 26508
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_297 26509
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_298 26510
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_299 26511
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_300 26512
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_301 26513
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_302 26514
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_303 26515
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_304 26516
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_305 26517
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_306 26518
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_307 26519
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_308 26520
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_309 26521
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_310 26522
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_311 26523
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_312 26524
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_313 26525
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_314 26526
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_315 26527
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_316 26528
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_317 26529
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_318 26530
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_319 26531
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_320 26532
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_321 26533
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_322 26534
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_323 26535
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_324 26536
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_325 26537
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_326 26538
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_327 26539
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_328 26540
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_329 26541
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_330 26542
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_331 26543
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_332 26544
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_333 26545
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_334 26546
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_335 26547
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_336 26548
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_337 26549
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_338 26550
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_339 26551
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_340 26552
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_341 26553
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_342 26554
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_343 26555
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_344 26556
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_345 26557
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_346 26558
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_347 26559
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_348 26560
#define IDS_PRIVACY_SANDBOX_TOPICS_TAXONOMY_V1_TOPIC_ID_349 26561
#define IDS_REGISTER_PROTOCOL_HANDLER_TOOLTIP 278
#define IDS_REGISTER_PROTOCOL_HANDLER_MAILTO_NAME 26562
#define IDS_REGISTER_PROTOCOL_HANDLER_WEBCAL_NAME 26563
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM 26564
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM_REPLACE 26565
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM_FRAGMENT 26566
#define IDS_REGISTER_PROTOCOL_HANDLER_CONFIRM_REPLACE_FRAGMENT 26567
#define IDS_REGISTER_PROTOCOL_HANDLER_ACCEPT 26568
#define IDS_REGISTER_PROTOCOL_HANDLER_DENY 26569
#define IDS_REGISTER_PROTOCOL_HANDLER_IGNORE 26570
#define IDS_RESET_PASSWORD_TITLE 26571
#define IDS_RESET_PASSWORD_WARNING_HEADING 26572
#define IDS_RESET_PASSWORD_HEADING 26573
#define IDS_RESET_PASSWORD_WARNING_EXPLANATION_PARAGRAPH 26574
#define IDS_RESET_PASSWORD_WARNING_EXPLANATION_PARAGRAPH_WITH_ORG_NAME 26575
#define IDS_RESET_PASSWORD_BUTTON 26576
#define IDS_RESET_PASSWORD_EXPLANATION_PARAGRAPH 26577
#define IDS_RESET_PASSWORD_EXPLANATION_PARAGRAPH_WITH_ORG_NAME 26578
#define IDS_SSL_OPEN_DETAILS_BUTTON 26579
#define IDS_SSL_CLOSE_DETAILS_BUTTON 26580
#define IDS_CAPTIVE_PORTAL_AUTHORIZATION_DIALOG_NAME 26581
#define IDS_CAPTIVE_PORTAL_HEADING_WIRED 26582
#define IDS_CAPTIVE_PORTAL_HEADING_WIFI 26583
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_WIRED 26584
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_WIFI 26585
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_WIFI_SSID 26586
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_NO_LOGIN_URL_WIRED 26587
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_NO_LOGIN_URL_WIFI 26588
#define IDS_CAPTIVE_PORTAL_PRIMARY_PARAGRAPH_NO_LOGIN_URL_WIFI_SSID 26589
#define IDS_CAPTIVE_PORTAL_BUTTON_OPEN_LOGIN_PAGE 26590
#define IDS_MITM_SOFTWARE_HEADING 26591
#define IDS_MITM_SOFTWARE_PRIMARY_PARAGRAPH_ENTERPRISE 26592
#define IDS_MITM_SOFTWARE_PRIMARY_PARAGRAPH_NONENTERPRISE 26593
#define IDS_MITM_SOFTWARE_EXPLANATION_ENTERPRISE 26594
#define IDS_MITM_SOFTWARE_EXPLANATION_NONENTERPRISE 26595
#define IDS_MITM_SOFTWARE_EXPLANATION 26596
#define IDS_LOOKALIKE_URL_TITLE 26597
#define IDS_LOOKALIKE_URL_HEADING 26598
#define IDS_LOOKALIKE_URL_IGNORE 26599
#define IDS_LOOKALIKE_URL_CONTINUE 26600
#define IDS_LOOKALIKE_URL_PRIMARY_PARAGRAPH 26601
#define IDS_LOOKALIKE_URL_HEADING_NO_SUGGESTED_URL 26602
#define IDS_LOOKALIKE_URL_PRIMARY_PARAGRAPH_NO_SUGGESTED_URL 26603
#define IDS_LOOKALIKE_URL_BACK_TO_SAFETY 26604
#define IDS_LOOKALIKE_URL_CLOSE_PAGE 26605
#define IDS_CLOCK_ERROR_TITLE 26606
#define IDS_CLOCK_ERROR_AHEAD_HEADING 26607
#define IDS_CLOCK_ERROR_BEHIND_HEADING 26608
#define IDS_CLOCK_ERROR_UPDATE_DATE_AND_TIME 26609
#define IDS_CLOCK_ERROR_PRIMARY_PARAGRAPH 26610
#define IDS_CLOCK_ERROR_EXPLANATION 26611
#define IDS_SSL_V2_TITLE 26612
#define IDS_SSL_V2_HEADING 26613
#define IDS_SSL_V2_PRIMARY_PARAGRAPH 26614
#define IDS_SSL_V2_RECURRENT_ERROR_PARAGRAPH 26615
#define IDS_SSL_OVERRIDABLE_SAFETY_BUTTON 26616
#define IDS_SSL_OVERRIDABLE_CLOSE_PAGE_BUTTON 26617
#define IDS_SSL_OVERRIDABLE_PROCEED_PARAGRAPH 26618
#define IDS_SSL_RELOAD 26619
#define IDS_SSL_NONOVERRIDABLE_PINNED 26620
#define IDS_SSL_NONOVERRIDABLE_HSTS 26621
#define IDS_SSL_NONOVERRIDABLE_REVOKED 26622
#define IDS_SSL_NONOVERRIDABLE_MORE 26623
#define IDS_SSL_NONOVERRIDABLE_INVALID 26624
#define IDS_SAFEBROWSING_V3_TITLE 26625
#define IDS_SAFEBROWSING_V3_OPEN_DETAILS_BUTTON 26626
#define IDS_SAFEBROWSING_V3_CLOSE_DETAILS_BUTTON 26627
#define IDS_SAFEBROWSING_OVERRIDABLE_SAFETY_BUTTON 26628
#define IDS_MALWARE_V3_HEADING 26629
#define IDS_MALWARE_V3_PRIMARY_PARAGRAPH 26630
#define IDS_MALWARE_V3_EXPLANATION_PARAGRAPH 26631
#define IDS_MALWARE_V3_EXPLANATION_PARAGRAPH_SUBRESOURCE 26632
#define IDS_MALWARE_V3_PROCEED_PARAGRAPH 26633
#define IDS_SAFE_BROWSING_SCOUT_REPORTING_AGREE 26634
#define IDS_SAFE_BROWSING_ENHANCED_PROTECTION_MESSAGE 26635
#define IDS_HARMFUL_V3_HEADING 26636
#define IDS_HARMFUL_V3_PRIMARY_PARAGRAPH 26637
#define IDS_HARMFUL_V3_EXPLANATION_PARAGRAPH 26638
#define IDS_HARMFUL_V3_PROCEED_PARAGRAPH 26639
#define IDS_PHISHING_V4_HEADING 26640
#define IDS_PHISHING_V4_PRIMARY_PARAGRAPH 26641
#define IDS_PHISHING_V4_EXPLANATION_PARAGRAPH 26642
#define IDS_PHISHING_V4_PROCEED_AND_REPORT_PARAGRAPH 26643
#define IDS_MALWARE_WEBVIEW_HEADING 26644
#define IDS_MALWARE_WEBVIEW_EXPLANATION_PARAGRAPH 26645
#define IDS_PHISHING_WEBVIEW_HEADING 26646
#define IDS_PHISHING_WEBVIEW_EXPLANATION_PARAGRAPH 26647
#define IDS_HARMFUL_WEBVIEW_HEADING 26648
#define IDS_HARMFUL_WEBVIEW_EXPLANATION_PARAGRAPH 26649
#define IDS_BILLING_WEBVIEW_HEADING 26650
#define IDS_BILLING_WEBVIEW_EXPLANATION_PARAGRAPH 26651
#define IDS_CONNECTION_HELP_SHOW_MORE 26652
#define IDS_CONNECTION_HELP_SHOW_LESS 26653
#define IDS_CONNECTION_HELP_TITLE 26654
#define IDS_CONNECTION_HELP_HEADING 26655
#define IDS_CONNECTION_HELP_GENERAL_HELP 26656
#define IDS_CONNECTION_HELP_SPECIFIC_ERROR_HEADING 26657
#define IDS_CONNECTION_HELP_CONNECTION_NOT_PRIVATE_TITLE 26658
#define IDS_CONNECTION_HELP_CONNECT_TO_NETWORK_TITLE 26659
#define IDS_CONNECTION_HELP_INCORRECT_CLOCK_TITLE 26660
#define IDS_CONNECTION_HELP_CONNECTION_NOT_PRIVATE_DETAILS 26661
#define IDS_CONNECTION_HELP_CONNECT_TO_NETWORK_DETAILS 26662
#define IDS_CONNECTION_HELP_INCORRECT_CLOCK_DETAILS 26663
#define IDS_CONNECTION_HELP_MITM_SOFTWARE_TITLE 26664
#define IDS_CONNECTION_HELP_MITM_SOFTWARE_DETAILS 26665
#define IDS_BILLING_TITLE 26666
#define IDS_BILLING_HEADING 26667
#define IDS_BILLING_PRIMARY_PARAGRAPH 26668
#define IDS_BILLING_PRIMARY_BUTTON 26669
#define IDS_BILLING_PROCEED_BUTTON 26670
#define IDS_ORIGIN_POLICY_TITLE 26671
#define IDS_ORIGIN_POLICY_HEADING 26672
#define IDS_ORIGIN_POLICY_INFO 26673
#define IDS_ORIGIN_POLICY_INFO2 26674
#define IDS_ORIGIN_POLICY_BUTTON 26675
#define IDS_ORIGIN_POLICY_DETAILS 26676
#define IDS_ORIGIN_POLICY_EXPLANATION_CANNOT_LOAD 26677
#define IDS_ORIGIN_POLICY_EXPLANATION_CANNOT_PARSE_HEADER 26678
#define IDS_ORIGIN_POLICY_FINAL_PARAGRAPH 26679
#define IDS_ORIGIN_POLICY_CLOSE 26680
#define IDS_BLOCKED_INTERCEPTION_HEADING 26681
#define IDS_KNOWN_INTERCEPTION_TITLE 26682
#define IDS_KNOWN_INTERCEPTION_HEADER 26683
#define IDS_KNOWN_INTERCEPTION_BODY1 26684
#define IDS_KNOWN_INTERCEPTION_BODY2 26685
#define IDS_KNOWN_INTERCEPTION_INFOBAR_HEADING 26686
#define IDS_KNOWN_INTERCEPTION_INFOBAR_BUTTON_TEXT 26687
#define IDS_LEGACY_TLS_HEADING 26688
#define IDS_LEGACY_TLS_PRIMARY_PARAGRAPH 26689
#define IDS_LEGACY_TLS_EXPLANATION 26690
#define IDS_INSECURE_FORM_TITLE 26691
#define IDS_INSECURE_FORM_HEADING 26692
#define IDS_INSECURE_FORM_PRIMARY_PARAGRAPH 26693
#define IDS_INSECURE_FORM_BACK_BUTTON 26694
#define IDS_INSECURE_FORM_SUBMIT_BUTTON 26695
#define IDS_HTTPS_ONLY_MODE_TITLE 26696
#define IDS_HTTPS_ONLY_MODE_HEADING 26697
#define IDS_HTTPS_ONLY_MODE_PRIMARY_PARAGRAPH 26698
#define IDS_HTTPS_ONLY_MODE_BACK_BUTTON 26699
#define IDS_HTTPS_ONLY_MODE_SUBMIT_BUTTON 26700
#define IDS_SHARING_DEVICE_TYPE_COMPUTER 26701
#define IDS_SHARING_DEVICE_TYPE_DEVICE 26702
#define IDS_SHARING_DEVICE_TYPE_PHONE 26703
#define IDS_SHARING_DEVICE_TYPE_TABLET 26704
#define IDS_SITE_SETTINGS_TYPE_ADS 26705
#define IDS_SITE_SETTINGS_TYPE_ADS_MID_SENTENCE 26706
#define IDS_SITE_SETTINGS_TYPE_AR 26707
#define IDS_SITE_SETTINGS_TYPE_AR_MID_SENTENCE 26708
#define IDS_SITE_SETTINGS_TYPE_AUTOMATIC_DOWNLOADS 26709
#define IDS_SITE_SETTINGS_TYPE_AUTOMATIC_DOWNLOADS_MID_SENTENCE 26710
#define IDS_SITE_SETTINGS_TYPE_BACKGROUND_SYNC 26711
#define IDS_SITE_SETTINGS_TYPE_BACKGROUND_SYNC_MID_SENTENCE 26712
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_DEVICES 26713
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_DEVICES_MID_SENTENCE 26714
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_SCANNING 26715
#define IDS_SITE_SETTINGS_TYPE_BLUETOOTH_SCANNING_MID_SENTENCE 26716
#define IDS_SITE_SETTINGS_TYPE_CAMERA 26717
#define IDS_SITE_SETTINGS_TYPE_CAMERA_MID_SENTENCE 26718
#define IDS_SITE_SETTINGS_TYPE_CAMERA_PAN_TILT_ZOOM 26719
#define IDS_SITE_SETTINGS_TYPE_CAMERA_PAN_TILT_ZOOM_MID_SENTENCE 26720
#define IDS_SITE_SETTINGS_TYPE_CLIPBOARD 26721
#define IDS_SITE_SETTINGS_TYPE_CLIPBOARD_MID_SENTENCE 26722
#define IDS_SITE_SETTINGS_TYPE_COOKIES 26723
#define IDS_SITE_SETTINGS_TYPE_COOKIES_MID_SENTENCE 26724
#define IDS_SITE_SETTINGS_TYPE_IDLE_DETECTION 26725
#define IDS_SITE_SETTINGS_TYPE_IDLE_DETECTION_MID_SENTENCE 26726
#define IDS_SITE_SETTINGS_TYPE_JAVASCRIPT 26727
#define IDS_SITE_SETTINGS_TYPE_JAVASCRIPT_MID_SENTENCE 26728
#define IDS_SITE_SETTINGS_TYPE_LOCATION 26729
#define IDS_SITE_SETTINGS_TYPE_LOCATION_MID_SENTENCE 26730
#define IDS_SITE_SETTINGS_TYPE_MIC 26731
#define IDS_SITE_SETTINGS_TYPE_MIC_MID_SENTENCE 26732
#define IDS_SITE_SETTINGS_TYPE_MIDI_SYSEX 26733
#define IDS_SITE_SETTINGS_TYPE_MIDI_SYSEX_MID_SENTENCE 26734
#define IDS_SITE_SETTINGS_TYPE_MOTION_SENSORS 26735
#define IDS_SITE_SETTINGS_TYPE_MOTION_SENSORS_MID_SENTENCE 26736
#define IDS_SITE_SETTINGS_TYPE_NFC 26737
#define IDS_SITE_SETTINGS_TYPE_NFC_MID_SENTENCE 26738
#define IDS_SITE_SETTINGS_TYPE_NOTIFICATIONS 26739
#define IDS_SITE_SETTINGS_TYPE_NOTIFICATIONS_MID_SENTENCE 26740
#define IDS_SITE_SETTINGS_TYPE_POPUPS_REDIRECTS 26741
#define IDS_SITE_SETTINGS_TYPE_POPUPS_REDIRECTS_MID_SENTENCE 26742
#define IDS_SITE_SETTINGS_TYPE_PROTECTED_MEDIA_ID 26743
#define IDS_SITE_SETTINGS_TYPE_PROTECTED_MEDIA_ID_MID_SENTENCE 26744
#define IDS_SITE_SETTINGS_TYPE_SENSORS 26745
#define IDS_SITE_SETTINGS_TYPE_SENSORS_MID_SENTENCE 26746
#define IDS_SITE_SETTINGS_TYPE_SOUND 26747
#define IDS_SITE_SETTINGS_TYPE_SOUND_MID_SENTENCE 26748
#define IDS_SITE_SETTINGS_TYPE_USB_DEVICES 26749
#define IDS_SITE_SETTINGS_TYPE_USB_DEVICES_MID_SENTENCE 26750
#define IDS_SITE_SETTINGS_TYPE_VR 26751
#define IDS_SITE_SETTINGS_TYPE_VR_MID_SENTENCE 26752
#define IDS_SITE_SETTINGS_TYPE_FEDERATED_IDENTITY_API 26753
#define IDS_SITE_SETTINGS_TYPE_FEDERATED_IDENTITY_API_MID_SENTENCE 26754
#define IDS_SITE_SETTINGS_TYPE_FILE_SYSTEM_ACCESS_WRITE 26755
#define IDS_SITE_SETTINGS_TYPE_FILE_SYSTEM_ACCESS_WRITE_MID_SENTENCE 26756
#define IDS_SITE_SETTINGS_TYPE_FONT_ACCESS 26757
#define IDS_SITE_SETTINGS_TYPE_FONT_ACCESS_MID_SENTENCE 26758
#define IDS_SITE_SETTINGS_TYPE_HANDLERS 26759
#define IDS_SITE_SETTINGS_TYPE_HANDLERS_MID_SENTENCE 26760
#define IDS_SITE_SETTINGS_TYPE_HID_DEVICES 26761
#define IDS_SITE_SETTINGS_TYPE_HID_DEVICES_MID_SENTENCE 26762
#define IDS_SITE_SETTINGS_TYPE_IMAGES 26763
#define IDS_SITE_SETTINGS_TYPE_IMAGES_MID_SENTENCE 26764
#define IDS_SITE_SETTINGS_TYPE_INSECURE_CONTENT 26765
#define IDS_SITE_SETTINGS_TYPE_INSECURE_CONTENT_MID_SENTENCE 26766
#define IDS_SITE_SETTINGS_TYPE_PAYMENT_HANDLER 26767
#define IDS_SITE_SETTINGS_TYPE_PAYMENT_HANDLER_MID_SENTENCE 26768
#define IDS_SITE_SETTINGS_TYPE_PDF_DOCUMENTS 26769
#define IDS_SITE_SETTINGS_TYPE_SERIAL_PORTS 26770
#define IDS_SITE_SETTINGS_TYPE_SERIAL_PORTS_MID_SENTENCE 26771
#define IDS_SITE_SETTINGS_TYPE_ZOOM_LEVELS 26772
#define IDS_SITE_SETTINGS_TYPE_ZOOM_LEVELS_MID_SENTENCE 26773
#define IDS_SITE_SETTINGS_TYPE_WINDOW_PLACEMENT 26774
#define IDS_SITE_SETTINGS_TYPE_WINDOW_PLACEMENT_MID_SENTENCE 26775
#define IDS_SMS_INFOBAR_TITLE 26776
#define IDS_SMS_INFOBAR_STATUS_SMS_RECEIVED 26777
#define IDS_SMS_INFOBAR_STATUS_SMS_RECEIVED_FROM_EMBEDDED_FRAME 26778
#define IDS_SMS_INFOBAR_BUTTON_OK 26779
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_ENGLISH 26780
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_FRENCH 26781
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_GERMAN 26782
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_ITALIAN 26783
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_JAPANESE 26784
#define IDS_SODA_LANGUAGE_DISPLAY_NAME_SPANISH 26785
#define IDS_CERT_ERROR_NO_SUBJECT_ALTERNATIVE_NAMES_DETAILS 26786
#define IDS_CERT_ERROR_COMMON_NAME_INVALID_DETAILS 26787
#define IDS_CERT_ERROR_COMMON_NAME_INVALID_DESCRIPTION 26788
#define IDS_CERT_ERROR_EXPIRED_DETAILS 26789
#define IDS_CERT_ERROR_EXPIRED_DESCRIPTION 26790
#define IDS_CERT_ERROR_NOT_YET_VALID_DETAILS 26791
#define IDS_CERT_ERROR_NOT_YET_VALID_DESCRIPTION 26792
#define IDS_CERT_ERROR_NOT_VALID_AT_THIS_TIME_DETAILS 26793
#define IDS_CERT_ERROR_NOT_VALID_AT_THIS_TIME_DESCRIPTION 26794
#define IDS_CERT_ERROR_AUTHORITY_INVALID_DESCRIPTION 26795
#define IDS_CERT_ERROR_CONTAINS_ERRORS_DETAILS 26796
#define IDS_CERT_ERROR_CONTAINS_ERRORS_DESCRIPTION 26797
#define IDS_CERT_ERROR_UNABLE_TO_CHECK_REVOCATION_DETAILS 26798
#define IDS_CERT_ERROR_UNABLE_TO_CHECK_REVOCATION_DESCRIPTION 26799
#define IDS_CERT_ERROR_NO_REVOCATION_MECHANISM_DETAILS 26800
#define IDS_CERT_ERROR_NO_REVOCATION_MECHANISM_DESCRIPTION 26801
#define IDS_CERT_ERROR_REVOKED_CERT_DETAILS 26802
#define IDS_CERT_ERROR_REVOKED_CERT_DESCRIPTION 26803
#define IDS_CERT_ERROR_INVALID_CERT_DETAILS 26804
#define IDS_CERT_ERROR_INVALID_CERT_DESCRIPTION 26805
#define IDS_CERT_ERROR_WEAK_SIGNATURE_ALGORITHM_DETAILS 26806
#define IDS_CERT_ERROR_WEAK_SIGNATURE_ALGORITHM_DESCRIPTION 26807
#define IDS_CERT_ERROR_WEAK_KEY_DETAILS 26808
#define IDS_CERT_ERROR_WEAK_KEY_DESCRIPTION 26809
#define IDS_CERT_ERROR_NAME_CONSTRAINT_VIOLATION_DETAILS 26810
#define IDS_CERT_ERROR_NAME_CONSTRAINT_VIOLATION_DESCRIPTION 26811
#define IDS_CERT_ERROR_VALIDITY_TOO_LONG_DETAILS 26812
#define IDS_CERT_ERROR_VALIDITY_TOO_LONG_DESCRIPTION 26813
#define IDS_CERT_ERROR_UNKNOWN_ERROR_DETAILS 26814
#define IDS_CERT_ERROR_UNKNOWN_ERROR_DESCRIPTION 26815
#define IDS_CERT_ERROR_SUMMARY_PINNING_FAILURE_DETAILS 26816
#define IDS_CERT_ERROR_SUMMARY_PINNING_FAILURE_DESCRIPTION 26817
#define IDS_CERT_ERROR_CERTIFICATE_TRANSPARENCY_REQUIRED_DETAILS 26818
#define IDS_CERT_ERROR_CERTIFICATE_TRANSPARENCY_REQUIRED_DESCRIPTION 26819
#define IDS_SSL_ERROR_OBSOLETE_VERSION_DETAILS 26820
#define IDS_SSL_ERROR_OBSOLETE_VERSION_DESCRIPTION 26821
#define IDS_CERT_ERROR_AUTHORITY_INVALID_DETAILS 26822
#define IDS_ALWAYS_ALLOW_ADS 26823
#define IDS_BLOCKED_ADS_PROMPT_TITLE 26824
#define IDS_BLOCKED_ADS_PROMPT_EXPLANATION 26825
#define IDS_SYNC_BASIC_ENCRYPTION_DATA 26832
#define IDS_SYNC_DATATYPE_AUTOFILL 26833
#define IDS_SYNC_DATATYPE_BOOKMARKS 26834
#define IDS_SYNC_DATATYPE_PASSWORDS 26835
#define IDS_SYNC_DATATYPE_PREFERENCES 26836
#define IDS_SYNC_DATATYPE_TABS 26837
#define IDS_SYNC_DATATYPE_TYPED_URLS 26838
#define IDS_SYNC_DATATYPE_READING_LIST 26839
#define IDS_SYNC_EMPTY_PASSPHRASE_ERROR 26840
#define IDS_SYNC_ENCRYPTION_SECTION_TITLE 26841
#define IDS_SYNC_FULL_ENCRYPTION_DATA 26842
#define IDS_SYNC_LOGIN_SETTING_UP 26843
#define IDS_SYNC_PASSPHRASE_LABEL 26844
#define IDS_SYNC_PASSPHRASE_MISMATCH_ERROR 26845
#define IDS_SYNC_SERVICE_UNAVAILABLE 26846
#define IDS_SYNC_ENTER_PASSPHRASE_BODY_WITH_DATE 26847
#define IDS_SYNC_ENTER_PASSPHRASE_BODY 26848
#define IDS_TRANSLATE_INFOBAR_OPTIONS_MORE_LANGUAGE 26849
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NOT_SOURCE_LANGUAGE 26850
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NEVER_TRANSLATE_LANG 26851
#define IDS_TRANSLATE_INFOBAR_OPTIONS_NEVER_TRANSLATE_SITE 26852
#define IDS_TRANSLATE_INFOBAR_OPTIONS_ALWAYS 26853
#define IDS_TRANSLATE_INFOBAR_OPTIONS_REPORT_ERROR 26854
#define IDS_TRANSLATE_INFOBAR_OPTIONS_ABOUT 26855
#define IDS_TRANSLATE_INFOBAR_ACCEPT 26857
#define IDS_TRANSLATE_INFOBAR_DENY 26858
#define IDS_TRANSLATE_INFOBAR_NEVER_TRANSLATE 26859
#define IDS_TRANSLATE_INFOBAR_ALWAYS_TRANSLATE 26860
#define IDS_TRANSLATE_INFOBAR_AFTER_MESSAGE 26861
#define IDS_TRANSLATE_INFOBAR_AFTER_MESSAGE_AUTODETERMINED_SOURCE_LANGUAGE 26862
#define IDS_TRANSLATE_INFOBAR_REVERT 26863
#define IDS_TRANSLATE_INFOBAR_RETRY 26864
#define IDS_TRANSLATE_NOTIFICATION_ERROR 26884
#define IDS_TRANSLATE_NOTIFICATION_ALWAYS_TRANSLATE 26885
#define IDS_TRANSLATE_NOTIFICATION_LANGUAGE_NEVER 26886
#define IDS_TRANSLATE_NOTIFICATION_SITE_NEVER 26887
#define IDS_TRANSLATE_NOTIFICATION_UNDO 26888
#define IDS_TRANSLATE_DETECTED_LANGUAGE 26889
#define IDS_TAB_GROUP_COLOR_GREY 26890
#define IDS_TAB_GROUP_COLOR_BLUE 26891
#define IDS_TAB_GROUP_COLOR_RED 26892
#define IDS_TAB_GROUP_COLOR_YELLOW 26893
#define IDS_TAB_GROUP_COLOR_GREEN 26894
#define IDS_TAB_GROUP_COLOR_PINK 26895
#define IDS_TAB_GROUP_COLOR_PURPLE 26896
#define IDS_TAB_GROUP_COLOR_CYAN 26897
#define IDS_TAB_GROUP_COLOR_ORANGE 26898
#define IDS_BOOKMARK_BAR_UNDO 26899
#define IDS_BOOKMARK_BAR_REDO 26900
#define IDS_BOOKMARK_BAR_UNDO_ADD 26901
#define IDS_BOOKMARK_BAR_REDO_ADD 26902
#define IDS_BOOKMARK_BAR_UNDO_DELETE 26903
#define IDS_BOOKMARK_BAR_REDO_DELETE 26904
#define IDS_BOOKMARK_BAR_UNDO_EDIT 26905
#define IDS_BOOKMARK_BAR_REDO_EDIT 26906
#define IDS_BOOKMARK_BAR_UNDO_MOVE 26907
#define IDS_BOOKMARK_BAR_REDO_MOVE 26908
#define IDS_BOOKMARK_BAR_UNDO_REORDER 26909
#define IDS_BOOKMARK_BAR_REDO_REORDER 26910
#define IDS_CLOSE_TUTORIAL 26911
#define IDS_CLOSE_PROMO 26912
#define IDS_PROMO_DISMISS_BUTTON 26913
#define IDS_PROMO_SNOOZE_BUTTON 26914
#define IDS_PROMO_SHOW_TUTORIAL_BUTTON 26915
#define IDS_TUTORIAL_RESTART_TUTORIAL 26916
#define IDS_TUTORIAL_CLOSE_TUTORIAL 26917
#define IDS_VERSION_UI_TITLE 26918
#define IDS_VERSION_UI_OFFICIAL 26919
#define IDS_VERSION_UI_UNOFFICIAL 26920
#define IDS_VERSION_UI_32BIT 26921
#define IDS_VERSION_UI_64BIT 26922
#define IDS_VERSION_UI_REVISION 26926
#define IDS_VERSION_UI_OS 26927
#define IDS_VERSION_UI_USER_AGENT 26929
#define IDS_VERSION_UI_COMMAND_LINE 26930
#define IDS_VERSION_UI_EXECUTABLE_PATH 26934
#define IDS_VERSION_UI_PROFILE_PATH 26935
#define IDS_VERSION_UI_PATH_NOTFOUND 26936
#define IDS_VERSION_UI_VARIATIONS 26937
#define IDS_VERSION_UI_VARIATIONS_CMD 26938
#define IDS_VERSION_UI_COHORT_NAME 26939
#define IDS_MANAGEMENT_TITLE 26946
#define IDS_MANAGEMENT_TOOLBAR_TITLE 26947
#define IDS_MANAGEMENT_SUBTITLE 26951
#define IDS_MANAGEMENT_SUBTITLE_MANAGED_BY 26949
#define IDS_MANAGEMENT_NOT_MANAGED_SUBTITLE 26950
#define IDS_MANAGEMENT_BROWSER_NOTICE 26952
#define IDS_MANAGEMENT_NOT_MANAGED_NOTICE 26953
#define IDS_MANAGEMENT_EXTENSION_REPORTING 26985
#define IDS_MANAGEMENT_EXTENSIONS_INSTALLED 26986
#define IDS_MANAGEMENT_EXTENSIONS_INSTALLED_BY 26987
#define IDS_MANAGEMENT_EXTENSIONS_NAME 26988
#define IDS_MANAGEMENT_EXTENSIONS_PERMISSIONS 26989
#define IDS_MANAGEMENT_MANAGED_WEBSITES 26990
#define IDS_MANAGEMENT_MANAGED_WEBSITES_EXPLANATION 26991
#define IDS_MANAGEMENT_MANAGED_WEBSITES_BY_EXPLANATION 26992
#define IDS_MANAGEMENT_BROWSER_REPORTING 26993
#define IDS_MANAGEMENT_BROWSER_REPORTING_EXPLANATION 26994
#define IDS_MANAGEMENT_EXTENSION_REPORT_MACHINE_NAME 26995
#define IDS_MANAGEMENT_EXTENSION_REPORT_MACHINE_NAME_ADDRESS 26996
#define IDS_MANAGEMENT_EXTENSION_REPORT_USERNAME 26997
#define IDS_MANAGEMENT_EXTENSION_REPORT_VERSION 26998
#define IDS_MANAGEMENT_EXTENSION_REPORT_EXTENSIONS_PLUGINS 26999
#define IDS_MANAGEMENT_EXTENSION_REPORT_USER_BROWSING_DATA 27000
#define IDS_MANAGEMENT_EXTENSION_REPORT_PERF_CRASH 27001
#define IDS_MANAGEMENT_THREAT_PROTECTION 27002
#define IDS_MANAGEMENT_THREAT_PROTECTION_DESCRIPTION 27003
#define IDS_MANAGEMENT_THREAT_PROTECTION_DESCRIPTION_BY 27004
#define IDS_MANAGEMENT_CONNECTORS_EVENT 27005
#define IDS_MANAGEMENT_CONNECTORS_VISIBLE_DATA 27006
#define IDS_MANAGEMENT_FILE_ATTACHED_EVENT 27007
#define IDS_MANAGEMENT_FILE_DOWNLOADED_EVENT 27008
#define IDS_MANAGEMENT_TEXT_ENTERED_EVENT 27009
#define IDS_MANAGEMENT_PAGE_PRINTED_EVENT 27010
#define IDS_MANAGEMENT_ENTERPRISE_REPORTING_EVENT 27011
#define IDS_MANAGEMENT_PAGE_VISITED_EVENT 27012
#define IDS_MANAGEMENT_FILE_ATTACHED_VISIBLE_DATA 27013
#define IDS_MANAGEMENT_FILE_DOWNLOADED_VISIBLE_DATA 27014
#define IDS_MANAGEMENT_TEXT_ENTERED_VISIBLE_DATA 27015
#define IDS_MANAGEMENT_PAGE_PRINTED_VISIBLE_DATA 27016
#define IDS_MANAGEMENT_ENTERPRISE_REPORTING_VISIBLE_DATA 27017
#define IDS_MANAGEMENT_PAGE_VISITED_VISIBLE_DATA 27018
#define IDS_HISTORY_CLUSTERS_DISABLE_MENU_ITEM_LABEL 27019
#define IDS_HISTORY_CLUSTERS_ENABLE_MENU_ITEM_LABEL 27020
#define IDS_HISTORY_CLUSTERS_JOURNEYS_TAB_LABEL 27021
#define IDS_HISTORY_CLUSTERS_LIST_TAB_LABEL 27022
#define IDS_HISTORY_CLUSTERS_CLUSTER_LABEL_SEARCH_TERMS 27023
#define IDS_HISTORY_CLUSTERS_CLUSTER_LABEL_MULTIPLE_HOSTNAMES 27024
#define IDS_HISTORY_CLUSTERS_LOAD_MORE_BUTTON_LABEL 27025
#define IDS_HISTORY_CLUSTERS_OPEN_ALL_IN_TABGROUP 27026
#define IDS_HISTORY_CLUSTERS_RELATED_SEARCHES_HEADER 27027
#define IDS_HISTORY_CLUSTERS_REMOVE_ALL_ITEMS 27028
#define IDS_HISTORY_CLUSTERS_REMOVE_ITEM_TOAST 27029
#define IDS_HISTORY_CLUSTERS_SAVED_IN_TABGROUP_LABEL 27030
#define IDS_HISTORY_CLUSTERS_SHOW_LESS_BUTTON_LABEL 27031
#define IDS_HISTORY_CLUSTERS_SHOW_MORE_BUTTON_LABEL 27032
#define IDS_HISTORY_CLUSTERS_SEARCH_YOUR_JOURNEYS 27033
#define IDS_CANCEL 27045
#define IDS_CLOSE 27046
#define IDS_CLEAR 27047
#define IDS_DONE 27048
#define IDS_LEARN_MORE 221
#define IDS_OK 27049
#define IDS_RELOAD 27050
#define IDS_ADD 27051
#define IDS_REMOVE 27052
#define IDS_SAVE 27053
#define IDS_MENU 27054
#define IDS_INSTALL 27055
#define IDS_UPDATE 27056
#define IDS_NO_THANKS 27059
#define IDS_NOT_NOW 27060
#define IDS_TURN_OFF 27061
#define IDS_PLUGIN_NOT_SUPPORTED 27062
#define IDS_PRINT 334
#define IDS_RECENTLY_CLOSED 319
#define IDS_CHOOSE 27063
#define IDS_ACCNAME_BACK 253
#define IDS_ACCNAME_FORWARD 256
#define IDS_ACCNAME_OK 27064
#define IDS_ACCNAME_CANCEL 27065
#define IDS_ACCNAME_DONE 27066
#define IDS_ACCNAME_SAVE 27067
#define IDS_ACCNAME_CLOSE 286
#define IDS_ACCNAME_OPEN 27068
#define IDS_ACCNAME_PREVIOUS 27069
#define IDS_ACCNAME_NEXT 27070
#define IDS_ACCNAME_LOCATION 27071
#define IDS_ACCNAME_PARTICLE_DISC 27072
#define IDS_ACCNAME_TAB_LIST 27073
#define IDS_UTILITY_PROCESS_JSON_PARSER_NAME 27074
#define IDS_SESSION_CRASHED_VIEW_RESTORE_BUTTON 27075
#define IDS_SESSION_CRASHED_VIEW_STARTUP_PAGES_BUTTON 27076
#define IDS_OPTIONS_ADVANCED_SECTION_TITLE_PRIVACY 27077
#define IDS_OPTIONS_ADVANCED_SECTION_TITLE_SAFETY_CHECK 27078
#define IDS_PATCH_SERVICE_DISPLAY_NAME 27079
#define IDS_UNZIP_SERVICE_DISPLAY_NAME 27080

// ---------------------------------------------------------------------------
// From extensions_strings.h:

#define IDS_EXTENSION_BAD_FILE_ENCODING 31060
#define IDS_EXTENSION_CANT_GET_ABSOLUTE_PATH 31061
#define IDS_EXTENSION_CONTAINS_PRIVATE_KEY 31062
#define IDS_EXTENSION_CRX_EXISTS 31063
#define IDS_EXTENSION_DIRECTORY_NO_EXISTS 31064
#define IDS_EXTENSION_ERROR_WHILE_SIGNING 31065
#define IDS_EXTENSION_FAILED_DURING_PACKAGING 31066
#define IDS_EXTENSION_LOAD_ABOUT_PAGE_FAILED 31067
#define IDS_EXTENSION_LOAD_BACKGROUND_SCRIPT_FAILED 31068
#define IDS_EXTENSION_LOAD_BACKGROUND_PAGE_FAILED 31069
#define IDS_EXTENSION_LOAD_CSS_FAILED 31070
#define IDS_EXTENSION_LOAD_JAVASCRIPT_FAILED 31071
#define IDS_EXTENSION_LOAD_OPTIONS_PAGE_FAILED 31072
#define IDS_EXTENSION_LOCALES_NO_DEFAULT_LOCALE_SPECIFIED 31073
#define IDS_EXTENSION_MANIFEST_UNREADABLE 31074
#define IDS_EXTENSION_MANIFEST_INVALID 31075
#define IDS_EXTENSION_PACKAGE_IMAGE_ERROR 31076
#define IDS_EXTENSION_PACKAGE_UNZIP_ERROR 31077
#define IDS_EXTENSION_PRIVATE_KEY_EXISTS 31078
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_READ 31079
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_EXPORT 31080
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_GENERATE 31081
#define IDS_EXTENSION_PRIVATE_KEY_FAILED_TO_OUTPUT 31082
#define IDS_EXTENSION_PRIVATE_KEY_INVALID 31083
#define IDS_EXTENSION_PRIVATE_KEY_NO_EXISTS 31084
#define IDS_EXTENSION_PRIVATE_KEY_INVALID_PATH 31085
#define IDS_EXTENSION_PRIVATE_KEY_INVALID_FORMAT 31086
#define IDS_EXTENSION_PUBLIC_KEY_FAILED_TO_EXPORT 31087
#define IDS_EXTENSION_SHARING_VIOLATION 31088
#define IDS_EXTENSION_CANT_INSTALL_POLICY_BLOCKED 31089
#define IDS_EXTENSION_CANT_MODIFY_POLICY_REQUIRED 31090
#define IDS_EXTENSION_CANT_UNINSTALL_POLICY_REQUIRED 31091
#define IDS_EXTENSION_DISABLED_UPDATE_REQUIRED_BY_POLICY 31092
#define IDS_DEVICE_NAME_WITH_PRODUCT_SERIAL 31093
#define IDS_DEVICE_NAME_WITH_PRODUCT_UNKNOWN_VENDOR 31094
#define IDS_DEVICE_NAME_WITH_PRODUCT_UNKNOWN_VENDOR_SERIAL 31095
#define IDS_DEVICE_NAME_WITH_PRODUCT_VENDOR 31096
#define IDS_DEVICE_NAME_WITH_PRODUCT_VENDOR_SERIAL 31097
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_UNKNOWN_VENDOR 31098
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_UNKNOWN_VENDOR_SERIAL 31099
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_VENDOR 31100
#define IDS_DEVICE_NAME_WITH_UNKNOWN_PRODUCT_VENDOR_SERIAL 31101
#define IDS_DEVICE_PERMISSIONS_PROMPT_SINGLE_SELECTION 31102
#define IDS_DEVICE_PERMISSIONS_PROMPT_MULTIPLE_SELECTION 31103
#define IDS_CAMERA_FACING_USER 31104
#define IDS_CAMERA_FACING_ENVIRONMENT 31105
#define IDS_EXTENSION_USB_DEVICE_PRODUCT_NAME_AND_VENDOR 164
#define IDS_EXTENSION_TASK_MANAGER_APPVIEW_TAG_PREFIX 31106
#define IDS_EXTENSION_TASK_MANAGER_EXTENSIONOPTIONS_TAG_PREFIX 31107
#define IDS_EXTENSION_TASK_MANAGER_MIMEHANDLERVIEW_TAG_PREFIX 31108
#define IDS_EXTENSION_TASK_MANAGER_WEBVIEW_TAG_PREFIX 31109
#define IDS_EXTENSION_WARNINGS_NETWORK_DELAY 31110
#define IDS_EXTENSION_WARNINGS_DOWNLOAD_FILENAME_CONFLICT 31111
#define IDS_EXTENSION_WARNING_RELOAD_TOO_FREQUENT 31112
#define IDS_EXTENSION_WARNING_RULESET_FAILED_TO_LOAD 31113
#define IDS_EXTENSION_WARNING_ENABLED_RULE_COUNT_EXCEEDED 31114
#define IDS_EXTENSION_INSTALL_PROCESS_CRASHED 31115
#define IDS_EXTENSION_PACKAGE_ERROR_CODE 31116
#define IDS_EXTENSION_PACKAGE_ERROR_MESSAGE 31117
#define IDS_EXTENSION_PACKAGE_INSTALL_ERROR 31118
#define IDS_EXTENSION_UNPACK_FAILED 31119
#define IDS_EXTENSION_WEBGL_NOT_SUPPORTED 31120

// ---------------------------------------------------------------------------
// From generated_resources.h:

#define IDS_ACCESS_CODE_CAST_ACCESS_CODE_MESSAGE 1000
#define IDS_ACCESS_CODE_CAST_BACK 1001
#define IDS_ACCESS_CODE_CAST_CAST 1002
#define IDS_ACCESS_CODE_CAST_CONNECT 1003
#define IDS_ACCESS_CODE_CAST_DIALOG_TITLE 1004
#define IDS_ACCESS_CODE_CAST_ENTER_CHARACTER 1005
#define IDS_ACCESS_CODE_CAST_ERROR_ACCESS_CODE 1006
#define IDS_ACCESS_CODE_CAST_ERROR_NETWORK 1007
#define IDS_ACCESS_CODE_CAST_ERROR_PERMISSION 1008
#define IDS_ACCESS_CODE_CAST_ERROR_TOO_MANY_REQUESTS 1009
#define IDS_ACCESS_CODE_CAST_ERROR_UNKNOWN 1010
#define IDS_ACCESS_CODE_CAST_INPUT_ARIA_LABEL 1011
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_DAYS 1012
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_HOURS 1013
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_MONTHS 1014
#define IDS_ACCESS_CODE_CAST_MANAGED_FOOTNOTE_YEARS 1015
#define IDS_ACCESS_CODE_CAST_SUBMIT 1016
#define IDS_ACCESS_CODE_CAST_USE_CAMERA 1017
#define IDS_BOOKMARK_GROUP_FROM_IE 1018
#define IDS_BOOKMARK_GROUP_FROM_EDGE 1019
#define IDS_BOOKMARK_GROUP_FROM_FIREFOX 1020
#define IDS_BOOKMARK_GROUP_FROM_SAFARI 1021
#define IDS_BOOKMARK_GROUP 1022
#define IDS_BOOKMARK_BAR_SHOW_APPS_SHORTCUT 1023
#define IDS_BOOKMARK_BAR_SHOW_READING_LIST 1024
#define IDS_BOOKMARK_BAR_SHOW_MANAGED_BOOKMARKS_DEFAULT_NAME 1025
#define IDS_BOOKMARK_BAR_SHOW_MANAGED_BOOKMARKS 1026
#define IDS_BOOKMARK_BAR_APPS_SHORTCUT_NAME 287
#define IDS_BOOKMARK_BAR_APPS_SHORTCUT_TOOLTIP 288
#define IDS_BOOKMARK_BAR_OVERFLOW_BUTTON_TOOLTIP 1027
#define IDS_BOOKMARK_BAR_OPEN_ALL 1028
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT 1029
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT_NEW_WINDOW 1030
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT_INCOGNITO 1031
#define IDS_BOOKMARK_BAR_OPEN_ALL_COUNT_NEW_TAB_GROUP 1032
#define IDS_BOOKMARK_BAR_OPEN_IN_NEW_TAB 1033
#define IDS_BOOKMARK_BAR_OPEN_IN_NEW_WINDOW 1034
#define IDS_BOOKMARK_BAR_OPEN_INCOGNITO 1035
#define IDS_BOOKMARK_BAR_EDIT 1036
#define IDS_BOOKMARK_BAR_RENAME_FOLDER 1037
#define IDS_BOOKMARK_BAR_REMOVE 1038
#define IDS_BOOKMARK_BAR_ADD_NEW_BOOKMARK 1039
#define IDS_BOOKMARK_BAR_NEW_FOLDER 1040
#define IDS_SHOW_BOOKMARK_BAR 327
#define IDS_BOOKMARK_BAR_SHOULD_OPEN_ALL 1041
#define IDS_BOOKMARK_BUBBLE_PAGE_BOOKMARKED 1042
#define IDS_BOOKMARK_BUBBLE_PAGE_BOOKMARK 1043
#define IDS_BOOKMARK_BUBBLE_NAME_LABEL 1044
#define IDS_BOOKMARK_AX_BUBBLE_NAME_LABEL 1045
#define IDS_BOOKMARK_BUBBLE_FOLDER_LABEL 1046
#define IDS_BOOKMARK_AX_BUBBLE_FOLDER_LABEL 1047
#define IDS_BOOKMARK_BUBBLE_OPTIONS 1048
#define IDS_BOOKMARK_BUBBLE_CHOOSER_ANOTHER_FOLDER 1049
#define IDS_BOOKMARK_DICE_PROMO_SYNC_MESSAGE 1050
#define IDS_BOOKMARK_BUBBLE_DESKTOP_TO_IOS_PROMO_TITLE 1051
#define IDS_BOOKMARK_BUBBLE_DESKTOP_TO_IOS_PROMO_TITLE_V2 1052
#define IDS_BOOKMARK_BUBBLE_DESKTOP_TO_IOS_PROMO_TITLE_V3 1053
#define IDS_BOOKMARK_FOOTNOTE_DESKTOP_TO_IOS_PROMO_MESSAGE 1054
#define IDS_BOOKMARK_EDITOR_NAME_LABEL 1055
#define IDS_BOOKMARK_AX_EDITOR_NAME_LABEL 1056
#define IDS_BOOKMARK_AX_EDITOR_URL_LABEL 1058
#define IDS_BOOKMARK_EDITOR_URL_LABEL 1059
#define IDS_BOOKMARK_EDITOR_CONFIRM_DELETE 1060
#define IDS_BOOKMARK_EDITOR_NEW_FOLDER_BUTTON 1061
#define IDS_BOOKMARK_EDITOR_NEW_FOLDER_MENU_ITEM 1062
#define IDS_BOOKMARK_FOLDER_EDITOR_WINDOW_TITLE 1063
#define IDS_BOOKMARK_FOLDER_EDITOR_WINDOW_TITLE_NEW 1064
#define IDS_BOOKMARK_ALL_TABS_DIALOG_TITLE 1065
#define IDS_BOOKMARK_MANAGER_TITLE 1066
#define IDS_BOOKMARK_MANAGER_SEARCH_BUTTON 1067
#define IDS_BOOKMARK_MANAGER 328
#define IDS_BOOKMARK_MANAGER_ORGANIZE_MENU 1068
#define IDS_BOOKMARK_MANAGER_INVALID_URL 1069
#define IDS_EXPORT_BOOKMARKS_DEFAULT_FILENAME 1070
#define IDS_BOOKMARK_MANAGER_ADD_BOOKMARK_TITLE 1071
#define IDS_BOOKMARK_MANAGER_ADD_FOLDER_TITLE 1072
#define IDS_BOOKMARK_MANAGER_CLEAR_SEARCH 1073
#define IDS_BOOKMARK_MANAGER_EMPTY_LIST 1074
#define IDS_BOOKMARK_MANAGER_EMPTY_UNMODIFIABLE_LIST 1075
#define IDS_BOOKMARK_MANAGER_FOLDER_LABEL 1076
#define IDS_BOOKMARK_MANAGER_FOLDER_RENAME_TITLE 1077
#define IDS_BOOKMARK_MANAGER_FOLDER_LIST_CHANGED 1078
#define IDS_BOOKMARK_MANAGER_LIST_AX_LABEL 1079
#define IDS_BOOKMARK_MANAGER_MENU_ADD_BOOKMARK 1080
#define IDS_BOOKMARK_MANAGER_MENU_ADD_FOLDER 1081
#define IDS_BOOKMARK_MANAGER_MENU_CUT 1082
#define IDS_BOOKMARK_MANAGER_MENU_COPY 1083
#define IDS_BOOKMARK_MANAGER_MENU_PASTE 1084
#define IDS_BOOKMARK_MANAGER_MENU_EXPORT 1085
#define IDS_BOOKMARK_MANAGER_MENU_HELP_CENTER 1086
#define IDS_BOOKMARK_MANAGER_MENU_IMPORT 1087
#define IDS_BOOKMARK_MANAGER_MENU_IMPORT_BEGAN 1088
#define IDS_BOOKMARK_MANAGER_MENU_IMPORT_ENDED 1089
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL 1090
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_WITH_COUNT 1091
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_NEW_WINDOW 1092
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_NEW_WINDOW_WITH_COUNT 1093
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_INCOGNITO 1094
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_ALL_INCOGNITO_WITH_COUNT 1095
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_IN_NEW_TAB 1096
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_IN_NEW_WINDOW 1097
#define IDS_BOOKMARK_MANAGER_MENU_OPEN_INCOGNITO 1098
#define IDS_BOOKMARK_MANAGER_MENU_RENAME 1099
#define IDS_BOOKMARK_MANAGER_MENU_SHOW_IN_FOLDER 1100
#define IDS_BOOKMARK_MANAGER_MENU_SORT 1101
#define IDS_BOOKMARK_MANAGER_MORE_ACTIONS 1102
#define IDS_BOOKMARK_MANAGER_MORE_ACTIONS_AX_LABEL 1103
#define IDS_BOOKMARK_MANAGER_MORE_ACTIONS_MULTI_AX_LABEL 1104
#define IDS_BOOKMARK_MANAGER_OPEN_DIALOG_TITLE 1105
#define IDS_BOOKMARK_MANAGER_OPEN_DIALOG_CONFIRM 1106
#define IDS_BOOKMARK_MANAGER_ITEMS_SELECTED 1107
#define IDS_BOOKMARK_MANAGER_ITEMS_UNSELECTED 1108
#define IDS_BOOKMARK_MANAGER_SIDEBAR_AX_LABEL 1109
#define IDS_BOOKMARK_MANAGER_TOAST_FOLDER_SORTED 1110
#define IDS_BOOKMARK_MANAGER_TOAST_ITEM_DELETED 1111
#define IDS_BOOKMARK_MANAGER_TOAST_ITEMS_DELETED 1112
#define IDS_BOOKMARK_MANAGER_TOAST_ITEM_COPIED 1113
#define IDS_BOOKMARK_MANAGER_TOAST_ITEMS_COPIED 1114
#define IDS_BOOKMARKS_MENU 330
#define IDS_BOOKMARK_THIS_TAB 324
#define IDS_BOOKMARK_ALL_TABS 325
#define IDS_TOOLTIP_STARRED 1115
#define IDS_BOOKMARK_SCREEN_READER_CREATED 1116
#define IDS_BOOKMARK_SCREEN_READER_REORDERED 1117
#define IDS_BOOKMARK_SCREEN_READER_MOVED 1118
#define IDS_APP_MANAGEMENT_CAMERA 1119
#define IDS_APP_MANAGEMENT_LOCATION 1120
#define IDS_APP_MANAGEMENT_MICROPHONE 1121
#define IDS_APP_MANAGEMENT_NO_APPS_FOUND 1122
#define IDS_APP_MANAGEMENT_NOTIFICATIONS 1123
#define IDS_APP_MANAGEMENT_PERMISSIONS 1124
#define IDS_APP_MANAGEMENT_MORE_SETTINGS 1125
#define IDS_APP_MANAGEMENT_PIN_TO_SHELF 1126
#define IDS_APP_MANAGEMENT_PRESET_WINDOW_SIZES 1127
#define IDS_APP_MANAGEMENT_WINDOW 1128
#define IDS_APP_MANAGEMENT_PRESET_WINDOW_SIZES_TEXT 1129
#define IDS_APP_MANAGEMENT_PRINTING 1130
#define IDS_APP_MANAGEMENT_SEARCH_PROMPT 1131
#define IDS_APP_MANAGEMENT_UNINSTALL_APP 1132
#define IDS_APP_MANAGEMENT_CONTACTS 1133
#define IDS_APP_MANAGEMENT_STORAGE 1134
#define IDS_APP_MANAGEMENT_RUN_ON_OS_LOGIN 1135
#define IDS_APP_MANAGEMENT_POLICY_APP_POLICY_STRING 1136
#define IDS_APP_MANAGEMENT_INTENT_SETTINGS_TITLE 1137
#define IDS_APP_MANAGEMENT_INTENT_SHARING_APP_OPEN 1138
#define IDS_APP_MANAGEMENT_INTENT_SHARING_BROWSER_OPEN 1139
#define IDS_APP_MANAGEMENT_INTENT_SHARING_TAB_EXPLANATION 1140
#define IDS_APP_MANAGEMENT_INTENT_SETTINGS_DIALOG_TITLE 1141
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_CHANGE_BUTTON 1142
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TITLE 1143
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_1_APP 1144
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_2_APPS 1145
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_3_APPS 1146
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_4_APPS 1147
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_DIALOG_TEXT_5_OR_MORE_APPS 1148
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_1_APP 1149
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_2_APPS 1150
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_3_APPS 1151
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_4_APPS 1152
#define IDS_APP_MANAGEMENT_INTENT_OVERLAP_WARNING_TEXT_5_OR_MORE_APPS 1153
#define IDS_APP_MANAGEMENT_APP_DETAILS_TITLE 1154
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_ANDROID 1155
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_CHROME 1156
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_WEB 1157
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_SYSTEM 1158
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_CROS_SYSTEM 1159
#define IDS_APP_MANAGEMENT_APP_DETAILS_INSTALL_SOURCE_WEB_STORE 1160
#define IDS_APP_MANAGEMENT_APP_DETAILS_INSTALL_SOURCE_PLAY_STORE 1161
#define IDS_APP_MANAGEMENT_APP_DETAILS_INSTALL_SOURCE_BROWSER 1162
#define IDS_APP_MANAGEMENT_APP_DETAILS_TYPE_AND_SOURCE_COMBINED 1163
#define IDS_APP_MANAGEMENT_APP_DETAILS_VERSION 1164
#define IDS_APP_MANAGEMENT_APP_DETAILS_STORAGE_TITLE 1165
#define IDS_APP_MANAGEMENT_APP_DETAILS_APP_SIZE 1166
#define IDS_APP_MANAGEMENT_APP_DETAILS_DATA_SIZE 1167
#define IDS_APP_MANAGEMENT_FILE_HANDLING_HEADER 1168
#define IDS_APP_MANAGEMENT_FILE_HANDLING_OVERFLOW_DIALOG_TITLE 1169
#define IDS_APP_MANAGEMENT_FILE_HANDLING_SET_DEFAULTS_LINK 1170
#define IDS_APP_MANAGEMENT_FILE_HANDLING_TYPES 1171
#define IDS_MEDIA_ROUTER_ICON_TOOLTIP_TEXT 271
#define IDS_MEDIA_ROUTER_MENU_ITEM_TITLE 335
#define IDS_MEDIA_ROUTER_PRESENTATION_CAST_MODE 1172
#define IDS_MEDIA_ROUTER_DESKTOP_MIRROR_CAST_MODE 1173
#define IDS_MEDIA_ROUTER_TAB_MIRROR_CAST_MODE 1174
#define IDS_MEDIA_ROUTER_LOCAL_FILE_CAST_MODE 1175
#define IDS_MEDIA_ROUTER_CAST_LOCAL_MEDIA_TITLE 1176
#define IDS_MEDIA_ROUTER_ALTERNATIVE_SOURCES_BUTTON 1177
#define IDS_MEDIA_ROUTER_ABOUT 265
#define IDS_MEDIA_ROUTER_HELP 266
#define IDS_MEDIA_ROUTER_ALWAYS_SHOW_TOOLBAR_ACTION 267
#define IDS_MEDIA_ROUTER_REPORT_ISSUE 270
#define IDS_MEDIA_ROUTER_SHOWN_BY_POLICY 1178
#define IDS_MEDIA_ROUTER_TOGGLE_MEDIA_REMOTING 268
#define IDS_MEDIA_ROUTER_ISSUE_CREATE_ROUTE_TIMEOUT 1179
#define IDS_MEDIA_ROUTER_ISSUE_CREATE_ROUTE_TIMEOUT_FOR_DESKTOP 1180
#define IDS_MEDIA_ROUTER_ISSUE_CREATE_ROUTE_TIMEOUT_FOR_TAB 1181
#define IDS_MEDIA_ROUTER_ISSUE_UNABLE_TO_CAST_DESKTOP 1182
#define IDS_MEDIA_ROUTER_ISSUE_FILE_CAST_GENERIC_ERROR 1183
#define IDS_MEDIA_ROUTER_ISSUE_FILE_CAST_ERROR 1184
#define IDS_MEDIA_ROUTER_ISSUE_TAB_AUDIO_NOT_SUPPORTED 1185
#define IDS_MEDIA_ROUTER_STATUS_LOOKING_FOR_DEVICES 1187
#define IDS_MEDIA_ROUTER_STATUS_NO_DEVICES_FOUND 1188
#define IDS_MEDIA_ROUTER_NO_DEVICES_FOUND_BUTTON 1189
#define IDS_MEDIA_ROUTER_DESTINATION_MISSING 1190
#define IDS_MEDIA_ROUTER_SINK_AVAILABLE 1191
#define IDS_MEDIA_ROUTER_SINK_CONNECTING 1192
#define IDS_MEDIA_ROUTER_SINK_DISCONNECTING 1193
#define IDS_MEDIA_ROUTER_STOP_CASTING 1194
#define IDS_MEDIA_ROUTER_SOURCE_NOT_SUPPORTED 1195
#define IDS_MEDIA_ROUTER_AVAILABLE_SPECIFIC_SITES 1196
#define IDS_MEDIA_ROUTER_CASTING_DESKTOP 1197
#define IDS_MEDIA_ROUTER_CASTING_TAB 1198
#define IDS_MEDIA_ROUTER_PRESENTATION_ROUTE_DESCRIPTION 1199
#define IDS_MEDIA_ROUTER_FILE_DIALOG_AUDIO_VIDEO_FILTER 1200
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_TITLE 1201
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_BODY_TEXT 1202
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_CHECKBOX 1203
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_OPTIMIZE_BUTTON 1204
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_CANCEL_BUTTON 1205
#define IDS_MEDIA_ROUTER_REMOTING_DIALOG_CANCEL_BUTTON_MACOS 1206
#define IDS_MEDIA_ROUTER_CAST_TO_MEETING_DEPRECATED 1207
#define IDS_MEDIA_ROUTER_CAST_TO_MEETING_REMOVED 1208
#define IDS_MEDIA_ROUTER_WIRED_DISPLAY_SINK_NAME 1209
#define IDS_MEDIA_ROUTER_FEEDBACK_TITLE 1210
#define IDS_MEDIA_ROUTER_FEEDBACK_NA 1211
#define IDS_MEDIA_ROUTER_FEEDBACK_FORM_DESCRIPTION 1212
#define IDS_MEDIA_ROUTER_FEEDBACK_HEADER 1213
#define IDS_MEDIA_ROUTER_FEEDBACK_YOUR_ANSWER 1214
#define IDS_MEDIA_ROUTER_FEEDBACK_REQUIRED 1215
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_QUESTION 1216
#define IDS_MEDIA_ROUTER_FEEDBACK_PROMPT 1217
#define IDS_MEDIA_ROUTER_FEEDBACK_MIRRORING_QUALITY_SUBHEADING 1218
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_SMOOTHNESS 1219
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_QUALITY 1220
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_QUALITY 1221
#define IDS_MEDIA_ROUTER_FEEDBACK_CONTENT_QUESTION 1222
#define IDS_MEDIA_ROUTER_FEEDBACK_ADDITIONAL_COMMENTS 1223
#define IDS_MEDIA_ROUTER_FEEDBACK_ALLOW_CONTACT_BY_EMAIL 1224
#define IDS_MEDIA_ROUTER_FEEDBACK_YOUR_EMAIL_ADDRESS 1225
#define IDS_MEDIA_ROUTER_FEEDBACK_EMAIL_FIELD 1226
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_BUTTON 1227
#define IDS_MEDIA_ROUTER_FEEDBACK_DISCARD_CONFIRMATION 1228
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_BUG_OR_ERROR 1229
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_FEATURE_REQUEST 1230
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_PROJECTION_QUALITY 1231
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_DISCOVERY 1232
#define IDS_MEDIA_ROUTER_FEEDBACK_TYPE_OTHER 1233
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_FREEZES 1234
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_JERKY 1235
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_STUTTER 1236
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_SMOOTH 1237
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_PERFECT 1238
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_UNWATCHABLE 1239
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_POOR 1240
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_ACCEPTABLE 1241
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_GOOD 1242
#define IDS_MEDIA_ROUTER_FEEDBACK_VIDEO_GREAT 1243
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_UNINTELLIGIBLE 1244
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_POOR 1245
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_ACCEPTABLE 1246
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_GOOD 1247
#define IDS_MEDIA_ROUTER_FEEDBACK_AUDIO_PERFECT 1248
#define IDS_MEDIA_ROUTER_FEEDBACK_SENDING 1249
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_FAIL 1250
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_SUCCESS 1251
#define IDS_MEDIA_ROUTER_FEEDBACK_RESENDING 1252
#define IDS_MEDIA_ROUTER_FEEDBACK_LOGS_HEADER 1253
#define IDS_MEDIA_ROUTER_FEEDBACK_FINE_LOGS_WARNING 1254
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_LOGS_HTML 1255
#define IDS_MEDIA_ROUTER_FEEDBACK_SEND_LOGS 1256
#define IDS_MEDIA_ROUTER_FEEDBACK_PRIVACY_DATA_USAGE 1257
#define IDS_MEDIA_ROUTER_FEEDBACK_SETUP_VISIBILITY_QUESTION 1258
#define IDS_MEDIA_ROUTER_FEEDBACK_YES 1259
#define IDS_MEDIA_ROUTER_FEEDBACK_NO 1260
#define IDS_MEDIA_ROUTER_FEEDBACK_DID_NOT_TRY 1261
#define IDS_MEDIA_ROUTER_FEEDBACK_SOFTWARE_QUESTION 1262
#define IDS_MEDIA_ROUTER_FEEDBACK_UNKNOWN 1263
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_QUESTION 1264
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_SAME_WIFI 1265
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_DIFFERENT_WIFI 1266
#define IDS_MEDIA_ROUTER_FEEDBACK_NETWORK_WIRED_PC 1267
#define IDS_GLOBAL_MEDIA_CONTROLS_ICON_TOOLTIP_TEXT 272
#define IDS_GLOBAL_MEDIA_CONTROLS_DEVICES_LABEL_WITH_COLON 1268
#define IDS_GLOBAL_MEDIA_CONTROLS_DEVICES_LABEL 1269
#define IDS_GLOBAL_MEDIA_CONTROLS_STOP_CASTING_BUTTON_LABEL 1270
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_ENGLISH_ONLY 1271
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION 1272
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_SHOW_LANGUAGE 1273
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_DOWNLOAD_PROGRESS 1274
#define IDS_GLOBAL_MEDIA_CONTROLS_LIVE_CAPTION_DOWNLOAD_ERROR 1275
#define IDS_GLOBAL_MEDIA_CONTROLS_DIALOG_NAME 1276
#define IDS_GLOBAL_MEDIA_CONTROLS_SHOW_DEVICE_LIST 1277
#define IDS_GLOBAL_MEDIA_CONTROLS_HIDE_DEVICE_LIST 1278
#define IDS_GLOBAL_MEDIA_CONTROLS_CONTROL_CAST_SESSIONS_PROMO 1279
#define IDS_MEDIA_TOOLBAR_CONTEXT_REPORT_CAST_ISSUE 1280
#define IDS_MEDIA_TOOLBAR_CONTEXT_SHOW_OTHER_SESSIONS 1281
#define IDS_CHILD_INFO_ONE_CUSTODIAN 1282
#define IDS_CHILD_INFO_TWO_CUSTODIANS 1283
#define IDS_AVATAR_BUTTON_GUEST 1284
#define IDS_AVATAR_BUTTON_GUEST_TOOLTIP 1285
#define IDS_GUEST_BUBBLE_ACCESSIBLE_TITLE 1286
#define IDS_INCOGNITO_BUBBLE_ACCESSIBLE_TITLE 1287
#define IDS_AVATAR_BUTTON_INCOGNITO 1288
#define IDS_AVATAR_BUTTON_INCOGNITO_TOOLTIP 1289
#define IDS_AVATAR_BUTTON_SYNC_ERROR 1290
#define IDS_AVATAR_BUTTON_SYNC_ERROR_TOOLTIP 1291
#define IDS_AVATAR_BUTTON_SYNC_PAUSED 1292
#define IDS_BLOCK_INTERSTITIAL_DEFAULT_FEEDBACK_TEXT 1293
#define IDS_PROFILES_MENU_NAME 1294
#define IDS_PROFILES_PROFILE_BUBBLE_ACCESSIBLE_TITLE 1297
#define IDS_PROFILES_EXIT_PROFILE_BUTTON 1298
#define IDS_PROFILES_GAIA_SIGNIN_TITLE 1299
#define IDS_PROFILES_ACCOUNT_REMOVAL_TITLE 1300
#define IDS_PROFILES_SYNC_COMPLETE_TITLE 1301
#define IDS_PROFILES_OPEN_SYNC_SETTINGS_BUTTON 1302
#define IDS_PROFILES_DICE_SIGNIN_BUTTON 1303
#define IDS_PROFILES_DICE_NOT_SYNCING_TITLE 1304
#define IDS_PROFILES_DICE_SIGNIN_FIRST_ACCOUNT_BUTTON 1305
#define IDS_PROFILES_DICE_SIGNIN_FIRST_ACCOUNT_BUTTON_NO_NAME 1306
#define IDS_PROFILES_DICE_SYNC_DISABLED_TITLE 1307
#define IDS_PROFILES_DICE_SYNC_PAUSED_TITLE 1308
#define IDS_PROFILES_CLOSE_X_WINDOWS_BUTTON 1309
#define IDS_PROFILES_SIGNIN_PROMO 1310
#define IDS_PROFILES_PASSWORDS_LINK 1311
#define IDS_PROFILES_CREDIT_CARDS_LINK 1312
#define IDS_PROFILES_ADDRESSES_LINK 1313
#define IDS_PROFILES_LIST_PROFILES_TITLE 1314
#define IDS_PROFILES_PROFILE_MANAGE_ACCOUNTS_BUTTON 1315
#define IDS_PROFILES_PROFILE_HIDE_MANAGE_ACCOUNTS_BUTTON 1316
#define IDS_PROFILES_MANAGE_PROFILES_BUTTON_TOOLTIP 1317
#define IDS_PROFILES_CUSTOMIZE_PROFILE_BUTTON_TOOLTIP 1318
#define IDS_PROFILES_GUEST_PROFILE_NAME 1319
#define IDS_DEFAULT_PROFILE_NAME 1320
#define IDS_LEGACY_DEFAULT_PROFILE_NAME 1321
#define IDS_NUMBERED_PROFILE_NAME 1322
#define IDS_NEW_NUMBERED_PROFILE_NAME 1323
#define IDS_SINGLE_PROFILE_DISPLAY_NAME 1324
#define IDS_GUEST_PROFILE_NAME 1325
#define IDS_DEFAULT_AVATAR_NAME_8 1326
#define IDS_DEFAULT_AVATAR_NAME_9 1327
#define IDS_DEFAULT_AVATAR_NAME_10 1328
#define IDS_DEFAULT_AVATAR_NAME_11 1329
#define IDS_DEFAULT_AVATAR_NAME_12 1330
#define IDS_DEFAULT_AVATAR_NAME_13 1331
#define IDS_DEFAULT_AVATAR_NAME_14 1332
#define IDS_DEFAULT_AVATAR_NAME_15 1333
#define IDS_DEFAULT_AVATAR_NAME_16 1334
#define IDS_DEFAULT_AVATAR_NAME_17 1335
#define IDS_DEFAULT_AVATAR_NAME_18 1336
#define IDS_DEFAULT_AVATAR_NAME_19 1337
#define IDS_DEFAULT_AVATAR_NAME_20 1338
#define IDS_DEFAULT_AVATAR_NAME_21 1339
#define IDS_DEFAULT_AVATAR_NAME_22 1340
#define IDS_DEFAULT_AVATAR_NAME_23 1341
#define IDS_DEFAULT_AVATAR_NAME_24 1342
#define IDS_DEFAULT_AVATAR_NAME_25 1343
#define IDS_DEFAULT_AVATAR_NAME_26 1344
#define IDS_DEFAULT_AVATAR_LABEL_0 1345
#define IDS_DEFAULT_AVATAR_LABEL_1 1346
#define IDS_DEFAULT_AVATAR_LABEL_2 1347
#define IDS_DEFAULT_AVATAR_LABEL_3 1348
#define IDS_DEFAULT_AVATAR_LABEL_4 1349
#define IDS_DEFAULT_AVATAR_LABEL_5 1350
#define IDS_DEFAULT_AVATAR_LABEL_6 1351
#define IDS_DEFAULT_AVATAR_LABEL_7 1352
#define IDS_DEFAULT_AVATAR_LABEL_8 1353
#define IDS_DEFAULT_AVATAR_LABEL_9 1354
#define IDS_DEFAULT_AVATAR_LABEL_10 1355
#define IDS_DEFAULT_AVATAR_LABEL_11 1356
#define IDS_DEFAULT_AVATAR_LABEL_12 1357
#define IDS_DEFAULT_AVATAR_LABEL_13 1358
#define IDS_DEFAULT_AVATAR_LABEL_14 1359
#define IDS_DEFAULT_AVATAR_LABEL_15 1360
#define IDS_DEFAULT_AVATAR_LABEL_16 1361
#define IDS_DEFAULT_AVATAR_LABEL_17 1362
#define IDS_DEFAULT_AVATAR_LABEL_18 1363
#define IDS_DEFAULT_AVATAR_LABEL_19 1364
#define IDS_DEFAULT_AVATAR_LABEL_20 1365
#define IDS_DEFAULT_AVATAR_LABEL_21 1366
#define IDS_DEFAULT_AVATAR_LABEL_22 1367
#define IDS_DEFAULT_AVATAR_LABEL_23 1368
#define IDS_DEFAULT_AVATAR_LABEL_24 1369
#define IDS_DEFAULT_AVATAR_LABEL_25 1370
#define IDS_DEFAULT_AVATAR_LABEL_26 1371
#define IDS_DEFAULT_AVATAR_LABEL_27 1372
#define IDS_DEFAULT_AVATAR_LABEL_28 1373
#define IDS_DEFAULT_AVATAR_LABEL_29 1374
#define IDS_DEFAULT_AVATAR_LABEL_30 1375
#define IDS_DEFAULT_AVATAR_LABEL_31 1376
#define IDS_DEFAULT_AVATAR_LABEL_32 1377
#define IDS_DEFAULT_AVATAR_LABEL_33 1378
#define IDS_DEFAULT_AVATAR_LABEL_34 1379
#define IDS_DEFAULT_AVATAR_LABEL_35 1380
#define IDS_DEFAULT_AVATAR_LABEL_36 1381
#define IDS_DEFAULT_AVATAR_LABEL_37 1382
#define IDS_DEFAULT_AVATAR_LABEL_38 1383
#define IDS_DEFAULT_AVATAR_LABEL_39 1384
#define IDS_DEFAULT_AVATAR_LABEL_40 1385
#define IDS_DEFAULT_AVATAR_LABEL_41 1386
#define IDS_DEFAULT_AVATAR_LABEL_42 1387
#define IDS_DEFAULT_AVATAR_LABEL_43 1388
#define IDS_DEFAULT_AVATAR_LABEL_44 1389
#define IDS_DEFAULT_AVATAR_LABEL_45 1390
#define IDS_DEFAULT_AVATAR_LABEL_46 1391
#define IDS_DEFAULT_AVATAR_LABEL_47 1392
#define IDS_DEFAULT_AVATAR_LABEL_48 1393
#define IDS_DEFAULT_AVATAR_LABEL_49 1394
#define IDS_DEFAULT_AVATAR_LABEL_50 1395
#define IDS_DEFAULT_AVATAR_LABEL_51 1396
#define IDS_DEFAULT_AVATAR_LABEL_52 1397
#define IDS_DEFAULT_AVATAR_LABEL_53 1398
#define IDS_DEFAULT_AVATAR_LABEL_54 1399
#define IDS_DEFAULT_AVATAR_LABEL_55 1400
#define IDS_PROFILES_LOCAL_PROFILE_STATE 1401
#define IDS_PROFILES_CREATE_BUTTON_LABEL 1402
#define IDS_PROFILES_MANAGE_BUTTON_LABEL 1403
#define IDS_PROFILES_DEFAULT_NAME 151
#define IDS_SYNC_LOGIN_NAME_PROHIBITED 1404
#define IDS_SUPERVISED_USER_NOT_ALLOWED_BY_POLICY 1405
#define IDS_OLD_PROFILES_DISABLED_TITLE 1406
#define IDS_OLD_PROFILES_DISABLED_MESSAGE 1407
#define IDS_OLD_PROFILES_DISABLED_ADD_PERSON_SUGGESTION 1408
#define IDS_OLD_PROFILES_DISABLED_ADD_PERSON_SUGGESTION_WITH_DOMAIN 1409
#define IDS_OLD_PROFILES_DISABLED_REMOVED_OLD_PROFILE 1410
#define IDS_SYNC_USER_NAME_IN_USE_ERROR 1411
#define IDS_SYNC_USER_NAME_IN_USE_BY_ERROR 1412
#define IDS_SCREEN_LOCK_SIGN_OUT 1413
#define IDS_PROFILE_CUSTOMIZATION_DONE_BUTTON_LABEL 1420
#define IDS_PROFILE_CUSTOMIZATION_WELCOME 1421
#define IDS_PROFILE_CUSTOMIZATION_INPUT_LABEL 1422
#define IDS_ENTERPRISE_PROFILE_WELCOME_TITLE 1423
#define IDS_ENTERPRISE_PROFILE_WELCOME_ACCOUNT_MANAGED_BY 1424
#define IDS_ENTERPRISE_PROFILE_WELCOME_ACCOUNT_EMAIL_MANAGED_BY 1425
#define IDS_ENTERPRISE_PROFILE_WELCOME_DEVICE_MANAGED_BY 1426
#define IDS_ENTERPRISE_PROFILE_WELCOME_DEVICE_MANAGED 1427
#define IDS_ENTERPRISE_PROFILE_WELCOME_MANAGED_DESCRIPTION_WITH_SYNC 1428
#define IDS_ENTERPRISE_PROFILE_WELCOME_MANAGED_DESCRIPTION_WITHOUT_SYNC 1429
#define IDS_ENTERPRISE_WELCOME_PROFILE_REQUIRED_TITLE 1430
#define IDS_ENTERPRISE_WELCOME_PROFILE_WILL_BE_MANAGED_TITLE 1431
#define IDS_ENTERPRISE_PROFILE_WELCOME_CREATE_PROFILE_BUTTON 1432
#define IDS_ENTERPRISE_PROFILE_WELCOME_LINK_DATA_CHECKBOX 1433
#define IDS_PROFILE_PICKER_ADD_SPACE_BUTTON 1434
#define IDS_PROFILE_PICKER_BROWSE_AS_GUEST_BUTTON 1435
#define IDS_PROFILE_PICKER_BACK_BUTTON_ARIA_LABEL 1436
#define IDS_PROFILE_PICKER_BACK_BUTTON_SIGN_IN_LABEL 1437
#define IDS_PROFILE_PICKER_PROFILE_CARD_NEEDS_SIGNIN_PROMPT 1438
#define IDS_PROFILE_PICKER_PROFILE_CARD_LABEL 1439
#define IDS_PROFILE_PICKER_PROFILE_CARD_INPUT_LABEL 1440
#define IDS_PROFILE_PICKER_PROFILE_MENU_REMOVE_TEXT 1441
#define IDS_PROFILE_PICKER_PROFILE_MENU_REMOVE_CONFIRM 1442
#define IDS_PROFILE_PICKER_PROFILE_MENU_CUSTOMIZE_TEXT 1443
#define IDS_PROFILE_PICKER_PROFILE_MENU_INCOGNITO_TEXT 1444
#define IDS_PROFILE_PICKER_ASK_ON_STARTUP 1445
#define IDS_PROFILE_PICKER_REMOVE_WARNING_LOCAL_PROFILE_TITLE 1446
#define IDS_PROFILE_PICKER_REMOVE_WARNING_HISTORY 1447
#define IDS_PROFILE_PICKER_REMOVE_WARNING_PASSWORDS 1448
#define IDS_PROFILE_PICKER_REMOVE_WARNING_BOOKMARKS 1449
#define IDS_PROFILE_PICKER_REMOVE_WARNING_AUTOFILL 1450
#define IDS_PROFILE_PICKER_REMOVE_WARNING_CALCULATING 1451
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_SIGNIN_BUTTON_LABEL 1452
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_NOT_NOW_BUTTON_LABEL 1453
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_CUSTOMIZE_AVATAR_BUTTON_LABEL 1454
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_THEME_TEXT 1455
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_AVATAR_TEXT 1456
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_DONE 1457
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_AVATAR_DONE 1458
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_INPUT_NAME 1459
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_LOCAL_PROFILE_CREATION_SHORTCUT_TEXT 1460
#define IDS_PROFILE_PICKER_TAKE_A_TOUR_BUTTON_LABEL 1461
#define IDS_PROFILE_PICKER_IPH_NEXT_BUTTON_LABEL 1462
#define IDS_PROFILE_PICKER_IPH_DONE_BUTTON_LABEL 1463
#define IDS_PROFILE_PICKER_IPH_FOR_PROFILES_TITLE 1464
#define IDS_PROFILE_PICKER_IPH_FOR_ADD_PROFILE_TITLE 1465
#define IDS_PROFILE_PICKER_IPH_FOR_CUSTOMIZE_PROFILE_TITLE 1466
#define IDS_PROFILE_PICKER_IPH_FOR_CUSTOMIZE_PROFILE_TEXT 1467
#define IDS_PROFILE_PICKER_PROFILE_SWITCH_SWITCH_BUTTON_LABEL 1468
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_DEVICE_MANAGED_BY_DESCRIPTION 1469
#define IDS_PROFILE_PICKER_PROFILE_CREATION_FLOW_DEVICE_MANAGED_DESCRIPTION 1470
#define IDS_PROFILE_PICKER_REMOVE_WARNING_LOCAL_PROFILE 1471
#define IDS_PROFILE_PICKER_REMOVE_WARNING_SIGNED_IN_PROFILE_TITLE 1472
#define IDS_PROFILE_PICKER_REMOVE_WARNING_SIGNED_IN_PROFILE 1473
#define IDS_SETTINGS_EMPTY_STRING 5205
#define IDS_SETTINGS_CONTINUE 5206
#define IDS_SETTINGS_MORE_ACTIONS 5207
#define IDS_RELAUNCH_CONFIRMATION_DIALOG_BODY 5208
#define IDS_SETTINGS_ABOUT_PAGE_BROWSER_VERSION 5209
#define IDS_SETTINGS_ABOUT_PAGE_RELAUNCH 3415
#define IDS_SETTINGS_ACCESSIBILITY 5213
#define IDS_SETTINGS_ACCESSIBILITY_WEB_STORE 5214
#define IDS_SETTINGS_MORE_FEATURES_LINK 5215
#define IDS_SETTINGS_MORE_FEATURES_LINK_DESCRIPTION 5216
#define IDS_SETTINGS_ACCESSIBLE_IMAGE_LABELS_TITLE 5217
#define IDS_SETTINGS_ACCESSIBLE_IMAGE_LABELS_SUBTITLE 5218
#define IDS_SETTINGS_CAPTIONS_ENABLE_LIVE_CAPTION_TITLE 5219
#define IDS_SETTINGS_CAPTIONS_ENABLE_LIVE_CAPTION_SUBTITLE_ENGLISH_ONLY 5220
#define IDS_SETTINGS_CAPTIONS_ENABLE_LIVE_CAPTION_SUBTITLE 5221
#define IDS_SETTINGS_CAPTIONS_LIVE_CAPTION_DOWNLOAD_PROGRESS 5222
#define IDS_SETTINGS_CAPTIONS_LIVE_CAPTION_DOWNLOAD_COMPLETE 5223
#define IDS_SETTINGS_CAPTIONS_LIVE_CAPTION_DOWNLOAD_ERROR 5224
#define IDS_SETTINGS_ENABLE_CARET_BROWSING_TITLE 5225
#define IDS_SETTINGS_ENABLE_CARET_BROWSING_SUBTITLE 5226
#define IDS_SETTINGS_ACCESSIBILITY_FOCUS_HIGHLIGHT_DESCRIPTION 3772
#define IDS_SETTINGS_APPEARANCE 5227
#define IDS_SETTINGS_CUSTOM_WEB_ADDRESS 5228
#define IDS_SETTINGS_ENTER_CUSTOM_WEB_ADDRESS 5229
#define IDS_SETTINGS_HOME_BUTTON_DISABLED 5230
#define IDS_SETTINGS_THEMES 5231
#define IDS_SETTINGS_RESET_TO_DEFAULT_THEME 5236
#define IDS_SETTINGS_CHROME_COLORS 5237
#define IDS_SETTINGS_SHOW_HOME_BUTTON 5238
#define IDS_SETTINGS_SHOW_BOOKMARKS_BAR 5239
#define IDS_SETTINGS_HOME_PAGE_NTP 5240
#define IDS_SETTINGS_CHANGE_HOME_PAGE 5241
#define IDS_SETTINGS_WEB_STORE 5242
#define IDS_SETTINGS_READER_MODE 5245
#define IDS_SETTINGS_READER_MODE_DESCRIPTION 5246
#define IDS_SETTINGS_ADVANCED 5247
#define IDS_SETTINGS_BASIC 5248
#define IDS_SETTINGS_MENU_BUTTON_LABEL 5249
#define IDS_SETTINGS_MENU_EXTENSIONS_LINK_TOOLTIP 5250
#define IDS_SETTINGS_SEARCH_PROMPT 5251
#define IDS_SETTINGS_SEARCH_NO_RESULTS_HELP 5252
#define IDS_SETTINGS_SETTINGS 5253
#define IDS_SETTINGS_ALT_PAGE_TITLE 5254
#define IDS_SETTINGS_SUBPAGE_BUTTON 5255
#define IDS_SETTINGS_RESTART 5256
#define IDS_SETTINGS_CONTROLLED_BY_EXTENSION 5257
#define IDS_SETTINGS_CLEAR 5258
#define IDS_SETTINGS_CUSTOM 5259
#define IDS_SETTINGS_DELETE 5260
#define IDS_SETTINGS_EDIT 5261
#define IDS_SETTINGS_END_TIME 5262
#define IDS_SETTINGS_NOT_VALID 5263
#define IDS_SETTINGS_NOT_VALID_WEB_ADDRESS 5264
#define IDS_SETTINGS_NOT_VALID_WEB_ADDRESS_FOR_CONTENT_TYPE 5265
#define IDS_SETTINGS_RETRY 5266
#define IDS_SETTINGS_SLIDER_MIN_MAX_ARIA_ROLE_DESCRIPTION 5267
#define IDS_SETTINGS_START_TIME 5268
#define IDS_SETTINGS_AUTOFILL 5269
#define IDS_SETTINGS_GOOGLE_PAYMENTS 5270
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_ADD_TITLE 5271
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_EDIT_TITLE 5272
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_COUNTRY 5273
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_PHONE 5274
#define IDS_SETTINGS_AUTOFILL_ADDRESSES_EMAIL 5275
#define IDS_SETTINGS_AUTOFILL_ADDRESS_HONORIFIC_LABEL 5276
#define IDS_SETTINGS_AUTOFILL_CREDIT_CARD_TYPE_COLUMN_LABEL 5277
#define IDS_SETTINGS_AUTOFILL_DETAIL 5278
#define IDS_SETTINGS_AUTOFILL_MORE_ACTIONS_FOR_ADDRESS 5279
#define IDS_SETTINGS_AUTOFILL_MORE_ACTIONS_FOR_CREDIT_CARD 5280
#define IDS_SETTINGS_AUTOFILL_MORE_ACTIONS_CARD_DESCRIPTION 5281
#define IDS_AUTOFILL_ADD_VIRTUAL_CARD 5282
#define IDS_AUTOFILL_REMOVE_VIRTUAL_CARD 5283
#define IDS_AUTOFILL_EDIT_SERVER_CREDIT_CARD 5284
#define IDS_AUTOFILL_VIRTUAL_CARD_ENABLED_LABEL 5285
#define IDS_AUTOFILL_VIRTUAL_CARD_UNENROLL_DIALOG_TITLE 5286
#define IDS_AUTOFILL_VIRTUAL_CARD_UNENROLL_DIALOG_LABEL 5287
#define IDS_AUTOFILL_VIRTUAL_CARD_UNENROLL_DIALOG_CONFIRM_BUTTON_LABEL 5288
#define IDS_SETTINGS_ADDRESS_REMOVE 5289
#define IDS_SETTINGS_ADDRESS_REMOVE_CONFIRMATION_DESCRIPTION 5290
#define IDS_SETTINGS_ADDRESS_REMOVE_CONFIRMATION_TITLE 5291
#define IDS_SETTINGS_CREDIT_CARD_REMOVE 5292
#define IDS_SETTINGS_CREDIT_CARD_CLEAR 5293
#define IDS_SETTINGS_EDIT_CREDIT_CARD_TITLE 5294
#define IDS_SETTINGS_PAYMENTS_MANAGE_CREDIT_CARDS 5295
#define IDS_SETTINGS_PAYMENTS_SAVED_TO_THIS_DEVICE_ONLY 5296
#define IDS_SETTINGS_ADD_CREDIT_CARD_TITLE 5297
#define IDS_SETTINGS_MIGRATABLE_CARDS_LABEL 5298
#define IDS_SETTINGS_SINGLE_MIGRATABLE_CARD_INFO 5299
#define IDS_SETTINGS_MULTIPLE_MIGRATABLE_CARDS_INFO 5300
#define IDS_SETTINGS_REMOTE_CREDIT_CARD_LINK_LABEL 5301
#define IDS_SETTINGS_NAME_ON_CREDIT_CARD 5302
#define IDS_SETTINGS_CREDIT_CARD_NUMBER 5303
#define IDS_SETTINGS_CREDIT_CARD_EXPIRATION_DATE 5304
#define IDS_SETTINGS_CREDIT_CARD_EXPIRATION_MONTH 5305
#define IDS_SETTINGS_CREDIT_CARD_EXPIRATION_YEAR 5306
#define IDS_SETTINGS_CREDIT_CARD_EXPIRED 5307
#define IDS_SETTINGS_CREDIT_CARD_NICKNAME 5308
#define IDS_SETTINGS_CREDIT_CARD_NICKNAME_INVALID 5309
#define IDS_SETTINGS_UPI_ID_LABEL 5310
#define IDS_SETTINGS_UPI_ID_EXPIRATION_NEVER 5311
#define IDS_SETTINGS_PASSWORDS 5312
#define IDS_SETTINGS_PASSWORD_MANAGER 5313
#define IDS_SETTINGS_PASSWORD_MANAGER_DESCRIPTION 5314
#define IDS_SETTINGS_DEVICE_PASSWORDS 5315
#define IDS_SETTINGS_DEVICE_PASSWORDS_ON_DEVICE_ONLY_HEADING 5316
#define IDS_SETTINGS_DEVICE_PASSWORDS_ON_DEVICE_AND_ACCOUNT_HEADING 5317
#define IDS_SETTINGS_CHECK_PASSWORDS 5318
#define IDS_SETTINGS_CHECK_PASSWORDS_CANCELED 5319
#define IDS_SETTINGS_CHECKED_PASSWORDS 5320
#define IDS_SETTINGS_CHECK_PASSWORDS_DESCRIPTION 5321
#define IDS_SETTINGS_COMPROMISED_PASSWORDS_COUNT 5322
#define IDS_SETTINGS_COMPROMISED_PASSWORDS_COUNT_SHORT 5323
#define IDS_SETTINGS_WEAK_PASSWORDS_COUNT 5324
#define IDS_SETTINGS_WEAK_PASSWORDS_COUNT_SHORT 5325
#define IDS_SETTINGS_INSECURE_PASSWORDS_COUNT 5326
#define IDS_SETTINGS_CHECK_PASSWORDS_AGAIN 5327
#define IDS_SETTINGS_CHECK_PASSWORDS_AGAIN_AFTER_ERROR 5328
#define IDS_SETTINGS_CHECK_PASSWORDS_PROGRESS 5329
#define IDS_SETTINGS_CHECK_PASSWORDS_STOP 5330
#define IDS_SETTINGS_PASSWORDS_JUST_NOW 5331
#define IDS_SETTINGS_COMPROMISED_PASSWORDS 5332
#define IDS_SETTINGS_COMPROMISED_PASSWORDS_ADVICE 5333
#define IDS_SETTINGS_MUTED_PASSWORDS 5334
#define IDS_SETTINGS_WEAK_PASSWORDS 5335
#define IDS_SETTINGS_WEAK_PASSWORDS_DESCRIPTION 5336
#define IDS_SETTINGS_CHANGE_PASSWORD_BUTTON 5337
#define IDS_SETTINGS_CHANGE_PASSWORD_IN_APP_LABEL 5338
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REASON_LEAKED 5339
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REASON_PHISHED 5340
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REASON_PHISHED_AND_LEAKED 5341
#define IDS_SETTINGS_COMPROMISED_PASSWORD_SHOW 5342
#define IDS_SETTINGS_COMPROMISED_PASSWORD_HIDE 5343
#define IDS_SETTINGS_COMPROMISED_PASSWORD_REMOVE 5344
#define IDS_SETTINGS_COMPROMISED_PASSWORD_MUTE 5345
#define IDS_SETTINGS_COMPROMISED_PASSWORD_UNMUTE 5346
#define IDS_SETTINGS_REMOVE_COMPROMISED_PASSWORD_CONFIRMATION_TITLE 5347
#define IDS_SETTINGS_REMOVE_COMPROMISED_PASSWORD_CONFIRMATION_DESCRIPTION 5348
#define IDS_SETTINGS_COMPROMISED_EDIT_PASSWORD_SITE 5349
#define IDS_SETTINGS_COMPROMISED_EDIT_PASSWORD_APP 5350
#define IDS_SETTINGS_COMPROMISED_ALREADY_CHANGED_PASSWORD 5351
#define IDS_SETTINGS_COMPROMISED_EDIT_DISCLAIMER_TITLE 5352
#define IDS_SETTINGS_PASSWORDS_SAVE_PASSWORDS_TOGGLE_LABEL 5353
#define IDS_SETTINGS_PASSWORDS_AUTOSIGNIN_CHECKBOX_LABEL 5354
#define IDS_SETTINGS_PASSWORDS_AUTOSIGNIN_CHECKBOX_DESC 5355
#define IDS_SETTINGS_PASSWORDS_LEAK_DETECTION_LABEL 5356
#define IDS_SETTINGS_PASSWORDS_LEAK_DETECTION_SIGNED_OUT_ENABLED_DESC 5357
#define IDS_SETTINGS_PASSWORDS_SAVED_HEADING 5358
#define IDS_SETTINGS_PASSWORDS_EXCEPTIONS_HEADING 5359
#define IDS_SETTINGS_PASSWORDS_DELETE_EXCEPTION 5360
#define IDS_SETTINGS_PASSWORD_REMOVE 5361
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT 5362
#define IDS_SETTINGS_PASSWORD_SEARCH 5363
#define IDS_SETTINGS_PASSWORDS_VIEW_DETAILS_TITLE 5364
#define IDS_SETTINGS_PASSWORD_DETAILS 5365
#define IDS_SETTINGS_PASSWORD_EDIT_TITLE 5366
#define IDS_SETTINGS_PASSWORD_EDIT 5367
#define IDS_SETTINGS_PASSWORD_EDIT_FOOTNOTE 5368
#define IDS_SETTINGS_PASSWORD_USERNAME_ALREADY_USED 5369
#define IDS_SETTINGS_PASSWORD_VIEW_EXISTING_PASSWORD 5370
#define IDS_SETTINGS_PASSWORD_VIEW_EXISTING_PASSWORD_ARIA_DESCRIPTION 5371
#define IDS_SETTINGS_PASSWORD_MISSING_TLD 5372
#define IDS_SETTINGS_PASSWORD_ADD_TITLE 5373
#define IDS_SETTINGS_PASSWORD_ADD_FOOTNOTE 5374
#define IDS_SETTINGS_PASSWORD_ADD_STORE_OPTION_ACCOUNT 5375
#define IDS_SETTINGS_PASSWORD_COPY 5376
#define IDS_SETTINGS_PASSWORD_SEND 5377
#define IDS_SETTINGS_USERNAME_COPY 5378
#define IDS_SETTINGS_PASSWORDS_WEBSITE 5379
#define IDS_SETTINGS_PASSWORDS_ANDROID_APP 5380
#define IDS_SETTINGS_PASSWORDS_USERNAME 5381
#define IDS_SETTINGS_PASSWORDS_PASSWORD 5382
#define IDS_SETTINGS_PASSWORDS_NOTE 5383
#define IDS_SETTINGS_PASSWORDS_NOTE_CHARACTER_COUNT 5384
#define IDS_SETTINGS_PASSWORDS_NOTE_CHARACTER_COUNT_WARNING 5385
#define IDS_SETTINGS_ADDRESS_NONE 5386
#define IDS_SETTINGS_PAYMENT_METHODS_NONE 5387
#define IDS_SETTINGS_PASSWORDS_NONE 5388
#define IDS_SETTINGS_PASSWORDS_EXCEPTIONS_NONE 5389
#define IDS_SETTINGS_PASSWORD_UNDO 5390
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD 5391
#define IDS_SETTINGS_PASSWORD_STORED_ON_DEVICE 5392
#define IDS_SETTINGS_PASSWORD_STORED_IN_ACCOUNT 5393
#define IDS_SETTINGS_PASSWORD_STORED_IN_ACCOUNT_AND_ON_DEVICE 5394
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD_FROM_ACCOUNT 5395
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD_FROM_DEVICE 5396
#define IDS_SETTINGS_PASSWORD_DELETED_PASSWORD_FROM_ACCOUNT_AND_DEVICE 5397
#define IDS_SETTINGS_PASSWORD_COPIED_TO_CLIPBOARD 5398
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT 5399
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_COUNT 5400
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_DIALOG_BODY_TEXT 5401
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_DIALOG_TITLE 5402
#define IDS_SETTINGS_PASSWORD_MOVE_PASSWORDS_TO_ACCOUNT_SNACKBAR 5403
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_TITLE 5404
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_BODY 5405
#define IDS_SETTINGS_PASSWORD_MOVE_MULTIPLE_PASSWORDS_TO_ACCOUNT_DIALOG_MOVE_BUTTON_TEXT 5406
#define IDS_SETTINGS_PASSWORD_MOVE_MULTIPLE_PASSWORDS_TO_ACCOUNT_DIALOG_CANCEL_BUTTON_TEXT 5407
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_MOVE_BUTTON_TEXT 5408
#define IDS_SETTINGS_PASSWORD_MOVE_TO_ACCOUNT_DIALOG_CANCEL_BUTTON_TEXT 5409
#define IDS_SETTINGS_PASSWORD_OPEN_MOVE_MULTIPLE_PASSWORDS_TO_ACCOUNT_DIALOG_BUTTON_TEXT 5410
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_TITLE 5411
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_BODY 5412
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_REMOVE_BUTTON_TEXT 5413
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_CANCEL_BUTTON_TEXT 5414
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_FROM_ACCOUNT_CHECKBOX_LABEL 5415
#define IDS_SETTINGS_PASSWORD_REMOVE_DIALOG_FROM_DEVICE_CHECKBOX_LABEL 5416
#define IDS_SETTINGS_DEVICE_PASSWORDS_LINK_LABEL 5417
#define IDS_SETTINGS_PASSWORDS_MANAGE_PASSWORDS 5418
#define IDS_SETTINGS_PASSWORDS_MANAGE_PASSWORDS_PLAINTEXT 5419
#define IDS_SETTINGS_PASSWORDS_OPT_IN_ACCOUNT_STORAGE_BODY 5420
#define IDS_SETTINGS_PASSWORDS_OPT_IN_ACCOUNT_STORAGE_LABEL 5421
#define IDS_SETTINGS_PASSWORDS_OPT_OUT_ACCOUNT_STORAGE_BODY 5422
#define IDS_SETTINGS_PASSWORDS_OPT_OUT_ACCOUNT_STORAGE_LABEL 5423
#define IDS_SETTINGS_PASSWORDS_EXPORT_MENU_ITEM 5424
#define IDS_SETTINGS_PASSWORDS_EXPORT_TITLE 5425
#define IDS_SETTINGS_PASSWORDS_EXPORT_DESCRIPTION 5426
#define IDS_SETTINGS_PASSWORDS_EXPORT 5427
#define IDS_SETTINGS_PASSWORDS_EXPORT_TRY_AGAIN 5428
#define IDS_SETTINGS_PASSWORDS_EXPORTING_TITLE 5429
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TITLE 5430
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TIPS 5431
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TIP_ENOUGH_SPACE 5432
#define IDS_SETTINGS_PASSWORDS_EXPORTING_FAILURE_TIP_ANOTHER_FOLDER 5433
#define IDS_SETTINGS_PASSWORD_ROW_MORE_ACTIONS 5434
#define IDS_SETTINGS_PASSWORD_ROW_FEDERATED_MORE_ACTIONS 5435
#define IDS_SETTINGS_TRUSTED_VAULT_BANNER_LABEL 5436
#define IDS_SETTINGS_TRUSTED_VAULT_BANNER_SUB_LABEL_OFFER_OPT_IN 5437
#define IDS_SETTINGS_TRUSTED_VAULT_BANNER_SUB_LABEL_OPTED_IN 5438
#define IDS_SETTINGS_PASSWORD_SHOW_PASSWORD_A11Y 5439
#define IDS_SETTINGS_PASSWORD_HIDE_PASSWORD_A11Y 5440
#define IDS_SETTINGS_DEFAULT_BROWSER 5441
#define IDS_SETTINGS_DEFAULT_BROWSER_MAKE_DEFAULT_BUTTON 5442
#define IDS_SETTINGS_CLEAR_PERIOD_TITLE 5515
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC 5516
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC_ERROR 5517
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC_PASSPHRASE_ERROR 5518
#define IDS_SETTINGS_CLEAR_BROWSING_DATA_WITH_SYNC_PAUSED 5519
#define IDS_SETTINGS_CLEAR_BROWSING_HISTORY 5520
#define IDS_SETTINGS_CLEAR_COOKIES_AND_SITE_DATA_SUMMARY_BASIC 5521
#define IDS_SETTINGS_CLEAR_COOKIES_AND_SITE_DATA_SUMMARY_BASIC_WITH_EXCEPTION 5522
#define IDS_SETTINGS_CLEAR_COOKIES_AND_SITE_DATA_SUMMARY_BASIC_MAIN_PROFILE 5523
#define IDS_SETTINGS_CLEAR_BROWSING_HISTORY_SUMMARY 5524
#define IDS_SETTINGS_CLEAR_BROWSING_HISTORY_SUMMARY_SIGNED_IN_NO_LINK 5525
#define IDS_SETTINGS_CLEAR_GOOGLE_SEARCH_HISTORY_GOOGLE_DSE 5526
#define IDS_SETTINGS_CLEAR_GOOGLE_SEARCH_HISTORY_NON_GOOGLE_DSE 5527
#define IDS_SETTINGS_CLEAR_NON_GOOGLE_SEARCH_HISTORY_PREPOPULATED_DSE 5528
#define IDS_SETTINGS_CLEAR_NON_GOOGLE_SEARCH_HISTORY_NON_PREPOPULATED_DSE 5529
#define IDS_SETTINGS_CLEAR_DOWNLOAD_HISTORY 5530
#define IDS_SETTINGS_CLEAR_CACHE 5531
#define IDS_SETTINGS_CLEAR_COOKIES 5532
#define IDS_SETTINGS_CLEAR_PASSWORDS 5533
#define IDS_SETTINGS_CLEAR_FORM_DATA 5534
#define IDS_SETTINGS_CLEAR_HOSTED_APP_DATA 5535
#define IDS_SETTINGS_CLEAR_PERIOD_HOUR 5536
#define IDS_SETTINGS_CLEAR_PERIOD_24_HOURS 5537
#define IDS_SETTINGS_CLEAR_PERIOD_7_DAYS 5538
#define IDS_SETTINGS_CLEAR_PERIOD_FOUR_WEEKS 5539
#define IDS_SETTINGS_CLEAR_PERIOD_EVERYTHING 5540
#define IDS_SETTINGS_CLEAR_INSTALLED_APPS_DATA_TITLE 5541
#define IDS_SETTINGS_CLEAR_INSTALLED_APPS_DATA_CONFIRM 5542
#define IDS_SETTINGS_NOTIFICATION_WARNING 5543
#define IDS_SETTINGS_DOWNLOADS 5544
#define IDS_SETTINGS_DOWNLOAD_LOCATION 5545
#define IDS_SETTINGS_CHANGE_DOWNLOAD_LOCATION 5546
#define IDS_SETTINGS_PROMPT_FOR_DOWNLOAD 5547
#define IDS_SETTINGS_OPEN_FILE_TYPES_AUTOMATICALLY 5548
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_TITLE 5549
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_LEARN_MORE 5550
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_UNLINKED_MESSAGE 5551
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_SIGN_IN_BUTTON 5552
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_LINKED_MESSAGE 5553
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_SIGN_OUT_BUTTON 5554
#define IDS_SETTINGS_DOWNLOAD_CONNECTION_ACCOUNT_TITLE 5555
#define IDS_SETTINGS_DOWNLOAD_WEB_DRIVE_LOCATION 5556
#define IDS_SETTINGS_DOWNLOAD_LOCAL_LOCATION 5557
#define IDS_SETTINGS_ON_STARTUP 5558
#define IDS_SETTINGS_ON_STARTUP_OPEN_NEW_TAB 5559
#define IDS_SETTINGS_ON_STARTUP_CONTINUE 5560
#define IDS_SETTINGS_ON_STARTUP_OPEN_SPECIFIC 5561
#define IDS_SETTINGS_ON_STARTUP_CONTINUE_AND_OPEN_SPECIFIC 5562
#define IDS_SETTINGS_ON_STARTUP_USE_CURRENT 5563
#define IDS_SETTINGS_ON_STARTUP_ADD_NEW_PAGE 5564
#define IDS_SETTINGS_ON_STARTUP_EDIT_PAGE 5565
#define IDS_SETTINGS_ON_STARTUP_SITE_URL 5566
#define IDS_SETTINGS_ON_STARTUP_REMOVE 5567
#define IDS_SETTINGS_ON_STARTUP_PAGE_TOOLTIP 5568
#define IDS_SETTINGS_INVALID_URL 5569
#define IDS_SETTINGS_URL_TOOL_LONG 5570
#define IDS_SETTINGS_LANGUAGES_PAGE_TITLE 5571
#define IDS_SETTINGS_LANGUAGE_SEARCH 5572
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_MOVE_TO_TOP 5573
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_MOVE_UP 5574
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_MOVE_DOWN 5575
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_REMOVE 5576
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_ADD 5577
#define IDS_SETTINGS_LANGUAGES_MANAGE_LANGUAGES_TITLE 5578
#define IDS_SETTINGS_LANGUAGES_LANGUAGES_LIST_TITLE 5579
#define IDS_SETTINGS_LANGUAGES_EXPAND_ACCESSIBILITY_LABEL 5580
#define IDS_SETTINGS_LANGUAGES_BROWSER_LANGUAGES_LIST_ORDERING_INSTRUCTIONS 5581
#define IDS_SETTINGS_LANGUAGES_OFFER_TO_TRANSLATE_IN_THIS_LANGUAGE 5582
#define IDS_SETTINGS_LANGUAGES_OFFER_TO_ENABLE_TRANSLATE 5583
#define IDS_SETTINGS_LANGUAGES_TRANSLATE_TARGET 5584
#define IDS_SETTINGS_LANGUAGES_MANAGED_DIALOG_TITLE 5585
#define IDS_SETTINGS_LANGUAGES_MANAGED_DIALOG_BODY 5586
#define IDS_SETTINGS_LANGUAGES_NO_LANGUAGES_ADDED 5587
#define IDS_SETTINGS_LANGUAGES_AUTOMATIC_TRANSLATE 5588
#define IDS_SETTINGS_LANGUAGES_NEVER_LANGUAGES 5589
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_TITLE 5590
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_BASIC_LABEL 5591
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_ENHANCED_LABEL 5592
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_ENHANCED_DESCRIPTION 5593
#define IDS_SETTING_LANGUAGES_SPELL_CHECK_DISABLED_REASON 5594
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_LANGUAGES_LIST_TITLE 5595
#define IDS_SETTINGS_LANGUAGES_SPELL_CHECK_MANAGE 5596
#define IDS_SETTINGS_LANGUAGES_EDIT_DICTIONARY_TITLE 5597
#define IDS_SETTINGS_LANGUAGES_ADD_DICTIONARY_WORD 5598
#define IDS_SETTINGS_LANGUAGES_ADD_DICTIONARY_WORD_BUTTON 5599
#define IDS_SETTINGS_LANGUAGES_ADD_DICTIONARY_WORD_DUPLICATE_ERROR 5600
#define IDS_SETTINGS_LANGUAGES_ADD_DICTIONARY_WORD_LENGTH_ERROR 5601
#define IDS_SETTINGS_LANGUAGES_DELETE_DICTIONARY_WORD_BUTTON 5602
#define IDS_SETTINGS_LANGUAGES_DICTIONARY_WORDS 5603
#define IDS_SETTINGS_LANGUAGES_DICTIONARY_WORDS_NONE 5604
#define IDS_SETTINGS_LANGUAGES_DICTIONARY_DOWNLOAD_FAILED 5605
#define IDS_SETTINGS_LANGUAGES_DICTIONARY_DOWNLOAD_FAILED_HELP 5606
#define IDS_SETTINGS_PRIVACY 5609
#define IDS_SETTINGS_PRIVACY_V2 5610
#define IDS_SETTINGS_PRIVACY_MORE 5611
#define IDS_SETTINGS_PRIVACY_SANDBOX_TITLE 5613
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_HEADING 5614
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_EXPLANATION1_PHASE2 5615
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_EXPLANATION2_PHASE2 5616
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_TITLE 5617
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_EXPLANATION1_PHASE2 5618
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_EXPLANATION2_PHASE2 5619
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_SETTING_EXPLANATION3_PHASE2 5620
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_DETAILS 5621
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_ENABLED 5622
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_DISABLED 5623
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_HEADING 5624
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_EXPLANATION 5625
#define IDS_SETTINGS_PRIVACY_SANDBOX_FLOC_TRIAL_ACTIVE 5626
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_STATUS 5627
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_COHORT 5628
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_COHORT_NEXT_UPDATE 5629
#define IDS_SETTINGS_PRIVACY_SANDBOX_PAGE_FLOC_RESET_COHORT 5630
#define IDS_SETTINGS_PRIVACY_SANDBOX_COOKIES_DIALOG 5631
#define IDS_SETTINGS_PRIVACY_SANDBOX_COOKIES_DIALOG_MORE 5632
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_TITLE 5633
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_SUMMARY 5634
#define IDS_SETTINGS_PRIVACY_SANDBOX_TRIALS_SUMMARY_LEARN_MORE 5635
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_TITLE 5636
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_SUMMARY 5637
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_TITLE 5638
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_SUMMARY 5639
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_TITLE 5640
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_SUMMARY 5641
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_TITLE 5642
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_TITLE 5643
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_DATA_TYPES 5644
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_DATA_USAGE 5645
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_DATA_MANAGEMENT 5646
#define IDS_SETTINGS_PRIVACY_SANDBOX_LEARN_MORE_DIALOG_FLEDGE_DATA_MANAGEMENT 5647
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TITLE 5648
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_REMOVED_DIALOG_TITLE 5649
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_REMOVED_DIALOG_DESCRIPTION 5650
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_TOPICS_EMPTY 5651
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_TOPICS_LABEL 5652
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_TOPICS_EMPTY 5653
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_TITLE 5654
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_EMPTY 5655
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_FLEDGE_LABEL 5656
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_REMOVED_FLEDGE_EMPTY 5657
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_LEARN_MORE_2 5658
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_PERSONALIZATION_DIALOG_FLEDGE_LEARN_MORE_3 5659
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_TITLE 5660
#define IDS_SETTINGS_PRIVACY_SANDBOX_AD_MEASUREMENT_DIALOG_CONTROL_MEASUREMENT 5661
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_TITLE 5662
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_1 5663
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_1_TRIALS_OFF 5664
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_2 5665
#define IDS_SETTINGS_PRIVACY_SANDBOX_SPAM_AND_FRAUD_DIALOG_DESCRIPTION_3 5666
#define IDS_SETTINGS_PRIVACY_GUIDE_LABEL 5667
#define IDS_SETTINGS_PRIVACY_GUIDE_SUBLABEL 5668
#define IDS_SETTINGS_PRIVACY_GUIDE_PROMO_HEADER 5669
#define IDS_SETTINGS_PRIVACY_GUIDE_PROMO_START_BUTTON 5670
#define IDS_SETTINGS_PRIVACY_GUIDE_BACK_TO_SETTINGS_ARIA_LABEL 5671
#define IDS_SETTINGS_PRIVACY_GUIDE_BACK_TO_SETTINGS_ARIA_ROLE_DESC 5672
#define IDS_SETTINGS_PRIVACY_GUIDE_BACK_BUTTON 5673
#define IDS_SETTINGS_PRIVACY_GUIDE_STEPS 5674
#define IDS_SETTINGS_PRIVACY_GUIDE_NEXT_BUTTON 5675
#define IDS_SETTINGS_PRIVACY_GUIDE_FEATURE_DESCRIPTION_HEADER 5676
#define IDS_SETTINGS_PRIVACY_GUIDE_THINGS_TO_CONSIDER 5677
#define IDS_SETTINGS_PRIVACY_GUIDE_WELCOME_CARD_HEADER 5678
#define IDS_SETTINGS_PRIVACY_GUIDE_WELCOME_CARD_SUB_HEADER 5679
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_HEADER 5680
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_SUB_HEADER 5681
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_LEAVE_BUTTON 5682
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_PRIVACY_SANDBOX_LABEL 5683
#define IDS_SETTINGS_PRIVACY_GUIDE_COMPLETION_CARD_WAA_LABEL 5684
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_CARD_HEADER 5685
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_FEATURE_DESCRIPTION1 5686
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_FEATURE_DESCRIPTION2 5687
#define IDS_SETTINGS_PRIVACY_GUIDE_MSBB_PRIVACY_DESCRIPTION1 5688
#define IDS_SETTINGS_PRIVACY_GUIDE_CLEAR_ON_EXIT_CARD_HEADER 5689
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_CARD_HEADER 5690
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_SETTING_LABEL 5691
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_FEATURE_DESCRIPTION1 5692
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_FEATURE_DESCRIPTION2 5693
#define IDS_SETTINGS_PRIVACY_GUIDE_HISTORY_SYNC_PRIVACY_DESCRIPTION1 5694
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_HEADER 5695
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_SUBHEADER 5696
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_FEATURE_DESCRIPTION1 5697
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_FEATURE_DESCRIPTION2 5698
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_PRIVACY_DESCRIPTION1 5699
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_INCOGNITO_PRIVACY_DESCRIPTION2 5700
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_SUBHEADER 5701
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_FEATURE_DESCRIPTION1 5702
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_FEATURE_DESCRIPTION2 5703
#define IDS_SETTINGS_PRIVACY_GUIDE_COOKIES_CARD_BLOCK_TPC_PRIVACY_DESCRIPTION1 5704
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_HEADER 5705
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_ENHANCED_PROTECTION_PRIVACY_DESCRIPTION1 5706
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_ENHANCED_PROTECTION_PRIVACY_DESCRIPTION2 5707
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_ENHANCED_PROTECTION_PRIVACY_DESCRIPTION3 5708
#define IDS_SETTINGS_PRIVACY_GUIDE_SAFE_BROWSING_CARD_STANDARD_PROTECTION_FEATURE_DESCRIPTION1 5709
#define IDS_SETTINGS_SAFETY_CHECK_SECTION_TITLE 5710
#define IDS_SETTINGS_SAFETY_CHECK_RUNNING 5711
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER 5712
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_MINS 5713
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_HOURS 5714
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_TIME 5715
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_TODAY 5716
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_YESTERDAY 5717
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_DAYS 5718
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_PRIMARY_LABEL_AFTER_DATE 5719
#define IDS_SETTINGS_SAFETY_CHECK_ARIA_LIVE_RUNNING 5720
#define IDS_SETTINGS_SAFETY_CHECK_ARIA_LIVE_AFTER 5721
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_BUTTON 5722
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_BUTTON_ARIA_LABEL 5723
#define IDS_SETTINGS_SAFETY_CHECK_PARENT_RUN_AGAIN_BUTTON_ARIA_LABEL 5724
#define IDS_SETTINGS_SAFETY_CHECK_ICON_RUNNING_ARIA_LABEL 5725
#define IDS_SETTINGS_SAFETY_CHECK_ICON_SAFE_ARIA_LABEL 5726
#define IDS_SETTINGS_SAFETY_CHECK_ICON_INFO_ARIA_LABEL 5727
#define IDS_SETTINGS_SAFETY_CHECK_ICON_WARNING_ARIA_LABEL 5728
#define IDS_SETTINGS_SAFETY_CHECK_REVIEW 5729
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_PRIMARY_LABEL 5730
#define IDS_SETTINGS_SAFETY_CHECK_UPDATES_DISABLED_BY_ADMIN 5731
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_PRIMARY_LABEL 5732
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_FEATURE_UNAVAILABLE 5733
#define IDS_SETTINGS_SAFETY_CHECK_PASSWORDS_BUTTON_ARIA_LABEL 5734
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED 5735
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED_STANDARD 5736
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED_STANDARD_AVAILABLE_ENHANCED 5737
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_ENABLED_ENHANCED 5738
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_DISABLED_BY_ADMIN 5739
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_DISABLED_BY_EXTENSION 5740
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_BUTTON 5741
#define IDS_SETTINGS_SAFETY_CHECK_SAFE_BROWSING_BUTTON_ARIA_LABEL 5742
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_PRIMARY_LABEL 5743
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_SAFE 5744
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BLOCKLISTED_OFF 5745
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BLOCKLISTED_ON_USER 5746
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BLOCKLISTED_ON_ADMIN 5747
#define IDS_SETTINGS_SAFETY_CHECK_EXTENSIONS_BUTTON_ARIA_LABEL 5748
#define IDS_SETTINGS_NETWORK_PREDICTION_ENABLED_LABEL 5762
#define IDS_SETTINGS_NETWORK_PREDICTION_ENABLED_DESC 5763
#define IDS_SETTINGS_NETWORK_PREDICTION_ENABLED_DESC_COOKIES_PAGE 5764
#define IDS_SETTINGS_SAFEBROWSING_ENABLEPROTECTION 5765
#define IDS_SETTINGS_SAFEBROWSING_ENABLEPROTECTION_DESC 5766
#define IDS_SETTINGS_SAFEBROWSING_ENABLE_REPORTING_DESC 5767
#define IDS_SETTINGS_SAFEBROWSING_SECTION_LABEL 5768
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED 5769
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_DESC 5770
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_EXPAND_ACCESSIBILITY_LABEL 5771
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_ONE 5772
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_THREE 5773
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_FOUR 5774
#define IDS_SETTINGS_SAFEBROWSING_ENHANCED_BULLET_FIVE 5775
#define IDS_SETTINGS_SAFEBROWSING_STANDARD 5776
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_DESC 5777
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_EXPAND_ACCESSIBILITY_LABEL 5778
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_BULLET_ONE 5779
#define IDS_SETTINGS_SAFEBROWSING_STANDARD_HELP_IMPROVE 5780
#define IDS_SETTINGS_SAFEBROWSING_NONE 5781
#define IDS_SETTINGS_SAFEBROWSING_NONE_DESC 5782
#define IDS_SETTINGS_SAFEBROWSING_DISABLE_DIALOG_TITLE 5783
#define IDS_SETTINGS_SAFEBROWSING_DISABLE_DIALOG_DESC 5784
#define IDS_SETTINGS_SAFEBROWSING_DISABLE_DIALOG_CONFIRM 5785
#define IDS_SETTINGS_ENABLE_DO_NOT_TRACK 5786
#define IDS_SETTINGS_ENABLE_DO_NOT_TRACK_DIALOG_TITLE 5787
#define IDS_SETTINGS_ENABLE_DO_NOT_TRACK_DIALOG_TEXT 5788
#define IDS_SETTINGS_PERMISSIONS 5789
#define IDS_SETTINGS_PERMISSIONS_DESCRIPTION 5790
#define IDS_SETTINGS_SECURITY 5791
#define IDS_SETTINGS_SECURITY_DESCRIPTION 5792
#define IDS_SETTINGS_ADVANCED_PROTECTION_PROGRAM 5793
#define IDS_SETTINGS_ADVANCED_PROTECTION_PROGRAM_DESC 5794
#define IDS_SETTINGS_HTTPS_ONLY_MODE 5795
#define IDS_SETTINGS_HTTPS_ONLY_MODE_DESCRIPTION 5796
#define IDS_SETTINGS_MANAGE_CERTIFICATES 5797
#define IDS_SETTINGS_MANAGE_CERTIFICATES_DESCRIPTION 5798
#define IDS_SETTINGS_SECURE_DNS 5799
#define IDS_SETTINGS_SECURE_DNS_DESCRIPTION 5800
#define IDS_SETTINGS_AUTOMATIC_MODE_DESCRIPTION 5801
#define IDS_SETTINGS_AUTOMATIC_MODE_DESCRIPTION_SECONDARY 5802
#define IDS_SETTINGS_SECURE_MODE_DESCRIPTION_ACCESSIBILITY_LABEL 5803
#define IDS_SETTINGS_SECURE_DNS_DROPDOWN_ACCESSIBILITY_LABEL 5804
#define IDS_SETTINGS_SECURE_DROPDOWN_MODE_DESCRIPTION 5805
#define IDS_SETTINGS_SECURE_DROPDOWN_MODE_PRIVACY_POLICY 5806
#define IDS_SETTINGS_SECURE_DNS_DISABLED_FOR_MANAGED_ENVIRONMENT 5807
#define IDS_SETTINGS_SECURE_DNS_DISABLED_FOR_PARENTAL_CONTROL 5808
#define IDS_SETTINGS_SECURE_DNS_CUSTOM_PLACEHOLDER 5809
#define IDS_SETTINGS_SECURE_DNS_CUSTOM_FORMAT_ERROR 5810
#define IDS_SETTINGS_SECURE_DNS_CUSTOM_CONNECTION_ERROR 5811
#define IDS_SETTINGS_CONTENT_SETTINGS 5812
#define IDS_SETTINGS_SITE_SETTINGS 5813
#define IDS_SETTINGS_SITE_SETTINGS_DESCRIPTION 5814
#define IDS_SETTINGS_CLEAR_DATA 5815
#define IDS_SETTINGS_CLEARING_DATA 5816
#define IDS_SETTINGS_CLEARED_DATA 5817
#define IDS_SETTINGS_CLEAR_BROWSING_DATA 5818
#define IDS_SETTINGS_CLEAR_DATA_DESCRIPTION 5819
#define IDS_SETTINGS_TITLE_AND_COUNT 5820
#define IDS_SETTINGS_SYNC_AND_GOOGLE_SERVICES_PRIVACY_DESC_UNIFIED_CONSENT 5821
#define IDS_SETTINGS_RECENT_PERMISSIONS_NO_CHANGES 5822
#define IDS_SETTINGS_RECENT_PERMISSIONS_ALLOWED_ONE_ITEM 5823
#define IDS_SETTINGS_RECENT_PERMISSIONS_ALLOWED_TWO_ITEMS 5824
#define IDS_SETTINGS_RECENT_PERMISSIONS_ALLOWED_MORE_THAN_TWO_ITEMS 5825
#define IDS_SETTINGS_RECENT_PERMISSIONS_AUTOMATICALLY_BLOCKED_ONE_ITEM 5826
#define IDS_SETTINGS_RECENT_PERMISSIONS_AUTOMATICALLY_BLOCKED_TWO_ITEMS 5827
#define IDS_SETTINGS_RECENT_PERMISSIONS_AUTOMATICALLY_BLOCKED_MORE_THAN_TWO_ITEMS 5828
#define IDS_SETTINGS_RECENT_PERMISSIONS_BLOCKED_ONE_ITEM 5829
#define IDS_SETTINGS_RECENT_PERMISSIONS_BLOCKED_TWO_ITEMS 5830
#define IDS_SETTINGS_RECENT_PERMISSIONS_BLOCKED_MORE_THAN_TWO_ITEMS 5831
#define IDS_SETTINGS_RESET_PROMPT_TITLE 5832
#define IDS_SETTINGS_RESET 5833
#define IDS_SETTINGS_RESET_SETTINGS_TRIGGER 5834
#define IDS_SETTINGS_RESET_AUTOMATED_DIALOG_TITLE 5835
#define IDS_SETTINGS_RESET_BANNER_TEXT 5836
#define IDS_SETTINGS_RESET_BANNER_RESET_BUTTON_TEXT 5837
#define IDS_SETTINGS_SEARCH 5840
#define IDS_SETTINGS_SEARCH_EXPLANATION 5841
#define IDS_SETTINGS_SEARCH_MANAGE_SEARCH_ENGINES 5842
#define IDS_SETTINGS_SEARCH_MANAGE_SEARCH_ENGINES_AND_SITE_SEARCH 5843
#define IDS_SETTINGS_SEARCH_MANAGE_SEARCH_ENGINES_EXPLANATION 5844
#define IDS_SETTINGS_SEARCH_ENGINES 5845
#define IDS_SETTINGS_SEARCH_ENGINES_PAGE_EXPLANATION 5846
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH 5847
#define IDS_SETTINGS_SEARCH_ENGINES_ADD_SEARCH_ENGINE 5848
#define IDS_SETTINGS_SEARCH_ENGINES_EDIT_SEARCH_ENGINE 5849
#define IDS_SETTINGS_SEARCH_ENGINES_DELETE_CONFIRMATION_TITLE 5850
#define IDS_SETTINGS_SEARCH_ENGINES_DELETE_CONFIRMATION_DESCRIPTION 5851
#define IDS_SETTINGS_SEARCH_ENGINES_DEFAULT_ENGINES 5852
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH_ENGINES 5853
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH_ENGINES_EXPLANATION 5854
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_SEARCH 5855
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_SEARCH_EXPLANATION 5856
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_SEARCH_EXPLANATION_STARTER_PACK 5857
#define IDS_SETTINGS_SEARCH_ENGINES_NO_SITES_ADDED 5858
#define IDS_SETTINGS_SEARCH_ENGINES_INACTIVE_SHORTCUTS 5859
#define IDS_SETTINGS_SEARCH_ENGINES_NO_INACTIVE_SHORTCUTS 5860
#define IDS_SETTINGS_SEARCH_ENGINES_OTHER_ENGINES 5861
#define IDS_SETTINGS_SEARCH_ENGINES_NO_OTHER_ENGINES 5862
#define IDS_SETTINGS_SEARCH_ENGINES_EXTENSION_ENGINES 5863
#define IDS_SETTINGS_SEARCH_ENGINES_EXTENSION_ENGINES_EXPLANATION 5864
#define IDS_SETTINGS_SEARCH_ENGINES_SEARCH_ENGINE 5865
#define IDS_SETTINGS_SEARCH_ENGINES_SITE_OR_PAGE 5866
#define IDS_SETTINGS_SEARCH_ENGINES_INACTIVE_SITE 5867
#define IDS_SETTINGS_SEARCH_ENGINES_KEYWORD 5868
#define IDS_SETTINGS_SEARCH_ENGINES_SHORTCUT 5869
#define IDS_SETTINGS_SEARCH_ENGINES_QUERY_URL 5870
#define IDS_SETTINGS_SEARCH_ENGINES_QUERY_URL_EXPLANATION 5871
#define IDS_SETTINGS_SEARCH_ENGINES_MAKE_DEFAULT 5872
#define IDS_SETTINGS_SEARCH_ENGINES_ACTIVATE 5873
#define IDS_SETTINGS_SEARCH_ENGINES_DEACTIVATE 5874
#define IDS_SETTINGS_SEARCH_ENGINES_REMOVE_FROM_LIST 5875
#define IDS_SETTINGS_SEARCH_ENGINES_MANAGE_EXTENSION 5876
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_TITLE 5877
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_DESCRIPTION 5878
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_DESCRIPTION_STARTER_PACK 5879
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_SPACE_OR_TAB 5880
#define IDS_SETTINGS_SEARCH_ENGINES_KEYBOARD_SHORTCUTS_TAB 5881
#define IDS_SETTINGS_SEARCH_ENGINES_ADDITIONAL_SITES 5882
#define IDS_SETTINGS_SEARCH_ENGINES_ADDITIONAL_INACTIVE_SITES 5883
#define IDS_SETTINGS_SEARCH_ENGINES_ADDITIONAL_EXTENSIONS 5884
#define IDS_SETTINGS_EXCEPTIONS_EMBEDDED_ON_HOST 5885
#define IDS_SETTINGS_EXCEPTIONS_EMBEDDED_ON_ANY_HOST 5886
#define IDS_SETTINGS_SITE_SETTINGS_CATEGORY 5887
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES 5888
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_DESCRIPTION 5889
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SEARCH 5890
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT 5891
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT_METHOD_MOST_VISITED 5892
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT_METHOD_STORAGE 5893
#define IDS_SETTINGS_SITE_SETTINGS_ALL_SITES_SORT_METHOD_NAME 5894
#define IDS_SETTINGS_SITE_SETTINGS_SITE_ENTRY_PARTITIONED_LABEL 5895
#define IDS_SETTINGS_SITE_SETTINGS_SITE_REPRESENTATION_SEPARATOR 5896
#define IDS_SETTINGS_SITE_SETTINGS_DEFAULT_BEHAVIOR 5897
#define IDS_SETTINGS_SITE_SETTINGS_DEFAULT_BEHAVIOR_DESCRIPTION 5898
#define IDS_SETTINGS_SITE_SETTINGS_CUSTOMIZED_BEHAVIORS 5899
#define IDS_SETTINGS_SITE_SETTINGS_CUSTOMIZED_BEHAVIORS_DESCRIPTION 5900
#define IDS_SETTINGS_SITE_SETTINGS_CUSTOMIZED_BEHAVIORS_DESCRIPTION_SHORT 5901
#define IDS_SETTINGS_SITE_SETTINGS_ADS_DESCRIPTION 5902
#define IDS_SETTINGS_SITE_SETTINGS_ADS_ALLOWED 5903
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCKED 5904
#define IDS_SETTINGS_SITE_SETTINGS_ADS_ALLOWED_EXCEPTIONS 5905
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCKED_EXCEPTIONS 5906
#define IDS_SETTINGS_SITE_SETTINGS_AR_DESCRIPTION 5907
#define IDS_SETTINGS_SITE_SETTINGS_AR_ALLOWED 5908
#define IDS_SETTINGS_SITE_SETTINGS_AR_BLOCKED 5909
#define IDS_SETTINGS_SITE_SETTINGS_AR_ALLOWED_EXCEPTIONS 5910
#define IDS_SETTINGS_SITE_SETTINGS_AR_BLOCKED_EXCEPTIONS 5911
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_DESCRIPTION 5912
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_ALLOWED 5913
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_BLOCKED 5914
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_ALLOWED_EXCEPTIONS 5915
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOADS_BLOCKED_EXCEPTIONS 5916
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_DESCRIPTION 5917
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_ALLOWED 5918
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCKED 5919
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCKED_SUB_LABEL 5920
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_ALLOWED_EXCEPTIONS 5921
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCKED_EXCEPTIONS 5922
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_DESCRIPTION 5923
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_ALLOWED 5924
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_BLOCKED 5925
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_DESCRIPTION 5926
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_ALLOWED 5927
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_BLOCKED 5928
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_BLOCKED_SUB_LABEL 5929
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_ALLOWED_EXCEPTIONS 5930
#define IDS_SETTINGS_SITE_SETTINGS_CAMERA_BLOCKED_EXCEPTIONS 5931
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_DESCRIPTION 5932
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ALLOWED 5933
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_BLOCKED 5934
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ALLOWED_EXCEPTIONS 5935
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_BLOCKED_EXCEPTIONS 5936
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_DESCRIPTION 5937
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_ALLOWED 5938
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_BLOCKED 5939
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_ALLOWED_EXCEPTIONS 5940
#define IDS_SETTINGS_SITE_SETTINGS_DEVICE_USE_BLOCKED_EXCEPTIONS 5941
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_DESCRIPTION 5942
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_ALLOWED 5943
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_BLOCKED 5944
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_ALLOWED_EXCEPTIONS 5945
#define IDS_SETTINGS_SITE_SETTINGS_FEDERATED_IDENTITY_API_BLOCKED_EXCEPTIONS 5946
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_DESCRIPTION 5947
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_ALLOWED 5948
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_BLOCKED 5949
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_WRITE_BLOCKED_EXCEPTIONS 5950
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_DESCRIPTION 5951
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_ALLOWED 5952
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_BLOCKED 5953
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_ALLOWED_EXCEPTIONS 5954
#define IDS_SETTINGS_SITE_SETTINGS_FONTS_BLOCKED_EXCEPTIONS 5955
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_DESCRIPTION 5956
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_ALLOWED 5957
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_BLOCKED 5958
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_DESCRIPTION 5959
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_ALLOWED 5960
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_BLOCKED 5961
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_BLOCKED_SUB_LABEL 5962
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_ALLOWED_EXCEPTIONS 5963
#define IDS_SETTINGS_SITE_SETTINGS_IMAGES_BLOCKED_EXCEPTIONS 5964
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_DESCRIPTION 5965
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_ALLOWED_EXCEPTIONS 5966
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_BLOCKED_EXCEPTIONS 5967
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_DESCRIPTION 5968
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_ALLOWED 5969
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_BLOCKED 5970
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_ALLOWED_EXCEPTIONS 5971
#define IDS_SETTINGS_SITE_SETTINGS_JAVASCRIPT_BLOCKED_EXCEPTIONS 5972
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_DESCRIPTION 5973
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_ALLOWED 5974
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_BLOCKED 5975
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_BLOCKED_SUB_LABEL 5976
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_ALLOWED_EXCEPTIONS 5977
#define IDS_SETTINGS_SITE_SETTINGS_LOCATION_BLOCKED_EXCEPTIONS 5978
#define IDS_SETTINGS_SITE_SETTINGS_MIC_DESCRIPTION 5979
#define IDS_SETTINGS_SITE_SETTINGS_MIC_ALLOWED 5980
#define IDS_SETTINGS_SITE_SETTINGS_MIC_BLOCKED 5981
#define IDS_SETTINGS_SITE_SETTINGS_MIC_BLOCKED_SUB_LABEL 5982
#define IDS_SETTINGS_SITE_SETTINGS_MIC_ALLOWED_EXCEPTIONS 5983
#define IDS_SETTINGS_SITE_SETTINGS_MIC_BLOCKED_EXCEPTIONS 5984
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_DESCRIPTION 5985
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_ALLOWED 5986
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_BLOCKED 5987
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_ALLOWED_EXCEPTIONS 5988
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_BLOCKED_EXCEPTIONS 5989
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_DESCRIPTION 5990
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_ALLOWED 5991
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_BLOCKED 5992
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_BLOCKED_SUB_LABEL 5993
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_ALLOWED_EXCEPTIONS 5994
#define IDS_SETTINGS_SITE_SETTINGS_MOTION_SENSORS_BLOCKED_EXCEPTIONS 5995
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_DESCRIPTION 5996
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_ALLOWED 5997
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_PARTIAL 5998
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_PARTIAL_SUB_LABEL 5999
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCKED 6000
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCKED_SUB_LABEL 6001
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_ALLOWED_EXCEPTIONS 6002
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCKED_EXCEPTIONS 6003
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_DESCRIPTION 6004
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_ALLOWED 6005
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_BLOCKED 6006
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_ALLOWED_EXCEPTIONS 6007
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLERS_BLOCKED_EXCEPTIONS 6008
#define IDS_SETTINGS_SITE_SETTINGS_PDFS_DESCRIPTION 6009
#define IDS_SETTINGS_SITE_SETTINGS_PDFS_ALLOWED 6010
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_DESCRIPTION 6011
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_ALLOWED 6012
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_BLOCKED 6013
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_ALLOWED_EXCEPTIONS 6014
#define IDS_SETTINGS_SITE_SETTINGS_POPUPS_BLOCKED_EXCEPTIONS 6015
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_ENABLE 6016
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_DESCRIPTION 6017
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_ALLOWED 6018
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_BLOCKED 6019
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_BLOCKED_SUB_LABEL 6020
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_IDENTIFIERS_EXPLANATION 6021
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_ENABLE_IDENTIFIERS 6022
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_IDENTIFIERS_ALLOWED 6023
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_IDENTIFIERS_BLOCKED 6024
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_IDENTIFIERS_BLOCKED_SUB_LABEL 6025
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_IDENTIFIERS_ALLOWED_EXCEPTIONS 6026
#define IDS_SETTINGS_SITE_SETTINGS_PROTECTED_CONTENT_IDENTIFIERS_BLOCKED_EXCEPTIONS 6027
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_DESCRIPTION 6029
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_ALLOWED 6030
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_BLOCKED 6031
#define IDS_SETTINGS_SITE_SETTINGS_PROTOCOL_HANDLERS_BLOCKED_EXCEPTIONS 6032
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_DESCRIPTION 6033
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_ALLOWED 6034
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_BLOCKED 6035
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_DESCRIPTION 6036
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_ALLOWED 6037
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_BLOCKED 6038
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_BLOCKED_SUB_LABEL 6039
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_ALLOWED_EXCEPTIONS 6040
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_BLOCKED_EXCEPTIONS 6041
#define IDS_SETTINGS_SITE_SETTINGS_USB_DESCRIPTION 6042
#define IDS_SETTINGS_SITE_SETTINGS_USB_ALLOWED 6043
#define IDS_SETTINGS_SITE_SETTINGS_USB_BLOCKED 6044
#define IDS_SETTINGS_SITE_SETTINGS_VR_DESCRIPTION 6045
#define IDS_SETTINGS_SITE_SETTINGS_VR_ALLOWED 6046
#define IDS_SETTINGS_SITE_SETTINGS_VR_BLOCKED 6047
#define IDS_SETTINGS_SITE_SETTINGS_VR_ALLOWED_EXCEPTIONS 6048
#define IDS_SETTINGS_SITE_SETTINGS_VR_BLOCKED_EXCEPTIONS 6049
#define IDS_SETTINGS_SITE_SETTINGS_ZOOM_LEVELS_DESCRIPTION 6050
#define IDS_SETTINGS_SITE_SETTINGS_AR_ASK 6051
#define IDS_SETTINGS_SITE_SETTINGS_AR_ASK_RECOMMENDED 6052
#define IDS_SETTINGS_SITE_SETTINGS_AR_BLOCK 6053
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ASK 6054
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_ASK_RECOMMENDED 6055
#define IDS_SETTINGS_SITE_SETTINGS_CLIPBOARD_BLOCK 6056
#define IDS_SETTINGS_COOKIES_PAGE 6057
#define IDS_SETTINGS_COOKIES_CONTROLS 6058
#define IDS_SETTINGS_COOKIES_ALLOW_ALL 6059
#define IDS_SETTINGS_COOKIES_ALLOW_ALL_EXPAND_A11Y_LABEL 6060
#define IDS_SETTINGS_COOKIES_ALLOW_ALL_BULLET_ONE 6061
#define IDS_SETTINGS_COOKIES_ALLOW_ALL_BULLET_TWO 6062
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO 6063
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO_EXPAND_A11Y_LABEL 6064
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO_BULLET_ONE 6065
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO_BULLET_TWO 6066
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY 6067
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_EXPAND_A11Y_LABEL 6068
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_BULLET_ONE 6069
#define IDS_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_BULLET_TWO 6070
#define IDS_SETTINGS_COOKIES_BLOCK_ALL 6071
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_EXPAND_A11Y_LABEL 6072
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_BULLET_ONE 6073
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_BULLET_TWO 6074
#define IDS_SETTINGS_COOKIES_BLOCK_ALL_BULLET_THREE 6075
#define IDS_SETTINGS_COOKIES_CLEAR_ON_EXIT 6076
#define IDS_SETTINGS_COOKIES_ALL_SITES_LINK 6077
#define IDS_SETTINGS_COOKIES_SITE_SPECIFIC_EXCEPTIONS 6078
#define IDS_SETTINGS_COOKIES_ALLOW_EXCEPTIONS 6079
#define IDS_SETTINGS_COOKIES_SESSION_ONLY_EXCEPTIONS 6080
#define IDS_SETTINGS_COOKIES_BLOCK_EXCEPTIONS 6081
#define IDS_SETTINGS_SITE_SETTINGS_APP_PROTOCOL_HANDLERS 6082
#define IDS_SETTINGS_SITE_SETTINGS_APP_ALLOWED_PROTOCOL_HANDLERS_DESCRIPTION 6083
#define IDS_SETTINGS_SITE_SETTINGS_APP_DISALLOWED_PROTOCOL_HANDLERS_DESCRIPTION 6084
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_ASK 6085
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_ASK_RECOMMENDED 6086
#define IDS_SETTINGS_SITE_SETTINGS_HID_DEVICES_BLOCK 6087
#define IDS_SETTINGS_SITE_SETTINGS_INSECURE_CONTENT_BLOCK 6088
#define IDS_SETTINGS_SITE_SETTINGS_PAYMENT_HANDLER_ALLOW_RECOMMENDED 6089
#define IDS_SETTINGS_SITE_SETTINGS_PDF_DOWNLOAD_PDFS 6090
#define IDS_SETTINGS_SITE_SETTINGS_VR_ASK_RECOMMENDED 6091
#define IDS_SETTINGS_SITE_SETTINGS_RECENT_ACTIVITY 6092
#define IDS_SETTINGS_SITE_SETTINGS_MIDI_DEVICES_ASK_RECOMMENDED 6093
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_DEVICES_ASK_RECOMMENDED 6094
#define IDS_SETTINGS_SITE_SETTINGS_USB_DEVICES_ASK_RECOMMENDED 6095
#define IDS_SETTINGS_SITE_SETTINGS_SERIAL_PORTS_ASK_RECOMMENDED 6096
#define IDS_SETTINGS_SITE_SETTINGS_FILE_SYSTEM_ACCESS_WRITE_ASK_RECOMMENDED 6097
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_ZOOM_LEVEL 6098
#define IDS_SETTINGS_SITE_SETTINGS_MAY_SAVE_COOKIES 6099
#define IDS_SETTINGS_SITE_SETTINGS_ASK_FIRST 6100
#define IDS_SETTINGS_SITE_SETTINGS_ASK_FIRST_RECOMMENDED 6101
#define IDS_SETTINGS_SITE_SETTINGS_ASK_BEFORE_ACCESSING_RECOMMENDED 6102
#define IDS_SETTINGS_SITE_SETTINGS_ASK_BEFORE_SENDING 6103
#define IDS_SETTINGS_SITE_SETTINGS_ASK_BEFORE_SENDING_RECOMMENDED 6104
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATICALLY_BLOCKED_NOTIFICATIONS 6105
#define IDS_SETTINGS_SITE_SETTINGS_SHOW_BLOCKED_NOTIFICATIONS_INDICATOR 6106
#define IDS_SETTINGS_SITE_SETTINGS_ENABLE_QUIET_NOTIFICATION_PROMPTS 6107
#define IDS_SETTINGS_SITE_SETTINGS_ENABLE_QUIET_NOTIFICATION_PROMPTS_DESCRIPTION 6108
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_BLOCK 6109
#define IDS_SETTINGS_SITE_SETTINGS_NOTIFICATIONS_ASK 6110
#define IDS_SETTINGS_SITE_SETTINGS_SHOW_ALL_RECOMMENDED 6111
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_ALLOW 6112
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_BLOCK 6113
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_BLOCK_THIRD_PARTY 6114
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_BLOCK_THIRD_PARTY_INCOGNITO 6115
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_ALLOW_SITES 6116
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_ALLOW_SITES_RECOMMENDED 6117
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW_RECENTLY_CLOSED_SITES_RECOMMENDED 6118
#define IDS_SETTINGS_SITE_SETTINGS_BACKGROUND_SYNC_BLOCK 6119
#define IDS_SETTINGS_SITE_SETTINGS_HANDLERS_ASK_RECOMMENDED 6120
#define IDS_SETTINGS_SITE_SETTINGS_HANDLERS_BLOCKED 6121
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCK_RECOMMENDED 6122
#define IDS_SETTINGS_SITE_SETTINGS_SOUND_ALLOW_RECOMMENDED 6123
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DOWNLOAD_ASK_RECOMMENDED 6124
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_DESCRIPTION 6125
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_ASK 6126
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_BLOCKED 6127
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_ASK_EXCEPTIONS 6128
#define IDS_SETTINGS_SITE_SETTINGS_WINDOW_PLACEMENT_BLOCKED_EXCEPTIONS 6129
#define IDS_SETTINGS_SITE_SETTINGS_IDLE_DETECTION_BLOCK 6130
#define IDS_SETTINGS_SITE_SETTINGS_ALLOWED_RECOMMENDED 6131
#define IDS_SETTINGS_SITE_SETTINGS_BLOCKED 6132
#define IDS_SETTINGS_SITE_SETTINGS_BLOCKED_RECOMMENDED 6133
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW 6134
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK 6135
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_SOUND 6136
#define IDS_SETTINGS_SITE_SETTINGS_SESSION_ONLY 6137
#define IDS_SETTINGS_SITE_SETTINGS_SITE_URL 6138
#define IDS_SETTINGS_SITE_SETTINGS_ASK_DEFAULT_MENU 6139
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW_DEFAULT_MENU 6140
#define IDS_SETTINGS_SITE_SETTINGS_AUTOMATIC_DEFAULT_MENU 6141
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_DEFAULT_MENU 6142
#define IDS_SETTINGS_SITE_SETTINGS_MUTE_DEFAULT_MENU 6143
#define IDS_SETTINGS_SITE_SETTINGS_ALLOW_MENU 6144
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_MENU 6145
#define IDS_SETTINGS_SITE_SETTINGS_ASK_MENU 6146
#define IDS_SETTINGS_SITE_SETTINGS_MUTE_MENU 6147
#define IDS_SETTINGS_SITE_SETTINGS_RESET_MENU 6148
#define IDS_SETTINGS_SITE_SETTINGS_SESSION_ONLY_MENU 6149
#define IDS_SETTINGS_SITE_SETTINGS_USAGE 6150
#define IDS_SETTINGS_SITE_SETTINGS_USAGE_NONE 6151
#define IDS_SETTINGS_SITE_SETTINGS_PERMISSIONS 6152
#define IDS_SETTINGS_SITE_SETTINGS_PERMISSIONS_MORE 6153
#define IDS_SETTINGS_SITE_SETTINGS_CONTENT 6154
#define IDS_SETTINGS_SITE_SETTINGS_CONTENT_MORE 6155
#define IDS_SETTINGS_SITE_SETTINGS_ALLOWLISTED 6156
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCK_BLOCKLISTED_SINGULAR 6157
#define IDS_SETTINGS_SITE_SETTINGS_ADS_BLOCK_NOT_BLOCKLISTED_SINGULAR 6158
#define IDS_SETTINGS_SITE_SETTINGS_SOURCE_KILL_SWITCH 6159
#define IDS_SETTINGS_SITE_SETTINGS_SOURCE_INSECURE_ORIGIN 6160
#define IDS_SETTINGS_SITE_SETTINGS_RESET_BUTTON 6161
#define IDS_SETTINGS_SITE_SETTINGS_DELETE 6162
#define IDS_SETTINGS_SITE_SETTINGS_GROUP_RESET 6163
#define IDS_SETTINGS_SITE_SETTINGS_GROUP_DELETE 6164
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_HEADER 6165
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_LINK 6166
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE 6167
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_ALL 6168
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_ALL_SHOWN 6169
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_ALL_THIRD_PARTY 6170
#define IDS_SETTINGS_SITE_SETTINGS_THIRD_PARTY_COOKIE_REMOVE_DIALOG_TITLE 6171
#define IDS_SETTINGS_SITE_SETTINGS_THIRD_PARTY_COOKIE_REMOVE_CONFIRMATION 6172
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_THIRD_PARTY_COOKIES 6173
#define IDS_SETTINGS_SITE_SETTINGS_THIRD_PARTY_COOKIES_EXCEPTION_LABEL 6174
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_DIALOG_TITLE 6175
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_DESCRIPTION 6176
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_LABEL 6177
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_CONFIRMATION 6178
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_CONFIRMATION_INSTALLED 6179
#define IDS_SETTINGS_SITE_SETTINGS_CLEAR_ALL_STORAGE_SIGN_OUT 6180
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_DIALOG_TITLE 6181
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_SUBPAGE 6182
#define IDS_SETTINGS_SITE_SETTINGS_SITE_RESET_CONFIRMATION 6183
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_DIALOG_TITLE 6184
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_CONFIRMATION 6185
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_CONFIRMATION_NEW 6186
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_SIGN_OUT 6187
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_OFFLINE_DATA 6188
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE_APPS 6189
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_RESET_DIALOG_TITLE 6190
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_RESET_CONFIRMATION 6191
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_DIALOG_TITLE 6192
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION 6193
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION_NEW 6194
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION_INSTALLED 6195
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_CONFIRMATION_INSTALLED_PLURAL 6196
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_SIGN_OUT 6197
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_OFFLINE_DATA 6198
#define IDS_SETTINGS_SITE_SETTINGS_SITE_GROUP_DELETE_APPS 6199
#define IDS_SETTINGS_SITE_SETTINGS_ORIGIN_DELETE_CONFIRMATION 6200
#define IDS_SETTINGS_SITE_SETTINGS_ORIGIN_DELETE_CONFIRMATION_INSTALLED 6201
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_MULTIPLE 6202
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_DIALOG_TITLE 6203
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_APP_DIALOG_TITLE 6204
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_PARTITIONED_DIALOG_TITLE 6205
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_DIALOG_TITLE 6206
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_APP_DIALOG_TITLE 6207
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_APP_PLURAL_DIALOG_TITLE 6208
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_ORIGIN_LOGOUT 6209
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_GROUP_LOGOUT 6210
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_OFFLINE_DATA 6211
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_PERMISSIONS 6212
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE_SITE_CONFIRM 6213
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_REMOVE_SITE 6214
#define IDS_SETTINGS_SITE_SETTINGS_COOKIES_CLEAR_ALL 6215
#define IDS_SETTINGS_SITE_SETTINGS_SITE_RESET_ALL 6216
#define IDS_SETTINGS_SITE_SETTINGS_SITE_CLEAR_STORAGE 6217
#define IDS_SETTINGS_SITE_SETTINGS_COOKIE_SEARCH 6218
#define IDS_SETTINGS_SITE_SETTINGS_HANDLER_IS_DEFAULT 6219
#define IDS_SETTINGS_SITE_SETTINGS_HANDLER_SET_DEFAULT 6220
#define IDS_SETTINGS_SITE_SETTINGS_REMOVE 6221
#define IDS_SETTINGS_SITE_SETTINGS_INCOGNITO_ONLY 6222
#define IDS_SETTINGS_SITE_SETTINGS_INCOGNITO_SITE_EXCEPTION_DESC 6223
#define IDS_SETTINGS_SITE_SETTINGS_NO_ZOOMED_SITES 6224
#define IDS_SETTINGS_SITE_NO_SITES_ADDED 6225
#define IDS_SETTINGS_SITE_SETTINGS_BLOCK_AUTOPLAY 6226
#define IDS_SETTINGS_SITE_SETTINGS_EMPTY_ALL_SITES_PAGE 6227
#define IDS_SETTINGS_SITE_SETTINGS_NO_SITES_FOUND 6228
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_SCANNING_ASK 6229
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_SCANNING_ASK_RECOMMENDED 6230
#define IDS_SETTINGS_SITE_SETTINGS_BLUETOOTH_SCANNING_BLOCK 6231
#define IDS_SETTINGS_NO_BLUETOOTH_DEVICES_FOUND 6232
#define IDS_SETTINGS_NO_USB_DEVICES_FOUND 6233
#define IDS_SETTINGS_NO_SERIAL_PORTS_FOUND 6234
#define IDS_SETTINGS_NO_HID_DEVICES_FOUND 6235
#define IDS_SETTINGS_ADD_SITE_TITLE 6236
#define IDS_SETTINGS_EDIT_SITE_TITLE 6237
#define IDS_SETTINGS_ADD_SITE 6238
#define IDS_SETTINGS_COOKIES_COOKIE_NAME_LABEL 6240
#define IDS_SETTINGS_COOKIES_COOKIE_CONTENT_LABEL 6241
#define IDS_SETTINGS_COOKIES_COOKIE_DOMAIN_LABEL 6242
#define IDS_SETTINGS_COOKIES_COOKIE_PATH_LABEL 6243
#define IDS_SETTINGS_COOKIES_COOKIE_SENDFOR_LABEL 6244
#define IDS_SETTINGS_COOKIES_COOKIE_ACCESSIBLE_TO_SCRIPT_LABEL 6245
#define IDS_SETTINGS_COOKIES_COOKIE_CREATED_LABEL 6246
#define IDS_SETTINGS_COOKIES_COOKIE_EXPIRES_LABEL 6247
#define IDS_SETTINGS_COOKIES_FLASH_LSO 6248
#define IDS_SETTINGS_SITE_SETTINGS_NUM_COOKIES 6249
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE_ORIGIN_LABEL 6250
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE_SIZE_ON_DISK_LABEL 6251
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE_LAST_MODIFIED_LABEL 6252
#define IDS_SETTINGS_COOKIES_DATABASE_STORAGE 6253
#define IDS_SETTINGS_COOKIES_LOCAL_STORAGE 6254
#define IDS_SETTINGS_COOKIES_MEDIA_LICENSE 6255
#define IDS_SETTINGS_COOKIES_FILE_SYSTEM 6256
#define IDS_SETTINGS_COOKIES_FILE_SYSTEM_TEMPORARY_USAGE_LABEL 6257
#define IDS_SETTINGS_COOKIES_FILE_SYSTEM_PERSISTENT_USAGE_LABEL 6258
#define IDS_SETTINGS_COOKIES_SERVICE_WORKER 6259
#define IDS_SETTINGS_COOKIES_SHARED_WORKER 6260
#define IDS_SETTINGS_COOKIES_SHARED_WORKER_WORKER_LABEL 6261
#define IDS_SETTINGS_COOKIES_CACHE_STORAGE 6262
#define IDS_SETTINGS_PEOPLE 6263
#define IDS_SETTINGS_CHANGE_PICTURE_PROFILE_PHOTO 6264
#define IDS_SETTINGS_PEOPLE_SIGN_IN 6265
#define IDS_SETTINGS_SYNC_DISCONNECT_MANAGED_PROFILE_EXPLANATION 6266
#define IDS_SETTINGS_TURN_OFF_SYNC_DIALOG_TITLE 6267
#define IDS_SETTINGS_TURN_OFF_SYNC_AND_SIGN_OUT_DIALOG_TITLE 6268
#define IDS_SETTINGS_TURN_OFF_SYNC_DIALOG_MANAGED_CONFIRM 6269
#define IDS_SETTINGS_TURN_OFF_SYNC_DIALOG_CHECKBOX 6270
#define IDS_SETTINGS_SYNC_SETTINGS_SAVED_TOAST_LABEL 6271
#define IDS_SETTINGS_PROFILE_NAME_INPUT_LABEL 6272
#define IDS_SETTINGS_PROFILE_SHORTCUT_TOGGLE_LABEL 6273
#define IDS_SETTINGS_CUSTOMIZE_PROFILE 6274
#define IDS_SETTINGS_PICK_A_THEME_COLOR 6275
#define IDS_SETTINGS_PICK_AN_AVATAR 6276
#define IDS_SETTINGS_CREATE_SHORTCUT 6277
#define IDS_SETTINGS_CREATE_SHORTCUT_SUBTITLE 6278
#define IDS_SETTINGS_SYNC_DISCONNECT_EXPLANATION 6279
#define IDS_SETTINGS_SYNC_DISCONNECT_MAIN_PROFILE_EXPLANATION 6280
#define IDS_SETTINGS_SYNC_DISCONNECT_AND_SIGN_OUT_EXPLANATION 6281
#define IDS_SETTINGS_SYNC_DISCONNECT_EXPAND_ACCESSIBILITY_LABEL 6282
#define IDS_SETTINGS_SYNC_DISCONNECT_DELETE_PROFILE 6283
#define IDS_SETTINGS_SYNC_WILL_START 6284
#define IDS_SETTINGS_MANAGE_GOOGLE_ACCOUNT 6285
#define IDS_SETTINGS_USER_EVENTS_CHECKBOX_LABEL 6286
#define IDS_SETTINGS_USER_EVENTS_CHECKBOX_TEXT 6287
#define IDS_SETTINGS_ENCRYPT_WITH_SYNC_PASSPHRASE_LABEL 6288
#define IDS_SETTINGS_PASSPHRASE_EXPLANATION_TEXT 6289
#define IDS_SETTINGS_PASSPHRASE_RESET_HINT_ENCRYPTION 6290
#define IDS_SETTINGS_PASSPHRASE_RESET_HINT_TOGGLE 6291
#define IDS_SETTINGS_PASSPHRASE_RECOVER 6292
#define IDS_SETTINGS_PERSONALIZE_GOOGLE_SERVICES_TITLE 6293
#define IDS_SETTINGS_IMPORT_SETTINGS_TITLE 6294
#define IDS_SETTINGS_IMPORT_FROM_LABEL 6295
#define IDS_SETTINGS_IMPORT_ITEMS_LABEL 6296
#define IDS_SETTINGS_IMPORT_LOADING_PROFILES 6297
#define IDS_SETTINGS_IMPORT_HISTORY_CHECKBOX 6298
#define IDS_SETTINGS_IMPORT_FAVORITES_CHECKBOX 6299
#define IDS_SETTINGS_IMPORT_PASSWORDS_CHECKBOX 6300
#define IDS_SETTINGS_IMPORT_SEARCH_ENGINES_CHECKBOX 6301
#define IDS_SETTINGS_IMPORT_AUTOFILL_FORM_DATA_CHECKBOX 6302
#define IDS_SETTINGS_IMPORT_CHOOSE_FILE 6303
#define IDS_SETTINGS_IMPORT_COMMIT 6304
#define IDS_SETTINGS_IMPORT_SUCCESS 6305
#define IDS_SETTINGS_IMPORT_NO_PROFILE_FOUND 6306
#define IDS_SETTINGS_PAGE_ZOOM_LABEL 6307
#define IDS_SETTINGS_FONT_SIZE_LABEL 6308
#define IDS_SETTINGS_VERY_SMALL_FONT 6309
#define IDS_SETTINGS_SMALL_FONT 6310
#define IDS_SETTINGS_MEDIUM_FONT 6311
#define IDS_SETTINGS_LARGE_FONT 6312
#define IDS_SETTINGS_VERY_LARGE_FONT 6313
#define IDS_SETTINGS_CUSTOMIZE_FONTS 6314
#define IDS_SETTINGS_FONTS 6315
#define IDS_SETTINGS_STANDARD_FONT_LABEL 6316
#define IDS_SETTINGS_SERIF_FONT_LABEL 6317
#define IDS_SETTINGS_SANS_SERIF_FONT_LABEL 6318
#define IDS_SETTINGS_FIXED_WIDTH_FONT_LABEL 6319
#define IDS_SETTINGS_MATH_FONT_LABEL 6320
#define IDS_SETTINGS_MINIMUM_FONT_SIZE_LABEL 6321
#define IDS_SETTINGS_TINY_FONT_SIZE 6322
#define IDS_SETTINGS_HUGE_FONT_SIZE 6323
#define IDS_SETTINGS_QUICK_BROWN_FOX 6324
#define IDS_SETTINGS_SYSTEM 6325
#define IDS_SETTINGS_SYSTEM_HARDWARE_ACCELERATION_LABEL 6326
#define IDS_SETTINGS_SYSTEM_PROXY_SETTINGS_LABEL 6327
#define IDS_SETTINGS_SYSTEM_PROXY_SETTINGS_EXTENSION_LABEL 6328
#define IDS_SETTINGS_SYSTEM_PROXY_SETTINGS_POLICY_LABEL 6329
#define IDS_PAGE_NOT_AVAILABLE_FOR_GUEST_HEADING 6373
#define IDS_SETTINGS_SECURITY_KEYS_TITLE 6374
#define IDS_SETTINGS_SECURITY_KEYS_DESC 6375
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN 6376
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_BUTTON 6377
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_DESC 6378
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_INITIAL_TITLE 6379
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_CREATE_TITLE 6380
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_CHANGE_TITLE 6381
#define IDS_SETTINGS_SECURITY_KEYS_RESET 6382
#define IDS_SETTINGS_SECURITY_KEYS_RESET_DESC 6383
#define IDS_SETTINGS_SECURITY_KEYS_RESET_TITLE 6384
#define IDS_SETTINGS_SECURITY_KEYS_RESET_CONFIRM_TITLE 6385
#define IDS_SETTINGS_SECURITY_KEYS_RESET_STEP1 6386
#define IDS_SETTINGS_SECURITY_KEYS_RESET_STEP2 6387
#define IDS_SETTINGS_SECURITY_KEYS_NO_RESET 6388
#define IDS_SETTINGS_SECURITY_KEYS_RESET_ERROR 6389
#define IDS_SETTINGS_SECURITY_KEYS_RESET_SUCCESS 6390
#define IDS_SETTINGS_SECURITY_KEYS_RESET_NOTALLOWED 6391
#define IDS_SETTINGS_SECURITY_KEYS_NO_PIN 6392
#define IDS_SETTINGS_SECURITY_KEYS_CURRENT_PIN_INTRO 6393
#define IDS_SETTINGS_SECURITY_KEYS_PIN_INCORRECT 6394
#define IDS_SETTINGS_SECURITY_KEYS_PIN_INCORRECT_RETRIES_SIN 6395
#define IDS_SETTINGS_SECURITY_KEYS_PIN_INCORRECT_RETRIES_PL 6396
#define IDS_SETTINGS_SECURITY_KEYS_SAME_PIN_AS_CURRENT 6397
#define IDS_SETTINGS_SECURITY_KEYS_NEW_PIN 6398
#define IDS_SETTINGS_SECURITY_KEYS_SET_PIN_CONFIRM 6399
#define IDS_SETTINGS_SECURITY_KEYS_CURRENT_PIN 6400
#define IDS_SETTINGS_SECURITY_KEYS_PIN 6401
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_TOO_SHORT_SMALL 6402
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_TOO_LONG 6403
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_INVALID 6404
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR_MISMATCH 6405
#define IDS_SETTINGS_SECURITY_KEYS_CONFIRM_PIN 6406
#define IDS_SETTINGS_SECURITY_KEYS_PIN_SUCCESS 6407
#define IDS_SETTINGS_SECURITY_KEYS_PIN_ERROR 6408
#define IDS_SETTINGS_SECURITY_KEYS_PIN_HARD_LOCK 6409
#define IDS_SETTINGS_SECURITY_KEYS_PIN_SOFT_LOCK 6410
#define IDS_SETTINGS_SECURITY_KEYS_SHOW_PINS 6411
#define IDS_SETTINGS_SECURITY_KEYS_HIDE_PINS 6412
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_LABEL 6413
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_USERNAME_LABEL 6414
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_DISPLAYNAME_LABEL 6415
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_WEBSITE_LABEL 6416
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DESC 6417
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DIALOG_TITLE 6418
#define IDS_SETTINGS_SECURITY_KEYS_UPDATE_CREDENTIAL_DIALOG_TITLE 6419
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_CONFIRM_DELETE_TITLE 6420
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_CONFIRM_DELETE_CREDENTIAL 6421
#define IDS_SETTINGS_SECURITY_KEYS_INPUT_ERROR_TOO_LONG 6422
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_NO_CREDENTIALS 6423
#define IDS_SETTINGS_SECURITY_KEYS_NO_CREDENTIAL_MANAGEMENT 6424
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_REMOVED 6425
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_NO_PIN 6426
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_ERROR 6427
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DELETE_SUCCESS 6428
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_DELETE_FAILED 6429
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_UPDATE_SUCCESS 6430
#define IDS_SETTINGS_SECURITY_KEYS_CREDENTIAL_MANAGEMENT_UPDATE_FAILED 6431
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_SUBPAGE_LABEL 6432
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_SUBPAGE_DESCRIPTION 6433
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_DIALOG_TITLE 6434
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ADD_TITLE 6435
#define IDS_SETTINGS_SECURITY_KEYS_BIO_CHOOSE_NAME 6436
#define IDS_SETTINGS_SECURITY_KEYS_BIO_NAME_LABEL 6437
#define IDS_SETTINGS_SECURITY_KEYS_BIO_NAME_LABEL_TOO_LONG 6438
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_NO_ENROLLMENTS_LABEL 6439
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ENROLLMENTS_LABEL 6440
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ENROLLING_LABEL 6441
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_TRY_AGAIN_LABEL 6442
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_FAILED_LABEL 6443
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_ENROLLING_COMPLETE_LABEL 6444
#define IDS_SETTINGS_SECURITY_KEYS_NO_BIOMETRIC_ENROLLMENT 6445
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_DELETE 6446
#define IDS_SETTINGS_SECURITY_KEYS_BIO_NO_PIN 6447
#define IDS_SETTINGS_SECURITY_KEYS_BIO_ENROLLMENT_STORAGE_FULL 6448
#define IDS_SETTINGS_SECURITY_KEYS_TOUCH_TO_CONTINUE 6449
#define IDS_SETTINGS_SECURITY_KEYS_PIN_PROMPT 6450
#define IDS_SETTINGS_SECURITY_KEYS_FORCE_PIN_CHANGE 6451
#define IDS_SETTINGS_SECURITY_KEYS_PHONE_EDIT_DIALOG_TITLE 6452
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_YOUR_DEVICES 6453
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_SYNCED_DESC 6454
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_LINKED_DEVICES 6455
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_LINKED_DESC 6456
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_MANAGE 6457
#define IDS_SETTINGS_SECURITY_KEYS_PHONES_MANAGE_DESC 6458
#define IDS_SETTINGS_SUBPAGE_BACK_BUTTON_ARIA_LABEL 6459
#define IDS_SETTINGS_SUBPAGE_BACK_BUTTON_ARIA_ROLE_DESCRIPTION 6460
#define IDS_SETTINGS_CAPTIONS 6461
#define IDS_SETTINGS_CAPTIONS_PREFERENCES_TITLE 6462
#define IDS_SETTINGS_CAPTIONS_PREFERENCES_SUBTITLE 6463
#define IDS_SETTINGS_CAPTIONS_TEXT_SIZE 6464
#define IDS_SETTINGS_CAPTIONS_TEXT_FONT 6465
#define IDS_SETTINGS_CAPTIONS_TEXT_COLOR 6466
#define IDS_SETTINGS_CAPTIONS_TEXT_OPACITY 6467
#define IDS_SETTINGS_CAPTIONS_BACKGROUND_OPACITY 6468
#define IDS_SETTINGS_CAPTIONS_OPACITY_OPAQUE 6469
#define IDS_SETTINGS_CAPTIONS_OPACITY_SEMI_TRANSPARENT 6470
#define IDS_SETTINGS_CAPTIONS_OPACITY_TRANSPARENT 6471
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW 6472
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_NONE 6473
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_RAISED 6474
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_DEPRESSED 6475
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_UNIFORM 6476
#define IDS_SETTINGS_CAPTIONS_TEXT_SHADOW_DROP_SHADOW 6477
#define IDS_SETTINGS_CAPTIONS_BACKGROUND_COLOR 6478
#define IDS_SETTINGS_CAPTIONS_COLOR_BLACK 6479
#define IDS_SETTINGS_CAPTIONS_COLOR_WHITE 6480
#define IDS_SETTINGS_CAPTIONS_COLOR_RED 6481
#define IDS_SETTINGS_CAPTIONS_COLOR_GREEN 6482
#define IDS_SETTINGS_CAPTIONS_COLOR_BLUE 6483
#define IDS_SETTINGS_CAPTIONS_COLOR_YELLOW 6484
#define IDS_SETTINGS_CAPTIONS_COLOR_CYAN 6485
#define IDS_SETTINGS_CAPTIONS_COLOR_MAGENTA 6486
#define IDS_SETTINGS_CAPTIONS_DEFAULT_SETTING 6487
#define IDS_SETTINGS_NEARBY_SHARE_TITLE 6488
#define IDS_SETTINGS_NEARBY_SHARE_SET_UP_BUTTON_TITLE 6489
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_ROW_TITLE 6490
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_DIALOG_TITLE 6491
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_FIELD_LABEL 6492
#define IDS_SETTINGS_NEARBY_SHARE_EDIT_DEVICE_NAME 6493
#define IDS_SETTINGS_NEARBY_SHARE_FAST_INITIATION_NOTIFICATION_TOGGLE_TITLE 6494
#define IDS_SETTINGS_NEARBY_SHARE_FAST_INITIATION_NOTIFICATION_TOGGLE_DESCRIPTION 6495
#define IDS_SETTINGS_NEARBY_SHARE_FAST_INITIATION_NOTIFICATION_TOGGLE_ARIA_LABEL 6496
#define IDS_SETTINGS_NEARBY_SHARE_DEVICE_NAME_ARIA_DESCRIPTION 6497
#define IDS_SETTINGS_NEARBY_SHARE_CONFIRM_DEVICE_NAME 6498
#define IDS_SETTINGS_NEARBY_SHARE_MANAGE_CONTACTS_LABEL 6499
#define IDS_SETTINGS_NEARBY_SHARE_MANAGE_CONTACTS_ROW_TITLE 6500
#define IDS_SETTINGS_NEARBY_SHARE_EDIT_DATA_USAGE 6501
#define IDS_SETTINGS_NEARBY_SHARE_UPDATE_DATA_USAGE 6502
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_DIALOG_TITLE 6503
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_WIFI_ONLY_LABEL 6504
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_WIFI_ONLY_DESCRIPTION 6505
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_MOBILE_DATA_LABEL 6506
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_MOBILE_DATA_DESCRIPTION 6507
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_MOBILE_DATA_TOOLTIP 6508
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_OFFLINE_LABEL 6509
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_OFFLINE_DESCRIPTION 6510
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_EDIT_BUTTON_DATA_DESCRIPTION 6511
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_EDIT_BUTTON_WIFI_ONLY_DESCRIPTION 6512
#define IDS_SETTINGS_NEARBY_SHARE_DATA_USAGE_EDIT_BUTTON_OFFLINE_DESCRIPTION 6513
#define IDS_SETTINGS_NEARBY_SHARE_CONTACT_VISIBILITY_ROW_TITLE 6514
#define IDS_SETTINGS_NEARBY_SHARE_EDIT_VISIBILITY 6515
#define IDS_SETTINGS_NEARBY_SHARE_VISIBILITY_DIALOG_TITLE 6516
#define IDS_SETTINGS_NEARBY_SHARE_VISIBILITY_DIALOG_SAVE 6517
#define IDS_SETTINGS_NEARBY_SHARE_DESCRIPTION 6518
#define IDS_SETTINGS_NEARBY_SHARE_HIGH_VISIBILITY_TITLE 6519
#define IDS_SETTINGS_NEARBY_SHARE_HIGH_VISIBILITY_ON 6520
#define IDS_SETTINGS_NEARBY_SHARE_HIGH_VISIBILITY_OFF 6521
#define IDS_SETTINGS_ENABLE_URL_KEYED_ANONYMIZED_DATA_COLLECTION 6522
#define IDS_SETTINGS_ENABLE_URL_KEYED_ANONYMIZED_DATA_COLLECTION_DESC 6523
#define IDS_SETTINGS_SPELLING_PREF 6524
#define IDS_SETTINGS_SUGGEST_PREF 6525
#define IDS_SETTINGS_SUGGEST_PREF_DESC 6526
#define IDS_SETTINGS_ENABLE_LOGGING_PREF 6527
#define IDS_SETTINGS_ENABLE_LOGGING_PREF_DESC 6528
#define IDS_SETTINGS_LINKDOCTOR_PREF 6529
#define IDS_SETTINGS_LINKDOCTOR_PREF_DESC 6530
#define IDS_DRIVE_SUGGEST_PREF 6531
#define IDS_SETTINGS_PEOPLE_SYNCING_TO_ACCOUNT 6532
#define IDS_SETTINGS_PEOPLE_SYNC_PAUSED 6533
#define IDS_SETTINGS_PEOPLE_SIGN_IN_PROMPT 6534
#define IDS_SETTINGS_PEOPLE_SYNC_TURN_OFF 6535
#define IDS_SETTINGS_SETTINGS_CHECKBOX_LABEL 6536
#define IDS_SETTINGS_PEOPLE_SYNC_NOT_WORKING 6537
#define IDS_SETTINGS_PEOPLE_SYNC_PASSWORDS_NOT_WORKING 6538
#define IDS_SETTINGS_SYNC_ADVANCED_PAGE_TITLE 6539
#define IDS_SETTINGS_NEW_SYNC_ADVANCED_PAGE_TITLE 6540
#define IDS_SETTINGS_PEOPLE_SYNC_ANOTHER_ACCOUNT 6541
#define IDS_SETTINGS_SYNC_DISCONNECT_CONFIRM 6542
#define IDS_SETTINGS_PEOPLE_SIGN_OUT 6543
#define IDS_SETTINGS_AUTOFILL_CHECKBOX_LABEL 6544
#define IDS_SETTINGS_HISTORY_CHECKBOX_LABEL 6545
#define IDS_SETTINGS_EXTENSIONS_CHECKBOX_LABEL 6546
#define IDS_SETTINGS_OPEN_TABS_CHECKBOX_LABEL 6547
#define IDS_SETTINGS_WIFI_CONFIGURATIONS_CHECKBOX_LABEL 6548
#define IDS_SETTINGS_SYNC_EVERYTHING_CHECKBOX_LABEL 6549
#define IDS_SETTINGS_APPS_CHECKBOX_LABEL 6550
#define IDS_SETTINGS_NON_PERSONALIZED_SERVICES_SECTION_LABEL 6551
#define IDS_SETTINGS_CUSTOMIZE_SYNC 6552
#define IDS_SETTINGS_SYNC_DATA 6553
#define IDS_SETTINGS_PASSWORDS_CHECKBOX_LABEL 6554
#define IDS_SETTINGS_PASSPHRASE_PLACEHOLDER 6555
#define IDS_SETTINGS_EXISTING_PASSPHRASE_TITLE 6556
#define IDS_SETTINGS_SUBMIT_PASSPHRASE 6557
#define IDS_SETTINGS_ENCRYPT_WITH_GOOGLE_CREDENTIALS_LABEL 6558
#define IDS_SETTINGS_BOOKMARKS_CHECKBOX_LABEL 6559
#define IDS_SETTINGS_READING_LIST_CHECKBOX_LABEL 6560
#define IDS_SETTINGS_ENCRYPTION_OPTIONS 6561
#define IDS_SETTINGS_MISMATCHED_PASSPHRASE_ERROR 6562
#define IDS_SETTINGS_EMPTY_PASSPHRASE_ERROR 6563
#define IDS_SETTINGS_INCORRECT_PASSPHRASE_ERROR 6564
#define IDS_SETTINGS_NEW_MANAGE_SYNCED_DATA_TITLE_UNIFIED_CONSENT 6565
#define IDS_SETTINGS_SYNC_SYNC_AND_NON_PERSONALIZED_SERVICES 6566
#define IDS_SETTINGS_PASSPHRASE_CONFIRMATION_PLACEHOLDER 6567
#define IDS_SETTINGS_SYNC_LOADING 6568
#define IDS_SETTINGS_SYNC_TIMEOUT 6569
#define IDS_SETTINGS_SYNC 6570
#define IDS_SETTINGS_SYNC_SETTINGS_CANCEL_SYNC 6571
#define IDS_SETTINGS_SYNC_SETUP_CANCEL_DIALOG_TITLE 6572
#define IDS_SETTINGS_SYNC_SETUP_CANCEL_DIALOG_BODY 6573
#define IDS_SETTINGS_ABOUT_UPGRADE_CHECK_STARTED 6577
#define IDS_SETTINGS_THEME_CHECKBOX_LABEL 6579
#define IDS_EXTENSIONS_ALLOW_FILE_ACCESS 6583
#define IDS_EXTENSIONS_ALLOW_ON_ALL_URLS 6584
#define IDS_EXTENSIONS_ALLOW_ON_FOLLOWING_SITES 6585
#define IDS_EXTENSIONS_VIEW_ACTIVITY_LOG 6586
#define IDS_EXTENSIONS_BACKGROUND_PAGE 6587
#define IDS_EXTENSIONS_SERVICE_WORKER_BACKGROUND 6588
#define IDS_EXTENSIONS_CORRUPTED_EXTENSION 6589
#define IDS_EXTENSIONS_ENABLE_ERROR_COLLECTION 6590
#define IDS_EXTENSIONS_ERROR_NO_ERRORS_CODE_MESSAGE 6591
#define IDS_EXTENSIONS_INSTALL_DROP_TARGET 6592
#define IDS_EXTENSIONS_INSTALL_WARNINGS 6593
#define IDS_EXTENSIONS_LOG_LEVEL_ERROR 6594
#define IDS_EXTENSIONS_LOG_LEVEL_INFO 6595
#define IDS_EXTENSIONS_LOG_LEVEL_WARN 6596
#define IDS_EXTENSIONS_PATH 6597
#define IDS_EXTENSIONS_PERMISSIONS_OFF 6598
#define IDS_EXTENSIONS_RELOAD_TERMINATED 6599
#define IDS_EXTENSIONS_REPAIR_CORRUPTED 6600
#define IDS_EXTENSIONS_VIEW_IFRAME 6601
#define IDS_EXTENSIONS_VIEW_INACTIVE 6602
#define IDS_EXTENSIONS_VIEW_INCOGNITO 6603
#define IDS_EXTENSIONS_DEVELOPER_MODE 6604
#define IDS_EXTENSIONS_DISABLED_UPDATE_REQUIRED_BY_POLICY 6605
#define IDS_EXTENSIONS_MATCHING_RESTRICTED_SITES_WARNING 6606
#define IDS_EXTENSIONS_MENU_BUTTON_LABEL 6607
#define IDS_EXTENSIONS_ERROR_PAGE_HEADING 6608
#define IDS_EXTENSIONS_EDIT_SITE_PERMISSIONS_ALLOW_ALL_EXTENSIONS 6609
#define IDS_EXTENSIONS_EDIT_SITE_PERMISSIONS_RESTRICT_EXTENSIONS 6610
#define IDS_EXTENSIONS_ERROR_ANONYMOUS_FUNCTION 6611
#define IDS_EXTENSIONS_ERROR_CONTEXT 6612
#define IDS_EXTENSIONS_ERROR_CONTEXT_UNKNOWN 6613
#define IDS_EXTENSIONS_CLEAR_ACTIVITIES 6614
#define IDS_EXTENSIONS_ERROR_CLEAR_ALL 6615
#define IDS_EXTENSIONS_A11Y_CLEAR_ENTRY 6616
#define IDS_EXTENSIONS_ERROR_STACK_TRACE 6617
#define IDS_EXTENSIONS_ERROR_LINES_NOT_SHOWN 6618
#define IDS_EXTENSIONS_HOST_PERMISSIONS_DESCRIPTION 6619
#define IDS_EXTENSIONS_HOST_PERMISSIONS_EDIT 6620
#define IDS_EXTENSIONS_ITEM_ERRORS 6621
#define IDS_EXTENSIONS_ITEM_HOST_PERMISSIONS_HEADING 6622
#define IDS_EXTENSIONS_NEW_HOST_PERMISSIONS_HEADING 6623
#define IDS_EXTENSIONS_HOST_PERMISSIONS_SUB_HEADING 6624
#define IDS_EXTENSIONS_HOST_ACCESS_ON_CLICK 6625
#define IDS_EXTENSIONS_HOST_ACCESS_WHEN_CLICKED 6626
#define IDS_EXTENSIONS_HOST_ACCESS_ON_SPECIFIC_SITES 6627
#define IDS_EXTENSIONS_HOST_ACCESS_ALLOW_ON_SPECIFIC_SITES 6628
#define IDS_EXTENSIONS_HOST_ACCESS_ON_ALL_SITES 6629
#define IDS_EXTENSIONS_HOST_ACCESS_ALLOW_ON_ALL_SITES 6630
#define IDS_EXTENSIONS_ITEM_ALLOWED_HOSTS 6631
#define IDS_EXTENSIONS_ACCESSIBILITY_ERROR_LINE 6632
#define IDS_EXTENSIONS_ACCESSIBILITY_ERROR_MULTI_LINE 6633
#define IDS_EXTENSIONS_ACTIVITY_LOG_PAGE_HEADING 6634
#define IDS_EXTENSIONS_ACTIVITY_LOG_SEARCH_LABEL 6635
#define IDS_EXTENSIONS_ACTIVITY_LOG_TYPE_COLUMN 6636
#define IDS_EXTENSIONS_ACTIVITY_LOG_NAME_COLUMN 6637
#define IDS_EXTENSIONS_ACTIVITY_LOG_COUNT_COLUMN 6638
#define IDS_EXTENSIONS_ACTIVITY_LOG_TIME_COLUMN 6639
#define IDS_EXTENSIONS_ACTIVITY_LOG_HISTORY_TAB_HEADING 6640
#define IDS_EXTENSIONS_ACTIVITY_LOG_STREAM_TAB_HEADING 6641
#define IDS_EXTENSIONS_START_ACTIVITY_STREAM 6642
#define IDS_EXTENSIONS_STOP_ACTIVITY_STREAM 6643
#define IDS_EXTENSIONS_EMPTY_STREAM_STARTED 6644
#define IDS_EXTENSIONS_EMPTY_STREAM_STOPPED 6645
#define IDS_EXTENSIONS_ACTIVITY_ARGUMENTS_HEADING 6646
#define IDS_EXTENSIONS_WEB_REQUEST_INFO_HEADING 6647
#define IDS_EXTENSIONS_ACTIVITY_LOG_MORE_ACTIONS_LABEL 6648
#define IDS_EXTENSIONS_ACTIVITY_LOG_EXPAND_ALL 6649
#define IDS_EXTENSIONS_ACTIVITY_LOG_COLLAPSE_ALL 6650
#define IDS_EXTENSIONS_ACTIVITY_LOG_EXPORT_HISTORY 6651
#define IDS_EXTENSIONS_ITEM_ID 6652
#define IDS_EXTENSIONS_ITEM_INSPECT_VIEWS 6653
#define IDS_EXTENSIONS_ITEM_INSPECT_VIEWS_EXTRA 6654
#define IDS_EXTENSIONS_ITEM_NO_ACTIVE_VIEWS 6655
#define IDS_EXTENSIONS_ITEM_ALLOW_INCOGNITO 6656
#define IDS_EXTENSIONS_ITEM_DEPENDENCIES 6657
#define IDS_EXTENSIONS_DEPENDENT_ENTRY 6658
#define IDS_EXTENSIONS_ITEM_DESCRIPTION 6659
#define IDS_EXTENSIONS_ITEM_DETAILS 6660
#define IDS_EXTENSIONS_DETAILS_BACK_BUTTON_ARIA_LABEL 6661
#define IDS_EXTENSIONS_DETAILS_BACK_BUTTON_ARIA_ROLE_DESCRIPTION 6662
#define IDS_EXTENSIONS_EXTENSION_A11Y_ASSOCIATION 6663
#define IDS_EXTENSIONS_APP_ICON 6664
#define IDS_EXTENSIONS_EXTENSION_ICON 6665
#define IDS_EXTENSIONS_ITEM_ID_HEADING 6666
#define IDS_EXTENSIONS_EXTENSION_ENABLED 6667
#define IDS_EXTENSIONS_APP_ENABLED 6668
#define IDS_EXTENSIONS_ITEM_OFF 6669
#define IDS_EXTENSIONS_ITEM_ON 6670
#define IDS_EXTENSIONS_ITEM_EXTENSION_WEBSITE 6671
#define IDS_EXTENSIONS_ITEM_CHROME_WEB_STORE 6672
#define IDS_EXTENSIONS_ITEM_OPTIONS 6673
#define IDS_EXTENSIONS_ITEM_PERMISSIONS 6674
#define IDS_EXTENSIONS_ITEM_PERMISSIONS_EMPTY 6675
#define IDS_EXTENSIONS_ITEM_PERMISSIONS_AND_SITE_ACCESS_EMPTY 6676
#define IDS_EXTENSIONS_ITEM_REMOVE_EXTENSION 6677
#define IDS_EXTENSIONS_ITEM_SITE_ACCESS 6678
#define IDS_EXTENSIONS_ITEM_SITE_ACCESS_ADD_HOST 6679
#define IDS_EXTENSIONS_ITEM_SITE_ACCESS_EMPTY 6680
#define IDS_EXTENSIONS_REMOVE_SITES_DIALOG_TITLE 6681
#define IDS_EXTENSIONS_ITEM_SOURCE 6682
#define IDS_EXTENSIONS_ITEM_SOURCE_POLICY 6683
#define IDS_EXTENSIONS_ITEM_SOURCE_SIDELOADED 6684
#define IDS_EXTENSIONS_ITEM_SOURCE_UNPACKED 6685
#define IDS_EXTENSIONS_ITEM_SOURCE_WEBSTORE 6686
#define IDS_EXTENSIONS_ITEM_VERSION 6687
#define IDS_EXTENSIONS_ITEM_RELOADED 6688
#define IDS_EXTENSIONS_ITEM_RELOADING 6689
#define IDS_EXTENSIONS_LOAD_ERROR_HEADING 6690
#define IDS_EXTENSIONS_LOAD_ERROR_ERROR_LABEL 6691
#define IDS_EXTENSIONS_LOAD_ERROR_FILE_LABEL 6692
#define IDS_EXTENSIONS_LOAD_ERROR_COULD_NOT_LOAD_MANIFEST 6693
#define IDS_EXTENSIONS_LOAD_ERROR_RETRY 6694
#define IDS_EXTENSIONS_LOADING_ACTIVITIES 6695
#define IDS_MISSING_OR_UNINSTALLED_EXTENSION 6696
#define IDS_EXTENSIONS_NO_ACTIVITIES 6697
#define IDS_EXTENSIONS_NO_INSTALLED_ITEMS 6698
#define IDS_EXTENSIONS_NO_DESCRIPTION 6699
#define IDS_EXTENSIONS_PACK_DIALOG_TITLE 6700
#define IDS_EXTENSIONS_PACK_DIALOG_WARNING_TITLE 6701
#define IDS_EXTENSIONS_PACK_DIALOG_ERROR_TITLE 6702
#define IDS_EXTENSIONS_PACK_DIALOG_PROCEED_ANYWAY 6703
#define IDS_EXTENSIONS_PACK_DIALOG_BROWSE_BUTTON 6704
#define IDS_EXTENSIONS_PACK_DIALOG_EXTENSION_ROOT_LABEL 6705
#define IDS_EXTENSIONS_PACK_DIALOG_KEY_FILE_LABEL 6706
#define IDS_EXTENSIONS_PACK_DIALOG_CONFIRM_BUTTON 6707
#define IDS_EXTENSIONS_TOOLBAR_TITLE 6708
#define IDS_EXTENSIONS_SEARCH 6709
#define IDS_EXTENSIONS_SITE_PERMISSIONS 6710
#define IDS_EXTENSIONS_SITE_PERMISSIONS_PAGE_TITLE 6711
#define IDS_EXTENSIONS_SITE_PERMISSIONS_VIEW_ALL_SITES 6712
#define IDS_EXTENSIONS_SITE_PERMISSIONS_ALL_SITES_PAGE_TITLE 6713
#define IDS_EXTENSIONS_PERMITTED_SITES 6714
#define IDS_EXTENSIONS_RESTRICTED_SITES 6715
#define IDS_EXTENSIONS_NO_SITES_ADDED 6716
#define IDS_EXTENSIONS_SITE_PERMISSIONS_ADD_SITE_DIALOG_TITLE 6717
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_SITE_DIALOG_TITLE 6718
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_URL 6719
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_PERMISSIONS_DIALOG_TITLE 6720
#define IDS_EXTENSIONS_SITE_PERMISSIONS_EDIT_PERMISSIONS 6721
#define IDS_EXTENSIONS_SITE_PERMISSIONS_DIALOG_INPUT_ERROR 6722
#define IDS_EXTENSIONS_SITE_PERMISSIONS_DIALOG_INPUT_LABEL 6723
#define IDS_EXTENSIONS_EDIT_SHORTCUT 6724
#define IDS_EXTENSIONS_SHORTCUT_NOT_SET 6725
#define IDS_EXTENSIONS_SHORTCUT_SCOPE_LABEL 6726
#define IDS_EXTENSIONS_SHORTCUT_SCOPE_GLOBAL 6727
#define IDS_EXTENSIONS_APPS_TITLE 6728
#define IDS_EXTENSIONS_REMOVE 6729
#define IDS_EXTENSIONS_RUNTIME_HOSTS_DIALOG_TITLE 6730
#define IDS_EXTENSIONS_RUNTIME_HOSTS_DIALOG_INPUT_ERROR 6731
#define IDS_EXTENSIONS_RUNTIME_HOSTS_DIALOG_INPUT_LABEL 6732
#define IDS_EXTENSIONS_SIDEBAR_EXTENSIONS 6733
#define IDS_EXTENSIONS_SIDEBAR_OPEN_CHROME_WEB_STORE 6734
#define IDS_EXTENSIONS_SIDEBAR_KEYBOARD_SHORTCUTS 6735
#define IDS_EXTENSIONS_TOOLBAR_LOAD_UNPACKED 6736
#define IDS_EXTENSIONS_TOOLBAR_LOAD_UNPACKED_DONE 6737
#define IDS_EXTENSIONS_TOOLBAR_PACK 6738
#define IDS_EXTENSIONS_TOOLBAR_UPDATE_NOW 6739
#define IDS_EXTENSIONS_TOOLBAR_UPDATE_NOW_TOOLTIP 6740
#define IDS_EXTENSIONS_TOOLBAR_UPDATE_DONE 6741
#define IDS_EXTENSIONS_TOOLBAR_UPDATING_TOAST 6742
#define IDS_EXTENSIONS_SHORTCUT_SET 6743
#define IDS_EXTENSIONS_TYPE_A_SHORTCUT 6744
#define IDS_EXTENSIONS_SUBPAGE_BUTTON 6745
#define IDS_EXTENSIONS_INCLUDE_START_MODIFIER 6746
#define IDS_EXTENSIONS_TOO_MANY_MODIFIERS 6747
#define IDS_EXTENSIONS_NEED_CHARACTER 6748
#define IDS_NEARBY_CONFIRMATION_PAGE_ADD_CONTACT_SUBTITLE 6759
#define IDS_NEARBY_CONFIRMATION_PAGE_ADD_CONTACT_TITLE 6760
#define IDS_NEARBY_CONFIRMATION_PAGE_TITLE 6761
#define IDS_NEARBY_CONTACT_VISIBILITY_DOWNLOAD_FAILED 6762
#define IDS_NEARBY_CONTACT_VISIBILITY_DOWNLOADING 6763
#define IDS_NEARBY_CONTACT_VISIBILITY_NO_CONTACTS_SUBTITLE 6764
#define IDS_NEARBY_CONTACT_VISIBILITY_NO_CONTACTS_TITLE 6765
#define IDS_NEARBY_CONTACT_VISIBILITY_NUM_UNREACHABLE 6766
#define IDS_NEARBY_CONTACT_VISIBILITY_OWN_ALL 6767
#define IDS_NEARBY_CONTACT_VISIBILITY_OWN_NONE 6768
#define IDS_NEARBY_CONTACT_VISIBILITY_OWN_SOME 6769
#define IDS_NEARBY_CONTACT_VISIBILITY_ZERO_STATE_TEXT 6770
#define IDS_NEARBY_DEVICE_NAME_EMPTY_ERROR 6771
#define IDS_NEARBY_DEVICE_NAME_TOO_LONG_ERROR 6772
#define IDS_NEARBY_DEVICE_NAME_INVALID_CHARACTERS_ERROR 6773
#define IDS_NEARBY_DISCOVERY_PAGE_INFO 6774
#define IDS_NEARBY_DISCOVERY_PAGE_SUBTITLE 6775
#define IDS_NEARBY_DISCOVERY_PAGE_TITLE 6776
#define IDS_NEARBY_DISCOVERY_PAGE_PLACEHOLDER 6777
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_NAME 6778
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_NAME_HELP 6779
#define IDS_NEARBY_ONBOARDING_PAGE_SUBTITLE 6780
#define IDS_NEARBY_ONBOARDING_PAGE_TITLE 6781
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_VISIBILITY 6782
#define IDS_NEARBY_ONBOARDING_PAGE_DEVICE_VISIBILITY_HELP_ALL_CONTACTS 6783
#define IDS_NEARBY_VISIBILITY_PAGE_MANAGE_CONTACTS 6784
#define IDS_NEARBY_VISIBILITY_PAGE_SUBTITLE 6785
#define IDS_NEARBY_VISIBILITY_PAGE_TITLE 6786
#define IDS_NEARBY_SHARE_FEATURE_NAME 6787
#define IDS_NEARBY_ACTIONS_ACCEPT 6788
#define IDS_NEARBY_ACTIONS_CANCEL 6789
#define IDS_NEARBY_ACTIONS_CLOSE 6790
#define IDS_NEARBY_ACTIONS_CONFIRM 6791
#define IDS_NEARBY_ACTIONS_DECLINE 6792
#define IDS_NEARBY_ACTIONS_NEXT 6793
#define IDS_NEARBY_ACTIONS_REJECT 6794
#define IDS_NEARBY_DEFAULT_DEVICE_NAME 6795
#define IDS_NEARBY_ERROR_CANCELLED 6796
#define IDS_NEARBY_ERROR_CANT_RECEIVE 6797
#define IDS_NEARBY_ERROR_CANT_SHARE 6798
#define IDS_NEARBY_ERROR_NO_RESPONSE 6799
#define IDS_NEARBY_ERROR_TRANSFER_IN_PROGRESS 6800
#define IDS_NEARBY_ERROR_NOT_ENOUGH_SPACE 6801
#define IDS_NEARBY_ERROR_REJECTED 6802
#define IDS_NEARBY_ERROR_SOMETHING_WRONG 6803
#define IDS_NEARBY_ERROR_TIME_OUT 6804
#define IDS_NEARBY_ERROR_TRY_AGAIN 6805
#define IDS_NEARBY_ERROR_UNSUPPORTED_FILE_TYPE 6806
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_APPS 6807
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_APPS 6808
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_IMAGES 6809
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_IMAGES 6810
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_UNKNOWN 6811
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_UNKNOWN 6812
#define IDS_NEARBY_FILE_ATTACHMENTS_CAPITALIZED_VIDEOS 6813
#define IDS_NEARBY_FILE_ATTACHMENTS_NOT_CAPITALIZED_VIDEOS 6814
#define IDS_NEARBY_SECURE_CONNECTION_ID 6815
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_ADDRESSES 6816
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_ADDRESSES 6817
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_LINKS 6818
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_LINKS 6819
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_PHONE_NUMBERS 6820
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_PHONE_NUMBERS 6821
#define IDS_NEARBY_TEXT_ATTACHMENTS_CAPITALIZED_UNKNOWN 6822
#define IDS_NEARBY_TEXT_ATTACHMENTS_NOT_CAPITALIZED_UNKNOWN 6823
#define IDS_NEARBY_CAPITALIZED_UNKNOWN_ATTACHMENTS 6824
#define IDS_NEARBY_NOT_CAPITALIZED_UNKNOWN_ATTACHMENTS 6825
#define IDS_NEARBY_HIGH_VISIBILITY_SUB_TITLE 6826
#define IDS_NEARBY_HIGH_VISIBILITY_SUB_TITLE_MINUTES 6827
#define IDS_NEARBY_HIGH_VISIBILITY_SUB_TITLE_SECONDS 6828
#define IDS_NEARBY_HIGH_VISIBILITY_HELP_TEXT 6829
#define IDS_NEARBY_HIGH_VISIBILITY_TIMEOUT_TEXT 6830
#define IDS_NEARBY_HIGH_VISIBILITY_TRANSFER_IN_PROGRESS_ERROR 6831
#define IDS_NEARBY_HIGH_VISIBILITY_TRANSFER_IN_PROGRESS_DESCRIPTION 6832
#define IDS_NEARBY_HIGH_VISIBILITY_CONNECTION_MEDIUM_ERROR 6833
#define IDS_NEARBY_HIGH_VISIBILITY_CONNECTION_MEDIUM_DESCRIPTION 6834
#define IDS_NEARBY_RECEIVE_CONFIRM_PAGE_TITLE 6835
#define IDS_NEARBY_RECEIVE_CONFIRM_PAGE_CONNECTION_ID 6836
#define IDS_NEARBY_PREVIEW_TITLE_MULTIPLE_FILE 6837
#define IDS_NEARBY_ACCOUNT_ROW_LABEL 6838
#define IDS_NEARBY_SETTINGS_HELP_CAPTION 6839
#define IDS_NEARBY_VISIBLITY_ALL_CONTACTS 6840
#define IDS_NEARBY_VISIBLITY_ALL_CONTACTS_DESCRIPTION 6841
#define IDS_NEARBY_VISIBLITY_SOME_CONTACTS 6842
#define IDS_NEARBY_VISIBLITY_SOME_CONTACTS_DESCRIPTION 6843
#define IDS_NEARBY_VISIBLITY_HIDDEN 6844
#define IDS_NEARBY_VISIBLITY_HIDDEN_DESCRIPTION 6845
#define IDS_NEARBY_VISIBLITY_UNKNOWN 6846
#define IDS_NEARBY_VISIBLITY_UNKNOWN_DESCRIPTION 6847
#define IDS_NEARBY_NOTIFICATION_ACTION_COPY_TO_CLIPBOARD 6848
#define IDS_NEARBY_NOTIFICATION_ACTION_OPEN_FOLDER 6849
#define IDS_NEARBY_NOTIFICATION_ACTION_OPEN_NETWORK_LIST 6850
#define IDS_NEARBY_NOTIFICATION_ACTION_OPEN_URL 6851
#define IDS_NEARBY_NOTIFICATION_ACCEPT_ACTION 6852
#define IDS_NEARBY_NOTIFICATION_CONNECTION_REQUEST_MESSAGE 6853
#define IDS_NEARBY_NOTIFICATION_CONNECTION_REQUEST_MESSAGE_WIFI_CREDENTIALS 6854
#define IDS_NEARBY_NOTIFICATION_CONNECTION_REQUEST_TITLE 6855
#define IDS_NEARBY_NOTIFICATION_DECLINE_ACTION 6856
#define IDS_NEARBY_NOTIFICATION_ONBOARDING_MESSAGE 6857
#define IDS_NEARBY_NOTIFICATION_GO_VISIBLE_MESSAGE 6858
#define IDS_NEARBY_NOTIFICATION_ONBOARDING_TITLE 6859
#define IDS_NEARBY_NOTIFICATION_SET_UP_ACTION 6860
#define IDS_NEARBY_NOTIFICATION_GO_VISIBLE_ACTION 6861
#define IDS_NEARBY_NOTIFICATION_VISIBILITY_REMINDER_TITLE 6862
#define IDS_NEARBY_NOTIFICATION_VISIBILITY_REMINDER_MESSAGE 6863
#define IDS_NEARBY_NOTIFICATION_GO_TO_SETTINGS_ACTION 6864
#define IDS_NEARBY_NOTIFICATION_DISMISS_ACTION 6865
#define IDS_NEARBY_NOTIFICATION_RECEIVE_FAILURE_TITLE 6866
#define IDS_NEARBY_NOTIFICATION_RECEIVE_FAILURE_TITLE_WIFI_CREDENTIALS 6867
#define IDS_NEARBY_NOTIFICATION_RECEIVE_PROGRESS_TITLE 6868
#define IDS_NEARBY_NOTIFICATION_RECEIVE_PROGRESS_TITLE_WIFI_CREDENTIALS 6869
#define IDS_NEARBY_NOTIFICATION_RECEIVE_SUCCESS_TITLE 6870
#define IDS_NEARBY_NOTIFICATION_RECEIVE_SUCCESS_TITLE_WIFI_CREDENTIALS 6871
#define IDS_NEARBY_NOTIFICATION_SEND_FAILURE_TITLE 6872
#define IDS_NEARBY_NOTIFICATION_SEND_PROGRESS_TITLE 6873
#define IDS_NEARBY_NOTIFICATION_SEND_SUCCESS_TITLE 6874
#define IDS_NEARBY_NOTIFICATION_SOURCE 6875
#define IDS_NEARBY_NOTIFICATION_SENDER_CANCELLED 6876
#define IDS_WELCOME_NEXT 6877
#define IDS_WELCOME_SKIP 6878
#define IDS_WELCOME_STEPS 6879
#define IDS_WELCOME_BOOKMARK_ADDED 6880
#define IDS_WELCOME_BOOKMARKS_ADDED 6881
#define IDS_WELCOME_BOOKMARK_REMOVED 6882
#define IDS_WELCOME_BOOKMARKS_REMOVED 6883
#define IDS_DEFAULT_BROWSER_CHANGED 6884
#define IDS_WELCOME_GOOGLE_APPS_DESCRIPTION 6885
#define IDS_WELCOME_GOOGLE_GMAIL 6887
#define IDS_WELCOME_GOOGLE_APPS_MAPS 6888
#define IDS_WELCOME_GOOGLE_APPS_NEWS 6889
#define IDS_WELCOME_GOOGLE_APPS_TRANSLATE 6890
#define IDS_WELCOME_GOOGLE_APPS_YOUTUBE 6891
#define IDS_WELCOME_NTP_BACKGROUND_DESCRIPTION 6892
#define IDS_WELCOME_NTP_BACKGROUND_DEFAULT_TITLE 6893
#define IDS_WELCOME_NTP_BACKGROUND_ART_TITLE 6894
#define IDS_WELCOME_NTP_BACKGROUND_LANDSCAPE_TITLE 6895
#define IDS_WELCOME_NTP_BACKGROUND_CITYSCAPE_TITLE 6896
#define IDS_WELCOME_NTP_BACKGROUND_EARTH_TITLE 6897
#define IDS_WELCOME_NTP_BACKGROUND_GEOMETRIC_SHAPES_TITLE 6898
#define IDS_WELCOME_NTP_BACKGROUND_PHOTO_BY_LABEL 6899
#define IDS_WELCOME_NTP_BACKGROUND_PREVIEW_UPDATED 6900
#define IDS_WELCOME_NTP_BACKGROUND_RESET 6901
#define IDS_WELCOME_SET_AS_DEFAULT_HEADER 6902
#define IDS_WELCOME_SET_AS_DEFAULT_SUB_HEADER 6903
#define IDS_WELCOME_SET_AS_DEFAULT_SET_AS_DEFAULT 6904
#define IDS_WELCOME_LANDING_TITLE 6905
#define IDS_WELCOME_LANDING_DESCRIPTION 6906
#define IDS_WELCOME_LANDING_NEW_USER 6907
#define IDS_WELCOME_LANDING_EXISTING_USER 6908
#define IDS_WELCOME_SIGNIN_VIEW_HEADER 6909
#define IDS_WELCOME_SIGNIN_VIEW_SUB_HEADER 6910
#define IDS_WELCOME_SIGNIN_VIEW_SIGNIN 6911
#define IDS_WHATS_NEW_TITLE 6912
#define IDS_UTILITY_PROCESS_PRINTING_SERVICE_NAME 6913
#define IDS_UTILITY_PROCESS_PRINT_BACKEND_SERVICE_NAME 6914
#define IDS_PRINT_INVALID_PRINTER_SETTINGS 6915
#define IDS_PRINT_PREVIEW_TITLE 6916
#define IDS_PRINT_PREVIEW_DESCRIPTION 6917
#define IDS_PRINT_PREVIEW_LOADING 6918
#define IDS_PRINT_PREVIEW_FAILED 6919
#define IDS_PRINT_PREVIEW_INVALID_PRINTER_SETTINGS 6920
#define IDS_PRINT_PREVIEW_PRINT_BUTTON 6921
#define IDS_PRINT_PREVIEW_SAVE_BUTTON 6922
#define IDS_PRINT_PREVIEW_PRINTING 6923
#define IDS_PRINT_PREVIEW_SAVING 6924
#define IDS_PRINT_PREVIEW_OPTION_ALL_PAGES 6925
#define IDS_PRINT_PREVIEW_OPTION_ODD_PAGES 6926
#define IDS_PRINT_PREVIEW_OPTION_EVEN_PAGES 6927
#define IDS_PRINT_PREVIEW_OPTION_CUSTOM_PAGES 6928
#define IDS_PRINT_PREVIEW_DESTINATION_LABEL 6929
#define IDS_PRINT_PREVIEW_OPTION_BW 6930
#define IDS_PRINT_PREVIEW_OPTION_COLLATE 6931
#define IDS_PRINT_PREVIEW_OPTION_COLOR 6932
#define IDS_PRINT_PREVIEW_OPTION_LANDSCAPE 6933
#define IDS_PRINT_PREVIEW_OPTION_PORTRAIT 6934
#define IDS_PRINT_PREVIEW_OPTION_TWO_SIDED 6935
#define IDS_PRINT_PREVIEW_PRINT_ON_BOTH_SIDES_LABEL 6936
#define IDS_PRINT_PREVIEW_OPTION_LONG_EDGE 6937
#define IDS_PRINT_PREVIEW_OPTION_SHORT_EDGE 6938
#define IDS_PRINT_PREVIEW_PAGES_LABEL 6939
#define IDS_PRINT_PREVIEW_LAYOUT_LABEL 6940
#define IDS_PRINT_PREVIEW_COPIES_LABEL 6941
#define IDS_PRINT_PREVIEW_SCALING_LABEL 6942
#define IDS_PRINT_PREVIEW_OPTION_DEFAULT_SCALING 6943
#define IDS_PRINT_PREVIEW_OPTION_CUSTOM_SCALING 6944
#define IDS_PRINT_PREVIEW_PAGES_PER_SHEET_LABEL 6945
#define IDS_PRINT_PREVIEW_EXAMPLE_PAGE_RANGE_TEXT 6946
#define IDS_PRINT_PREVIEW_PRINT_TO_PDF 6947
#define IDS_PRINT_PREVIEW_SHEET_SUMMARY_LABEL 6948
#define IDS_PRINT_PREVIEW_PAGE_SUMMARY_LABEL 6949
#define IDS_PRINT_PREVIEW_PAGE_RANGE_SYNTAX_INSTRUCTION 6950
#define IDS_PRINT_PREVIEW_PAGE_RANGE_LIMIT_INSTRUCTION_WITH_VALUE 6951
#define IDS_PRINT_PREVIEW_COPIES_INSTRUCTION 6952
#define IDS_PRINT_PREVIEW_SCALING_INSTRUCTION 6953
#define IDS_PRINT_PREVIEW_PRINT_PAGES_LABEL 6954
#define IDS_PRINT_PREVIEW_OPTIONS_LABEL 6955
#define IDS_PRINT_PREVIEW_OPTION_HEADER_FOOTER 6956
#define IDS_PRINT_PREVIEW_OPTION_FIT_TO_PAGE 6957
#define IDS_PRINT_PREVIEW_OPTION_FIT_TO_PAPER 6958
#define IDS_PRINT_PREVIEW_OPTION_BACKGROUND_COLORS_AND_IMAGES 6959
#define IDS_PRINT_PREVIEW_OPTION_SELECTION_ONLY 6960
#define IDS_PRINT_PREVIEW_OPTION_RASTERIZE 6961
#define IDS_PRINT_PREVIEW_MARGINS_LABEL 6962
#define IDS_PRINT_PREVIEW_DEFAULT_MARGINS 6963
#define IDS_PRINT_PREVIEW_NO_MARGINS 6964
#define IDS_PRINT_PREVIEW_CUSTOM_MARGINS 6965
#define IDS_PRINT_PREVIEW_MINIMUM_MARGINS 6966
#define IDS_PRINT_PREVIEW_TOP_MARGIN_LABEL 6967
#define IDS_PRINT_PREVIEW_BOTTOM_MARGIN_LABEL 6968
#define IDS_PRINT_PREVIEW_LEFT_MARGIN_LABEL 6969
#define IDS_PRINT_PREVIEW_RIGHT_MARGIN_LABEL 6970
#define IDS_PRINT_PREVIEW_MEDIA_SIZE_LABEL 6971
#define IDS_PRINT_PREVIEW_DPI_LABEL 6972
#define IDS_PRINT_PREVIEW_NON_ISOTROPIC_DPI_ITEM_LABEL 6973
#define IDS_PRINT_PREVIEW_DPI_ITEM_LABEL 6974
#define IDS_PRINT_PREVIEW_DESTINATION_SEARCH_TITLE 6975
#define IDS_PRINT_PREVIEW_SEARCH_BOX_PLACEHOLDER 6976
#define IDS_PRINT_PREVIEW_NO_DESTINATIONS_MESSAGE 6977
#define IDS_PRINT_PREVIEW_PRINT_DESTINATIONS_TITLE 6978
#define IDS_PRINT_PREVIEW_MANAGE 6979
#define IDS_PRINT_PREVIEW_SEE_MORE 6980
#define IDS_PRINT_PREVIEW_SEE_MORE_DESTINATIONS_LABEL 6981
#define IDS_PRINT_PREVIEW_EXTENSION_DESTINATION_ICON_TOOLTIP 6982
#define IDS_MORE_OPTIONS_LABEL 6983
#define IDS_PRINT_PREVIEW_COULD_NOT_PRINT 6984
#define IDS_PRINT_PREVIEW_ADVANCED_SETTINGS_SEARCH_BOX_PLACEHOLDER 6985
#define IDS_PRINT_PREVIEW_ADVANCED_SETTINGS_DIALOG_TITLE 6986
#define IDS_PRINT_PREVIEW_NO_ADVANCED_SETTINGS_MATCH_SEARCH_HINT 6987
#define IDS_PRINT_PREVIEW_ADVANCED_SETTINGS_DIALOG_CONFIRM 6988
#define IDS_PRINT_PREVIEW_NEW_SHOW_ADVANCED_OPTIONS 6989
#define IDS_PRINT_PREVIEW_BUTTON_SELECT 6990
#define IDS_PRINT_PREVIEW_BUTTON_GO_BACK 6991
#define IDS_PRINT_PREVIEW_RESOLVE_EXTENSION_USB_DIALOG_TITLE 6992
#define IDS_PRINT_PREVIEW_RESOLVE_EXTENSION_USB_PERMISSION_MESSAGE 6993
#define IDS_PRINT_PREVIEW_RESOLVE_EXTENSION_USB_ERROR_MESSAGE 6994
#define IDS_PRINT_PREVIEW_MANAGED_SETTINGS_TEXT 6995
#define IDS_PRINT_PREVIEW_SYSTEM_DIALOG_OPTION 7019
#define IDS_DEFAULT_PRINT_DOCUMENT_TITLE 7022
#define IDS_PRINT_SPOOL_FAILED_TITLE_TEXT 7023
#define IDS_PRINT_SPOOL_FAILED_ERROR_TEXT 7024
#define IDS_PRESS_APP_TO_EXIT 7025
#define IDS_VR_SHELL_SITE_IS_TRACKING_LOCATION 7026
#define IDS_VR_SHELL_SITE_IS_USING_MICROPHONE 7027
#define IDS_VR_SHELL_SITE_IS_USING_CAMERA 7028
#define IDS_VR_SHELL_SITE_IS_SHARING_SCREEN 7029
#define IDS_VR_SHELL_BG_IS_USING_MICROPHONE 7030
#define IDS_VR_SHELL_BG_IS_USING_CAMERA 7031
#define IDS_VR_SHELL_BG_IS_SHARING_SCREEN 7032
#define IDS_VR_SHELL_SITE_CAN_TRACK_LOCATION 7033
#define IDS_VR_SHELL_SITE_CAN_USE_MICROPHONE 7034
#define IDS_VR_SHELL_SITE_CAN_USE_CAMERA 7035
#define IDS_VR_SHELL_SITE_CAN_SHARE_SCREEN 7036
#define IDS_VR_SHELL_SITE_IS_USING_BLUETOOTH 7037
#define IDS_VR_SHELL_SITE_CAN_USE_BLUETOOTH 7039
#define IDS_DESKTOP_PROMPT_DOFF_HEADSET 7040
#define IDS_VR_DESKTOP_GENERIC_PERMISSION_PROMPT 7041
#define IDS_VR_SHELL_SITE_IS_USING_USB 7042
#define IDS_VR_SHELL_SITE_IS_USING_MIDI 7043
#define IDS_VR_SHELL_SITE_CAN_USE_MIDI 7044
#define IDS_VR_UPDATE_KEYBOARD_PROMPT 7045
#define IDS_VR_SHELL_EXIT_PROMPT_DESCRIPTION 7046
#define IDS_VR_SHELL_EXIT_PROMPT_DESCRIPTION_SITE_INFO 7047
#define IDS_VR_SHELL_AUDIO_PERMISSION_PROMPT_DESCRIPTION 7048
#define IDS_VR_SHELL_AUDIO_PERMISSION_PROMPT_ABORT_BUTTON 7049
#define IDS_VR_SHELL_AUDIO_PERMISSION_PROMPT_CONTINUE_BUTTON 7050
#define IDS_VR_SHELL_EXIT_PROMPT_EXIT_VR_BUTTON 7051
#define IDS_VR_BROWSER_UNSUPPORTED_PAGE 7052
#define IDS_VR_WEB_VR_TIMEOUT_MESSAGE 7053
#define IDS_VR_WEB_VR_EXIT_BUTTON_LABEL 7054
#define IDS_VR_NO_SPEECH_RECOGNITION_RESULT 7055
#define IDS_VR_BUTTON_TRACKPAD 7056
#define IDS_VR_BUTTON_EXIT 7057
#define IDS_VR_BUTTON_BACK 7058
#define IDS_VR_BUTTON_TRACKPAD_REPOSITION 7059
#define IDS_VR_BUTTON_APP_REPOSITION 7060
#define IDS_VR_MENU_NEW_INCOGNITO_TAB 7061
#define IDS_VR_MENU_PREFERENCES 7062
#define IDS_VR_MENU_CLOSE_INCOGNITO_TABS 7063
#define IDS_VR_TABS_BUTTON_REGULAR 7064
#define IDS_VR_TABS_BUTTON_INCOGNITO 7065
#define IDS_BACK_BUTTON_AUTHENTICATOR_REQUEST_DIALOG 7106
#define IDS_BACKGROUND_APP_INSTALLED_BALLOON_TITLE 7107
#define IDS_BACKGROUND_APP_INSTALLED_BALLOON_BODY 7108
#define IDS_BACKGROUND_CRASHED_APP_BALLOON_MESSAGE 7109
#define IDS_BACKGROUND_CRASHED_EXTENSION_BALLOON_MESSAGE 7110
#define IDS_BACKGROUND_APP_NOT_INSTALLED 7111
#define IDS_PERMISSIONS_REQUESTED_SCREENREADER_ANNOUNCEMENT 7112
#define IDS_PERMISSIONS_EXPIRED_SCREENREADER_ANNOUNCEMENT 7113
#define IDS_PERMISSIONS_BUBBLE_PROMPT 7114
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ACCESSIBLE_TITLE_ONE_PERM 7115
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ACCESSIBLE_TITLE_TWO_PERMS 7116
#define IDS_PERMISSIONS_BUBBLE_PROMPT_ACCESSIBLE_TITLE_TWO_PERMS_MORE 7117
#define IDS_PERMISSIONS_BUBBLE_PROMPT_THIS_FILE 7118
#define IDS_PERMISSION_CUSTOMIZE 7119
#define IDS_ALTERNATE_NAV_URL_VIEW_LABEL 7120
#define IDS_DOWNLOAD_TITLE 7121
#define IDS_TAB_LOADING_TITLE 7122
#define IDS_HOVER_CARD_FILE_URL_SOURCE 7123
#define IDS_HOVER_CARD_BLOB_URL_SOURCE 7124
#define IDS_HOVER_CARD_CRASHED_TITLE 7125
#define IDS_HISTORY_SEARCH_PROMPT 7126
#define IDS_HISTORY_DELETE 7127
#define IDS_HISTORY_ITEMS_SELECTED 7128
#define IDS_HISTORY_ITEMS_UNSELECTED 7129
#define IDS_HISTORY_HISTORY_MENU_DESCRIPTION 7130
#define IDS_HISTORY_HISTORY_MENU_ITEM 7131
#define IDS_HISTORY_NO_SYNCED_RESULTS 7132
#define IDS_HISTORY_OPEN_TABS_MENU_ITEM 7133
#define IDS_HISTORY_SIGN_IN_BUTTON 7134
#define IDS_HISTORY_SIGN_IN_PROMO 7135
#define IDS_HISTORY_SIGN_IN_PROMO_DESC 7136
#define IDS_EDIT 344
#define IDS_CLEAR_SEARCH 7137
#define IDS_CONFIRM 7138
#define IDS_DISABLE 7139
#define IDS_SEARCH_CLEARED 7140
#define IDS_SEARCH_RESULTS 7141
#define IDS_SEARCH_RESULTS_SINGULAR 7142
#define IDS_SEARCH_RESULTS_PLURAL 7143
#define IDS_SEARCH_RESULT_BUBBLE_TEXT 7144
#define IDS_SEARCH_RESULTS_BUBBLE_TEXT 7145
#define IDS_SEARCH_NO_RESULTS 7146
#define IDS_SHOW_BUBBLE_INACTIVE_DESCRIPTION 7147
#define IDS_FOCUS_HELP_BUBBLE_DESCRIPTION 7148
#define IDS_FOCUS_HELP_BUBBLE_TOGGLE_DESCRIPTION 7149
#define IDS_FOCUS_HELP_BUBBLE_TUTORIAL_DESCRIPTION 7150
#define IDS_CONTENT_CONTEXT_INSPECTELEMENT 7151
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_DIALOG_TITLE 7152
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_ENABLE 7153
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_BUBBLE_DISABLE 7154
#define IDS_CONTENT_CONTEXT_BACK 7155
#define IDS_CONTENT_CONTEXT_FORWARD 7156
#define IDS_CONTENT_CONTEXT_SAVEPAGEAS 7157
#define IDS_CONTENT_CONTEXT_PRINT 7158
#define IDS_CONTENT_CONTEXT_VIEWPAGESOURCE 7159
#define IDS_CONTENT_CONTEXT_OPENLINKWITH 7160
#define IDS_CONTENT_CONTEXT_OPENLINKWITH_CONFIGURE 7161
#define IDS_CONTENT_CONTEXT_INSPECTBACKGROUNDPAGE 7162
#define IDS_CONTENT_CONTEXT_READ_ANYTHING 7163
#define IDS_CONTENT_CONTEXT_RELOAD 7164
#define IDS_CONTENT_CONTEXT_RESTART_APP 7165
#define IDS_CONTENT_CONTEXT_RELOAD_PACKAGED_APP 7166
#define IDS_CONTENT_CONTEXT_TRANSLATE 7167
#define IDS_CONTENT_CONTEXT_EXIT_FULLSCREEN 7168
#define IDS_CONTENT_CONTEXT_RELOADFRAME 7169
#define IDS_CONTENT_CONTEXT_VIEWFRAMESOURCE 7170
#define IDS_CONTENT_CONTEXT_OPENLINKNEWTAB 7171
#define IDS_CONTENT_CONTEXT_OPENLINKNEWWINDOW 7172
#define IDS_CONTENT_CONTEXT_OPENLINKOFFTHERECORD 7173
#define IDS_CONTENT_CONTEXT_OPENLINKINPROFILES 7174
#define IDS_CONTENT_CONTEXT_OPENLINKINPROFILE 7175
#define IDS_CONTENT_CONTEXT_OPENLINKBOOKMARKAPP 7176
#define IDS_CONTENT_CONTEXT_OPENLINKBOOKMARKAPP_SAMEAPP 7177
#define IDS_CONTENT_CONTEXT_SAVELINKAS 7178
#define IDS_CONTENT_CONTEXT_COPYLINKLOCATION 7179
#define IDS_CONTENT_CONTEXT_COPYEMAILADDRESS 7180
#define IDS_CONTENT_CONTEXT_COPYLINKTEXT 7181
#define IDS_CONTENT_CONTEXT_COPYLINKTOTEXT 7182
#define IDS_CONTENT_CONTEXT_REMOVELINKTOTEXT 7183
#define IDS_CONTENT_CONTEXT_RESHARELINKTOTEXT 7184
#define IDS_CONTENT_CONTEXT_SAVEIMAGEAS 7185
#define IDS_CONTENT_CONTEXT_COPYIMAGELOCATION 7186
#define IDS_CONTENT_CONTEXT_COPYIMAGE 7187
#define IDS_CONTENT_CONTEXT_OPENIMAGENEWTAB 7188
#define IDS_CONTENT_CONTEXT_OPEN_ORIGINAL_IMAGE_NEW_TAB 7189
#define IDS_CONTENT_CONTEXT_LOAD_IMAGE 7190
#define IDS_CONTENT_CONTEXT_LOOP 7191
#define IDS_CONTENT_CONTEXT_CONTROLS 7192
#define IDS_CONTENT_CONTEXT_ROTATECW 7193
#define IDS_CONTENT_CONTEXT_ROTATECCW 7194
#define IDS_CONTENT_CONTEXT_SAVEVIDEOAS 7195
#define IDS_CONTENT_CONTEXT_COPYVIDEOLOCATION 7196
#define IDS_CONTENT_CONTEXT_OPENVIDEONEWTAB 7197
#define IDS_CONTENT_CONTEXT_SAVEAUDIOAS 7198
#define IDS_CONTENT_CONTEXT_COPYAUDIOLOCATION 7199
#define IDS_CONTENT_CONTEXT_OPENAUDIONEWTAB 7200
#define IDS_CONTENT_CONTEXT_PICTUREINPICTURE 7201
#define IDS_CONTENT_CONTEXT_UNDO 7202
#define IDS_CONTENT_CONTEXT_REDO 7203
#define IDS_CONTENT_CONTEXT_CUT 7204
#define IDS_CONTENT_CONTEXT_COPY 7205
#define IDS_CONTENT_CONTEXT_PASTE 7206
#define IDS_CONTENT_CONTEXT_PASTE_AND_MATCH_STYLE 7207
#define IDS_CONTENT_CONTEXT_ADD_TO_DICTIONARY 7208
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_MENU_OPTION 7209
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_SEND 7210
#define IDS_CONTENT_CONTEXT_ACCESSIBILITY_LABELS_SEND_ONCE 7211
#define IDS_CONTENT_CONTEXT_SPELLING_ASK_GOOGLE 7212
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_TITLE 7213
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_ENABLE 7214
#define IDS_CONTENT_CONTEXT_SPELLING_BUBBLE_DISABLE 7215
#define IDS_CONTENT_CONTEXT_SPELLING_CHECKING 7216
#define IDS_CONTENT_CONTEXT_SPELLING_NO_SUGGESTIONS_FROM_GOOGLE 7217
#define IDS_CONTENT_CONTEXT_SELECTALL 7218
#define IDS_CONTENT_CONTEXT_SEARCHWEBFOR 7219
#define IDS_CONTENT_CONTEXT_SEARCHWEBFORIMAGE 7220
#define IDS_CONTENT_CONTEXT_SEARCHLENSFORIMAGE 7221
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH 7222
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT1 7223
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT2 7224
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT3 7225
#define IDS_CONTENT_CONTEXT_LENS_REGION_SEARCH_ALT4 7226
#define IDS_CONTENT_CONTEXT_GOTOURL 7227
#define IDS_CONTENT_CONTEXT_GENERATEPASSWORD 7228
#define IDS_CONTENT_CONTEXT_MORE_APPS 7229
#define IDS_CONTENT_CONTEXT_OPEN_WITH_APP 7230
#define IDS_SHARE_MENU_TITLE 7231
#define IDS_CONTENT_CONTEXT_PLUGIN_RUN 7232
#define IDS_CONTENT_CONTEXT_PLUGIN_HIDE 7233
#define IDS_CONTENT_CONTEXT_ENABLE_FLASH 7234
#define IDS_CONTENT_CONTEXT_SPELLCHECK_MENU 7236
#define IDS_CONTENT_CONTEXT_LANGUAGE_SETTINGS 7235
#define IDS_CONTENT_CONTEXT_SPELLCHECK_MULTI_LINGUAL 7237
#define IDS_CONTENT_CONTEXT_CHECK_SPELLING_WHILE_TYPING 7238
#define IDS_NEW_TAB 315
#define IDS_NEW_INCOGNITO_TAB 7239
#define IDS_SHOW_AS_TAB 7240
#define IDS_NEW_WINDOW 316
#define IDS_NEW_INCOGNITO_WINDOW 317
#define IDS_PIN_TO_START_SCREEN 326
#define IDS_EDIT2 360
#define IDS_CUT 345
#define IDS_COPY 346
#define IDS_PASTE 347
#define IDS_DELETE 7241
#define IDS_FIND 336
#define IDS_SAVE_PAGE 337
#define IDS_DISTILL_PAGE 7242
#define IDS_EXIT_DISTILLED_PAGE 7243
#define IDS_MORE_TOOLS_MENU 343
#define IDS_ZOOM_MENU 331
#define IDS_ZOOM_MENU2 355
#define IDS_ZOOM_PLUS 7244
#define IDS_ZOOM_PLUS2 333
#define IDS_ZOOM_NORMAL 7245
#define IDS_ZOOM_MINUS 7246
#define IDS_ZOOM_MINUS2 332
#define IDS_COPY_URL 7247
#define IDS_OPEN_IN_APP_WINDOW 7248
#define IDS_MOVE_TAB_TO_NEW_WINDOW 7249
#define IDS_TOGGLE_QUICK_COMMANDS 7250
#define IDS_SEARCH_TABS 7251
#define IDS_ACCNAME_ZOOM_PLUS2 357
#define IDS_ACCNAME_ZOOM_MINUS2 356
#define IDS_VIEW_SOURCE 7253
#define IDS_FEEDBACK 351
#define IDS_DEV_TOOLS 342
#define IDS_DEV_TOOLS_ELEMENTS 7254
#define IDS_DEV_TOOLS_CONSOLE 7255
#define IDS_DEV_TOOLS_DEVICES 7256
#define IDS_TASK_MANAGER 341
#define IDS_TAKE_SCREENSHOT 7257
#define IDS_RESTORE_ALL_TABS 7258
#define IDS_RESTORE_TAB 7259
#define IDS_REOPEN_WINDOW 7260
#define IDS_REOPEN_GROUP 7261
#define IDS_RESTORE_WINDOW 7262
#define IDS_RESTORE_GROUP 7263
#define IDS_NAME_WINDOW 7264
#define IDS_TOS_NOTIFICATION_TITLE 7267
#define IDS_TOS_NOTIFICATION_BODY_TEXT 7268
#define IDS_TOS_NOTIFICATION_ACK_BUTTON_TEXT 7269
#define IDS_TOS_NOTIFICATION_REVIEW_BUTTON_TEXT 7270
#define IDS_TOS_NOTIFICATION_LINK 7271
#define IDS_HELP_MENU 352
#define IDS_MANAGED 7272
#define IDS_MANAGED_BY 354
#define IDS_CHROME_TIPS 7273
#define IDS_CHROME_WHATS_NEW 7274
#define IDS_IMPORT_SETTINGS_MENU_LABEL 329
#define IDS_PROFILING_ENABLED 7275
#define IDS_FULLSCREEN 7276
#define IDS_CLEAR_BROWSING_DATA 339
#define IDS_SHOW_DOWNLOADS 323
#define IDS_SHOW_EXTENSIONS 340
#define IDS_SETTINGS 348
#define IDS_OPTIONS 7277
#define IDS_HELP_PAGE 350
#define IDS_BETA_FORUM 7278
#define IDS_GET_HELP 7279
#define IDS_EXIT 353
#define IDS_AUTOCOMPLETE_MATCH_DESCRIPTION_SEPARATOR 233
#define IDS_MANAGE_SEARCH_ENGINES 7280
#define IDS_MANAGE_SEARCH_ENGINES_AND_SITE_SEARCH 7281
#define IDS_SEARCH_ENGINES_EDITOR_KEYWORD_COLUMN 7282
#define IDS_SEARCH_ENGINES_EDITOR_DESCRIPTION_COLUMN 7283
#define IDS_SEARCH_ENGINES_EDITOR_DEFAULT_ENGINE 7284
#define IDS_ACCNAME_DOWNLOADS_BAR 7285
#define IDS_HIDE_DOWNLOADS 7286
#define IDS_SHOW_ALL_DOWNLOADS 7287
#define IDS_DOWNLOAD_STARTED 7288
#define IDS_DOWNLOAD_INTERRUPTED_STATUS 7289
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_ACCESS_DENIED 7290
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_ACCESS_DENIED 7291
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_PATH_TOO_LONG 7292
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_PATH_TOO_LONG 7293
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_DISK_FULL 7294
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_DISK_FULL 7295
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FILE_TOO_LARGE 7296
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FILE_TOO_LARGE 7297
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_TEMPORARY_PROBLEM 7298
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_TEMPORARY_PROBLEM 7299
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_VIRUS 7300
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_VIRUS 7301
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_BLOCKED 7302
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_BLOCKED 7303
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_CONTENT_LENGTH_MISMATCH 7304
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_CONTENT_LENGTH_MISMATCH 7305
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SECURITY_CHECK_FAILED 7306
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SECURITY_CHECK_FAILED 7307
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FILE_TOO_SHORT 7308
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FILE_TOO_SHORT 7309
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FILE_SAME_AS_SOURCE 7310
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FILE_SAME_AS_SOURCE 7311
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NETWORK_TIMEOUT 7312
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NETWORK_TIMEOUT 7313
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NETWORK_DISCONNECTED 7314
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NETWORK_DISCONNECTED 7315
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SERVER_DOWN 7316
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SERVER_DOWN 7317
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NETWORK_ERROR 7318
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NETWORK_ERROR 7319
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_NO_FILE 7320
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_NO_FILE 7321
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SERVER_PROBLEM 7322
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SERVER_PROBLEM 7323
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_WEB_DRIVE_ERROR 7324
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SHUTDOWN 7325
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SHUTDOWN 7326
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_CRASH 7327
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_CRASH 7328
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_UNAUTHORIZED 7329
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_UNAUTHORIZED 7330
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_SERVER_CERT_PROBLEM 7331
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_SERVER_CERT_PROBLEM 7332
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_FORBIDDEN 7333
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_FORBIDDEN 7334
#define IDS_DOWNLOAD_INTERRUPTED_STATUS_UNREACHABLE 7335
#define IDS_DOWNLOAD_INTERRUPTED_DESCRIPTION_UNREACHABLE 7336
#define IDS_DOWNLOAD_NOTIFICATION_LABEL_OPEN_WHEN_COMPLETE 7337
#define IDS_DOWNLOAD_NOTIFICATION_LABEL_OPEN 7338
#define IDS_DOWNLOAD_STATUS_STARTING 7339
#define IDS_DOWNLOAD_STATUS_IN_PROGRESS 7349
#define IDS_DOWNLOAD_STATUS_SIZES 7350
#define IDS_DOWNLOAD_STATUS_OPEN_IN 7351
#define IDS_DOWNLOAD_STATUS_OPEN_WHEN_COMPLETE 7352
#define IDS_DOWNLOAD_STATUS_OPENING 7353
#define IDS_DOWNLOAD_STATUS_IN_PROGRESS_SHORT 7354
#define IDS_DOWNLOAD_STATUS_CANCELLED 7355
#define IDS_DOWNLOAD_STATUS_REMOVED 7356
#define IDS_DOWNLOAD_STATUS_UPLOADING 7357
#define IDS_DOWNLOAD_STATUS_UPLOADED 7358
#define IDS_DOWNLOAD_STATUS_UPLOAD_INTERRUPTED 7359
#define IDS_DOWNLOAD_STATUS_INTERRUPTED 7360
#define IDS_DOWNLOAD_UNCONFIRMED_PREFIX 7361
#define IDS_PROMPT_DANGEROUS_DOWNLOAD 7362
#define IDS_PROMPT_DANGEROUS_DOWNLOAD_EXTENSION 7363
#define IDS_PROMPT_UNCOMMON_DOWNLOAD_CONTENT 7364
#define IDS_PROMPT_UNCOMMON_DOWNLOAD_CONTENT_IN_ADVANCED_PROTECTION 7365
#define IDS_PROMPT_DEEP_SCANNING_DOWNLOAD 7366
#define IDS_PROMPT_DEEP_SCANNING_DOWNLOAD_SHORT 7367
#define IDS_PROMPT_DEEP_SCANNING_APP_DOWNLOAD 7368
#define IDS_PROMPT_CONFIRM_MIXED_CONTENT_DOWNLOAD 7369
#define IDS_PROMPT_DOWNLOAD_BLOCKED_TOO_LARGE 7370
#define IDS_PROMPT_DOWNLOAD_BLOCKED_PASSWORD_PROTECTED 7371
#define IDS_PROMPT_DOWNLOAD_DEEP_SCANNED_SAFE 7372
#define IDS_PROMPT_DOWNLOAD_SENSITIVE_CONTENT_WARNING 7373
#define IDS_PROMPT_DOWNLOAD_SENSITIVE_CONTENT_BLOCKED 7374
#define IDS_PROMPT_DOWNLOAD_DEEP_SCANNED_OPENED_DANGEROUS 7375
#define IDS_PROMPT_DOWNLOAD_MIXED_CONTENT_WARNING 7376
#define IDS_PROMPT_DOWNLOAD_MIXED_CONTENT_BLOCKED 7377
#define IDS_PROMPT_APP_DEEP_SCANNING 7378
#define IDS_PROMPT_DEEP_SCANNING 7379
#define IDS_BLOCK_REASON_UNCOMMON_DOWNLOAD 7380
#define IDS_BLOCK_REASON_UNCOMMON_DOWNLOAD_IN_ADVANCED_PROTECTION 7381
#define IDS_BLOCK_REASON_GENERIC_DOWNLOAD 7382
#define IDS_BLOCK_REASON_MIXED_CONTENT 7383
#define IDS_BLOCK_REASON_DEEP_SCANNING 7384
#define IDS_BLOCK_REASON_ACCOUNT_COMPROMISE 7385
#define IDS_DEEP_SCANNED_SAFE_DESCRIPTION 7386
#define IDS_DEEP_SCANNED_OPENED_DANGEROUS_DESCRIPTION 7387
#define IDS_BLOCK_REASON_SENSITIVE_CONTENT_WARNING 7388
#define IDS_SENSITIVE_CONTENT_BLOCKED_DESCRIPTION 7389
#define IDS_BLOCKED_TOO_LARGE_DESCRIPTION 7390
#define IDS_BLOCKED_PASSWORD_PROTECTED_DESCRIPTION 7391
#define IDS_CONFIRM_KEEP_DANGEROUS_DOWNLOAD_TITLE 7395
#define IDS_KEEP_DANGEROUS_DOWNLOAD_TITLE 7396
#define IDS_KEEP_UNCOMMON_DOWNLOAD_TITLE 7397
#define IDS_PROMPT_CONFIRM_KEEP_DANGEROUS_DOWNLOAD 7398
#define IDS_PROMPT_CONFIRM_KEEP_MALICIOUS_DOWNLOAD_BODY 7399
#define IDS_CONFIRM_DOWNLOAD_AGAIN 7400
#define IDS_CONFIRM_DOWNLOAD 7401
#define IDS_CONFIRM_DOWNLOAD_RESTORE 7402
#define IDS_CONTINUE_EXTENSION_DOWNLOAD 7403
#define IDS_DISCARD_DOWNLOAD 7404
#define IDS_OPEN_DOWNLOAD_NOW 7405
#define IDS_SCAN_DOWNLOAD 7406
#define IDS_REVIEW_DOWNLOAD 7407
#define IDS_DOWNLOAD_LINK_PAUSE 7408
#define IDS_DOWNLOAD_SEARCH 7409
#define IDS_DOWNLOAD_NO_DOWNLOADS 7410
#define IDS_DOWNLOAD_ITEM_DROPDOWN_BUTTON_ACCESSIBLE_TEXT 7411
#define IDS_DOWNLOAD_LINK_RESUME 7412
#define IDS_DOWNLOAD_LINK_REMOVE 7413
#define IDS_DOWNLOAD_LINK_REMOVE_ARIA_LABEL 7414
#define IDS_DOWNLOAD_LINK_CANCEL 7415
#define IDS_DOWNLOAD_LINK_RETRY 7416
#define IDS_DOWNLOAD_LINK_SHOW_IN_WEB_DRIVE 7417
#define IDS_DOWNLOAD_LINK_SHOW 7418
#define IDS_DOWNLOAD_TAB_CANCELLED 7419
#define IDS_DOWNLOAD_FILE_REMOVED 7420
#define IDS_DOWNLOAD_TAB_PROGRESS_STATUS_TIME_UNKNOWN 7421
#define IDS_DOWNLOAD_TAB_PROGRESS_STATUS 7422
#define IDS_DOWNLOAD_TAB_PROGRESS_SIZE 7423
#define IDS_DOWNLOAD_PROGRESS_PAUSED 7424
#define IDS_DOWNLOAD_LINK_CLEAR_ALL 7425
#define IDS_DOWNLOAD_LINK_OPEN_DOWNLOADS_FOLDER 7426
#define IDS_DOWNLOAD_MORE_ACTIONS 7427
#define IDS_DOWNLOAD_ACTION_MENU_DESCRIPTION 7428
#define IDS_DOWNLOAD_BY_EXTENSION_URL 7429
#define IDS_DOWNLOAD_IN_INCOGNITO 7430
#define IDS_UNDO_DESCRIPTION 7431
#define IDS_DOWNLOAD_UNDO 7432
#define IDS_DOWNLOAD_TOAST_REMOVED_FROM_LIST 7433
#define IDS_DOWNLOAD_TOAST_CLEARED_ALL 7434
#define IDS_DOWNLOAD_STATUS_IN_PROGRESS_ACCESSIBLE_ALERT 7435
#define IDS_DOWNLOAD_STATUS_PERCENT_COMPLETE_ACCESSIBLE_ALERT 7436
#define IDS_DOWNLOAD_STATUS_TIME_REMAINING_ACCESSIBLE_ALERT 7437
#define IDS_DOWNLOAD_FAILED_ACCESSIBLE_ALERT 7438
#define IDS_DOWNLOAD_CANCELLED_ACCESSIBLE_ALERT 7439
#define IDS_DOWNLOAD_COMPLETE_ACCESSIBLE_ALERT 7440
#define IDS_PROMPT_APP_DEEP_SCANNING_ACCESSIBLE_ALERT 7441
#define IDS_PROMPT_DEEP_SCANNING_ACCESSIBLE_ALERT 7442
#define IDS_DEEP_SCANNING_ACCESSIBLE_ALERT 7443
#define IDS_PROMPT_DOWNLOAD_MIXED_CONTENT_BLOCKED_ACCESSIBLE_ALERT 7444
#define IDS_DOWNLOAD_NOTIFICATION_COPY_TO_CLIPBOARD 7445
#define IDS_DOWNLOAD_MENU_SHOW 7446
#define IDS_DOWNLOAD_MENU_OPEN_WHEN_COMPLETE 7447
#define IDS_DOWNLOAD_MENU_OPEN 7448
#define IDS_DOWNLOAD_MENU_ALWAYS_OPEN_TYPE 7449
#define IDS_DOWNLOAD_MENU_PLATFORM_OPEN 7450
#define IDS_DOWNLOAD_MENU_PLATFORM_OPEN_ALWAYS 7451
#define IDS_DOWNLOAD_MENU_CANCEL 7452
#define IDS_DOWNLOAD_MENU_PAUSE_ITEM 7453
#define IDS_DOWNLOAD_MENU_RESUME_ITEM 7454
#define IDS_DOWNLOAD_MENU_DISCARD 7455
#define IDS_DOWNLOAD_MENU_KEEP 7456
#define IDS_DOWNLOAD_MENU_LEARN_MORE_SCANNING 7457
#define IDS_DOWNLOAD_MENU_LEARN_MORE_INTERRUPTED 7458
#define IDS_DOWNLOAD_MENU_LEARN_MORE_MIXED_CONTENT 7459
#define IDS_DOWNLOAD_MENU_ALWAYS_OPEN_PDF_IN_READER 7460
#define IDS_DOWNLOAD_MENU_DEEP_SCAN 7461
#define IDS_ABANDON_DOWNLOAD_DIALOG_TITLE 7472
#define IDS_ABANDON_DOWNLOAD_DIALOG_CONTINUE_BUTTON 7473
#define IDS_ABANDON_DOWNLOAD_DIALOG_INCOGNITO_MESSAGE 7474
#define IDS_ABANDON_DOWNLOAD_DIALOG_GUEST_MESSAGE 7475
#define IDS_ABANDON_DOWNLOAD_DIALOG_EXIT_BUTTON 7476
#define IDS_DOWNLOAD_BUBBLE_HEADER_TEXT 7477
#define IDS_DOWNLOAD_BUBBLE_FOOTER_LINK 7478
#define IDS_DOWNLOAD_BUBBLE_DOWNLOAD_SEPERATOR 7479
#define IDS_DOWNLOAD_BUBBLE_DOWNLOAD_SYMBOL 7480
#define IDS_DOWNLOAD_BUBBLE_STATUS_RESUMING 7481
#define IDS_DOWNLOAD_BUBBLE_STATUS_DONE 7482
#define IDS_DOWNLOAD_BUBBLE_STATUS_BLOCKED 7483
#define IDS_DOWNLOAD_BUBBLE_STATUS_MALWARE 7484
#define IDS_DOWNLOAD_BUBBLE_CHECKBOX_BYPASS 7485
#define IDS_DOWNLOAD_BUBBLE_CONTINUE 7486
#define IDS_DOWNLOAD_BUBBLE_DELETE 7487
#define IDS_DOWNLOAD_BUBBLE_SCAN 7488
#define IDS_DOWNLOAD_BUBBLE_OPEN 7489
#define IDS_DOWNLOAD_BUBBLE_OPEN_NOW 7490
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_DISK_FULL 7491
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_DISK_FULL 7492
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_PATH_TOO_LONG 7493
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_PATH_TOO_LONG 7494
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_NEEDS_PERMISSION 7495
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_FILE_TOO_LARGE 7496
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_FILE_TOO_LARGE 7497
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_UNFINISHED 7498
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_BLOCKED_ORGANIZATION 7499
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_BLOCKED_ORGANIZATION 7500
#define IDS_DOWNLOAD_BUBBLE_WARNING_SUBPAGE_SUMMARY_BLOCKED_ORGANIZATION 7501
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_WRONG 7502
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_NETWORK_ERROR 7503
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_SITE_UNAVAILABLE 7504
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_FILE_UNAVAILABLE 7505
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_SUBPAGE_SUMMARY_FILE_UNAVAILABLE 7506
#define IDS_DOWNLOAD_BUBBLE_INTERRUPTED_STATUS_FILE_UNFINISHED 7507
#define IDS_DOWNLOAD_BUBBLE_WARNING_STATUS_INSECURE 7508
#define IDS_DOWNLOAD_BUBBLE_WARNING_SUBPAGE_SUMMARY_INSECURE 7509
#define IDS_DOWNLOAD_BUBBLE_STATUS_DANGEROUS 7510
#define IDS_DOWNLOAD_BUBBLE_STATUS_ENCRYPTED 7511
#define IDS_DOWNLOAD_BUBBLE_STATUS_TOO_BIG 7512
#define IDS_DOWNLOAD_BUBBLE_STATUS_ADVANCED_PROTECTION 7513
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_ADVANCED_PROTECTION 7514
#define IDS_DOWNLOAD_BUBBLE_STATUS_UNCOMMON_FILE 7515
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_UNCOMMON_FILE 7516
#define IDS_DOWNLOAD_BUBBLE_STATUS_UNKNOWN_SOURCE 7517
#define IDS_DOWNLOAD_BUBBLE_STATUS_SENSITIVE_CONTENT 7518
#define IDS_DOWNLOAD_BUBBLE_SUBPAGE_SUMMARY_SENSITIVE_CONTENT 7519
#define IDS_DOWNLOAD_BUBBLE_TRY_AGAIN 7520
#define IDS_DOWNLOAD_BUBBLE_STATUS_DEEP_SCANNING_PROMPT 7521
#define IDS_DOWNLOAD_BUBBLE_STATUS_ASYNC_SCANNING 7522
#define IDS_OMNIBOX_PWA_INSTALL_ICON_LABEL 281
#define IDS_OMNIBOX_PWA_INSTALL_ICON_TOOLTIP 7523
#define IDS_ADD_TO_OS_LAUNCH_SURFACE_BUBBLE_TITLE 7524
#define IDS_CREATE_SHORTCUTS_BUTTON_LABEL 7525
#define IDS_INSTALL_TO_OS_LAUNCH_SURFACE_BUBBLE_TITLE 7526
#define IDS_INSTALL_PWA_BUTTON_LABEL 7527
#define IDS_BOOKMARK_APP_AX_BUBBLE_NAME_LABEL 7528
#define IDS_BOOKMARK_APP_BUBBLE_OPEN_AS_TAB 7529
#define IDS_BOOKMARK_APP_BUBBLE_OPEN_AS_WINDOW 7530
#define IDS_BOOKMARK_APP_BUBBLE_OPEN_AS_TABBED_WINDOW 7531
#define IDS_WEBAPP_UPDATE_DIALOG_TITLE_NAME 7532
#define IDS_WEBAPP_UPDATE_DIALOG_TITLE_ICON 7533
#define IDS_WEBAPP_UPDATE_DIALOG_TITLE_NAME_AND_ICON 7534
#define IDS_WEBAPP_UPDATE_EXPLANATION 7535
#define IDS_WEBAPP_UPDATE_CURRENT_ICON 7536
#define IDS_WEBAPP_UPDATE_NEW_ICON 7537
#define IDS_WEBAPP_UPDATE_NEGATIVE_BUTTON 7538
#define IDS_FINISH_POLICY_WEB_APP_INSTALLATION 7539
#define IDS_FINISH_POLICY_WEB_APP_INSTALATION_RESTART 7540
#define IDS_FINISH_POLICY_WEB_APP_INSTALLATION_NOT_NOW 7541
#define IDS_ADD_TO_OS_LAUNCH_SURFACE 338
#define IDS_INSTALL_TO_OS_LAUNCH_SURFACE 7546
#define IDS_UNINSTALL_FROM_OS_LAUNCH_SURFACE 7547
#define IDS_APPLICATION_INFO_WEB_STORE_LINK 7548
#define IDS_APPLICATION_INFO_HOMEPAGE_LINK 7549
#define IDS_ARC_APPLICATION_INFO_MANAGE_LINK 7550
#define IDS_APPLICATION_INFO_APP_OVERVIEW_TITLE 7551
#define IDS_APPLICATION_INFO_APP_PERMISSIONS_TITLE 7552
#define IDS_APPLICATION_INFO_UNINSTALL_BUTTON_TEXT 7553
#define IDS_APPLICATION_INFO_LICENSES_BUTTON_TEXT 7554
#define IDS_APPLICATION_INFO_SIZE_LABEL 7555
#define IDS_APPLICATION_INFO_VERSION_LABEL 7556
#define IDS_APPLICATION_INFO_CREATE_SHORTCUTS_BUTTON_TEXT 7557
#define IDS_APPLICATION_INFO_LAUNCH_OPTIONS_ACCNAME 7558
#define IDS_APPLICATION_INFO_SIZE_LOADING_LABEL 7559
#define IDS_APPLICATION_INFO_SIZE_SMALL_LABEL 7560
#define IDS_APPLICATION_INFO_REVOKE_PERMISSION_ALT_TEXT 7561
#define IDS_APPLICATION_INFO_APP_NO_PERMISSIONS_TEXT 7562
#define IDS_APPLICATION_INFO_EXTENSION_NO_PERMISSIONS_TEXT 7563
#define IDS_APPLICATION_INFO_RETAINED_FILES 7564
#define IDS_APPLICATION_INFO_RETAINED_DEVICES 7565
#define IDS_CREATE_SHORTCUTS_TITLE 7566
#define IDS_CREATE_SHORTCUTS_LABEL 7567
#define IDS_CREATE_SHORTCUTS_DESKTOP_CHKBOX 7568
#define IDS_CREATE_SHORTCUTS_MENU_CHKBOX 7569
#define IDS_CREATE_SHORTCUTS_COMMIT 7570
#define IDS_CREATE_SHORTCUTS_START_MENU_CHKBOX 7571
#define IDS_CREATE_SHORTCUTS_QUICK_LAUNCH_BAR_CHKBOX 7572
#define IDS_PIN_TO_TASKBAR_CHKBOX 7573
#define IDS_BLUETOOTH_DEVICE_CREDENTIALS_TITLE 7574
#define IDS_BLUETOOTH_DEVICE_CREDENTIALS_LABEL 7575
#define IDS_MANAGE 7576
#define IDS_LIST_BULLET 7577
#define IDS_BLOCKED_DOWNLOAD_NO_ACTION 7578
#define IDS_BLOCKED_DOWNLOAD_UNBLOCK 7579
#define IDS_ALLOWED_DOWNLOAD_TITLE 7580
#define IDS_BLOCKED_DOWNLOAD_TITLE 7581
#define IDS_BLOCKED_DOWNLOADS_EXPLANATION 7582
#define IDS_ALLOWED_DOWNLOAD_NO_ACTION 7583
#define IDS_ALLOWED_DOWNLOAD_BLOCK 7584
#define IDS_BLOCKED_COOKIES_TITLE 7585
#define IDS_ACCESSED_COOKIES_TITLE 7586
#define IDS_BLOCKED_COOKIES_MESSAGE 7587
#define IDS_ACCESSED_COOKIES_MESSAGE 7588
#define IDS_BLOCKED_COOKIES_INFO 7589
#define IDS_BLOCKED_IMAGES_TITLE 7590
#define IDS_BLOCKED_IMAGES_MESSAGE 7591
#define IDS_BLOCKED_COOKIES_UNBLOCK 7592
#define IDS_BLOCKED_IMAGES_UNBLOCK 7593
#define IDS_BLOCKED_COOKIES_NO_ACTION 7594
#define IDS_ALLOWED_COOKIES_NO_ACTION 7595
#define IDS_ALLOWED_COOKIES_BLOCK 7596
#define IDS_BLOCKED_IMAGES_NO_ACTION 7597
#define IDS_BLOCKED_POPUPS_TOOLTIP 7598
#define IDS_BLOCKED_POPUPS_TITLE 7599
#define IDS_BLOCKED_POPUPS_REDIRECTS_UNBLOCK 7600
#define IDS_BLOCKED_POPUPS_REDIRECTS_NO_ACTION 7601
#define IDS_BLOCKED_MEDIASTREAM_MIC_AND_CAMERA_ALLOW 7602
#define IDS_BLOCKED_MEDIASTREAM_MIC_ALLOW 7603
#define IDS_BLOCKED_MEDIASTREAM_CAMERA_ALLOW 7604
#define IDS_BLOCKED_MEDIASTREAM_MIC_AND_CAMERA_ASK 7605
#define IDS_BLOCKED_MEDIASTREAM_MIC_ASK 7606
#define IDS_BLOCKED_MEDIASTREAM_CAMERA_ASK 7607
#define IDS_ALLOWED_MEDIASTREAM_MIC_AND_CAMERA_BLOCK 7608
#define IDS_ALLOWED_MEDIASTREAM_MIC_BLOCK 7609
#define IDS_ALLOWED_MEDIASTREAM_CAMERA_BLOCK 7610
#define IDS_BLOCKED_MEDIASTREAM_MIC_AND_CAMERA_NO_ACTION 7611
#define IDS_BLOCKED_MEDIASTREAM_MIC_NO_ACTION 7612
#define IDS_BLOCKED_MEDIASTREAM_CAMERA_NO_ACTION 7613
#define IDS_ALLOWED_MEDIASTREAM_MIC_AND_CAMERA_NO_ACTION 7614
#define IDS_ALLOWED_MEDIASTREAM_MIC_AND_CAMERA_PAN_TILT_ZOOM_NO_ACTION 7615
#define IDS_ALLOWED_MEDIASTREAM_MIC_NO_ACTION 7616
#define IDS_ALLOWED_MEDIASTREAM_CAMERA_NO_ACTION 7617
#define IDS_ALLOWED_CAMERA_PAN_TILT_ZOOM_NO_ACTION 7618
#define IDS_BLOCKED_POPUPS_EXPLANATORY_TEXT 7619
#define IDS_BLOCKED_JAVASCRIPT_TITLE 7620
#define IDS_BLOCKED_JAVASCRIPT_MESSAGE 7621
#define IDS_BLOCKED_JAVASCRIPT_UNBLOCK 7622
#define IDS_BLOCKED_JAVASCRIPT_NO_ACTION 7623
#define IDS_BLOCKED_SOUND_TITLE 7624
#define IDS_BLOCKED_SOUND_UNBLOCK 7625
#define IDS_BLOCKED_SOUND_NO_ACTION 7626
#define IDS_NOTIFICATIONS_OFF_EXPLANATORY_TEXT 279
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_TITLE 7627
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_DESCRIPTION 7628
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_ALLOW_BUTTON 7629
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_COMPACT_ALLOW_BUTTON 7630
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_CONTINUE_BLOCKING_BUTTON 7631
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_EARLY_PROMO 7632
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_NEW_REQUEST_PROMO 7633
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_CROWD_DENY_DESCRIPTION 7634
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_ABUSIVE_DESCRIPTION 7635
#define IDS_NOTIFICATIONS_QUIET_PERMISSION_BUBBLE_PREDICTION_SERVICE_DESCRIPTION 7636
#define IDS_GEOLOCATION_OFF_EXPLANATORY_TEXT 7637
#define IDS_GEOLOCATION_QUIET_PERMISSION_BUBBLE_TITLE 7638
#define IDS_GEOLOCATION_QUIET_PERMISSION_BUBBLE_PREDICTION_SERVICE_DESCRIPTION 7639
#define IDS_GEOLOCATION_QUIET_PERMISSION_BUBBLE_ALLOW_BUTTON 7640
#define IDS_COOKIE_CONTROLS_DIALOG_TITLE 7641
#define IDS_COOKIE_CONTROLS_DIALOG_TITLE_ALL_BLOCKED 7642
#define IDS_COOKIE_CONTROLS_DIALOG_TITLE_OFF 7643
#define IDS_COOKIE_CONTROLS_TURN_ON_BUTTON 7644
#define IDS_COOKIE_CONTROLS_TURN_OFF_BUTTON 7645
#define IDS_COOKIE_CONTROLS_NOT_WORKING_TITLE 7646
#define IDS_COOKIE_CONTROLS_NOT_WORKING_DESCRIPTION 7647
#define IDS_COOKIE_CONTROLS_BLOCKED_MESSAGE 7648
#define IDS_COOKIE_CONTROLS_TOOLTIP 7649
#define IDS_COOKIE_CONTROLS_HELP 7650
#define IDS_CERT_SELECTOR_SUBJECT_COLUMN 7651
#define IDS_CERT_SELECTOR_ISSUER_COLUMN 7652
#define IDS_CERT_SELECTOR_PROVIDER_COLUMN 7653
#define IDS_CERT_SELECTOR_SERIAL_COLUMN 7654
#define IDS_CERT_EXPORT_TYPE_BASE64 7655
#define IDS_CERT_EXPORT_TYPE_BASE64_CHAIN 7656
#define IDS_CERT_EXPORT_TYPE_DER 7657
#define IDS_CERT_EXPORT_TYPE_PKCS7 7658
#define IDS_CERT_EXPORT_TYPE_PKCS7_CHAIN 7659
#define IDS_CERT_INFO_DIALOG_TITLE 7660
#define IDS_CERT_INFO_GENERAL_TAB_LABEL 7661
#define IDS_CERT_INFO_DETAILS_TAB_LABEL 7662
#define IDS_CERT_USAGE_SSL_CLIENT 7663
#define IDS_CERT_USAGE_SSL_SERVER 7664
#define IDS_CERT_USAGE_OBJECT_SIGNER 7665
#define IDS_CERT_USAGE_SSL_CA 7666
#define IDS_CERT_INFO_IDN_VALUE_FORMAT 7667
#define IDS_CERT_INFO_FIELD_NOT_PRESENT 7668
#define IDS_CERT_DETAILS_CERTIFICATE_HIERARCHY_LABEL 7669
#define IDS_CERT_DETAILS_CERTIFICATE_FIELDS_LABEL 7670
#define IDS_CERT_DETAILS_CERTIFICATE_FIELD_VALUE_LABEL 7671
#define IDS_CERT_DETAILS_CERTIFICATE 7672
#define IDS_CERT_DETAILS_VERSION 7673
#define IDS_CERT_DETAILS_VERSION_FORMAT 7674
#define IDS_CERT_DETAILS_SERIAL_NUMBER 7675
#define IDS_CERT_DETAILS_CERTIFICATE_SIG_ALG 7676
#define IDS_CERT_DETAILS_ISSUER 7677
#define IDS_CERT_DETAILS_VALIDITY 7678
#define IDS_CERT_DETAILS_NOT_BEFORE 7679
#define IDS_CERT_DETAILS_NOT_AFTER 7680
#define IDS_CERT_DETAILS_SUBJECT 7681
#define IDS_CERT_DETAILS_SUBJECT_KEY_INFO 7682
#define IDS_CERT_DETAILS_SUBJECT_KEY_ALG 7683
#define IDS_CERT_DETAILS_SUBJECT_KEY 7684
#define IDS_CERT_RSA_PUBLIC_KEY_DUMP_FORMAT 7685
#define IDS_CERT_DETAILS_CERTIFICATE_SIG_VALUE 7686
#define IDS_CERT_DETAILS_EXPORT_CERTIFICATE 7687
#define IDS_CERT_OID_AVA_COMMON_NAME 7688
#define IDS_CERT_OID_AVA_STATE_OR_PROVINCE 7689
#define IDS_CERT_OID_AVA_ORGANIZATION_NAME 7690
#define IDS_CERT_OID_AVA_ORGANIZATIONAL_UNIT_NAME 7691
#define IDS_CERT_OID_AVA_DN_QUALIFIER 7692
#define IDS_CERT_OID_AVA_COUNTRY_NAME 7693
#define IDS_CERT_OID_AVA_SERIAL_NUMBER 7694
#define IDS_CERT_OID_AVA_LOCALITY 7695
#define IDS_CERT_OID_AVA_DC 7696
#define IDS_CERT_OID_RFC1274_MAIL 7697
#define IDS_CERT_OID_RFC1274_UID 7698
#define IDS_CERT_OID_PKCS9_EMAIL_ADDRESS 7699
#define IDS_CERT_OID_BUSINESS_CATEGORY 7700
#define IDS_CERT_OID_EV_INCORPORATION_LOCALITY 7701
#define IDS_CERT_OID_EV_INCORPORATION_STATE 7702
#define IDS_CERT_OID_EV_INCORPORATION_COUNTRY 7703
#define IDS_CERT_OID_AVA_STREET_ADDRESS 7704
#define IDS_CERT_OID_AVA_POSTAL_CODE 7705
#define IDS_CERT_OID_PKCS1_RSA_ENCRYPTION 7706
#define IDS_CERT_OID_PKCS1_MD2_WITH_RSA_ENCRYPTION 7707
#define IDS_CERT_OID_PKCS1_MD4_WITH_RSA_ENCRYPTION 7708
#define IDS_CERT_OID_PKCS1_MD5_WITH_RSA_ENCRYPTION 7709
#define IDS_CERT_OID_PKCS1_SHA1_WITH_RSA_ENCRYPTION 7710
#define IDS_CERT_OID_PKCS1_SHA256_WITH_RSA_ENCRYPTION 7711
#define IDS_CERT_OID_PKCS1_SHA384_WITH_RSA_ENCRYPTION 7712
#define IDS_CERT_OID_PKCS1_SHA512_WITH_RSA_ENCRYPTION 7713
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA1_SIGNATURE 7714
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA256_SIGNATURE 7715
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA384_SIGNATURE 7716
#define IDS_CERT_OID_ANSIX962_ECDSA_SHA512_SIGNATURE 7717
#define IDS_CERT_OID_ANSIX962_EC_PUBLIC_KEY 7718
#define IDS_CERT_OID_SECG_EC_SECP256R1 7719
#define IDS_CERT_OID_SECG_EC_SECP384R1 7720
#define IDS_CERT_OID_SECG_EC_SECP521R1 7721
#define IDS_CERT_EXT_NS_CERT_TYPE 7722
#define IDS_CERT_EXT_NS_CERT_TYPE_EMAIL 7723
#define IDS_CERT_EXT_NS_CERT_TYPE_EMAIL_CA 7724
#define IDS_CERT_EXT_NS_CERT_BASE_URL 7725
#define IDS_CERT_EXT_NS_CERT_REVOCATION_URL 7726
#define IDS_CERT_EXT_NS_CA_REVOCATION_URL 7727
#define IDS_CERT_EXT_NS_CERT_RENEWAL_URL 7728
#define IDS_CERT_EXT_NS_CA_POLICY_URL 7729
#define IDS_CERT_EXT_NS_SSL_SERVER_NAME 7730
#define IDS_CERT_EXT_NS_COMMENT 7731
#define IDS_CERT_EXT_NS_LOST_PASSWORD_URL 7732
#define IDS_CERT_EXT_NS_CERT_RENEWAL_TIME 7733
#define IDS_CERT_X509_SUBJECT_DIRECTORY_ATTR 7734
#define IDS_CERT_X509_SUBJECT_KEYID 7735
#define IDS_CERT_KEYID_FORMAT 7736
#define IDS_CERT_ISSUER_FORMAT 7737
#define IDS_CERT_SERIAL_NUMBER_FORMAT 7738
#define IDS_CERT_X509_KEY_USAGE 7739
#define IDS_CERT_X509_ISSUER_ALT_NAME 7740
#define IDS_CERT_X509_BASIC_CONSTRAINTS 7741
#define IDS_CERT_X509_NAME_CONSTRAINTS 7742
#define IDS_CERT_X509_CRL_DIST_POINTS 7743
#define IDS_CERT_X509_CERT_POLICIES 7744
#define IDS_CERT_X509_POLICY_MAPPINGS 7745
#define IDS_CERT_X509_POLICY_CONSTRAINTS 7746
#define IDS_CERT_X509_AUTH_KEYID 7747
#define IDS_CERT_X509_EXT_KEY_USAGE 7748
#define IDS_CERT_X509_AUTH_INFO_ACCESS 7749
#define IDS_CERT_X509_KEY_USAGE_SIGNING 7750
#define IDS_CERT_X509_KEY_USAGE_NONREP 7751
#define IDS_CERT_X509_KEY_USAGE_ENCIPHERMENT 7752
#define IDS_CERT_X509_KEY_USAGE_DATA_ENCIPHERMENT 7753
#define IDS_CERT_X509_KEY_USAGE_KEY_AGREEMENT 7754
#define IDS_CERT_X509_KEY_USAGE_CERT_SIGNER 7755
#define IDS_CERT_X509_KEY_USAGE_CRL_SIGNER 7756
#define IDS_CERT_X509_KEY_USAGE_ENCIPHER_ONLY 7757
#define IDS_CERT_X509_KEY_USAGE_DECIPHER_ONLY 7758
#define IDS_CERT_X509_BASIC_CONSTRAINT_IS_CA 7759
#define IDS_CERT_X509_BASIC_CONSTRAINT_IS_NOT_CA 7760
#define IDS_CERT_X509_BASIC_CONSTRAINT_PATH_LEN 7761
#define IDS_CERT_X509_BASIC_CONSTRAINT_PATH_LEN_UNLIMITED 7762
#define IDS_CERT_PKIX_CPS_POINTER_QUALIFIER 7763
#define IDS_CERT_PKIX_USER_NOTICE_QUALIFIER 7764
#define IDS_CERT_REVOCATION_REASON_UNUSED 7765
#define IDS_CERT_REVOCATION_REASON_KEY_COMPROMISE 7766
#define IDS_CERT_REVOCATION_REASON_CA_COMPROMISE 7767
#define IDS_CERT_REVOCATION_REASON_AFFILIATION_CHANGED 7768
#define IDS_CERT_REVOCATION_REASON_SUPERSEDED 7769
#define IDS_CERT_REVOCATION_REASON_CESSATION_OF_OPERATION 7770
#define IDS_CERT_REVOCATION_REASON_CERTIFICATE_HOLD 7771
#define IDS_CERT_OCSP_RESPONDER_FORMAT 7772
#define IDS_CERT_CA_ISSUERS_FORMAT 7773
#define IDS_CERT_UNKNOWN_OID_INFO_FORMAT 7774
#define IDS_CERT_EXT_KEY_USAGE_FORMAT 7775
#define IDS_CERT_MULTILINE_INFO_START_FORMAT 7776
#define IDS_CERT_GENERAL_NAME_RFC822_NAME 7777
#define IDS_CERT_GENERAL_NAME_DNS_NAME 7778
#define IDS_CERT_GENERAL_NAME_X400_ADDRESS 7779
#define IDS_CERT_GENERAL_NAME_DIRECTORY_NAME 7780
#define IDS_CERT_GENERAL_NAME_EDI_PARTY_NAME 7781
#define IDS_CERT_GENERAL_NAME_URI 7782
#define IDS_CERT_GENERAL_NAME_IP_ADDRESS 7783
#define IDS_CERT_GENERAL_NAME_REGISTERED_ID 7784
#define IDS_CERT_EXT_MS_CERT_TYPE 7785
#define IDS_CERT_EXT_MS_CA_VERSION 7786
#define IDS_CERT_EXT_MS_NT_PRINCIPAL_NAME 7787
#define IDS_CERT_EXT_MS_NTDS_REPLICATION 7788
#define IDS_CERT_EKU_ANY_EKU 7789
#define IDS_CERT_EKU_TLS_WEB_SERVER_AUTHENTICATION 7790
#define IDS_CERT_EKU_TLS_WEB_CLIENT_AUTHENTICATION 7791
#define IDS_CERT_EKU_CODE_SIGNING 7792
#define IDS_CERT_EKU_EMAIL_PROTECTION 7793
#define IDS_CERT_EKU_TIME_STAMPING 7794
#define IDS_CERT_EKU_OCSP_SIGNING 7795
#define IDS_CERT_EKU_MS_INDIVIDUAL_CODE_SIGNING 7796
#define IDS_CERT_EKU_MS_COMMERCIAL_CODE_SIGNING 7797
#define IDS_CERT_EKU_MS_TRUST_LIST_SIGNING 7798
#define IDS_CERT_EKU_MS_TIME_STAMPING 7799
#define IDS_CERT_EKU_MS_SERVER_GATED_CRYPTO 7800
#define IDS_CERT_EKU_MS_ENCRYPTING_FILE_SYSTEM 7801
#define IDS_CERT_EKU_MS_FILE_RECOVERY 7802
#define IDS_CERT_EKU_MS_WINDOWS_HARDWARE_DRIVER_VERIFICATION 7803
#define IDS_CERT_EKU_MS_QUALIFIED_SUBORDINATION 7804
#define IDS_CERT_EKU_MS_KEY_RECOVERY 7805
#define IDS_CERT_EKU_MS_DOCUMENT_SIGNING 7806
#define IDS_CERT_EKU_MS_LIFETIME_SIGNING 7807
#define IDS_CERT_EKU_MS_SMART_CARD_LOGON 7808
#define IDS_CERT_EKU_MS_KEY_RECOVERY_AGENT 7809
#define IDS_CERT_EKU_NETSCAPE_INTERNATIONAL_STEP_UP 7810
#define IDS_CERT_EXTENSION_CRITICAL 7811
#define IDS_CERT_EXTENSION_NON_CRITICAL 7812
#define IDS_CERT_EXTENSION_DUMP_ERROR 7813
#define IDS_CERTIFICATE_MANAGER_TITLE 7814
#define IDS_CERT_MANAGER_HARDWARE_BACKED_KEY_FORMAT 7815
#define IDS_CERT_MANAGER_HARDWARE_BACKED 7816
#define IDS_CERT_MANAGER_EXTENSION_PROVIDED_FORMAT 7817
#define IDS_DEV_TOOLS_INFOBAR_LABEL 7818
#define IDS_DEV_TOOLS_CONFIRM_ADD_FILE_SYSTEM_MESSAGE 7819
#define IDS_DEV_TOOLS_CONFIRM_ALLOW_BUTTON 7820
#define IDS_DEV_TOOLS_CONFIRM_DENY_BUTTON 7821
#define IDS_RELOAD_MENU_NORMAL_RELOAD_ITEM 258
#define IDS_RELOAD_MENU_HARD_RELOAD_ITEM 259
#define IDS_RELOAD_MENU_EMPTY_AND_HARD_RELOAD_ITEM 260
#define IDS_TAB_SHARING_INFOBAR_SHARING_CURRENT_TAB_LABEL 7822
#define IDS_TAB_SHARING_INFOBAR_SHARING_ANOTHER_UNTITLED_TAB_LABEL 7823
#define IDS_TAB_SHARING_INFOBAR_SHARING_ANOTHER_TAB_LABEL 7824
#define IDS_TAB_SHARING_INFOBAR_SHARE_BUTTON 7825
#define IDS_TAB_SHARING_INFOBAR_STOP_BUTTON 7826
#define IDS_TAB_SHARING_INFOBAR_SWITCH_TO_BUTTON 7827
#define IDS_TAB_SHARING_INFOBAR_SWITCH_TO_CAPTURER_BUTTON 7828
#define IDS_TAB_SHARING_INFOBAR_SWITCH_TO_CAPTURED_BUTTON 7829
#define IDS_TASK_MANAGER_KILL 7830
#define IDS_TASK_MANAGER_PROCESS_ID_COLUMN 7831
#define IDS_TASK_MANAGER_GDI_HANDLES_COLUMN 7832
#define IDS_TASK_MANAGER_USER_HANDLES_COLUMN 7833
#define IDS_TASK_MANAGER_TASK_COLUMN 7834
#define IDS_TASK_MANAGER_NACL_DEBUG_STUB_PORT_COLUMN 7835
#define IDS_TASK_MANAGER_NET_COLUMN 7836
#define IDS_TASK_MANAGER_CPU_COLUMN 7837
#define IDS_TASK_MANAGER_START_TIME_COLUMN 7838
#define IDS_TASK_MANAGER_CPU_TIME_COLUMN 7839
#define IDS_TASK_MANAGER_MEM_FOOTPRINT_COLUMN 7840
#define IDS_TASK_MANAGER_SWAPPED_MEM_COLUMN 7841
#define IDS_TASK_MANAGER_PROFILE_NAME_COLUMN 7842
#define IDS_TASK_MANAGER_IDLE_WAKEUPS_COLUMN 7843
#define IDS_TASK_MANAGER_HARD_FAULTS_COLUMN 7844
#define IDS_TASK_MANAGER_OPEN_FD_COUNT_COLUMN 7845
#define IDS_TASK_MANAGER_PROCESS_PRIORITY_COLUMN 7846
#define IDS_TASK_MANAGER_WEBCORE_IMAGE_CACHE_COLUMN 7847
#define IDS_TASK_MANAGER_WEBCORE_SCRIPTS_CACHE_COLUMN 7848
#define IDS_TASK_MANAGER_WEBCORE_CSS_CACHE_COLUMN 7849
#define IDS_TASK_MANAGER_VIDEO_MEMORY_COLUMN 7850
#define IDS_TASK_MANAGER_SQLITE_MEMORY_USED_COLUMN 7851
#define IDS_TASK_MANAGER_JAVASCRIPT_MEMORY_ALLOCATED_COLUMN 7852
#define IDS_TASK_MANAGER_KEEPALIVE_COUNT_COLUMN 7853
#define IDS_TASK_MANAGER_MEM_CELL_TEXT 7854
#define IDS_TASK_MANAGER_CACHE_SIZE_CELL_TEXT 7855
#define IDS_TASK_MANAGER_NA_CELL_TEXT 7856
#define IDS_TASK_MANAGER_BACKGROUNDED_TEXT 7857
#define IDS_TASK_MANAGER_FOREGROUNDED_TEXT 7858
#define IDS_TASK_MANAGER_UNKNOWN_VALUE_TEXT 7859
#define IDS_TASK_MANAGER_DISABLED_NACL_DBG_TEXT 7860
#define IDS_TASK_MANAGER_HANDLES_CELL_TEXT 7861
#define IDS_TASK_MANAGER_WEB_BROWSER_CELL_TEXT 7862
#define IDS_TASK_MANAGER_EXTENSION_PREFIX 7863
#define IDS_TASK_MANAGER_EXTENSION_INCOGNITO_PREFIX 7864
#define IDS_TASK_MANAGER_APP_PREFIX 7865
#define IDS_TASK_MANAGER_APP_INCOGNITO_PREFIX 7866
#define IDS_TASK_MANAGER_TAB_PREFIX 7867
#define IDS_TASK_MANAGER_TAB_INCOGNITO_PREFIX 7868
#define IDS_TASK_MANAGER_BACKGROUND_APP_PREFIX 7869
#define IDS_TASK_MANAGER_BACKGROUND_PREFIX 7870
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_PREFIX 7871
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_INCOGNITO_PREFIX 7872
#define IDS_TASK_MANAGER_PLUGIN_PREFIX 7873
#define IDS_TASK_MANAGER_PLUGIN_BROKER_PREFIX 7874
#define IDS_TASK_MANAGER_PRERENDER_PREFIX 7875
#define IDS_TASK_MANAGER_SPARE_RENDERER_PREFIX 7876
#define IDS_TASK_MANAGER_UNKNOWN_RENDERER_PREFIX 7877
#define IDS_TASK_MANAGER_DEDICATED_WORKER_PREFIX 7878
#define IDS_TASK_MANAGER_SHARED_WORKER_PREFIX 7879
#define IDS_TASK_MANAGER_SERVICE_WORKER_PREFIX 7880
#define IDS_TASK_MANAGER_UNKNOWN_PLUGIN_NAME 7881
#define IDS_TASK_MANAGER_UTILITY_PREFIX 7882
#define IDS_TASK_MANAGER_NACL_PREFIX 7883
#define IDS_TASK_MANAGER_NACL_BROKER_PREFIX 7884
#define IDS_TASK_MANAGER_GPU_PREFIX 7885
#define IDS_TASK_MANAGER_PRINT_PREFIX 7886
#define IDS_TASK_MANAGER_SUBFRAME_PREFIX 7887
#define IDS_TASK_MANAGER_SUBFRAME_INCOGNITO_PREFIX 7888
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_SUBFRAME_PREFIX 7889
#define IDS_TASK_MANAGER_BACK_FORWARD_CACHE_INCOGNITO_SUBFRAME_PREFIX 7890
#define IDS_TASK_MANAGER_PORTAL_PREFIX 7891
#define IDS_TASK_MANAGER_PORTAL_INCOGNITO_PREFIX 7892
#define IDS_TASK_MANAGER_ARC_PREFIX 7893
#define IDS_TASK_MANAGER_TOOL_PREFIX 7894
#define IDS_TASK_MANAGER_ARC_PREFIX_BACKGROUND_SERVICE 7895
#define IDS_TASK_MANAGER_ARC_PREFIX_RECEIVER 7896
#define IDS_TASK_MANAGER_ARC_SYSTEM 7897
#define IDS_TASK_MANAGER_LINUX_VM_PREFIX 7898
#define IDS_TASK_MANAGER_PLUGIN_VM_PREFIX 7899
#define IDS_UTILITY_PROCESS_FILE_UTILITY_NAME 7901
#define IDS_UTILITY_PROCESS_PROFILE_IMPORTER_NAME 7902
#define IDS_UTILITY_PROCESS_QRCODE_GENERATOR_SERVICE_NAME 7903
#define IDS_UTILITY_PROCESS_WIFI_CREDENTIALS_GETTER_NAME 7904
#define IDS_UTILITY_PROCESS_IMAGE_WRITER_NAME 7905
#define IDS_UTILITY_PROCESS_MEDIA_GALLERY_UTILITY_NAME 7906
#define IDS_UTILITY_PROCESS_NOOP_SERVICE_NAME 7907
#define IDS_SERVICE_PROCESS_DOCUMENT_ANALYSIS_NAME 7908
#define IDS_THEME_INSTALL_INFOBAR_LABEL 7909
#define IDS_THEME_INSTALL_INFOBAR_UNDO_BUTTON 7910
#define IDS_CRITICAL_NOTIFICATION_RESTART 7911
#define IDS_EXTENSION_DISABLED_ERROR_LABEL 7912
#define IDS_EXTENSION_IS_BLOCKLISTED 7913
#define IDS_EXTENSION_DISABLED_REMOTE_INSTALL_ERROR_TITLE 7914
#define IDS_EXTENSION_DISABLED_ERROR_TITLE 7915
#define IDS_EXTENSION_BLOCKED_ACTION_BUBBLE_HEADING 7916
#define IDS_EXTENSION_BLOCKED_ACTION_BUBBLE_OK_BUTTON 7917
#define IDS_APP_UNINSTALL_PROMPT_TITLE 7918
#define IDS_ARC_APP_UNINSTALL_PROMPT_DATA_REMOVAL_WARNING 7919
#define IDS_EXTENSION_UNINSTALL_PROMPT_TITLE 7920
#define IDS_EXTENSION_CONFIRM_PERMISSIONS 7921
#define IDS_EXTENSION_DELEGATED_INSTALL_PROMPT_TITLE 7922
#define IDS_EXTENSION_INSTALL_PROMPT_TITLE 7923
#define IDS_EXTENSION_UNINSTALL_PROMPT_HEADING 7924
#define IDS_EXTENSION_REQUEST_PROMPT_TITLE 7925
#define IDS_EXTENSION_PENDING_REQUEST_PROMPT_TITLE 7926
#define IDS_EXTENSION_BLOCKED_BY_POLICY_PROMPT_TITLE 7927
#define IDS_EXTENSION_PROGRAMMATIC_UNINSTALL_PROMPT_HEADING 7928
#define IDS_EXTENSION_RE_ENABLE_PROMPT_TITLE 7929
#define IDS_EXTENSION_PERMISSIONS_PROMPT_TITLE 7930
#define IDS_EXTENSION_POST_INSTALL_PERMISSIONS_PROMPT_TITLE 7931
#define IDS_EXTENSION_REMOTE_INSTALL_PROMPT_TITLE 7932
#define IDS_EXTENSION_REPAIR_PROMPT_TITLE 7933
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_TITLE_APP 7934
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_TITLE_EXTENSION 7935
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_TITLE_THEME 7936
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ACCEPT_BUTTON_EXTENSION 7937
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ACCEPT_BUTTON_APP 7938
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ACCEPT_BUTTON_THEME 7939
#define IDS_EXTENSION_EXTERNAL_INSTALL_PROMPT_ABORT_BUTTON 7940
#define IDS_EXTENSION_ALERT_TITLE 7941
#define IDS_APP_ALERT_TITLE 7942
#define IDS_EXTENSION_AND_APP_ALERT_TITLE 7943
#define IDS_POLICY_BLOCKED_EXTENSION_ALERT_TITLE 7944
#define IDS_POLICY_BLOCKED_EXTENSIONS_ALERT_ITEM_TITLE 7945
#define IDS_BLOCKLISTED_EXTENSIONS_ALERT_ITEM 7946
#define IDS_POLICY_BLOCKED_EXTENSION_ALERT_ITEM_DETAIL 7947
#define IDS_EXTENSION_ALERT_ITEM_OK 7948
#define IDS_EXTENSION_ALERT_ITEM_DETAILS 7949
#define IDS_EXTENSION_PROMPT_APP_CONNECT_FROM_INCOGNITO 7950
#define IDS_EXTENSION_PROMPT_EXTENSION_CONNECT_FROM_INCOGNITO 7951
#define IDS_EXTENSION_PROMPT_WILL_HAVE_ACCESS_TO 7952
#define IDS_EXTENSION_PROMPT_WILL_NOW_HAVE_ACCESS_TO 7953
#define IDS_EXTENSION_PROMPT_WANTS_ACCESS_TO 7954
#define IDS_EXTENSION_PROMPT_CAN_ACCESS 7955
#define IDS_EXTENSION_NO_SPECIAL_PERMISSIONS 7956
#define IDS_EXTENSION_WITHHOLD_PERMISSIONS 7957
#define IDS_EXTENSION_PROMPT_MESSAGE_FROM_ADMIN 7958
#define IDS_EXTENSION_PERMISSION_LINE 7959
#define IDS_EXTENSION_RATING_COUNT 7960
#define IDS_EXTENSION_PROMPT_RATING_ACCESSIBLE_TEXT 7961
#define IDS_EXTENSION_PROMPT_NO_RATINGS_ACCESSIBLE_TEXT 7962
#define IDS_EXTENSION_USER_COUNT 7963
#define IDS_EXTENSION_PROMPT_STORE_LINK 7964
#define IDS_EXTENSION_PROMPT_RETAINED_FILES 7965
#define IDS_EXTENSION_PROMPT_RETAINED_DEVICES 7966
#define IDS_EXTENSION_PROMPT_WARNING_FULL_ACCESS 7967
#define IDS_EXTENSION_PROMPT_WARNING_ALL_HOSTS 7968
#define IDS_EXTENSION_PROMPT_WARNING_CURRENT_HOST 7969
#define IDS_EXTENSION_PROMPT_WARNING_ALL_HOSTS_READ_ONLY 7970
#define IDS_EXTENSION_PROMPT_WARNING_AUDIO_CAPTURE 7971
#define IDS_EXTENSION_PROMPT_WARNING_VIDEO_CAPTURE 7972
#define IDS_EXTENSION_PROMPT_WARNING_AUDIO_AND_VIDEO_CAPTURE 7973
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH 7974
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH_DEVICES 7975
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH_PRIVATE 7976
#define IDS_EXTENSION_PROMPT_WARNING_BLUETOOTH_SERIAL 7977
#define IDS_EXTENSION_PROMPT_WARNING_BOOKMARKS 7978
#define IDS_EXTENSION_PROMPT_WARNING_CLIPBOARD 7979
#define IDS_EXTENSION_PROMPT_WARNING_CLIPBOARD_READWRITE 7980
#define IDS_EXTENSION_PROMPT_WARNING_CLIPBOARD_WRITE 7981
#define IDS_EXTENSION_PROMPT_WARNING_DEBUGGER 7982
#define IDS_EXTENSION_PROMPT_WARNING_DECLARATIVE_WEB_REQUEST 7983
#define IDS_EXTENSION_PROMPT_WARNING_DECLARATIVE_NET_REQUEST 7984
#define IDS_EXTENSION_PROMPT_WARNING_DOCUMENT_SCAN 7985
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_HARDWARE_PLATFORM 7986
#define IDS_EXTENSION_PROMPT_WARNING_FAVICON 7987
#define IDS_EXTENSION_PROMPT_WARNING_GEOLOCATION 7988
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_READ 7989
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_READ_AND_SESSIONS 7990
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_WRITE 7991
#define IDS_EXTENSION_PROMPT_WARNING_HISTORY_WRITE_AND_SESSIONS 7992
#define IDS_EXTENSION_PROMPT_WARNING_HOME_PAGE_SETTING_OVERRIDE 7993
#define IDS_EXTENSION_PROMPT_WARNING_1_HOST 7994
#define IDS_EXTENSION_PROMPT_WARNING_1_HOST_READ_ONLY 7995
#define IDS_EXTENSION_PROMPT_WARNING_2_HOSTS 7996
#define IDS_EXTENSION_PROMPT_WARNING_2_HOSTS_READ_ONLY 7997
#define IDS_EXTENSION_PROMPT_WARNING_3_HOSTS 7998
#define IDS_EXTENSION_PROMPT_WARNING_3_HOSTS_READ_ONLY 7999
#define IDS_EXTENSION_PROMPT_WARNING_HOSTS_LIST 8000
#define IDS_EXTENSION_PROMPT_WARNING_HOSTS_LIST_READ_ONLY 8001
#define IDS_EXTENSION_PROMPT_WARNING_HOST_AND_SUBDOMAIN 8002
#define IDS_EXTENSION_PROMPT_WARNING_HOST_AND_SUBDOMAIN_LIST 8003
#define IDS_EXTENSION_PROMPT_WARNING_INPUT 8004
#define IDS_EXTENSION_PROMPT_WARNING_LOGIN 8005
#define IDS_EXTENSION_PROMPT_WARNING_LOGIN_SCREEN_UI 8006
#define IDS_EXTENSION_PROMPT_WARNING_LOGIN_SCREEN_STORAGE 8007
#define IDS_EXTENSION_PROMPT_WARNING_MANAGEMENT 8008
#define IDS_EXTENSION_PROMPT_WARNING_MDNS 8009
#define IDS_EXTENSION_PROMPT_WARNING_NETWORK_STATE 8010
#define IDS_EXTENSION_PROMPT_WARNING_NETWORKING_PRIVATE 8011
#define IDS_EXTENSION_PROMPT_WARNING_PRINTING 8012
#define IDS_EXTENSION_PROMPT_WARNING_PRINTING_METRICS 8013
#define IDS_EXTENSION_PROMPT_WARNING_SEARCH_SETTINGS_OVERRIDE 8014
#define IDS_EXTENSION_PROMPT_WARNING_SERIAL 8015
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_ANY_HOST 8016
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_HOSTS_IN_DOMAIN 8017
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_HOSTS_IN_DOMAINS 8018
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_SPECIFIC_HOST 8019
#define IDS_EXTENSION_PROMPT_WARNING_SOCKET_SPECIFIC_HOSTS 8020
#define IDS_EXTENSION_PROMPT_WARNING_SPEECH_RECOGNITION 8021
#define IDS_EXTENSION_PROMPT_WARNING_START_PAGE_SETTING_OVERRIDE 8022
#define IDS_EXTENSION_PROMPT_WARNING_SYSTEM_STORAGE 8023
#define IDS_EXTENSION_PROMPT_WARNING_TAB_GROUPS 8024
#define IDS_EXTENSION_PROMPT_WARNING_TOPSITES 8025
#define IDS_EXTENSION_PROMPT_WARNING_TTS_ENGINE 8026
#define IDS_EXTENSION_PROMPT_WARNING_U2F_DEVICES 8027
#define IDS_EXTENSION_PROMPT_WARNING_NOTIFICATIONS 8028
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE 8029
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_LIST 8030
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_LIST_ITEM_UNKNOWN_PRODUCT 8031
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_LIST_ITEM_UNKNOWN_VENDOR 8032
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_UNKNOWN_PRODUCT 8033
#define IDS_EXTENSION_PROMPT_WARNING_USB_DEVICE_UNKNOWN_VENDOR 8034
#define IDS_EXTENSION_PROMPT_WARNING_VPN 8035
#define IDS_EXTENSION_PROMPT_WARNING_CONTENT_SETTINGS 8036
#define IDS_EXTENSION_PROMPT_WARNING_PRIVACY 8037
#define IDS_EXTENSION_PROMPT_WARNING_DOWNLOADS 8038
#define IDS_EXTENSION_PROMPT_WARNING_DOWNLOADS_OPEN 8039
#define IDS_EXTENSION_PROMPT_WARNING_IDENTITY_EMAIL 8040
#define IDS_EXTENSION_PROMPT_WARNING_WALLPAPER 8041
#define IDS_EXTENSION_PROMPT_WARNING_FILE_SYSTEM_DIRECTORY 8042
#define IDS_EXTENSION_PROMPT_WARNING_FILE_SYSTEM_WRITE_DIRECTORY 8043
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ 8044
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ_WRITE 8045
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ_DELETE 8046
#define IDS_EXTENSION_PROMPT_WARNING_MEDIA_GALLERIES_READ_WRITE_DELETE 8047
#define IDS_EXTENSION_PROMPT_WARNING_SYNCFILESYSTEM 8048
#define IDS_EXTENSION_PROMPT_WARNING_MUSIC_MANAGER_PRIVATE 8049
#define IDS_EXTENSION_PROMPT_WARNING_NATIVE_MESSAGING 8050
#define IDS_EXTENSION_PROMPT_WARNING_SCREENLOCK_PRIVATE 8051
#define IDS_EXTENSION_PROMPT_WARNING_ACTIVITY_LOG_PRIVATE 8052
#define IDS_EXTENSION_PROMPT_WARNING_DESKTOP_CAPTURE 8053
#define IDS_EXTENSION_PROMPT_WARNING_ACCESSIBILITY_FEATURES_MODIFY 8054
#define IDS_EXTENSION_PROMPT_WARNING_ACCESSIBILITY_FEATURES_READ 8055
#define IDS_EXTENSION_PROMPT_WARNING_ACCESSIBILITY_FEATURES_READ_MODIFY 8056
#define IDS_EXTENSION_PROMPT_WARNING_PLATFORMKEYS 8057
#define IDS_EXTENSION_PROMPT_WARNING_CERTIFICATEPROVIDER 8058
#define IDS_EXTENSION_PROMPT_WARNING_SETTINGS_PRIVATE 8059
#define IDS_EXTENSION_PROMPT_WARNING_AUTOFILL_PRIVATE 8060
#define IDS_EXTENSION_PROMPT_WARNING_PASSWORDS_PRIVATE 8061
#define IDS_EXTENSION_PROMPT_WARNING_USERS_PRIVATE 8062
#define IDS_EXTENSION_PROMPT_WARNING_NEW_TAB_PAGE_OVERRIDE 8063
#define IDS_EXTENSION_PROMPT_WARNING_TRANSIENT_BACKGROUND 8064
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_DEVICE_ATTRIBUTES 8065
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_NETWORKING_ATTRIBUTES 8066
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_PLATFORMKEYS 8067
#define IDS_EXTENSION_PROMPT_WARNING_ENTERPRISE_REPORTING_PRIVATE 8068
#define IDS_EXTENSION_PROMPT_WARNING_CHROMEOS_DIAGNOSTICS 8069
#define IDS_EXTENSION_PROMPT_WARNING_CHROMEOS_TELEMETRY 8070
#define IDS_EXTENSION_PROMPT_WARNING_CHROMEOS_TELEMETRY_SERIAL_NUMBER 8071
#define IDS_EXTENSION_CANT_DOWNGRADE_VERSION 8072
#define IDS_APP_CANT_DOWNGRADE_VERSION 8073
#define IDS_EXTENSION_MOVE_DIRECTORY_TO_PROFILE_FAILED 8076
#define IDS_EXTENSION_INSTALL_NOT_ENABLED 8077
#define IDS_EXTENSION_INSTALL_INCORRECT_APP_CONTENT_TYPE 8078
#define IDS_EXTENSION_INSTALL_INCORRECT_INSTALL_HOST 8079
#define IDS_EXTENSION_INSTALL_UNEXPECTED_ID 8080
#define IDS_EXTENSION_INSTALL_DISALLOWED_ON_SITE 8081
#define IDS_EXTENSION_INSTALL_UNEXPECTED_VERSION 8082
#define IDS_EXTENSION_INSTALL_DEPENDENCY_OLD_VERSION 8083
#define IDS_EXTENSION_INSTALL_DEPENDENCY_NOT_SHARED_MODULE 8084
#define IDS_EXTENSION_INSTALL_DEPENDENCY_NOT_ALLOWLISTED 8085
#define IDS_EXTENSION_INSTALL_GALLERY_ONLY 8086
#define IDS_EXTENSION_INSTALL_KIOSK_MODE_ONLY 8087
#define IDS_EXTENSION_OVERLAPPING_WEB_EXTENT 8088
#define IDS_EXTENSION_INVALID_IMAGE_PATH 8089
#define IDS_EXTENSION_INSTALLED_PAGE_ACTION_INFO 8092
#define IDS_EXTENSION_INSTALLED_PAGE_ACTION_INFO_WITH_SHORTCUT 8093
#define IDS_EXTENSION_INSTALLED_BROWSER_ACTION_INFO 8094
#define IDS_EXTENSION_INSTALLED_BROWSER_ACTION_INFO_WITH_SHORTCUT 8095
#define IDS_EXTENSION_INSTALLED_OMNIBOX_KEYWORD_INFO 8096
#define IDS_EXTENSION_INSTALLED_MANAGE_INFO 8097
#define IDS_EXTENSION_INSTALLED_MANAGE_SHORTCUTS 8098
#define IDS_EXTENSION_INSTALLED_DICE_PROMO_SYNC_MESSAGE 8099
#define IDS_EXTENSIONS_DIRECTORY_CONFIRMATION_DIALOG_TITLE 8100
#define IDS_EXTENSIONS_DIRECTORY_CONFIRMATION_DIALOG_MESSAGE_READ_ONLY 8101
#define IDS_EXTENSIONS_DIRECTORY_CONFIRMATION_DIALOG_MESSAGE_WRITABLE 8102
#define IDS_DIRECT_SOCKETS_CONNECTION_BUBBLE_TITLE_LABEL 8103
#define IDS_DIRECT_SOCKETS_CONNECTION_BUBBLE_ADDRESS_LABEL 8104
#define IDS_DIRECT_SOCKETS_CONNECTION_BUBBLE_PORT_LABEL 8105
#define IDS_EXTENSIONS_LOAD_ERROR_ALERT_HEADING 8106
#define IDS_EXTENSIONS_LOAD_ERROR_MESSAGE 165
#define IDS_EXTENSIONS_WANTS_ACCESS_TO_SITE 296
#define IDS_EXTENSIONS_HAS_ACCESS_TO_SITE 293
#define IDS_EXTENSIONS_REQUEST_ACCESS_BUTTON 8107
#define IDS_EXTENSIONS_REQUEST_ACCESS_BUTTON_TOOLTIP_SINGLE_EXTENSION 8108
#define IDS_EXTENSIONS_REQUEST_ACCESS_BUTTON_TOOLTIP_MULTIPLE_EXTENSIONS 8109
#define IDS_EXTENSIONS_CONTEXT_MENU_CANT_ACCESS_PAGE 8110
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS 8111
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_ALL_EXTENSIONS_GRANTED 8112
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_ALL_EXTENSIONS_BLOCKED 8113
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_RUN_ON_CLICK 8114
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_RUN_ON_SITE 8115
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_RUN_ON_ALL_SITES 8116
#define IDS_EXTENSIONS_CONTEXT_MENU_PAGE_ACCESS_LEARN_MORE 8117
#define IDS_EXTENSIONS_OPTIONS_MENU_ITEM 8118
#define IDS_EXTENSIONS_INSTALLED_BY_ADMIN 8119
#define IDS_EXTENSIONS_DISABLE 8120
#define IDS_EXTENSIONS_PIN_TO_TOOLBAR 8121
#define IDS_EXTENSIONS_UNPIN_FROM_TOOLBAR 8122
#define IDS_EXTENSIONS_PINNED_BY_ADMIN 8123
#define IDS_MANAGE_EXTENSION 8124
#define IDS_MANAGE_EXTENSIONS 8125
#define IDS_EXTENSION_ACTION_INSPECT_POPUP 8126
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX_RUN_ON_CLICK 8127
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX_RUN_ON_SITE 8128
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX_RUN_ON_ALL_SITES 8129
#define IDS_EXTENSIONS_LOCKED_SUPERVISED_USER 8133
#define IDS_EXTENSION_LOAD_FROM_DIRECTORY 8134
#define IDS_EXTENSION_COMMANDS_GENERIC_ACTIVATE 8135
#define IDS_EXTENSION_PACK_DIALOG_HEADING 8136
#define IDS_EXTENSION_PACK_DIALOG_SELECT_KEY 8137
#define IDS_EXTENSION_PACK_DIALOG_KEY_FILE_TYPE_DESCRIPTION 8138
#define IDS_EXTENSION_PACK_DIALOG_ERROR_ROOT_REQUIRED 8139
#define IDS_EXTENSION_PACK_DIALOG_ERROR_ROOT_INVALID 8140
#define IDS_EXTENSION_PACK_DIALOG_ERROR_KEY_INVALID 8141
#define IDS_EXTENSION_PACK_DIALOG_SUCCESS_BODY_NEW 8142
#define IDS_EXTENSION_PACK_DIALOG_SUCCESS_BODY_UPDATE 8143
#define IDS_EXTENSION_PROMPT_INSTALL_FRICTION_CONTINUE_BUTTON 8144
#define IDS_EXTENSION_PROMPT_INSTALL_FRICTION_TITLE 8145
#define IDS_EXTENSION_PROMPT_INSTALL_FRICTION_WARNING_TEXT 8146
#define IDS_EXTENSION_PROMPT_INSTALL_BUTTON 8147
#define IDS_EXTENSION_INSTALL_PROMPT_ACCEPT_BUTTON_EXTENSION 8148
#define IDS_EXTENSION_INSTALL_PROMPT_ACCEPT_BUTTON_APP 8149
#define IDS_EXTENSION_INSTALL_PROMPT_ACCEPT_BUTTON_THEME 8150
#define IDS_EXTENSION_INSTALL_PROMPT_ASK_A_PARENT_BUTTON 8151
#define IDS_EXTENSION_INSTALL_BLOCKED_BY_PARENT_PROMPT_TITLE 8152
#define IDS_EXTENSION_ENABLE_BLOCKED_BY_PARENT_PROMPT_TITLE 8153
#define IDS_PARENT_PERMISSION_PROMPT_GO_GET_A_PARENT_FOR_EXTENSION_LABEL 8154
#define IDS_PARENT_PERMISSION_PROMPT_EXTENSION_TYPE_EXTENSION 8155
#define IDS_PARENT_PERMISSION_PROMPT_EXTENSION_TYPE_APP 8156
#define IDS_PARENT_PERMISSION_PROMPT_CHILD_WANTS_TO_INSTALL_LABEL 8157
#define IDS_PARENT_PERMISSION_PROMPT_APPROVE_BUTTON 8158
#define IDS_PARENT_PERMISSION_PROMPT_CANCEL_BUTTON 8159
#define IDS_PARENT_PERMISSION_PROMPT_SELECT_PARENT_LABEL 8160
#define IDS_PARENT_PERMISSION_PROMPT_PARENT_ACCOUNT_LABEL 8161
#define IDS_PARENT_PERMISSION_PROMPT_ENTER_PASSWORD_LABEL 8162
#define IDS_PARENT_PERMISSION_PROMPT_PASSWORD_INCORRECT_LABEL 8163
#define IDS_EXTENSION_INSTALL_PROMPT_REQUEST_BUTTON 8164
#define IDS_EXTENSION_PROMPT_UNINSTALL_BUTTON 8165
#define IDS_EXTENSION_PROMPT_UNINSTALL_REPORT_ABUSE 8166
#define IDS_EXTENSION_PROMPT_UNINSTALL_REPORT_ABUSE_FROM_EXTENSION 8167
#define IDS_EXTENSION_PROMPT_UNINSTALL_TITLE 8168
#define IDS_EXTENSION_PROMPT_UNINSTALL_APP_BUTTON 8169
#define IDS_EXTENSION_PROMPT_UNINSTALL_TRIGGERED_BY_EXTENSION 8170
#define IDS_EXTENSION_PROMPT_RE_ENABLE_BUTTON 8171
#define IDS_EXTENSION_PROMPT_PERMISSIONS_BUTTON 8172
#define IDS_EXTENSION_PROMPT_PERMISSIONS_ACCEPT_BUTTON 8173
#define IDS_EXTENSION_PROMPT_PERMISSIONS_ABORT_BUTTON 8174
#define IDS_EXTENSION_PROMPT_PERMISSIONS_CLEAR_RETAINED_FILES_BUTTON 8175
#define IDS_EXTENSION_PROMPT_PERMISSIONS_CLEAR_RETAINED_DEVICES_BUTTON 8176
#define IDS_EXTENSION_PROMPT_PERMISSIONS_CLEAR_RETAINED_FILES_AND_DEVICES_BUTTON 8177
#define IDS_EXTENSION_PROMPT_REMOTE_INSTALL_BUTTON_EXTENSION 8178
#define IDS_EXTENSION_PROMPT_REMOTE_INSTALL_BUTTON_APP 8179
#define IDS_EXTENSION_PROMPT_REPAIR_BUTTON_EXTENSION 8180
#define IDS_EXTENSION_PROMPT_REPAIR_BUTTON_APP 8181
#define IDS_EXTENSION_WEB_STORE_TITLE 8182
#define IDS_EXTENSION_WEB_STORE_TITLE_SHORT 172
#define IDS_EXTENSIONS_SHOW_DETAILS 8183
#define IDS_EXTENSIONS_HIDE_DETAILS 8184
#define IDS_WEBSTORE_DOWNLOAD_ACCESS_DENIED 8185
#define IDS_EXTENSION_WARNINGS_WRENCH_MENU_ITEM 8186
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_EXTENSION 8187
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_APP 8188
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_THEME 8189
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_TITLE 8190
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_HEADING_APP 8191
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_HEADING_EXTENSION 8192
#define IDS_EXTENSION_EXTERNAL_INSTALL_ALERT_BUBBLE_HEADING_THEME 8193
#define IDS_EXTENSIONS_UNSUPPORTED_DISABLED_TITLE 8194
#define IDS_EXTENSIONS_DISABLED_AND_N_MORE 8195
#define IDS_EXTENSIONS_UNSUPPORTED_DISABLED_BUTTON 8196
#define IDS_EXTENSIONS_ADDED_WITHOUT_KNOWLEDGE 8197
#define IDS_EXTENSIONS_DISABLE_DEVELOPER_MODE_TITLE 8198
#define IDS_EXTENSIONS_DISABLE_DEVELOPER_MODE_BODY 8199
#define IDS_EXTENSION_SETTINGS_OVERRIDDEN_DIALOG_CHANGE_IT_BACK 8200
#define IDS_EXTENSION_SETTINGS_OVERRIDDEN_DIALOG_KEEP_IT 8201
#define IDS_EXTENSION_NTP_OVERRIDDEN_DIALOG_TITLE_GENERIC 8202
#define IDS_EXTENSION_NTP_OVERRIDDEN_DIALOG_TITLE_BACK_TO_GOOGLE 8203
#define IDS_EXTENSION_NTP_OVERRIDDEN_DIALOG_BODY_GENERIC 8204
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_TITLE_GENERIC 8205
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_TITLE_BACK_TO_GOOGLE 8206
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_TITLE_BACK_TO_OTHER 8207
#define IDS_EXTENSION_SEARCH_OVERRIDDEN_DIALOG_BODY_GENERIC 8208
#define IDS_FORCE_INSTALLED_DEPRECATED_APPS_CONTENT 8209
#define IDS_FORCE_INSTALLED_PREINSTALLED_DEPRECATED_APPS_TITLE 8210
#define IDS_FORCE_INSTALLED_PREINSTALLED_DEPRECATED_APPS_CONTENT 8211
#define IDS_FORCE_INSTALLED_DEPRECATED_APPS_LEARN_MORE_AX_LABEL 8212
#define IDS_DEPRECATED_APPS_RENDERER_TITLE 8213
#define IDS_DEPRECATED_APPS_MONITOR_RENDERER 8214
#define IDS_DEPRECATED_APPS_LEARN_MORE 8215
#define IDS_DEPRECATED_APPS_DELETION_LINK 8216
#define IDS_DEPRECATED_APPS_OK_LABEL 8217
#define IDS_DEPRECATED_APPS_CANCEL_LABEL 8218
#define IDS_EXTENSIONS_MENU_TITLE 8219
#define IDS_EXTENSIONS_MENU_CONTEXT_MENU_TOOLTIP 8220
#define IDS_EXTENSIONS_MENU_PIN_BUTTON_TOOLTIP 8221
#define IDS_EXTENSIONS_MENU_UNPIN_BUTTON_TOOLTIP 8222
#define IDS_EXTENSIONS_MENU_ACCESSING_SITE_DATA_SHORT 8223
#define IDS_EXTENSIONS_MENU_ACCESSING_SITE_DATA 8224
#define IDS_EXTENSIONS_MENU_WANTS_TO_ACCESS_SITE_DATA_SHORT 8225
#define IDS_EXTENSIONS_MENU_WANTS_TO_ACCESS_SITE_DATA 8226
#define IDS_EXTENSIONS_MENU_CANT_ACCESS_SITE_DATA_SHORT 8227
#define IDS_EXTENSIONS_MENU_CANT_ACCESS_SITE_DATA 8228
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_TITLE 8229
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_HAS_ACCESS_SECTION_TITLE 8230
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_REQUESTS_ACCESS_SECTION_TITLE 8231
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_BLOCK_ALL_EXTENSIONS_TEXT 8232
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_NO_EXTENSIONS_HAVE_ACCESS_TEXT 8233
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_TITLE 8234
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_ALLOW_ALL_TEXT 8235
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_BLOCK_ALL_TEXT 8236
#define IDS_EXTENSIONS_MENU_SITE_ACCESS_TAB_USER_SETTINGS_CUSTOMIZE_EACH_TEXT 8237
#define IDS_EXTENSIONS_MENU_EXTENSIONS_TAB_TITLE 8238
#define IDS_EXTENSIONS_MENU_EXTENSIONS_TAB_DISCOVER_MORE_TITLE 8239
#define IDS_EXTENSIONS_SETTINGS_API_TITLE_HOME_PAGE_BUBBLE 8240
#define IDS_EXTENSIONS_SETTINGS_API_TITLE_STARTUP_PAGES_BUBBLE 8241
#define IDS_EXTENSIONS_SETTINGS_API_TITLE_SEARCH_ENGINE_BUBBLE 8242
#define IDS_EXTENSIONS_NTP_CONTROLLED_TITLE_HOME_PAGE_BUBBLE 8243
#define IDS_EXTENSIONS_PROXY_CONTROLLED_TITLE_HOME_PAGE_BUBBLE 8244
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_SEARCH_ENGINE_SPECIFIC 8245
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_SEARCH_ENGINE 8246
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_HOME_PAGE_SPECIFIC 8247
#define IDS_EXTENSIONS_SETTINGS_API_FIRST_LINE_HOME_PAGE 8248
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_SEARCH_ENGINE 8249
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_HOME_PAGE 8250
#define IDS_EXTENSIONS_SETTINGS_API_SECOND_LINE_HOME_AND_SEARCH 8251
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_BUBBLE_TITLE 8252
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_BUBBLE_HEADING 8253
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_ALLOW 8254
#define IDS_EXTENSIONS_PRINTING_API_PRINT_REQUEST_DENY 8255
#define IDS_EXTENSIONS_NTP_CONTROLLED_FIRST_LINE 8256
#define IDS_EXTENSIONS_PROXY_CONTROLLED_FIRST_LINE 8257
#define IDS_EXTENSIONS_PROXY_CONTROLLED_FIRST_LINE_EXTENSION_SPECIFIC 8258
#define IDS_EXTENSIONS_SETTINGS_API_THIRD_LINE_CONFIRMATION 8259
#define IDS_EXTENSION_CONTROLLED_RESTORE_SETTINGS 8260
#define IDS_EXTENSION_CONTROLLED_KEEP_CHANGES 8261
#define IDS_WEB_APP_MENU_BUTTON_TOOLTIP 8262
#define IDS_WEB_APP_ENABLE_WINDOW_CONTROLS_OVERLAY_TOOLTIP 8263
#define IDS_WEB_APP_DISABLE_WINDOW_CONTROLS_OVERLAY_TOOLTIP 8264
#define IDS_WEB_APP_WINDOW_CONTROLS_OVERLAY_ENABLED_ALERT 8265
#define IDS_WEB_APP_WINDOW_CONTROLS_OVERLAY_DISABLED_ALERT 8266
#define IDS_WEB_APP_SETTINGS_TITLE 8267
#define IDS_WEB_APP_SETTINGS_LINK 8268
#define IDS_WEB_APP_SETTINGS_LINK_TOOLTIP 8269
#define IDS_COMPONENTS_TITLE 8270
#define IDS_COMPONENTS_VERSION 8271
#define IDS_COMPONENTS_NONE_INSTALLED 8272
#define IDS_COMPONENTS_NO_COMPONENTS 8273
#define IDS_COMPONENTS_CHECK_FOR_UPDATE 8274
#define IDS_COMPONENTS_STATUS_LABEL 8275
#define IDS_COMPONENTS_CHECKING_LABEL 8276
#define IDS_COMPONENTS_SVC_STATUS_NEW 8280
#define IDS_COMPONENTS_SVC_STATUS_CHECKING 8281
#define IDS_COMPONENTS_SVC_STATUS_UPDATE 8282
#define IDS_COMPONENTS_SVC_STATUS_DNL_DIFF 8283
#define IDS_COMPONENTS_SVC_STATUS_DNL 8284
#define IDS_COMPONENTS_SVC_STATUS_DOWNLOADED 8285
#define IDS_COMPONENTS_SVC_STATUS_UPDT_DIFF 8286
#define IDS_COMPONENTS_SVC_STATUS_UPDATING 8287
#define IDS_COMPONENTS_SVC_STATUS_UPDATED 8288
#define IDS_COMPONENTS_SVC_STATUS_UPTODATE 8289
#define IDS_COMPONENTS_SVC_STATUS_UPDATE_ERROR 8290
#define IDS_COMPONENTS_UNKNOWN 8291
#define IDS_COMPONENTS_EVT_STATUS_STARTED 8292
#define IDS_COMPONENTS_EVT_STATUS_SLEEPING 8293
#define IDS_COMPONENTS_EVT_STATUS_FOUND 8294
#define IDS_COMPONENTS_EVT_STATUS_READY 8295
#define IDS_COMPONENTS_EVT_STATUS_UPDATED 8296
#define IDS_COMPONENTS_EVT_STATUS_NOTUPDATED 8297
#define IDS_COMPONENTS_EVT_STATUS_UPDATE_ERROR 8298
#define IDS_COMPONENTS_EVT_STATUS_DOWNLOADING 8299
#define IDS_COMPONENTS_EVT_STATUS_UPDATING 8300
#define IDS_PASSWORD_MANAGER_ACCOUNT_CHOOSER_TITLE 8301
#define IDS_PASSWORD_MANAGER_CONFIRM_SAVED_TITLE 8302
#define IDS_PASSWORD_GENERATION_SUGGESTION 8303
#define IDS_PASSWORD_GENERATION_SUGGESTION_GPM 8304
#define IDS_PASSWORD_GENERATION_EDITING_SUGGESTION 8305
#define IDS_SAVE_PASSWORD 8306
#define IDS_SAVE_ACCOUNT 8307
#define IDS_UPDATE_PASSWORD 8308
#define IDS_SAVE_PASSWORD_DIFFERENT_DOMAINS_TITLE 8309
#define IDS_UPDATE_PASSWORD_DIFFERENT_DOMAINS_TITLE 8310
#define IDS_SAVE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION_GOOGLE_ACCOUNT 8311
#define IDS_UPDATE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION_GOOGLE_ACCOUNT 8312
#define IDS_SAVE_PASSWORD_FOOTER 8313
#define IDS_PASSWORD_BUBBLES_PASSWORD_MANAGER_LINK_TEXT_SYNCED_TO_ACCOUNT 8314
#define IDS_PASSWORD_BUBBLES_FOOTER_SYNCED_TO_ACCOUNT 8315
#define IDS_PASSWORD_BUBBLES_FOOTER_SAVING_ON_DEVICE 8316
#define IDS_PASSWORD_GENERATION_CONFIRMATION_GOOGLE_PASSWORD_MANAGER 8317
#define IDS_PASSWORD_GENERATION_PROMPT_GOOGLE_PASSWORD_MANAGER 8318
#define IDS_PASSWORD_MANAGEMENT_BUBBLE_FOOTER_ACCOUNT_STORE_USERS 8319
#define IDS_PASSWORD_MANAGER_GENERATED_PASSWORD_SAVED_MESSAGE_DESCRIPTION 8320
#define IDS_PASSWORD_MANAGER_SAVE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION 8321
#define IDS_PASSWORD_MANAGER_UPDATE_PASSWORD_SIGNED_IN_MESSAGE_DESCRIPTION 8322
#define IDS_PASSWORD_MANAGER_ACCOUNT_CHOOSER_SIGN_IN 8323
#define IDS_PASSWORD_MANAGER_MANAGE_PASSWORDS_BUTTON 8324
#define IDS_PASSWORD_MANAGER_ONBOARDING_TITLE_A 8325
#define IDS_PASSWORD_MANAGER_ONBOARDING_TITLE_B 8326
#define IDS_PASSWORD_MANAGER_ONBOARDING_TITLE_C 8327
#define IDS_PASSWORD_MANAGER_ONBOARDING_DETAILS_A 8328
#define IDS_PASSWORD_MANAGER_ONBOARDING_DETAILS_B 8329
#define IDS_PASSWORD_MANAGER_DICE_PROMO_SIGNIN_MESSAGE 8330
#define IDS_PASSWORD_MANAGER_DICE_PROMO_SYNC_MESSAGE 8331
#define IDS_WEBRTC_LOGS_TITLE 8332
#define IDS_WEBRTC_TEXT_LOGS_LOG_COUNT_BANNER_FORMAT 8333
#define IDS_WEBRTC_EVENT_LOGS_LOG_COUNT_BANNER_FORMAT 8334
#define IDS_WEBRTC_LOGS_LOG_HEADER_FORMAT 8335
#define IDS_WEBRTC_LOGS_LOG_LOCAL_FILE_LABEL_FORMAT 8336
#define IDS_WEBRTC_LOGS_NO_LOCAL_LOG_FILE_MESSAGE 8337
#define IDS_WEBRTC_LOGS_LOG_UPLOAD_TIME_FORMAT 8338
#define IDS_WEBRTC_LOGS_LOG_FAILED_UPLOAD_TIME_FORMAT 8339
#define IDS_WEBRTC_LOGS_LOG_REPORT_ID_FORMAT 8340
#define IDS_WEBRTC_LOGS_BUG_LINK_LABEL 8341
#define IDS_WEBRTC_LOGS_LOG_PENDING_MESSAGE 8342
#define IDS_WEBRTC_LOGS_LOG_ACTIVELY_UPLOADED_MESSAGE 8343
#define IDS_WEBRTC_LOGS_LOG_NOT_UPLOADED_MESSAGE 8344
#define IDS_WEBRTC_LOGS_EVENT_LOG_LOCAL_LOG_ID 8345
#define IDS_WEBRTC_LOGS_NO_TEXT_LOGS_MESSAGE 8346
#define IDS_WEBRTC_LOGS_NO_EVENT_LOGS_MESSAGE 8347
#define IDS_PLUGIN_HIDE 8348
#define IDS_PLUGIN_UPDATE 8349
#define IDS_PLUGIN_BLOCKED 8350
#define IDS_PLUGIN_BLOCKED_BY_POLICY 8351
#define IDS_PLUGIN_BLOCKED_NO_LOADING 8352
#define IDS_PLUGIN_OUTDATED 8354
#define IDS_PLUGIN_NOT_AUTHORIZED 8355
#define IDS_PLUGIN_DOWNLOADING 8356
#define IDS_PLUGIN_DOWNLOAD_ERROR 8357
#define IDS_PLUGIN_DOWNLOAD_ERROR_SHORT 8358
#define IDS_PLUGIN_UPDATING 8359
#define IDS_PLUGIN_DISABLED 8360
#define IDS_PLUGIN_DEPRECATED 8361
#define IDS_SESSION_CRASHED_BUBBLE_TITLE 8362
#define IDS_SESSION_CRASHED_BUBBLE_UMA_LINK_TEXT 8363
#define IDS_SETTINGS_STORAGE_PRESSURE_BUBBLE_VIEW_TITLE 8364
#define IDS_SETTINGS_STORAGE_PRESSURE_BUBBLE_VIEW_MESSAGE 8365
#define IDS_SETTINGS_STORAGE_PRESSURE_BUBBLE_VIEW_BUTTON_LABEL 8366
#define IDS_EXPERIMENTAL_LACROS_WARNING_MESSAGE 8367
#define IDS_EXPERIMENTAL_LACROS_WARNING_MESSAGE_PRIMARY 8368
#define IDS_EXPERIMENTAL_LACROS_WARNING_MESSAGE_MANAGED 8369
#define IDS_EXPERIMENTAL_LACROS_WARNING_LEARN_MORE 8370
#define IDS_BAD_FLAGS_WARNING_MESSAGE 8371
#define IDS_BAD_FEATURES_WARNING_MESSAGE 8372
#define IDS_BAD_ENVIRONMENT_VARIABLES_WARNING_MESSAGE 8373
#define IDS_BLOCKED_DISPLAYING_INSECURE_CONTENT_TITLE 8374
#define IDS_BLOCKED_DISPLAYING_INSECURE_CONTENT 8375
#define IDS_ALLOW_INSECURE_CONTENT_BUTTON 8376
#define IDS_ADD_TO_SHELF_INFOBAR_TITLE 8377
#define IDS_ADD_TO_SHELF_INFOBAR_ADD_BUTTON 8378
#define IDS_ABOUT_SYS_TITLE 8379
#define IDS_ABOUT_SYS_DESC 8380
#define IDS_ABOUT_SYS_TABLE_TITLE 8381
#define IDS_ABOUT_SYS_LOG_FILE_TABLE_TITLE 8382
#define IDS_ABOUT_SYS_EXPAND_ALL 8383
#define IDS_ABOUT_SYS_COLLAPSE_ALL 8384
#define IDS_ABOUT_SYS_EXPAND 8385
#define IDS_ABOUT_SYS_COLLAPSE 8386
#define IDS_ABOUT_SYS_PARSE_ERROR 8387
#define IDS_ABOUT_BROWSER_SWITCH_TITLE 8388
#define IDS_ABOUT_BROWSER_SWITCH_OPENING_TITLE_UNKNOWN_BROWSER 8389
#define IDS_ABOUT_BROWSER_SWITCH_OPENING_TITLE_KNOWN_BROWSER 8390
#define IDS_ABOUT_BROWSER_SWITCH_ERROR_TITLE_UNKNOWN_BROWSER 8391
#define IDS_ABOUT_BROWSER_SWITCH_ERROR_TITLE_KNOWN_BROWSER 8392
#define IDS_ABOUT_BROWSER_SWITCH_COUNTDOWN_TITLE_UNKNOWN_BROWSER 8393
#define IDS_ABOUT_BROWSER_SWITCH_COUNTDOWN_TITLE_KNOWN_BROWSER 8394
#define IDS_ABOUT_BROWSER_SWITCH_GENERIC_ERROR_UNKNOWN_BROWSER 8395
#define IDS_ABOUT_BROWSER_SWITCH_GENERIC_ERROR_KNOWN_BROWSER 8396
#define IDS_ABOUT_BROWSER_SWITCH_PROTOCOL_ERROR 8397
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_DESC 8398
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_TITLE 8399
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_NOTHING_SHOWN 8400
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_LBS_DISABLED 8401
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_URL_CHECKER_TITLE 8402
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_URL_CHECKER_DESC 8403
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_OPEN_BROWSER 8404
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_INVALID_URL 8405
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_TITLE 8406
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_DESC 8407
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SOURCE 8408
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_NOT_CONFIGURED 8409
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_NOT_FETCHED 8410
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_DOWNLOAD_BUTTON 8411
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_LAST_DOWNLOAD_DATE 8412
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_XML_SITELIST_NEXT_DOWNLOAD_DATE 8413
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_TITLE 8414
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_DESCRIPTION 8415
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_FIRST_PARAGRAPH 8416
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_IN_SECOND_PARAGRAPH 8417
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_TABLE_COLUMN_RULE 8418
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_TABLE_COLUMN_OPENS_IN 8419
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_FORCE_OPEN_TABLE_COLUMN_SOURCE 8420
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_TITLE 8421
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_DESCRIPTION 8422
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_FIRST_PARAGRAPH 8423
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_SECOND_PARAGRAPH 8424
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_TABLE_COLUMN_RULE 8425
#define IDS_ABOUT_BROWSER_SWITCH_INTERNALS_IGNORE_TABLE_COLUMN_SOURCE 8426
#define IDS_NACL_APP_MISSING_ARCH_MESSAGE 8430
#define IDS_ABOUT_BOX_ERROR_DURING_UPDATE_CHECK 8431
#define IDS_ABOUT_BOX_ERROR_UPDATE_CHECK_FAILED 8432
#define IDS_ABOUT_BOX_EXTERNAL_UPDATE_IS_RUNNING 8433
#define IDS_ABOUT_BOX_GOOGLE_UPDATE_ERROR 8434
#define IDS_OMNIBOX_PLACEHOLDER_TEXT 277
#define IDS_PASTE_AND_GO 8435
#define IDS_PASTE_AND_SEARCH 8436
#define IDS_PASTE_AND_GO_EMPTY 8437
#define IDS_OMNIBOX_KEYWORD_HINT 8438
#define IDS_OMNIBOX_EXTENSION_KEYWORD_HINT 8439
#define IDS_OMNIBOX_KEYWORD_HINT_KEY_ACCNAME 8440
#define IDS_OMNIBOX_KEYWORD_HINT_TOUCH 8441
#define IDS_OMNIBOX_EXTENSION_KEYWORD_HINT_TOUCH 8442
#define IDS_OMNIBOX_KEYWORD_TEXT 8443
#define IDS_OMNIBOX_KEYWORD_TEXT_MD 8444
#define IDS_OMNIBOX_CLEAR_ALL 282
#define IDS_SEARCH_OR_TYPE_WEB_ADDRESS 8445
#define IDS_OMNIBOX_WHY_THIS_SUGGESTION 8446
#define IDS_OMNIBOX_REMOVE_SUGGESTION 234
#define IDS_OMNIBOX_REMOVE_SUGGESTION_BUBBLE_TITLE 8447
#define IDS_OMNIBOX_REMOVE_SUGGESTION_BUBBLE_DESCRIPTION 8448
#define IDS_CONTEXT_MENU_SHOW_FULL_URLS 8449
#define IDS_GOOGLE_SEARCH_BOX_EMPTY_HINT 8450
#define IDS_GOOGLE_SEARCH_BOX_EMPTY_HINT_MD 181
#define IDS_GOOGLE_SEARCH_BOX_EMPTY_HINT_SHORT 8451
#define IDS_NTP_CUSTOM_LINKS_ADD_SHORTCUT_TOOLTIP 196
#define IDS_NTP_CUSTOM_LINKS_ADD_SHORTCUT_TITLE 195
#define IDS_NTP_CUSTOM_LINKS_EDIT_SHORTCUT_TOOLTIP 198
#define IDS_NTP_CUSTOM_LINKS_EDIT_SHORTCUT 197
#define IDS_UPLOAD_IMAGE_FORMAT 8452
#define IDS_NTP_CUSTOM_LINKS_NAME 199
#define IDS_NTP_CUSTOM_LINKS_URL 200
#define IDS_NTP_CUSTOM_LINKS_REMOVE 201
#define IDS_NTP_CUSTOM_LINKS_CANCEL 202
#define IDS_NTP_CUSTOM_LINKS_DONE 186
#define IDS_NTP_CUSTOM_LINKS_INVALID_URL 203
#define IDS_NTP_CUSTOM_LINKS_CANT_CREATE 207
#define IDS_NTP_CUSTOM_LINKS_CANT_EDIT 208
#define IDS_NTP_CUSTOM_LINKS_CANT_REMOVE 209
#define IDS_NTP_CONFIRM_MSG_SHORTCUT_REMOVED 174
#define IDS_NTP_CONFIRM_MSG_SHORTCUT_EDITED 204
#define IDS_NTP_CONFIRM_MSG_SHORTCUT_ADDED 205
#define IDS_NTP_CUSTOM_LINKS_ALREADY_EXISTS 8453
#define IDS_NTP_CONFIRM_MSG_RESTORE_DEFAULTS 206
#define IDS_NTP_CUSTOM_BG_CHROME_WALLPAPERS 182
#define IDS_NTP_CUSTOM_BG_UPLOAD_AN_IMAGE 183
#define IDS_NTP_CUSTOM_BG_SELECT_A_COLLECTION 185
#define IDS_NTP_CUSTOM_BG_DAILY_REFRESH 8454
#define IDS_NTP_CUSTOM_BG_RESTORE_DEFAULT 184
#define IDS_NTP_CUSTOM_BG_CANCEL 187
#define IDS_NTP_CONNECTION_ERROR_NO_PERIOD 188
#define IDS_NTP_CONNECTION_ERROR 189
#define IDS_NTP_ERROR_MORE_INFO 190
#define IDS_NTP_CUSTOM_BG_BACKGROUNDS_UNAVAILABLE 191
#define IDS_NTP_CUSTOM_BG_BACK_LABEL 193
#define IDS_NTP_CUSTOM_BG_IMAGE_SELECTED 194
#define IDS_NTP_CUSTOM_BG_CUSTOMIZE_NTP_LABEL 192
#define IDS_NTP_DISMISS_PROMO 239
#define IDS_NTP_DOODLE_SHARE_LABEL 210
#define IDS_NTP_DOODLE_SHARE_DIALOG_CLOSE_LABEL 211
#define IDS_NTP_DOODLE_SHARE_DIALOG_FACEBOOK_LABEL 212
#define IDS_NTP_DOODLE_SHARE_DIALOG_TWITTER_LABEL 213
#define IDS_NTP_DOODLE_SHARE_DIALOG_MAIL_LABEL 214
#define IDS_NTP_DOODLE_SHARE_DIALOG_COPY_LABEL 215
#define IDS_NTP_DOODLE_SHARE_DIALOG_LINK_LABEL 216
#define IDS_NTP_CUSTOMIZE_MENU_BACKGROUND_LABEL 8455
#define IDS_NTP_CUSTOMIZE_MENU_BACKGROUND_DISABLED_LABEL 8456
#define IDS_NTP_CUSTOMIZE_MENU_SHORTCUTS_LABEL 8457
#define IDS_NTP_CUSTOMIZE_MENU_MODULES_LABEL 8458
#define IDS_NTP_CUSTOMIZE_MENU_COLOR_LABEL 8459
#define IDS_NTP_CUSTOMIZE_NO_BACKGROUND_LABEL 8460
#define IDS_NTP_CUSTOMIZE_UPLOAD_FROM_DEVICE_LABEL 8461
#define IDS_NTP_CUSTOMIZE_HIDE_SHORTCUTS_LABEL 8462
#define IDS_NTP_CUSTOMIZE_HIDE_SHORTCUTS_DESC 8463
#define IDS_NTP_CUSTOMIZE_MY_SHORTCUTS_LABEL 8464
#define IDS_NTP_CUSTOMIZE_MOST_VISITED_LABEL 8465
#define IDS_NTP_CUSTOMIZE_MOST_VISITED_DESC 8466
#define IDS_NTP_CUSTOMIZE_MY_SHORTCUTS_DESC 8467
#define IDS_NTP_CUSTOMIZE_HIDE_ALL_CARDS_LABEL 8468
#define IDS_NTP_CUSTOMIZE_CUSTOMIZE_CARDS_LABEL 8469
#define IDS_NTP_CUSTOMIZE_3PT_THEME_DESC 8470
#define IDS_NTP_CUSTOMIZE_3PT_THEME_UNINSTALL 8471
#define IDS_NTP_CUSTOMIZE_COLOR_PICKER_LABEL 8472
#define IDS_NTP_THEME_MANAGED_DIALOG_TITLE 8473
#define IDS_NTP_THEME_MANAGED_DIALOG_BODY 8474
#define IDS_NTP_CUSTOMIZE_DEFAULT_LABEL 8475
#define IDS_NTP_COLORS_WARM_GREY 8476
#define IDS_NTP_COLORS_COOL_GREY 8477
#define IDS_NTP_COLORS_MIDNIGHT_BLUE 8478
#define IDS_NTP_COLORS_BLACK 8479
#define IDS_NTP_COLORS_BEIGE_AND_WHITE 8480
#define IDS_NTP_COLORS_YELLOW_AND_WHITE 8481
#define IDS_NTP_COLORS_GREEN_AND_WHITE 8482
#define IDS_NTP_COLORS_LIGHT_TEAL_AND_WHITE 8483
#define IDS_NTP_COLORS_LIGHT_PURPLE_AND_WHITE 8484
#define IDS_NTP_COLORS_PINK_AND_WHITE 8485
#define IDS_NTP_COLORS_BEIGE 8486
#define IDS_NTP_COLORS_ORANGE 8487
#define IDS_NTP_COLORS_LIGHT_GREEN 8488
#define IDS_NTP_COLORS_LIGHT_TEAL 8489
#define IDS_NTP_COLORS_LIGHT_BLUE 8490
#define IDS_NTP_COLORS_PINK 8491
#define IDS_NTP_COLORS_DARK_PINK_AND_RED 8492
#define IDS_NTP_COLORS_DARK_RED_AND_ORANGE 8493
#define IDS_NTP_COLORS_DARK_GREEN 8494
#define IDS_NTP_COLORS_DARK_TEAL 8495
#define IDS_NTP_COLORS_DARK_BLUE 8496
#define IDS_NTP_COLORS_DARK_PURPLE 8497
#define IDS_NTP_MODULES_INFO_BUTTON_TITLE 8498
#define IDS_NTP_MODULES_DISMISS_TOAST_MESSAGE 8499
#define IDS_NTP_MODULES_DISABLE_TOAST_MESSAGE 8500
#define IDS_NTP_MODULES_DISMISS_BUTTON_TEXT 8501
#define IDS_NTP_MODULES_DISABLE_BUTTON_TEXT 8502
#define IDS_NTP_MODULES_CUSTOMIZE_BUTTON_TEXT 8503
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_TODAY 8504
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_YESTERDAY 8505
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_PAST_WEEK 8506
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_PAST_MONTH 8507
#define IDS_NTP_MODULES_STATEFUL_TASKS_VIEWED_PREVIOUSLY 8508
#define IDS_NTP_MODULES_SHOPPING_TASKS_SENTENCE 8509
#define IDS_NTP_MODULES_SHOPPING_TASKS_LOWER 8510
#define IDS_NTP_MODULES_RECIPE_INFO 8511
#define IDS_NTP_MODULES_RECIPE_TASKS_SENTENCE 8512
#define IDS_NTP_MODULES_RECIPE_TASKS_LOWER 8513
#define IDS_NTP_MODULES_RECIPE_TASKS_LOWER_THESE 8514
#define IDS_NTP_MODULES_RECIPE_VIEWED_TASKS_SENTENCE 8515
#define IDS_NTP_MODULES_RECIPE_VIEWED_TASKS_LOWER 8516
#define IDS_NTP_MODULES_RECIPE_VIEWED_TASKS_LOWER_THESE 8517
#define IDS_NTP_MODULES_CART_SENTENCE 8518
#define IDS_NTP_MODULES_CART_SENTENCE_V2 8519
#define IDS_NTP_MODULES_CART_LOWER 8520
#define IDS_NTP_MODULES_CART_LOWER_THESE 8521
#define IDS_NTP_MODULES_CART_LOWER_YOUR 8522
#define IDS_NTP_MODULES_CART_INFO 8523
#define IDS_NTP_MODULES_CART_ITEM_COUNT_SINGULAR 8524
#define IDS_NTP_MODULES_CART_ITEM_COUNT_MULTIPLE 8525
#define IDS_NTP_MODULES_DRIVE_SENTENCE 8526
#define IDS_NTP_MODULES_DRIVE_SENTENCE2 8527
#define IDS_NTP_MODULES_DRIVE_FILES_SENTENCE 8528
#define IDS_NTP_MODULES_DRIVE_FILES_LOWER 8529
#define IDS_NTP_MODULES_DUMMY_LOWER 8530
#define IDS_NTP_MODULES_DUMMY_TITLE 8531
#define IDS_NTP_MODULES_DUMMY2_TITLE 8532
#define IDS_NTP_MODULES_DRIVE_TITLE 8533
#define IDS_NTP_MODULES_DRIVE_INFO 8534
#define IDS_NTP_MODULES_PHOTOS_TITLE 8535
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_TITLE 8536
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_HIDE_TODAY 8537
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_HIDDEN_TODAY 8538
#define IDS_NTP_MODULES_PHOTOS_SOFT_OPT_OUT 8539
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_DISABLE 8540
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_DISABLED 8541
#define IDS_NTP_MODULES_PHOTOS_INFO 8542
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_EXPLORE 8543
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_TITLE 8544
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_PERSONALIZED_WELCOME_TITLE_TEMPLATE 8545
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_RH_WELCOME_TITLE 8546
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_FAVORITE_PEOPLE_WELCOME_TITLE 8547
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_TRIPS_WELCOME_TITLE 8548
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_TEXT 8549
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_BUTTON_OPT_IN 8550
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_BUTTON_OPT_OUT 8551
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_BUTTON_SOFT_OPT_OUT 8552
#define IDS_NTP_MODULES_PHOTOS_MEMORIES_WELCOME_EXAMPLE 8553
#define IDS_NTP_MODULES_PHOTOS_NEW 8554
#define IDS_NTP_MODULES_KALEIDOSCOPE_TITLE 8555
#define IDS_NTP_MODULES_SHOPPING_TASKS_INFO_TITLE 8556
#define IDS_NTP_MODULES_TASKS_INFO 8557
#define IDS_NTP_MODULES_SHOPPING_TASKS_INFO_CLOSE 8558
#define IDS_NTP_MODULES_SHOPPING_TASKS_RELATED 8559
#define IDS_NTP_MODULES_RECIPE_TASKS_RECOMMENDED 8560
#define IDS_NTP_MODULES_CART_WARM_WELCOME 8561
#define IDS_NTP_MODULES_CART_MODULE_MENU_HIDE_TOAST_MESSAGE 8562
#define IDS_NTP_MODULES_CART_CART_MENU_HIDE_MERCHANT 8563
#define IDS_NTP_MODULES_CART_CART_MENU_HIDE_MERCHANT_TOAST_MESSAGE 8564
#define IDS_NTP_MODULES_CART_CART_MENU_REMOVE_MERCHANT 8565
#define IDS_NTP_MODULES_CART_CART_MENU_REMOVE_MERCHANT_TOAST_MESSAGE 8566
#define IDS_NTP_MODULES_CART_DISCOUNT_CHIP_AMOUNT 8567
#define IDS_NTP_MODULES_CART_DISCOUNT_CHIP_UP_TO_AMOUNT 8568
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONTENT 8569
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_ACCEPT 8570
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_ACCEPT_CONFIRMATION 8571
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_REJECT 8572
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_REJECT_CONFIRMATION 8573
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONFIRMATION_DISMISS 8574
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONTENT_V2 8575
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_CONTENT_V3 8576
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_WITH_MERCHANT_NAME 8577
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_WITH_TWO_MERCHANT_NAMES 8578
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_WITH_THREE_MERCHANT_NAMES 8579
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_STEP_1_CONTINUE 8580
#define IDS_NTP_MODULES_CART_DISCOUNT_CONSENT_TITLE 8581
#define IDS_NTP_MODULES_NEW_TAG_LABEL 8582
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_TITLE 8583
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_BODY_LINE_1 8584
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_BODY_LINE_2 8585
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_OPT_IN 8586
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_OPT_OUT 8587
#define IDS_NTP_MODULES_FIRST_RUN_EXPERIENCE_OPT_OUT_TOAST 8588
#define IDS_EXTENSIONS_PROMO_PERFORMANCE 8589
#define IDS_EXTENSIONS_PROMO_PRIVACY 8590
#define IDS_EXTENSIONS_PROMO_NEUTRAL 8591
#define IDS_STAR_VIEW_MENU_ADD_BOOKMARK 8593
#define IDS_STAR_VIEW_MENU_EDIT_BOOKMARK 8594
#define IDS_STAR_VIEW_MENU_MOVE_TO_READ_LATER 8595
#define IDS_STAR_VIEW_MENU_MARK_AS_READ 8596
#define IDS_READ_LATER_TITLE 8597
#define IDS_READ_LATER_MENU_UNREAD_HEADER 8598
#define IDS_READ_LATER_MENU_READ_HEADER 8599
#define IDS_READ_LATER_MENU_TOOLTIP_MARK_AS_READ 8600
#define IDS_READ_LATER_MENU_TOOLTIP_MARK_AS_UNREAD 8601
#define IDS_READ_LATER_MENU_EMPTY_STATE_ADD_FROM_DIALOG_SUBHEADER 8602
#define IDS_READ_LATER_MENU_EMPTY_STATE_HEADER 8603
#define IDS_READ_LATER_MENU_EMPTY_STATE_SUBHEADER 8604
#define IDS_READ_LATER_ADD_CURRENT_TAB 8605
#define IDS_READ_LATER_CANT_ADD_CURRENT_TAB 8606
#define IDS_READ_LATER_REMOVE_CURRENT_TAB 8607
#define IDS_READ_LATER_CONTEXT_MENU_MARK_AS_UNREAD 8608
#define IDS_READ_LATER_CONTEXT_MENU_MARK_AS_READ 8609
#define IDS_READ_LATER_CONTEXT_MENU_DELETE 8610
#define IDS_SIDE_PANEL_TITLE 8611
#define IDS_READ_ANYTHING_TITLE 8612
#define IDS_USER_NOTE_TITLE 8613
#define IDS_ADD_NEW_USER_NOTE_PLACEHOLDER_TEXT 8614
#define IDS_FEED_TITLE 8615
#define IDS_TOOLTIP_BACK 252
#define IDS_ACCDESCRIPTION_BACK 254
#define IDS_TOOLTIP_CHROMELABS_BUTTON 8616
#define IDS_TOOLTIP_LEFT_ALIGNED_SIDE_PANEL_BUTTON 8617
#define IDS_TOOLTIP_CHROMELABS_COMBOBOX 8618
#define IDS_TOOLTIP_CHROMELABS_FEEDBACK_BUTTON 8619
#define IDS_TOOLTIP_FORWARD 255
#define IDS_ACCDESCRIPTION_FORWARD 257
#define IDS_TOOLTIP_DOWNLOAD_ICON 8620
#define IDS_TOOLTIP_HOME 262
#define IDS_TOOLTIP_RELOAD 8621
#define IDS_TOOLTIP_RELOAD_WITH_MENU 8622
#define IDS_TOOLTIP_STOP 8623
#define IDS_TOOLTIP_EXTENSIONS_BUTTON 264
#define IDS_TOOLTIP_EXTENSIONS_SITE_ACCESS_BUTTON 8624
#define IDS_TOOLTIP_SIDE_PANEL_SHOW 8625
#define IDS_TOOLTIP_SIDE_PANEL_HIDE 8626
#define IDS_TOOLTIP_LOCATION_ICON 276
#define IDS_TOOLTIP_NEW_TAB 243
#define IDS_TOOLTIP_MIC_SEARCH 219
#define IDS_TOOLTIP_SAVE_CREDIT_CARD 8628
#define IDS_TOOLTIP_SAVE_CREDIT_CARD_PENDING 8629
#define IDS_TOOLTIP_SAVE_CREDIT_CARD_FAILURE 8630
#define IDS_TOOLTIP_MIGRATE_LOCAL_CARD 8631
#define IDS_TOOLTIP_TRANSLATE 8632
#define IDS_TOOLTIP_ZOOM 8633
#define IDS_TOOLTIP_ZOOM_EXTENSION_ICON 8634
#define IDS_ZOOM_SET_DEFAULT 8635
#define IDS_TOOLTIP_FIND 8636
#define IDS_TOOLTIP_TAB_SEARCH 8637
#define IDS_TOOLTIP_INTENT_PICKER_ICON 8638
#define IDS_INTENT_PICKER_BUBBLE_VIEW_OPEN_WITH 8639
#define IDS_INTENT_PICKER_BUBBLE_VIEW_REMEMBER_SELECTION 8640
#define IDS_INTENT_PICKER_BUBBLE_VIEW_OPEN 8641
#define IDS_INTENT_PICKER_BUBBLE_VIEW_STAY_IN_CHROME 8642
#define IDS_INTENT_PICKER_BUBBLE_VIEW_INITIATING_ORIGIN 8643
#define IDS_INTENT_CHIP_LABEL 8644
#define IDS_INTENT_CHIP_IPH 8645
#define IDS_ACCESSIBLE_INCOGNITO_WINDOW_TITLE_FORMAT 8648
#define IDS_ACCESSIBLE_GUEST_WINDOW_TITLE_FORMAT 8649
#define IDS_ACCESSIBLE_WINDOW_TITLE_WITH_PROFILE_FORMAT 8650
#define IDS_ACCNAME_APP_UPGRADE_RECOMMENDED 8651
#define IDS_ACCNAME_FULLSCREEN 358
#define IDS_ACCNAME_HOME 263
#define IDS_ACCNAME_RELOAD 261
#define IDS_ACCNAME_FIND 8652
#define IDS_ACCNAME_BOOKMARKS 8653
#define IDS_ACCNAME_SAVED_TAB_GROUPS 8654
#define IDS_ACCNAME_BOOKMARKS_CHEVRON 290
#define IDS_ACCNAME_BOOKMARK_BUTTON_ROLE_DESCRIPTION 8655
#define IDS_ACCNAME_BOOKMARK_FOLDER_BUTTON_ROLE_DESCRIPTION 8656
#define IDS_ACCNAME_SAVED_TAB_GROUP_BUTTON_ROLE_DESCRIPTION 8657
#define IDS_ACCNAME_CHROMELABS_BUTTON 8658
#define IDS_ACCNAME_LEFT_ALIGNED_SIDE_PANEL_BUTTON 8659
#define IDS_ACCESSIBLE_TEXT_CHROMELABS_BUTTON_ADDED_BY_ENTERPRISE_POLICY 8660
#define IDS_ACCESSIBLE_TEXT_CHROMELABS_BUTTON_REMOVED_BY_ENTERPRISE_POLICY 8661
#define IDS_ACCNAME_CHROMELABS_COMBOBOX 8662
#define IDS_ACCNAME_CHROMELABS_COMBOBOX_MAC 8663
#define IDS_ACCNAME_SEPARATOR 8664
#define IDS_ACCNAME_EXTENSIONS 8665
#define IDS_ACCNAME_NEWTAB 244
#define IDS_ACCNAME_MINIMIZE 8666
#define IDS_ACCNAME_MAXIMIZE 8667
#define IDS_ACCNAME_RESTORE 8668
#define IDS_ACCNAME_CLOSE_TAB 8669
#define IDS_ACCNAME_ZOOM_SET_DEFAULT 8670
#define IDS_ACCNAME_TAB_SEARCH 8671
#define IDS_ACCNAME_TAB_SCROLL_LEADING 8672
#define IDS_ACCNAME_TAB_SCROLL_TRAILING 8673
#define IDS_ACCNAME_MUTE_TAB 8674
#define IDS_ACCNAME_EXTENSIONS_MENU_SITE_ACCESS_COMBOBOX 8675
#define IDS_ALLOWED_CLIPBOARD_TITLE 8676
#define IDS_BLOCKED_CLIPBOARD_TITLE 8677
#define IDS_ALLOWED_CLIPBOARD_MESSAGE 8678
#define IDS_ALLOWED_CLIPBOARD_BLOCK 8679
#define IDS_ALLOWED_CLIPBOARD_NO_ACTION 8680
#define IDS_BLOCKED_CLIPBOARD_MESSAGE 8681
#define IDS_BLOCKED_CLIPBOARD_UNBLOCK 8682
#define IDS_BLOCKED_CLIPBOARD_NO_ACTION 8683
#define IDS_BOOKMARK_PROMO_0 8684
#define IDS_BOOKMARK_PROMO_1 8685
#define IDS_BOOKMARK_PROMO_2 8686
#define IDS_CHROME_TIP 8687
#define IDS_GLOBAL_MEDIA_CONTROLS_PROMO 8688
#define IDS_INCOGNITOWINDOW_PROMO_0 8689
#define IDS_INCOGNITOWINDOW_PROMO_1 8690
#define IDS_INCOGNITOWINDOW_PROMO_2 8691
#define IDS_INCOGNITOWINDOW_PROMO_3 8692
#define IDS_NEWTAB_PROMO_0 8693
#define IDS_NEWTAB_PROMO_1 8694
#define IDS_NEWTAB_PROMO_2 8695
#define IDS_READING_LIST_DISCOVERY_PROMO 8696
#define IDS_READING_LIST_ENTRY_POINT_PROMO 8697
#define IDS_REOPEN_TAB_PROMO 8698
#define IDS_DESKTOP_PWA_INSTALL_PROMO 8699
#define IDS_UPDATED_CONNECTION_SECURITY_INDICATORS_PROMO 8700
#define IDS_REOPEN_TAB_PROMO_SCREENREADER 8701
#define IDS_READING_LIST_IN_SIDE_PANEL_PROMO 8702
#define IDS_TAB_GROUPS_NEW_GROUP_PROMO 8703
#define IDS_TAB_GROUPS_UNNAMED_GROUP_TOOLTIP 8704
#define IDS_TAB_GROUPS_NAMED_GROUP_TOOLTIP 8705
#define IDS_TAB_AUDIO_MUTING_PROMO 8706
#define IDS_SHARED_HIGHLIGHTING_PROMO 8707
#define IDS_TUTORIAL_TAB_GROUP_ADD_TAB_TO_GROUP 8708
#define IDS_TUTORIAL_TAB_GROUP_EDIT_BUBBLE 8709
#define IDS_TUTORIAL_TAB_GROUP_DRAG_TAB 8710
#define IDS_TUTORIAL_TAB_GROUP_COLLAPSE 8711
#define IDS_TUTORIAL_TAB_GROUP_SUCCESS_TITLE 8712
#define IDS_TUTORIAL_TAB_GROUP_SUCCESS_DESCRIPTION 8713
#define IDS_BROWSER_HANGMONITOR 8714
#define IDS_BROWSER_HANGMONITOR_RENDERER_TITLE 8715
#define IDS_BROWSER_HANGMONITOR_RENDERER 8716
#define IDS_BROWSER_HANGMONITOR_RENDERER_INFOBAR 8717
#define IDS_BROWSER_HANGMONITOR_IFRAME_TITLE 8718
#define IDS_BROWSER_HANGMONITOR_RENDERER_INFOBAR_END 8719
#define IDS_BROWSER_HANGMONITOR_RENDERER_WAIT 8720
#define IDS_BROWSER_HANGMONITOR_RENDERER_END 8721
#define IDS_BROWSER_HANGMONITOR_PLUGIN_INFOBAR 8722
#define IDS_BROWSER_HANGMONITOR_PLUGIN_INFOBAR_KILLBUTTON 8723
#define IDS_PASSWORDS_AUTO_SIGNIN_TITLE 8724
#define IDS_PASSWORDS_AUTO_SIGNIN_DESCRIPTION 8725
#define IDS_PASSWORDS_VIA_FEDERATION 8726
#define IDS_CONFIRM_MESSAGEBOX_YES_BUTTON_LABEL 8727
#define IDS_CONFIRM_MESSAGEBOX_NO_BUTTON_LABEL 8728
#define IDS_NEAR_OOM_REDUCTION_MESSAGE_TITLE 8729
#define IDS_NEAR_OOM_REDUCTION_MESSAGE_DESCRIPTION 8730
#define IDS_TAILORED_SECURITY_UNCONSENTED_MESSAGE_TITLE 8731
#define IDS_TAILORED_SECURITY_UNCONSENTED_MESSAGE_ACCEPT 8732
#define IDS_TAILORED_SECURITY_DISPLAY_SOURCE 8733
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_NOTIFICATION_TITLE 8734
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_NOTIFICATION_ACCEPT 8735
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_NOTIFICATION_TITLE 8736
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_NOTIFICATION_DESCRIPTION 8737
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_NOTIFICATION_TURN_OFF 8738
#define IDS_PASSWORD_MANAGER_CANCEL_BUTTON 8739
#define IDS_PASSWORD_MANAGER_USERNAME_LABEL 8740
#define IDS_PASSWORD_MANAGER_PASSWORD_LABEL 8741
#define IDS_PASSWORD_MANAGER_UPDATED_BUBBLE_TITLE 8742
#define IDS_PASSWORD_MANAGER_MORE_TO_FIX_BODY_MESSAGE 8743
#define IDS_PASSWORD_MANAGER_MORE_TO_FIX_BODY_MESSAGE_GOOGLE_PASSWORD_MANAGER 8744
#define IDS_PASSWORD_MANAGER_SAFE_STATE_BODY_MESSAGE 8745
#define IDS_PASSWORD_MANAGER_SAFE_STATE_BODY_MESSAGE_GOOGLE_PASSWORD_MANAGER 8746
#define IDS_PASSWORD_MANAGER_SAFE_STATE_SETTINGS 8747
#define IDS_PASSWORD_MANAGER_CHECK_REMAINING_BUTTON 8748
#define IDS_PASSWORD_MANAGER_SAVE_BUTTON 8749
#define IDS_PASSWORD_MANAGER_SAVE_BUBBLE_OPT_IN_BUTTON 8750
#define IDS_PASSWORD_MANAGER_MOVE_BUBBLE_OK_BUTTON 8751
#define IDS_PASSWORD_MANAGER_MOVE_BUBBLE_CANCEL_BUTTON 8752
#define IDS_PASSWORD_MANAGER_UPDATE_BUTTON 8753
#define IDS_PASSWORD_MANAGER_BUBBLE_BLOCKLIST_BUTTON 8754
#define IDS_PASSWORD_MANAGER_DESTINATION_DROPDOWN_ACCESSIBLE_NAME 8755
#define IDS_PASSWORD_MANAGER_DESTINATION_DROPDOWN_SAVE_TO_ACCOUNT 8756
#define IDS_PASSWORD_MANAGER_DESTINATION_DROPDOWN_SAVE_TO_DEVICE 8757
#define IDS_PASSWORD_MANAGER_IPH_TITLE_SAVE_TO_ACCOUNT 8758
#define IDS_PASSWORD_MANAGER_IPH_BODY_SAVE_REAUTH_FAIL 8759
#define IDS_PASSWORD_MANAGER_IPH_BODY_SAVE_TO_ACCOUNT 8760
#define IDS_PASSWORD_MANAGER_TOOLTIP_SAVE 8761
#define IDS_PASSWORD_MANAGER_TOOLTIP_MANAGE 8762
#define IDS_PASSWORD_MANAGER_TOOLTIP_MOVE 8763
#define IDS_PASSWORD_MANAGER_IMPORT_BUTTON 8764
#define IDS_PASSWORD_MANAGER_IMPORT_DIALOG_TITLE 8765
#define IDS_PASSWORD_MANAGER_EXPORT_DIALOG_TITLE 8766
#define IDS_PASSWORD_MANAGER_MOVE_TITLE 8767
#define IDS_PASSWORD_MANAGER_MOVE_HINT 8768
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_TITLE 8769
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_TITLE_GPM 8770
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_DESCRIPTION 8771
#define IDS_PASSWORD_MANAGER_UNSYNCED_CREDENTIALS_BUBBLE_DESCRIPTION_GPM 8772
#define IDS_PASSWORD_MANAGER_SAVE_UNSYNCED_CREDENTIALS_BUTTON 8773
#define IDS_PASSWORD_MANAGER_SAVE_UNSYNCED_CREDENTIALS_BUTTON_GPM 8774
#define IDS_PASSWORD_MANAGER_DISCARD_UNSYNCED_CREDENTIALS_BUTTON 8775
#define IDS_IMPORT_FROM_IE 8790
#define IDS_IMPORT_FROM_EDGE 8791
#define IDS_IMPORT_FROM_FIREFOX 8792
#define IDS_IMPORT_FROM_ICEWEASEL 8793
#define IDS_IMPORT_FROM_SAFARI 8794
#define IDS_IMPORT_FROM_BOOKMARKS_HTML_FILE 8795
#define IDS_IMPORTER_LOCK_TITLE 8799
#define IDS_IMPORTER_LOCK_TEXT 8800
#define IDS_IMPORTER_LOCK_OK 8801
#define IDS_FEEDBACK_REPORT_APP_TITLE 159
#define IDS_FEEDBACK_REPORT_PAGE_TITLE 158
#define IDS_FEEDBACK_REPORT_PAGE_TITLE_SAD_TAB_FLOW 8802
#define IDS_FEEDBACK_MINIMIZE_BUTTON_LABEL 8803
#define IDS_FEEDBACK_CLOSE_BUTTON_LABEL 8804
#define IDS_FEEDBACK_FREE_TEXT_LABEL 8805
#define IDS_FEEDBACK_REPORT_URL_LABEL 8806
#define IDS_FEEDBACK_USER_EMAIL_LABEL 8807
#define IDS_FEEDBACK_ANONYMOUS_EMAIL_OPTION 8808
#define IDS_FEEDBACK_CONSENT_CHECKBOX_LABEL 8809
#define IDS_FEEDBACK_SCREENSHOT_LABEL 8810
#define IDS_FEEDBACK_SCREENSHOT_A11Y_TEXT 8811
#define IDS_FEEDBACK_INCLUDE_PERFORMANCE_TRACE_CHECKBOX 8812
#define IDS_FEEDBACK_BLUETOOTH_LOGS_CHECKBOX 8813
#define IDS_FEEDBACK_ASSISTANT_LOGS_MESSAGE 8814
#define IDS_FEEDBACK_BLUETOOTH_LOGS_MESSAGE 8815
#define IDS_FEEDBACK_OFFLINE_DIALOG_TITLE 8816
#define IDS_FEEDBACK_OFFLINE_DIALOG_TEXT 8817
#define IDS_FEEDBACK_INCLUDE_SYSTEM_INFORMATION_CHKBOX 8818
#define IDS_FEEDBACK_INCLUDE_ASSISTANT_INFORMATION_CHKBOX 8821
#define IDS_FEEDBACK_ATTACH_FILE_NOTE 8822
#define IDS_FEEDBACK_ATTACH_FILE_LABEL 8823
#define IDS_FEEDBACK_ATTACH_FILE_TO_BIG 8824
#define IDS_FEEDBACK_IWLWIFI_DEBUG_DUMP_EXPLAINER 8825
#define IDS_FEEDBACK_PRIVACY_NOTE 8826
#define IDS_FEEDBACK_NO_DESCRIPTION 8827
#define IDS_FEEDBACK_SEND_REPORT 8828
#define IDS_FEEDBACK_SYSINFO_PAGE_TITLE 8829
#define IDS_FEEDBACK_SYSINFO_PAGE_LOADING 8830
#define IDS_FEEDBACK_ADDITIONAL_INFO_LABEL 8831
#define IDS_CLEAR_BROWSING_DATA_TITLE 8832
#define IDS_CLEAR_BROWSING_DATA_HISTORY_NOTICE 8833
#define IDS_CLEAR_BROWSING_DATA_HISTORY_NOTICE_TITLE 8834
#define IDS_CLEAR_BROWSING_DATA_HISTORY_NOTICE_OK 8835
#define IDS_CLEAR_BROWSING_DATA_PASSWORDS_NOTICE 8836
#define IDS_CLEAR_BROWSING_DATA_PASSWORDS_NOTICE_TITLE 8837
#define IDS_CLEAR_BROWSING_DATA_PASSWORDS_NOTICE_OK 8838
#define IDS_MEDIA_SELECTED_MIC_LABEL 8839
#define IDS_MEDIA_SELECTED_CAMERA_LABEL 8840
#define IDS_MEDIA_MENU_NO_DEVICE_TITLE 8841
#define IDS_ZOOMLEVELS_CHROME_ERROR_PAGES_LABEL 8842
#define IDS_UPGRADE_ERROR 8849
#define IDS_UPGRADE_ERROR_DETAILS 8850
#define IDS_UPGRADE_DISABLED_BY_POLICY 8851
#define IDS_UPGRADE_DISABLED_BY_POLICY_MANUAL 8852
#define IDS_REPORT_AN_ISSUE 8853
#define IDS_CHROME_CLEANUP_PROMPT_DETAILS_BUTTON_LABEL 8854
#define IDS_CHROME_CLEANUP_PROMPT_REMOVE_BUTTON_LABEL 8855
#define IDS_CHROME_CLEANUP_PROMPT_TITLE 8856
#define IDS_CHROME_CLEANUP_REBOOT_PROMPT_TITLE 8857
#define IDS_CHROME_CLEANUP_REBOOT_PROMPT_RESTART_BUTTON_LABEL 8858
#define IDS_CHROME_CLEANUP_LOGS_PERMISSION 8859
#define IDS_SETTINGS_RESET_PROMPT_TITLE_SEARCH_ENGINE 8860
#define IDS_SETTINGS_RESET_PROMPT_TITLE_STARTUP_PAGE 8861
#define IDS_SETTINGS_RESET_PROMPT_TITLE_HOMEPAGE 8862
#define IDS_SETTINGS_RESET_PROMPT_ACCEPT_BUTTON_LABEL 8863
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_SEARCH_ENGINE_NO_EXTENSIONS 8864
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_STARTUP_PAGE_SINGLE_NO_EXTENSIONS 8865
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_STARTUP_PAGE_MULTIPLE_NO_EXTENSIONS 8866
#define IDS_SETTINGS_RESET_PROMPT_EXPLANATION_FOR_HOMEPAGE_NO_EXTENSIONS 8867
#define IDS_REENABLE_UPDATES 8868
#define IDS_PICTURE_IN_PICTURE_TITLE_TEXT 8869
#define IDS_PICTURE_IN_PICTURE_PAUSE_CONTROL_TEXT 8870
#define IDS_PICTURE_IN_PICTURE_PLAY_CONTROL_TEXT 8871
#define IDS_PICTURE_IN_PICTURE_REPLAY_CONTROL_TEXT 8872
#define IDS_PICTURE_IN_PICTURE_BACK_TO_TAB_CONTROL_TEXT 8873
#define IDS_PICTURE_IN_PICTURE_SKIP_AD_CONTROL_TEXT 8874
#define IDS_PICTURE_IN_PICTURE_MUTE_MICROPHONE_TEXT 8875
#define IDS_PICTURE_IN_PICTURE_UNMUTE_MICROPHONE_TEXT 8876
#define IDS_PICTURE_IN_PICTURE_TURN_ON_CAMERA_TEXT 8877
#define IDS_PICTURE_IN_PICTURE_TURN_OFF_CAMERA_TEXT 8878
#define IDS_PICTURE_IN_PICTURE_HANG_UP_TEXT 8879
#define IDS_PICTURE_IN_PICTURE_CLOSE_CONTROL_TEXT 8880
#define IDS_PICTURE_IN_PICTURE_RESIZE_HANDLE_TEXT 8881
#define IDS_PICTURE_IN_PICTURE_PLAY_PAUSE_CONTROL_ACCESSIBLE_TEXT 8882
#define IDS_PICTURE_IN_PICTURE_NEXT_TRACK_CONTROL_ACCESSIBLE_TEXT 8883
#define IDS_PICTURE_IN_PICTURE_PREVIOUS_TRACK_CONTROL_ACCESSIBLE_TEXT 8884
#define IDS_LOAD_STATE_WAITING_FOR_SOCKET_SLOT 8885
#define IDS_LOAD_STATE_WAITING_FOR_DELEGATE 8886
#define IDS_LOAD_STATE_WAITING_FOR_DELEGATE_GENERIC 8887
#define IDS_LOAD_STATE_WAITING_FOR_CACHE 8888
#define IDS_LOAD_STATE_ESTABLISHING_PROXY_TUNNEL 8889
#define IDS_LOAD_STATE_RESOLVING_PROXY_FOR_URL 8890
#define IDS_LOAD_STATE_RESOLVING_HOST_IN_PAC_FILE 8891
#define IDS_LOAD_STATE_DOWNLOADING_PAC_FILE 8892
#define IDS_LOAD_STATE_RESOLVING_HOST 8893
#define IDS_LOAD_STATE_CONNECTING 8894
#define IDS_LOAD_STATE_SSL_HANDSHAKE 8895
#define IDS_LOAD_STATE_SENDING_REQUEST 8896
#define IDS_LOAD_STATE_SENDING_REQUEST_WITH_PROGRESS 8897
#define IDS_LOAD_STATE_WAITING_FOR_RESPONSE 313
#define IDS_TAB_CXMENU_NEWTABTORIGHT 8898
#define IDS_TAB_CXMENU_NEWTABTOLEFT 8899
#define IDS_TAB_CXMENU_RELOAD 8900
#define IDS_TAB_CXMENU_DUPLICATE 8901
#define IDS_TAB_CXMENU_CLOSETAB 8902
#define IDS_TAB_CXMENU_CLOSEOTHERTABS 8903
#define IDS_TAB_CXMENU_CLOSETABSTORIGHT 8904
#define IDS_TAB_CXMENU_CLOSETABSTOLEFT 8905
#define IDS_TAB_CXMENU_FOCUS_THIS_TAB 8906
#define IDS_TAB_CXMENU_PIN_TAB 8907
#define IDS_TAB_CXMENU_UNPIN_TAB 8908
#define IDS_TAB_CXMENU_SOUND_MUTE_SITE 8909
#define IDS_TAB_CXMENU_SOUND_UNMUTE_SITE 8910
#define IDS_TAB_CXMENU_READ_LATER 8911
#define IDS_TAB_CXMENU_ADD_TAB_TO_GROUP 8912
#define IDS_TAB_CXMENU_ADD_TAB_TO_NEW_GROUP 8913
#define IDS_TAB_CXMENU_SUBMENU_NEW_GROUP 8914
#define IDS_TAB_CXMENU_REMOVE_TAB_FROM_GROUP 8915
#define IDS_TAB_CXMENU_PLACEHOLDER_GROUP_TITLE 8916
#define IDS_TAB_CXMENU_MOVE_TABS_TO_NEW_WINDOW 8917
#define IDS_TAB_CXMENU_MOVETOANOTHERWINDOW 8918
#define IDS_TAB_CXMENU_MOVETOANOTHERNEWWINDOW 8919
#define IDS_TAB_CXMENU_FOLLOW_SITE 8920
#define IDS_TAB_CXMENU_UNFOLLOW_SITE 8921
#define IDS_WEBUI_TAB_STRIP_PROMO 8925
#define IDS_TOOLTIP_WEBUI_TAB_STRIP_TAB_COUNTER 8926
#define IDS_WEBUI_TAB_STRIP_TAB_COUNTER_CXMENU_NEW_TAB 8927
#define IDS_WEBUI_TAB_STRIP_TAB_COUNTER_CXMENU_CLOSE_TAB 8928
#define IDS_TAB_SEARCH_PROMO 8929
#define IDS_TAB_SEARCH_SEARCH_TABS 8930
#define IDS_TAB_SEARCH_NO_RESULTS_FOUND 8931
#define IDS_TAB_SEARCH_CLOSE_TAB 8932
#define IDS_TAB_SEARCH_SUBMIT_FEEDBACK 8933
#define IDS_TAB_SEARCH_A11Y_TAB_CLOSED 8934
#define IDS_TAB_SEARCH_A11Y_FOUND_TAB 8935
#define IDS_TAB_SEARCH_A11Y_FOUND_TABS 8936
#define IDS_TAB_SEARCH_A11Y_FOUND_TAB_FOR 8937
#define IDS_TAB_SEARCH_A11Y_FOUND_TABS_FOR 8938
#define IDS_TAB_SEARCH_A11Y_OPEN_TAB 8939
#define IDS_TAB_SEARCH_A11Y_RECENTLY_CLOSED_TAB 8940
#define IDS_TAB_SEARCH_A11Y_RECENTLY_CLOSED_TAB_GROUP 8941
#define IDS_TAB_SEARCH_MEDIA_TABS 8942
#define IDS_TAB_SEARCH_OPEN_TABS 8943
#define IDS_TAB_SEARCH_RECENTLY_CLOSED 8944
#define IDS_TAB_SEARCH_RECENTLY_CLOSED_TABS 8945
#define IDS_TAB_SEARCH_ONE_TAB 8946
#define IDS_TAB_SEARCH_TAB_COUNT 8947
#define IDS_TAB_SEARCH_EXPAND_RECENTLY_CLOSED_ITEMS 8948
#define IDS_BROWSER_WINDOW_TITLE_MENU_ENTRY 8949
#define IDS_TAB_GROUP_HEADER_CXMENU_SAVE_GROUP 8950
#define IDS_TAB_GROUP_HEADER_CXMENU_NEW_TAB_IN_GROUP 8951
#define IDS_TAB_GROUP_HEADER_CXMENU_UNGROUP 8952
#define IDS_TAB_GROUP_HEADER_CXMENU_CLOSE_GROUP 8953
#define IDS_TAB_GROUP_HEADER_CXMENU_MOVE_GROUP_TO_NEW_WINDOW 8954
#define IDS_TAB_GROUP_HEADER_CXMENU_SEND_FEEDBACK 8955
#define IDS_TAB_GROUP_HEADER_BUBBLE_TITLE_PLACEHOLDER 8956
#define IDS_APP_MENU_RELOAD 8957
#define IDS_APP_MENU_NEW_WEB_PAGE 8958
#define IDS_APP_MENU_BUTTON_UPDATE 8959
#define IDS_APP_MENU_BUTTON_ERROR 8960
#define IDS_MEDIA_SCREEN_CAPTURE_CONFIRMATION_TITLE 8961
#define IDS_MEDIA_SCREEN_CAPTURE_CONFIRMATION_TEXT 8962
#define IDS_MEDIA_SCREEN_AND_AUDIO_CAPTURE_CONFIRMATION_TEXT 8963
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_TEXT 8964
#define IDS_MEDIA_SCREEN_CAPTURE_WITH_AUDIO_NOTIFICATION_TEXT 8965
#define IDS_MEDIA_WINDOW_CAPTURE_NOTIFICATION_TEXT 8966
#define IDS_MEDIA_TAB_CAPTURE_NOTIFICATION_TEXT 8967
#define IDS_MEDIA_TAB_CAPTURE_WITH_AUDIO_NOTIFICATION_TEXT 8968
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_HIDE 8969
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_SOURCE 8970
#define IDS_MEDIA_SCREEN_CAPTURE_NOTIFICATION_STOP 8971
#define IDS_PLATFORM_KEYS_SELECT_CERT_DIALOG_TEXT 8972
#define IDS_UNSAFE_FRAME_MESSAGE 8973
#define IDS_CLIENT_CERT_DIALOG_TITLE 8974
#define IDS_CLIENT_CERT_DIALOG_TEXT 8975
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TITLE 8976
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CERT_ENROLLMENT 8977
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CLIENT_AUTH 8978
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_LIST_CERTS 8979
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CERT_IMPORT 8980
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_TEXT_CERT_EXPORT 8981
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_PASSWORD_FIELD 8982
#define IDS_CRYPTO_MODULE_AUTH_DIALOG_OK_BUTTON_LABEL 8983
#define IDS_FR_ENABLE_LOGGING 8985
#define IDS_CRASHED_TAB_FEEDBACK_MESSAGE 8989
#define IDS_CRASHED_TAB_FEEDBACK_LINK 312
#define IDS_KILLED_TAB_FEEDBACK_MESSAGE 8991
#define IDS_HIDE_ICONS_NOT_SUPPORTED 8992
#define IDS_RELAUNCH_BUTTON 8993
#define IDS_TOOLBAR_INFORM_SET_HOME_PAGE 8996
#define IDS_MANAGE_EXTENSIONS_SETTING_WINDOWS_TITLE 8997
#define IDS_CONTROLLED_SETTING_POLICY 8998
#define IDS_CONTROLLED_SETTING_EXTENSION 8999
#define IDS_CONTROLLED_SETTING_EXTENSION_WITHOUT_NAME 9000
#define IDS_CONTROLLED_SETTING_RECOMMENDED 9001
#define IDS_CONTROLLED_SETTING_HAS_RECOMMENDATION 9002
#define IDS_CONTROLLED_SETTING_CHILD_RESTRICTION 9003
#define IDS_EXTENSIONS_INSTALL_LOCATION_UNKNOWN 9004
#define IDS_EXTENSIONS_INSTALL_LOCATION_3RD_PARTY 9005
#define IDS_EXTENSIONS_INSTALL_LOCATION_ENTERPRISE 9006
#define IDS_EXTENSIONS_INSTALL_LOCATION_SHARED_MODULE 9007
#define IDS_EXTENSIONS_BLOCKLISTED_MALWARE 9008
#define IDS_EXTENSIONS_BLOCKLISTED_SECURITY_VULNERABILITY 9009
#define IDS_EXTENSIONS_BLOCKLISTED_CWS_POLICY_VIOLATION 9010
#define IDS_EXTENSIONS_BLOCKLISTED_POTENTIALLY_UNWANTED 9011
#define IDS_EXTENSIONS_SAFE_BROWSING_CRX_ALLOWLIST_WARNING 9012
#define IDS_RESET_PROFILE_SETTINGS_EXPLANATION 9013
#define IDS_RESET_PROFILE_SETTINGS_EXPLANATION_IN_BULLET_POINTS 9014
#define IDS_TRIGGERED_RESET_PROFILE_SETTINGS_TITLE 9015
#define IDS_TRIGGERED_RESET_PROFILE_SETTINGS_EXPLANATION 9016
#define IDS_TRIGGERED_RESET_PROFILE_SETTINGS_DEFAULT_TOOL_NAME 9017
#define IDS_RESET_PROFILE_SETTINGS_LOCALE 9018
#define IDS_RESET_PROFILE_SETTINGS_STARTUP_URLS 9019
#define IDS_RESET_PROFILE_SETTINGS_STARTUP_TYPE 9020
#define IDS_RESET_PROFILE_SETTINGS_HOMEPAGE 9021
#define IDS_RESET_PROFILE_SETTINGS_HOMEPAGE_IS_NTP 9022
#define IDS_RESET_PROFILE_SETTINGS_YES 9023
#define IDS_RESET_PROFILE_SETTINGS_NO 9024
#define IDS_RESET_PROFILE_SETTINGS_SHOW_HOME_BUTTON 9025
#define IDS_RESET_PROFILE_SETTINGS_DSE 9026
#define IDS_RESET_PROFILE_SETTINGS_EXTENSIONS 9027
#define IDS_RESET_PROFILE_SETTINGS_SHORTCUTS 9028
#define IDS_RESET_PROFILE_SETTINGS_PROCESSING_SHORTCUTS 9029
#define IDS_AUTOFILL_DIALOG_PLACEHOLDER_EXPIRY_MONTH 9031
#define IDS_AUTOFILL_DIALOG_PLACEHOLDER_EXPIRY_YEAR 9032
#define IDS_AUTOFILL_FROM_GOOGLE_ACCOUNT 9033
#define IDS_OMNIBOX_ICON_SEND_TAB_TO_SELF 280
#define IDS_OMNIBOX_TOOLTIP_SEND_TAB_TO_SELF 9035
#define IDS_OMNIBOX_BUBBLE_ITEM_SUBTITLE_TODAY_SEND_TAB_TO_SELF 9036
#define IDS_OMNIBOX_BUBBLE_ITEM_SUBTITLE_DAY_SEND_TAB_TO_SELF 9037
#define IDS_OMNIBOX_BUBBLE_ITEM_SUBTITLE_DAYS_SEND_TAB_TO_SELF 9038
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_FROM_DEVICE 9039
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_TITLE 9040
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_BUTTON_LABEL 9041
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_BUTTON_A11Y_NAME 9042
#define IDS_TOOLBAR_BUTTON_SEND_TAB_TO_SELF_BUTTON_HINT_TEXT 9043
#define IDS_CONTEXT_MENU_SEND_TAB_TO_SELF 9044
#define IDS_SEND_TAB_TO_SELF_SENDING_ANNOUNCE 9047
#define IDS_SEND_TAB_TO_SELF_MANAGE_DEVICES_LINK 9048
#define IDS_SEND_TAB_TO_SELF_PROMO_LABEL 9049
#define IDS_CONTEXT_MENU_GENERATE_QR_CODE_PAGE 9050
#define IDS_CONTEXT_MENU_GENERATE_QR_CODE_IMAGE 9051
#define IDS_CONTEXT_MENU_GENERATE_QR_CODE_LINK 9052
#define IDS_OMNIBOX_QRCODE_GENERATOR_ICON_LABEL 9053
#define IDS_OMNIBOX_QRCODE_GENERATOR_ICON_TOOLTIP 9054
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_TITLE 9055
#define IDS_SHARING_HUB_GENERATE_QR_CODE_LABEL 9056
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_URL_TEXTFIELD_ACCESSIBLE_NAME 9057
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_TOOLTIP 9058
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_DOWNLOAD_BUTTON_LABEL 9059
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_ERROR_TOO_LONG 9060
#define IDS_BROWSER_SHARING_QR_CODE_DIALOG_ERROR_UNKNOWN 9061
#define IDS_SHARING_HUB_TITLE 9062
#define IDS_SHARING_HUB_TOOLTIP 9063
#define IDS_SHARING_HUB_SCREENSHOT_LABEL 9064
#define IDS_SHARING_HUB_COPY_LINK_LABEL 9065
#define IDS_SHARING_HUB_SAVE_PAGE_LABEL 9066
#define IDS_SHARING_HUB_MEDIA_ROUTER_LABEL 9067
#define IDS_SHARING_HUB_SHARE_LABEL 9068
#define IDS_SHARING_HUB_SHARE_LABEL_ACCESSIBILITY 9069
#define IDS_SHARING_HUB_FOLLOW_LABEL 9070
#define IDS_SHARING_HUB_FOLLOWING_LABEL 9071
#define IDS_BROWSER_SHARING_SCREENSHOT_POST_CAPTURE_TITLE 9072
#define IDS_BROWSER_SHARING_SCREENSHOT_DIALOG_DOWNLOAD_BUTTON_LABEL 9073
#define IDS_BROWSER_SHARING_SCREENSHOT_DIALOG_EDIT_BUTTON_LABEL 9074
#define IDS_BROWSER_SHARING_SCREENSHOT_DIALOG_SHARE_BUTTON_LABEL 9075
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_SELECTION 9076
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_CROP 9077
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_TEXT 9078
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ELLIPSE 9079
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_RECTANGLE 9080
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_LINE 9081
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ARROW 9082
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_BRUSH 9083
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_EMOJI 9084
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_HIGHLIGHTER 9085
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_UNDO 9086
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_REDO 9087
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ZOOM_IN 9088
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_ZOOM_OUT 9089
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_COMMIT_CROP 9090
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_COMMIT_CROP 9091
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_CANCEL_CROP 9092
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_TOOLTIP_CANCEL_CROP 9093
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_BUTTON_DOWNLOAD_IMAGE 9094
#define IDS_BROWSER_SHARING_SCREENSHOT_IMAGE_EDITOR_BUTTON_COPY 9095
#define IDS_CONTEXT_MENU_SHOW_CLIPBOARD_HISTORY_MENU 9096
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_TEXT_CONTENT_UNKNOWN_DEVICE 9097
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_TEXT_CONTENT 9098
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_IMAGE_CONTENT_UNKNOWN_DEVICE 9099
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_TITLE_IMAGE_CONTENT 9100
#define IDS_SHARING_REMOTE_COPY_NOTIFICATION_DESCRIPTION 9101
#define IDS_CONTENT_CONTEXT_SHARING_CLICK_TO_CALL_MULTIPLE_DEVICES 9102
#define IDS_CONTENT_CONTEXT_SHARING_CLICK_TO_CALL_SINGLE_DEVICE 9103
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_MULTIPLE_DEVICES 9104
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_SINGLE_DEVICE 9105
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_NOTIFICATION_TITLE_UNKNOWN_DEVICE 9106
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_NOTIFICATION_TITLE 9107
#define IDS_CONTENT_CONTEXT_SHARING_SHARED_CLIPBOARD_NOTIFICATION_DESCRIPTION 9108
#define IDS_OMNIBOX_TOOLTIP_SHARED_CLIPBOARD 9109
#define IDS_COLLECTED_COOKIES_DIALOG_TITLE 9110
#define IDS_COLLECTED_COOKIES_ALLOWED_COOKIES_LABEL 9111
#define IDS_COLLECTED_COOKIES_BLOCKED_COOKIES_LABEL 9112
#define IDS_COLLECTED_COOKIES_BLOCKED_THIRD_PARTY_BLOCKING_ENABLED 9113
#define IDS_COLLECTED_COOKIES_ALLOW_BUTTON 9114
#define IDS_COLLECTED_COOKIES_SESSION_ONLY_BUTTON 9115
#define IDS_COLLECTED_COOKIES_BLOCK_BUTTON 9116
#define IDS_COLLECTED_COOKIES_ALLOW_RULE_CREATED 9117
#define IDS_COLLECTED_COOKIES_BLOCK_RULE_CREATED 9118
#define IDS_COLLECTED_COOKIES_SESSION_RULE_CREATED 9119
#define IDS_COLLECTED_COOKIES_ALLOWED_COOKIES_TAB_LABEL 9120
#define IDS_COLLECTED_COOKIES_BLOCKED_COOKIES_TAB_LABEL 9121
#define IDS_COLLECTED_COOKIES_ALLOWED_AUX_TEXT 9122
#define IDS_COLLECTED_COOKIES_BLOCKED_AUX_TEXT 9123
#define IDS_COLLECTED_COOKIES_CLEAR_ON_EXIT_AUX_TEXT 9124
#define IDS_COLLECTED_COOKIES_INFOBAR_MESSAGE 9125
#define IDS_COLLECTED_COOKIES_INFOBAR_BUTTON 9126
#define IDS_COLLECTED_COOKIES_PARTITIONED_COOKIE 9127
#define IDS_ACCNAME_INFOBAR_CONTAINER 9128
#define IDS_ACCNAME_INFOBAR 9129
#define IDS_ONE_CLICK_BUBBLE_UNDO 9130
#define IDS_ONE_CLICK_SIGNIN_BUBBLE_MESSAGE 9131
#define IDS_ONE_CLICK_SIGNIN_DIALOG_OK_BUTTON 9132
#define IDS_ONE_CLICK_SIGNIN_DIALOG_UNDO_BUTTON 9133
#define IDS_ONE_CLICK_SIGNIN_DIALOG_ADVANCED 9134
#define IDS_ENTERPRISE_SIGNIN_CANCEL 9135
#define IDS_ENTERPRISE_SIGNIN_CREATE_NEW_PROFILE 9136
#define IDS_ENTERPRISE_SIGNIN_CREATE_NEW_WORK_PROFILE 9137
#define IDS_ENTERPRISE_SIGNIN_CONTINUE 9138
#define IDS_ENTERPRISE_SIGNIN_ALERT 9139
#define IDS_PROFILE_WILL_BE_DELETED_DIALOG_TITLE 9140
#define IDS_PROFILE_WILL_BE_DELETED_DIALOG_DESCRIPTION 9141
#define IDS_MANAGED_WITH_HYPERLINK 9144
#define IDS_MANAGED_BY_WITH_HYPERLINK 9145
#define IDS_COOKIES_REMOVE_LABEL 9146
#define IDS_COOKIES_COOKIE_NAME_LABEL 9147
#define IDS_COOKIES_COOKIE_CONTENT_LABEL 9148
#define IDS_COOKIES_COOKIE_DOMAIN_LABEL 9149
#define IDS_COOKIES_COOKIE_PATH_LABEL 9150
#define IDS_COOKIES_COOKIE_SENDFOR_LABEL 9151
#define IDS_COOKIES_COOKIE_CREATED_LABEL 9152
#define IDS_COOKIES_COOKIE_EXPIRES_LABEL 9153
#define IDS_COOKIES_COOKIE_EXPIRES_SESSION 9154
#define IDS_COOKIES_COOKIE_SENDFOR_ANY 9155
#define IDS_COOKIES_COOKIE_SENDFOR_SECURE 9156
#define IDS_COOKIES_COOKIE_SENDFOR_SAME_SITE 9157
#define IDS_COOKIES_COOKIE_SENDFOR_SECURE_SAME_SITE 9158
#define IDS_COOKIES_COOKIE_ACCESSIBLE_TO_SCRIPT_YES 9159
#define IDS_COOKIES_COOKIE_ACCESSIBLE_TO_SCRIPT_NO 9160
#define IDS_COOKIES_COOKIE_NONESELECTED 9161
#define IDS_COOKIES_COOKIES 9162
#define IDS_COOKIES_WEB_DATABASES 9163
#define IDS_COOKIES_LOCAL_STORAGE 9164
#define IDS_COOKIES_SESSION_STORAGE 9165
#define IDS_COOKIES_INDEXED_DBS 9166
#define IDS_COOKIES_MEDIA_LICENSE 9167
#define IDS_COOKIES_MEDIA_LICENSES 9168
#define IDS_COOKIES_FILE_SYSTEM 9169
#define IDS_COOKIES_FILE_SYSTEMS 9170
#define IDS_COOKIES_FILE_SYSTEM_USAGE_NONE 9171
#define IDS_COOKIES_SERVICE_WORKER 9172
#define IDS_COOKIES_SERVICE_WORKERS 9173
#define IDS_COOKIES_SHARED_WORKERS 9174
#define IDS_COOKIES_CACHE_STORAGE 9175
#define IDS_CLIENT_CERT_ECDSA_SIGN 9176
#define IDS_APP_DEFAULT_PAGE_NAME 9177
#define IDS_APP_LAUNCHER_TAB_TITLE 9178
#define IDS_NEW_TAB_GUEST_SESSION_HEADING 9179
#define IDS_NEW_TAB_GUEST_SESSION_DESCRIPTION 9180
#define IDS_NEW_TAB_TILE_GRID_ACCESSIBLE_DESCRIPTION 9181
#define IDS_NEW_TAB_APP_INSTALL_HINT_LABEL 9182
#define IDS_NEW_TAB_MOST_VISITED 180
#define IDS_NEW_TAB_RESTORE_THUMBNAILS_SHORT_LINK 177
#define IDS_NEW_TAB_ATTRIBUTION_INTRO 178
#define IDS_NEW_TAB_THUMBNAIL_REMOVED_NOTIFICATION 9183
#define IDS_NEW_TAB_REMOVE_THUMBNAIL_TOOLTIP 175
#define IDS_NEW_TAB_PAGE_SWITCHER_CHANGE_TITLE 9184
#define IDS_NEW_TAB_PAGE_SWITCHER_SAME_TITLE 9185
#define IDS_NEW_TAB_VOICE_AUDIO_ERROR 217
#define IDS_NEW_TAB_VOICE_CLOSE_TOOLTIP 231
#define IDS_NEW_TAB_VOICE_DETAILS 218
#define IDS_NEW_TAB_VOICE_LANGUAGE_ERROR 220
#define IDS_NEW_TAB_VOICE_LISTENING 222
#define IDS_NEW_TAB_VOICE_NETWORK_ERROR 223
#define IDS_NEW_TAB_VOICE_NO_TRANSLATION 224
#define IDS_NEW_TAB_VOICE_NO_VOICE 225
#define IDS_NEW_TAB_VOICE_OTHER_ERROR 230
#define IDS_NEW_TAB_VOICE_PERMISSION_ERROR 226
#define IDS_NEW_TAB_VOICE_READY 227
#define IDS_NEW_TAB_VOICE_TRY_AGAIN 228
#define IDS_NEW_TAB_VOICE_WAITING 229
#define IDS_NEW_TAB_VOICE_SEARCH_CLOSED 232
#define IDS_NEW_TAB_APP_OPTIONS 9186
#define IDS_NEW_TAB_APP_DETAILS 9187
#define IDS_NEW_TAB_APP_CREATE_SHORTCUT 9188
#define IDS_NEW_TAB_APP_INSTALL_LOCALLY 9189
#define IDS_APP_CONTEXT_MENU_SHOW_INFO 9190
#define IDS_APP_CONTEXT_MENU_OPEN_PINNED 9191
#define IDS_APP_CONTEXT_MENU_OPEN_REGULAR 9192
#define IDS_APP_CONTEXT_MENU_OPEN_WINDOW 9193
#define IDS_APP_CONTEXT_MENU_OPEN_FULLSCREEN 9194
#define IDS_APP_CONTEXT_MENU_OPEN_MAXIMIZED 9195
#define IDS_APP_CONTEXT_MENU_OPEN_TAB 9196
#define IDS_APP_CONTEXT_MENU_RUN_ON_OS_LOGIN 9201
#define IDS_APPS_PAGE_DEPRECATED_APP_TITLE 9204
#define IDS_SYNC_CONFIRMATION_TITLE 9205
#define IDS_SYNC_CONFIRMATION_SYNC_INFO_TITLE 9206
#define IDS_SYNC_CONFIRMATION_SYNC_INFO_DESC 9207
#define IDS_SYNC_CONFIRMATION_SETTINGS_INFO 9208
#define IDS_SYNC_CONFIRMATION_CONFIRM_BUTTON_LABEL 9209
#define IDS_SYNC_CONFIRMATION_SETTINGS_BUTTON_LABEL 9210
#define IDS_SYNC_CONFIRMATION_REFRESHED_SETTINGS_BUTTON_LABEL 9211
#define IDS_SYNC_DISABLED_CONFIRMATION_CHROME_SYNC_TITLE 9214
#define IDS_SYNC_DISABLED_CONFIRMATION_DETAILS 9215
#define IDS_SYNC_DISABLED_CONFIRMATION_CONFIRM_BUTTON_LABEL 9216
#define IDS_SYNC_DISABLED_CONFIRMATION_UNDO_BUTTON_LABEL 9217
#define IDS_SYNC_LOADING_CONFIRMATION_TITLE 9218
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_NEW_PROFILE_BUTTON_LABEL 9219
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_NEW_PROFILE_BUTTON_LABEL_V2 9220
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CANCEL_BUTTON_LABEL 9221
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CANCEL_SWITCH_BUTTON_LABEL 9222
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CONFIRM_SWITCH_BUTTON_LABEL 9223
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_CONFIRM_SWITCH_BUTTON_LABEL_V2 9224
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_BUBBLE_GUEST_LINK 9225
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_ENTERPRISE_BUBBLE_DESC 9226
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_SWITCH_BUBBLE_DESC_V2 9227
#define IDS_SIGNIN_DICE_WEB_INTERCEPT_ENTERPRISE_PROFILE_NAME 9228
#define IDS_SIGNIN_ERROR_TITLE 9229
#define IDS_SIGNIN_ERROR_EMAIL_TITLE 9230
#define IDS_SIGNIN_ERROR_DICE_EMAIL_TITLE 9231
#define IDS_SIGNIN_ERROR_CLOSE_BUTTON_LABEL 9232
#define IDS_SIGNIN_ERROR_OK_BUTTON_LABEL 9233
#define IDS_SIGNIN_ERROR_SWITCH_BUTTON_LABEL 9234
#define IDS_SIGNIN_ACCESSIBLE_CLOSE_BUTTON 9235
#define IDS_SIGNIN_ACCESSIBLE_BACK_BUTTON 9236
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CREATE_PROFILE_RADIO_BUTTON_TITLE 9237
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CREATE_PROFILE_RADIO_BUTTON_SUBTITLE 9238
#define IDS_SIGNIN_EMAIL_CONFIRMATION_START_SYNC_RADIO_BUTTON_TITLE 9239
#define IDS_SIGNIN_EMAIL_CONFIRMATION_START_SYNC_RADIO_BUTTON_SUBTITLE 9240
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CLOSE_BUTTON_LABEL 9241
#define IDS_SIGNIN_EMAIL_CONFIRMATION_CONFIRM_BUTTON_LABEL 9242
#define IDS_ACCOUNT_PASSWORDS_REAUTH_TITLE 9243
#define IDS_ACCOUNT_PASSWORDS_REAUTH_DESC 9244
#define IDS_ACCOUNT_PASSWORDS_REAUTH_DESC_ALREADY_SAVED_LOCALLY 9245
#define IDS_ACCOUNT_PASSWORDS_REAUTH_CONFIRM_BUTTON_LABEL 9246
#define IDS_ACCOUNT_PASSWORDS_REAUTH_CLOSE_BUTTON_LABEL 9247
#define IDS_ACCOUNT_PASSWORDS_REAUTH_CLOSE_BUTTON_LABEL_ALREADY_SAVED_LOCALLY 9248
#define IDS_PLUGIN_OUTDATED_PROMPT 9249
#define IDS_PLUGIN_ENABLE_TEMPORARILY 9250
#define IDS_PLUGIN_CRASHED_PROMPT 9251
#define IDS_PLUGIN_DISCONNECTED_PROMPT 9252
#define IDS_RELOAD_PAGE_WITH_PLUGIN 9253
#define IDS_PLUGIN_INITIALIZATION_ERROR_PROMPT 9254
#define IDS_EXTERNAL_PROTOCOL_TITLE 9255
#define IDS_EXTERNAL_PROTOCOL_MESSAGE_WITH_INITIATING_ORIGIN 9256
#define IDS_EXTERNAL_PROTOCOL_MESSAGE 9257
#define IDS_EXTERNAL_PROTOCOL_OK_BUTTON_TEXT 9258
#define IDS_EXTERNAL_PROTOCOL_CANCEL_BUTTON_TEXT 9259
#define IDS_EXTERNAL_PROTOCOL_CHECKBOX_PER_ORIGIN_TEXT 9260
#define IDS_DIRECTORY_LISTING_HEADER 9262
#define IDS_DIRECTORY_LISTING_PARENT 9263
#define IDS_DIRECTORY_LISTING_NAME 9264
#define IDS_DIRECTORY_LISTING_SIZE 9265
#define IDS_DIRECTORY_LISTING_DATE_MODIFIED 9266
#define IDS_SAVE_PAGE_DESC_HTML_ONLY 9267
#define IDS_SAVE_PAGE_DESC_SINGLE_FILE 9268
#define IDS_SAVE_PAGE_DESC_COMPLETE 9269
#define IDS_SAVE_PAGE_DESC_WEB_BUNDLE_FILE 9270
#define IDS_PROFILE_ERROR_DIALOG_TITLE 9271
#define IDS_COULDNT_OPEN_PROFILE_ERROR 9272
#define IDS_OPEN_PROFILE_DATA_LOSS 9273
#define IDS_PROFILE_ERROR_DIALOG_CHECKBOX 9274
#define IDS_PROFILE_ERROR_FEEDBACK_DESCRIPTION 9275
#define IDS_COULDNT_STARTUP_PROFILE_ERROR 9276
#define IDS_REFUSE_TO_RUN_AS_ROOT 9277
#define IDS_REFUSE_TO_RUN_AS_ROOT_2 9278
#define IDS_PROFILE_ON_NETWORK_WARNING 9279
#define IDS_CANT_WRITE_USER_DIRECTORY_TITLE 9280
#define IDS_RECENT_TABS_MENU 9281
#define IDS_RECENTLY_CLOSED_WINDOW 320
#define IDS_RECENTLY_CLOSED_GROUP 9282
#define IDS_RECENTLY_CLOSED_GROUP_UNNAMED 9283
#define IDS_RECENT_TABS_NO_DEVICE_TABS 321
#define IDS_HISTORY_MENU 322
#define IDS_DEFAULT_DOWNLOAD_FILENAME 9284
#define IDS_DEFAULT_BROWSER_INFOBAR_OK_BUTTON_LABEL 302
#define IDS_USED_EXISTING_BROWSER 9291
#define IDS_DECLINE_RECOVERY 9292
#define IDS_SYNC_ACCOUNT_SYNCING 9293
#define IDS_SYNC_ACCOUNT_SYNCING_CUSTOM_DATA_TYPES 9294
#define IDS_SIGNIN_ERROR_DISPLAY_SOURCE 9295
#define IDS_SIGNIN_ERROR_BUBBLE_VIEW_TITLE 9296
#define IDS_SYNC_ERROR_BUBBLE_VIEW_TITLE 9297
#define IDS_SYNC_ERROR_PASSWORDS_BUBBLE_VIEW_TITLE 9298
#define IDS_SYNC_ERROR_USER_MENU_TITLE 9299
#define IDS_SYNC_ERROR_PASSWORDS_USER_MENU_TITLE 9300
#define IDS_SYNC_ERROR_PASSWORDS_USER_MENU_TITLE_SIGNED_IN_ONLY 9301
#define IDS_SYNC_ERROR_RECOVERABILITY_DEGRADED_FOR_EVERYTHING_USER_MENU_TITLE 9302
#define IDS_SYNC_ERROR_RECOVERABILITY_DEGRADED_FOR_PASSWORDS_USER_MENU_TITLE 9303
#define IDS_SYNC_ERROR_USER_MENU_SIGNIN_BUTTON 9304
#define IDS_SYNC_NEEDS_VERIFICATION_BUBBLE_VIEW_TITLE 9305
#define IDS_SYNC_ERROR_USER_MENU_PASSPHRASE_BUTTON 9306
#define IDS_SYNC_ERROR_USER_MENU_RECOVERABILITY_BUTTON 9307
#define IDS_SYNC_ERROR_USER_MENU_RETRIEVE_KEYS_BUTTON 9308
#define IDS_SYNC_ERROR_USER_MENU_SIGNOUT_BUTTON 9309
#define IDS_SYNC_ERROR_USER_MENU_CONFIRM_SYNC_SETTINGS_BUTTON 9310
#define IDS_SYNC_OVERVIEW 9316
#define IDS_SYNC_START_SYNC_BUTTON_LABEL 9317
#define IDS_SIGNED_IN_WITH_SYNC_DISABLED_BY_POLICY 9318
#define IDS_SIGNED_IN_WITH_SYNC_STOPPED_VIA_DASHBOARD 9319
#define IDS_SYNC_SETTINGS_NOT_CONFIRMED 9320
#define IDS_SYNC_SETUP_IN_PROGRESS 9321
#define IDS_SYNC_STATUS_UNRECOVERABLE_ERROR 9322
#define IDS_SYNC_STATUS_UNRECOVERABLE_ERROR_NEEDS_SIGNOUT 9323
#define IDS_SYNC_STATUS_NEEDS_PASSWORD 9324
#define IDS_SYNC_STATUS_NEEDS_PASSWORD_BUTTON 9325
#define IDS_SYNC_STATUS_NEEDS_KEYS_BUTTON 9326
#define IDS_SYNC_SERVER_IS_UNREACHABLE 9327
#define IDS_SYNC_RELOGIN_ERROR 9328
#define IDS_SYNC_RELOGIN_BUTTON 9329
#define IDS_SYNC_ADVANCED_OPTIONS 9334
#define IDS_SYNC_FULL_ENCRYPTION_BODY_CUSTOM 9336
#define IDS_SYNC_FULL_ENCRYPTION_BODY_CUSTOM_WITH_DATE 9337
#define IDS_TRANSLATE_BUBBLE_BEFORE_TRANSLATE_TITLE 9338
#define IDS_TRANSLATE_BUBBLE_TRANSLATED_TITLE 9339
#define IDS_TRANSLATE_BUBBLE_COULD_NOT_TRANSLATE_TITLE 9340
#define IDS_TRANSLATE_BUBBLE_SOURCE_LANG_COMBOBOX_ACCNAME 9341
#define IDS_TRANSLATE_BUBBLE_TARGET_LANG_COMBOBOX_ACCNAME 9342
#define IDS_TRANSLATE_BUBBLE_TRANSLATION_COMPLETE_ANNOUNCEMENT 9343
#define IDS_TRANSLATE_BUBBLE_ADVANCED_BUTTON 9344
#define IDS_TRANSLATE_BUBBLE_ADVANCED_MENU_BUTTON 9345
#define IDS_TRANSLATE_BUBBLE_CHANGE_TARGET_LANGUAGE 9346
#define IDS_TRANSLATE_BUBBLE_CHANGE_SOURCE_LANGUAGE 9347
#define IDS_TRANSLATE_BUBBLE_ACCEPT 9348
#define IDS_TRANSLATE_BUBBLE_DENY 9349
#define IDS_TRANSLATE_BUBBLE_ALWAYS_TRANSLATE_LANG 9350
#define IDS_TRANSLATE_BUBBLE_NEVER_TRANSLATE_LANG 9351
#define IDS_TRANSLATE_BUBBLE_NEVER_TRANSLATE_SITE 9352
#define IDS_TRANSLATE_BUBBLE_TRANSLATING 9353
#define IDS_TRANSLATE_BUBBLE_REVERT 9354
#define IDS_TRANSLATE_BUBBLE_TRY_AGAIN 9355
#define IDS_TRANSLATE_BUBBLE_ALWAYS 9356
#define IDS_TRANSLATE_BUBBLE_OPTIONS_MENU_BUTTON 9357
#define IDS_TRANSLATE_BUBBLE_PAGE_LANGUAGE 9358
#define IDS_TRANSLATE_BUBBLE_TRANSLATION_LANGUAGE 9359
#define IDS_TRANSLATE_BUBBLE_ADVANCED_TARGET 9360
#define IDS_TRANSLATE_BUBBLE_ADVANCED_SOURCE 9361
#define IDS_TRANSLATE_BUBBLE_RESET 9362
#define IDS_NOTIFICATION_BUTTON_SETTINGS 9363
#define IDS_NOTIFICATION_BUTTON_CLOSE 9364
#define IDS_NOTIFICATION_BUTTON_MORE 9365
#define IDS_NOTIFICATION_REPLY_PLACEHOLDER 9366
#define IDS_NOTIFICATION_MUTED_MESSAGE 9367
#define IDS_NOTIFICATION_MUTED_TITLE 9368
#define IDS_NOTIFICATION_MUTED_ACTION_SHOW 9369
#define IDS_NOTIFICATION_MUTED_ACTION_SNOOZE 9370
#define IDS_ALLOWED_GEOLOCATION_TITLE 9508
#define IDS_BLOCKED_GEOLOCATION_TITLE 9509
#define IDS_ALLOWED_GEOLOCATION_MESSAGE 9510
#define IDS_ALLOWED_GEOLOCATION_BLOCK 9511
#define IDS_ALLOWED_GEOLOCATION_NO_ACTION 9512
#define IDS_BLOCKED_GEOLOCATION_MESSAGE 9513
#define IDS_BLOCKED_GEOLOCATION_UNBLOCK 9514
#define IDS_BLOCKED_GEOLOCATION_NO_ACTION 9515
#define IDS_GEOLOCATION 9516
#define IDS_GEOLOCATION_WILL_ASK_AGAIN 9517
#define IDS_ALLOWED_MIDI_SYSEX_TITLE 9521
#define IDS_BLOCKED_MIDI_SYSEX_TITLE 9522
#define IDS_ALLOWED_MIDI_SYSEX_MESSAGE 9523
#define IDS_ALLOWED_MIDI_SYSEX_BLOCK 9524
#define IDS_ALLOWED_MIDI_SYSEX_NO_ACTION 9525
#define IDS_BLOCKED_MIDI_SYSEX_MESSAGE 9526
#define IDS_BLOCKED_MIDI_SYSEX_UNBLOCK 9527
#define IDS_BLOCKED_MIDI_SYSEX_NO_ACTION 9528
#define IDS_MICROPHONE_CAMERA_ALLOWED 9529
#define IDS_MICROPHONE_CAMERA_BLOCKED 9530
#define IDS_MICROPHONE_ACCESSED 9531
#define IDS_CAMERA_ACCESSED 9532
#define IDS_MICROPHONE_BLOCKED 9533
#define IDS_CAMERA_BLOCKED 9534
#define IDS_MICROPHONE_CAMERA_ALLOWED_TITLE 9535
#define IDS_MICROPHONE_CAMERA_BLOCKED_TITLE 9536
#define IDS_MICROPHONE_ACCESSED_TITLE 9537
#define IDS_CAMERA_ACCESSED_TITLE 9538
#define IDS_MICROPHONE_BLOCKED_TITLE 9539
#define IDS_CAMERA_BLOCKED_TITLE 9540
#define IDS_MEDIASTREAM_SETTING_CHANGED_MESSAGE 9541
#define IDS_A11Y_OMNIBOX_CHIP_HINT 9551
#define IDS_MANAGE_PASSWORDS_CONFIRM_GENERATED_TEXT 9552
#define IDS_PASSWORDS_WEB_LINK 9553
#define IDS_MANAGE_PASSWORDS_LINK 9555
#define IDS_MANAGE_PASSWORDS_TITLE 9556
#define IDS_MANAGE_PASSWORDS_NO_PASSWORDS_TITLE 9557
#define IDS_MANAGE_PASSWORDS_DIFFERENT_DOMAIN_TITLE 9558
#define IDS_MANAGE_PASSWORDS_DIFFERENT_DOMAIN_NO_PASSWORDS_TITLE 9559
#define IDS_MANAGE_PASSWORDS_ACCOUNT_STORE_ICON_DESCRIPTION 9560
#define IDS_MANAGE_PASSWORDS_DELETED 9561
#define IDS_MANAGE_PASSWORDS_UNDO 9562
#define IDS_MANAGE_PASSWORDS_UNDO_TOOLTIP 9563
#define IDS_MANAGE_PASSWORDS_DELETE 9564
#define IDS_MANAGE_PASSWORDS_SHOW_PASSWORD 9565
#define IDS_MANAGE_PASSWORDS_HIDE_PASSWORD 9566
#define IDS_MANAGE_PASSWORDS_AUTO_SIGNIN_TITLE_MD 9567
#define IDS_AUTO_SIGNIN_FIRST_RUN_TITLE_MANY_DEVICES 9568
#define IDS_AUTO_SIGNIN_FIRST_RUN_TITLE_LOCAL_DEVICE 9569
#define IDS_AUTO_SIGNIN_FIRST_RUN_TEXT 9570
#define IDS_AUTO_SIGNIN_FIRST_RUN_OK 9571
#define IDS_FILE_SELECTION_DIALOG_INFOBAR 9580
#define IDS_IMAGE_FILES 9583
#define IDS_AUDIO_FILES 9584
#define IDS_VIDEO_FILES 9585
#define IDS_CUSTOM_FILES 9586
#define IDS_FULLSCREEN_USER_ENTERED_FULLSCREEN 9587
#define IDS_FULLSCREEN_EXTENSION_TRIGGERED_FULLSCREEN 9588
#define IDS_FULLSCREEN_UNKNOWN_EXTENSION_TRIGGERED_FULLSCREEN 9589
#define IDS_FULLSCREEN_SITE_ENTERED_FULLSCREEN 9590
#define IDS_FULLSCREEN_ENTERED_FULLSCREEN 9591
#define IDS_FULLSCREEN_SITE_ENTERED_FULLSCREEN_MOUSELOCK 9592
#define IDS_FULLSCREEN_ENTERED_FULLSCREEN_MOUSELOCK 9593
#define IDS_FULLSCREEN_SITE_ENTERED_MOUSELOCK 9594
#define IDS_FULLSCREEN_ENTERED_MOUSELOCK 9595
#define IDS_FULLSCREEN_PRESS_ESC_TO_EXIT_MOUSELOCK 9596
#define IDS_SENSORS_ALLOWED_TOOLTIP 9597
#define IDS_MOTION_SENSORS_ALLOWED_TOOLTIP 9598
#define IDS_SENSORS_BLOCKED_TOOLTIP 9599
#define IDS_MOTION_SENSORS_BLOCKED_TOOLTIP 9600
#define IDS_BLOCKED_SENSORS_UNBLOCK 9601
#define IDS_BLOCKED_SENSORS_NO_ACTION 9602
#define IDS_ALLOWED_SENSORS_TITLE 9603
#define IDS_BLOCKED_SENSORS_TITLE 9604
#define IDS_ALLOWED_SENSORS_MESSAGE 9605
#define IDS_ALLOWED_MOTION_SENSORS_MESSAGE 9606
#define IDS_BLOCKED_SENSORS_MESSAGE 9607
#define IDS_BLOCKED_MOTION_SENSORS_MESSAGE 9608
#define IDS_ALLOWED_SENSORS_NO_ACTION 9609
#define IDS_ALLOWED_SENSORS_BLOCK 9610
#define IDS_LIVE_CAPTION_PROMO 9617
#define IDS_LIVE_CAPTION_PROMO_SCREENREADER 9618
#define IDS_ENABLE_CARET_BROWSING_INFO 9619
#define IDS_CARET_BROWSING_DO_NOT_ASK 9620
#define IDS_ENABLE_CARET_BROWSING_TITLE 9621
#define IDS_ENABLE_CARET_BROWSING_TURN_ON 9622
#define IDS_MEDIA_GALLERIES_DIALOG_HEADER 9623
#define IDS_MEDIA_GALLERIES_DIALOG_SUBTEXT_READ_WRITE 9624
#define IDS_MEDIA_GALLERIES_DIALOG_SUBTEXT_READ_DELETE 9625
#define IDS_MEDIA_GALLERIES_DIALOG_SUBTEXT_READ_ONLY 9626
#define IDS_MEDIA_GALLERIES_PERMISSION_SUGGESTIONS 9627
#define IDS_MEDIA_GALLERIES_LAST_ATTACHED 9628
#define IDS_MEDIA_GALLERIES_DIALOG_ADD_GALLERY 9629
#define IDS_MEDIA_GALLERIES_DIALOG_CONFIRM 9630
#define IDS_MEDIA_GALLERIES_DIALOG_ADD_GALLERY_TITLE 9631
#define IDS_MEDIA_GALLERIES_DIALOG_DEVICE_ATTACHED 9632
#define IDS_MEDIA_GALLERIES_DIALOG_DEVICE_NOT_ATTACHED 9633
#define IDS_MEDIA_GALLERIES_DIALOG_DELETE 9634
#define IDS_CHROME_SHORTCUT_DESCRIPTION 9654
#define IDS_WEBSTORE_NAME_STORE 154
#define IDS_DESKTOP_MEDIA_PICKER_TITLE 9655
#define IDS_DESKTOP_MEDIA_PICKER_TITLE_SCREEN_ONLY 9656
#define IDS_DESKTOP_MEDIA_PICKER_TITLE_WINDOW_ONLY 9657
#define IDS_DESKTOP_MEDIA_PICKER_TEXT 9658
#define IDS_DESKTOP_MEDIA_PICKER_TEXT_DELEGATED 9659
#define IDS_DESKTOP_MEDIA_PICKER_AUDIO_SHARE_SCREEN 9660
#define IDS_DESKTOP_MEDIA_PICKER_AUDIO_SHARE_WINDOW 9661
#define IDS_DESKTOP_MEDIA_PICKER_AUDIO_SHARE_TAB 9662
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_SCREEN 9663
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_WINDOW 9664
#define IDS_DESKTOP_MEDIA_PICKER_SINGLE_SCREEN_NAME 9665
#define IDS_DESKTOP_MEDIA_PICKER_MULTIPLE_SCREEN_NAME 9666
#define IDS_DESKTOP_MEDIA_PICKER_MANAGED 9667
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_THIS_TAB 9668
#define IDS_DESKTOP_MEDIA_PICKER_SOURCE_TYPE_OTHER_TAB 9669
#define IDS_DESKTOP_MEDIA_PICKER_EMPTY_PREVIEW 9670
#define IDS_DESKTOP_MEDIA_PICKER_PREVIEW_ACCESSIBLE_NAME 9671
#define IDS_DESKTOP_MEDIA_SOURCE_EMPTY_ACCESSIBLE_NAME 9672
#define IDS_TAB_CAPTURE_TERMINATED_BY_POLICY_TITLE 9673
#define IDS_TAB_CAPTURE_TERMINATED_BY_POLICY_TEXT 9674
#define IDS_TOOLTIP_TAB_ALERT_STATE_MEDIA_RECORDING 9675
#define IDS_TOOLTIP_TAB_ALERT_STATE_TAB_CAPTURING 9676
#define IDS_TOOLTIP_TAB_ALERT_STATE_AUDIO_PLAYING 9677
#define IDS_TOOLTIP_TAB_ALERT_STATE_AUDIO_MUTING 9678
#define IDS_TOOLTIP_TAB_ALERT_STATE_BLUETOOTH_CONNECTED 9679
#define IDS_TOOLTIP_TAB_ALERT_STATE_BLUETOOTH_SCAN_ACTIVE 9680
#define IDS_TOOLTIP_TAB_ALERT_STATE_USB_CONNECTED 9681
#define IDS_TOOLTIP_TAB_ALERT_STATE_HID_CONNECTED 9682
#define IDS_TOOLTIP_TAB_ALERT_STATE_SERIAL_CONNECTED 9683
#define IDS_TOOLTIP_TAB_ALERT_STATE_PIP_PLAYING 9684
#define IDS_TOOLTIP_TAB_ALERT_STATE_DESKTOP_CAPTURING 9685
#define IDS_TOOLTIP_TAB_ALERT_STATE_VR_PRESENTING 9686
#define IDS_TAB_AX_LABEL_MEDIA_RECORDING_FORMAT 9687
#define IDS_TAB_AX_LABEL_TAB_CAPTURING_FORMAT 9688
#define IDS_TAB_AX_LABEL_PIP_PLAYING_FORMAT 9689
#define IDS_TAB_AX_LABEL_AUDIO_PLAYING_FORMAT 9690
#define IDS_TAB_AX_LABEL_AUDIO_MUTING_FORMAT 9691
#define IDS_TAB_AX_LABEL_BLUETOOTH_CONNECTED_FORMAT 9692
#define IDS_TAB_AX_LABEL_BLUETOOTH_SCAN_ACTIVE_FORMAT 9693
#define IDS_TAB_AX_LABEL_USB_CONNECTED_FORMAT 9694
#define IDS_TAB_AX_LABEL_HID_CONNECTED_FORMAT 9695
#define IDS_TAB_AX_LABEL_SERIAL_CONNECTED_FORMAT 9696
#define IDS_TAB_AX_LABEL_NETWORK_ERROR_FORMAT 9697
#define IDS_TAB_AX_LABEL_CRASHED_FORMAT 9698
#define IDS_TAB_AX_LABEL_DESKTOP_CAPTURING_FORMAT 9699
#define IDS_TAB_AX_LABEL_VR_PRESENTING 9700
#define IDS_TAB_AX_LABEL_UNNAMED_GROUP_FORMAT 9701
#define IDS_TAB_AX_LABEL_NAMED_GROUP_FORMAT 9702
#define IDS_TAB_AX_LABEL_PERMISSION_REQUESTED_FORMAT 9703
#define IDS_GROUP_AX_LABEL_UNNAMED_GROUP_FORMAT 9704
#define IDS_GROUP_AX_LABEL_NAMED_GROUP_FORMAT 9705
#define IDS_GROUP_AX_LABEL_COLLAPSED 9706
#define IDS_GROUP_AX_LABEL_EXPANDED 9707
#define IDS_TAB_AX_ANNOUNCE_MOVED_RIGHT 9708
#define IDS_TAB_AX_ANNOUNCE_MOVED_LEFT 9709
#define IDS_TAB_AX_ANNOUNCE_MOVED_FIRST 9710
#define IDS_TAB_AX_ANNOUNCE_MOVED_LAST 9711
#define IDS_TAB_AX_ANNOUNCE_TAB_ADDED_TO_UNNAMED_GROUP 9712
#define IDS_TAB_AX_ANNOUNCE_TAB_ADDED_TO_NAMED_GROUP 9713
#define IDS_TAB_AX_ANNOUNCE_TAB_REMOVED_FROM_UNNAMED_GROUP 9714
#define IDS_TAB_AX_ANNOUNCE_TAB_REMOVED_FROM_NAMED_GROUP 9715
#define IDS_PAGE_LOADING_AX_TITLE_FORMAT 9716
#define IDS_PROFILE_IN_USE_LINUX_QUIT 9717
#define IDS_PROFILE_IN_USE_LINUX_RELAUNCH 9718
#define IDS_DESKTOP_MEDIA_PICKER_SHARE 9729
#define IDS_PUSH_MESSAGING_GENERIC_NOTIFICATION_BODY 9730
#define IDS_DEVICE_PERMISSIONS_DIALOG_SELECT 9731
#define IDS_DEVICE_PERMISSIONS_DIALOG_LOADING_LABEL 9732
#define IDS_DEVICE_PERMISSIONS_DIALOG_LOADING_LABEL_TOOLTIP 9733
#define IDS_DEVICE_LOG_TITLE 9734
#define IDS_DEVICE_AUTO_REFRESH 9735
#define IDS_DEVICE_LOG_REFRESH 9736
#define IDS_DEVICE_LOG_CLEAR 9737
#define IDS_DEVICE_LOG_CLEAR_TYPES 9738
#define IDS_DEVICE_LOG_NO_ENTRIES 9739
#define IDS_DEVICE_LOG_LEVEL_LABEL 9740
#define IDS_DEVICE_LOG_LEVEL_ERROR 9741
#define IDS_DEVICE_LOG_LEVEL_USER 9742
#define IDS_DEVICE_LOG_LEVEL_EVENT 9743
#define IDS_DEVICE_LOG_LEVEL_DEBUG 9744
#define IDS_DEVICE_LOG_TYPE_LOGIN 9745
#define IDS_DEVICE_LOG_TYPE_NETWORK 9746
#define IDS_DEVICE_LOG_TYPE_POWER 9747
#define IDS_DEVICE_LOG_TYPE_BLUETOOTH 9748
#define IDS_DEVICE_LOG_TYPE_USB 9749
#define IDS_DEVICE_LOG_TYPE_HID 9750
#define IDS_DEVICE_LOG_TYPE_PRINTER 9751
#define IDS_DEVICE_LOG_TYPE_FIDO 9752
#define IDS_DEVICE_LOG_TYPE_SERIAL 9753
#define IDS_DEVICE_LOG_TYPE_CAMERA 9754
#define IDS_DEVICE_LOG_FILEINFO 9755
#define IDS_DEVICE_LOG_TIME_DETAIL 9756
#define IDS_DEVICE_LOG_ENTRY 9757
#define IDS_WEBUSB_DEVICE_DETECTED_NOTIFICATION 9758
#define IDS_WEBUSB_DEVICE_DETECTED_NOTIFICATION_TITLE 9759
#define IDS_DEFAULT_AUDIO_DEVICE_NAME 9768
#define IDS_COMMUNICATIONS_AUDIO_DEVICE_NAME 9769
#define IDS_BLUETOOTH_DEVICE_CHOOSER_PROMPT_EXTENSION_NAME 9770
#define IDS_BLUETOOTH_DEVICE_CHOOSER_TURN_ADAPTER_OFF 9771
#define IDS_BLUETOOTH_DEVICE_CHOOSER_TURN_ON_BLUETOOTH_LINK_TEXT 9772
#define IDS_BLUETOOTH_DEVICE_CHOOSER_AUTHORIZE_BLUETOOTH_LINK_TEXT 9773
#define IDS_BLUETOOTH_DEVICE_CHOOSER_RE_SCAN 9774
#define IDS_BLUETOOTH_DEVICE_CHOOSER_RE_SCAN_TOOLTIP 9775
#define IDS_DEVICE_CHOOSER_GET_HELP_LINK_WITH_SCANNING_STATUS 9776
#define IDS_DEVICE_CHOOSER_GET_HELP_LINK_WITH_RE_SCAN_LINK 9777
#define IDS_DEVICE_CHOOSER_PAIRED_STATUS_TEXT 9778
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_AND_PAIRED_STATUS_TEXT 9779
#define IDS_USB_DEVICE_CHOOSER_PROMPT_EXTENSION_NAME 9780
#define IDS_DEVICE_CHOOSER_ACCNAME_COMPATIBLE_DEVICES_LIST 9781
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_UNKNOWN_DEVICE_WITH_VENDOR_NAME 9782
#define IDS_DEVICE_CHOOSER_DEVICE_NAME_UNKNOWN_DEVICE_WITH_VENDOR_ID_AND_PRODUCT_ID 9783
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_PRODUCT_ID_AND_VENDOR_NAME 9784
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_PRODUCT_ID_AND_VENDOR_ID 9785
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_VENDOR_ID 9786
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_VENDOR_NAME 9787
#define IDS_USB_POLICY_DEVICE_DESCRIPTION_FOR_ANY_VENDOR 9788
#define IDS_SERIAL_PORT_CHOOSER_PROMPT_ORIGIN 9789
#define IDS_SERIAL_PORT_CHOOSER_PROMPT_EXTENSION_NAME 9790
#define IDS_SERIAL_PORT_CHOOSER_NAME_WITH_PATH 9791
#define IDS_SERIAL_PORT_CHOOSER_PATH_ONLY 9792
#define IDS_SERIAL_PORT_CHOOSER_CONNECT_BUTTON_TEXT 9793
#define IDS_SERIAL_PORT_CHOOSER_LOADING_LABEL 9794
#define IDS_SERIAL_PORT_CHOOSER_LOADING_LABEL_TOOLTIP 9795
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_PRODUCT_ID_AND_VENDOR_NAME 9796
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_PRODUCT_ID_AND_VENDOR_ID 9797
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_VENDOR_ID 9798
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_USB_VENDOR_NAME 9799
#define IDS_SERIAL_POLICY_DESCRIPTION_FOR_ANY_PORT 9800
#define IDS_HID_CHOOSER_PROMPT_ORIGIN 9801
#define IDS_HID_CHOOSER_PROMPT_EXTENSION_NAME 9802
#define IDS_HID_CHOOSER_ITEM_WITHOUT_NAME 9803
#define IDS_HID_CHOOSER_LOADING_LABEL 9804
#define IDS_HID_CHOOSER_LOADING_LABEL_TOOLTIP 9805
#define IDS_HID_POLICY_DESCRIPTION_FOR_VENDOR_ID_AND_PRODUCT_ID 9806
#define IDS_HID_POLICY_DESCRIPTION_FOR_VENDOR_ID 9807
#define IDS_HID_POLICY_DESCRIPTION_FOR_USAGE_AND_USAGE_PAGE 9808
#define IDS_HID_POLICY_DESCRIPTION_FOR_USAGE_PAGE 9809
#define IDS_HID_POLICY_DESCRIPTION_FOR_ANY_DEVICE 9810
#define IDS_WEB_APP_FILE_HANDLING_LIST_SEPARATOR 9811
#define IDS_WEB_APP_FILE_HANDLING_DIALOG_QUESTION 9812
#define IDS_WEB_APP_FILE_HANDLING_DIALOG_QUESTION_MULTIPLE 9813
#define IDS_WEB_APP_FILE_HANDLING_DIALOG_STICKY_CHOICE 9814
#define IDS_WEB_APP_FILE_HANDLING_POSITIVE_BUTTON 9815
#define IDS_WEB_APP_FILE_HANDLING_NEGATIVE_BUTTON 9816
#define IDS_WEB_APP_PERMISSION_NEGATIVE_BUTTON 9817
#define IDS_PROTOCOL_HANDLER_INTENT_PICKER_QUESTION 9818
#define IDS_FONT_ACCESS_CHOOSER_PROMPT_ORIGIN 9819
#define IDS_FONT_ACCESS_CHOOSER_NO_FONTS_FOUND_PROMPT 9820
#define IDS_FONT_ACCESS_CHOOSER_IMPORT_BUTTON_TEXT 9821
#define IDS_FONT_ACCESS_CHOOSER_CANCEL_BUTTON_TEXT 9822
#define IDS_FONT_ACCESS_CHOOSER_SELECT_ALL_CHECKBOX_TEXT 9823
#define IDS_FONT_ACCESS_CHOOSER_LOADING_LABEL 9824
#define IDS_FONT_ACCESS_CHOOSER_LOADING_LABEL_TOOLTIP 9825
#define IDS_IME_API_ACTIVATED_WARNING 9826
#define IDS_IME_API_NEVER_SHOW 9827
#define IDS_BLOCKED_ADS_PROMPT_TOOLTIP 9828
#define IDS_UTILITY_PROCESS_UTILITY_WIN_NAME 9829
#define IDS_UTILITY_PROCESS_QUARANTINE_SERVICE_NAME 9830
#define IDS_UTILITY_PROCESS_SYSTEM_SIGNALS_NAME 9831
#define IDS_REDIRECT_BLOCKED_MESSAGE 9832
#define IDS_REDIRECT_BLOCKED_TITLE 9833
#define IDS_REDIRECT_BLOCKED_TOOLTIP 9834
#define IDS_WIN10_TOAST_BROWSE_FAST 9835
#define IDS_WIN10_TOAST_BROWSE_SAFELY 9836
#define IDS_WIN10_TOAST_BROWSE_SMART 9837
#define IDS_WIN10_TOAST_SWITCH_FAST 9838
#define IDS_WIN10_TOAST_SWITCH_SMART 9839
#define IDS_WIN10_TOAST_SWITCH_SECURE 9840
#define IDS_WIN10_TOAST_SWITCH_SMART_AND_SECURE 9841
#define IDS_WIN10_TOAST_RECOMMENDATION 9842
#define IDS_WIN10_TOAST_OPEN_CHROME 9843
#define IDS_WIN10_TOAST_NO_THANKS 9844
#define IDS_CONTROLLED_BY_AUTOMATION 9847
#define IDS_DOWNLOAD_OPEN_CONFIRMATION_DIALOG_TITLE 9849
#define IDS_DOWNLOAD_OPEN_CONFIRMATION_DIALOG_MESSAGE 9850
#define IDS_CONFIRM_FILE_UPLOAD_TITLE 9851
#define IDS_CONFIRM_FILE_UPLOAD_TEXT 9852
#define IDS_CONFIRM_FILE_UPLOAD_OK_BUTTON 9853
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_TITLE 9854
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_ALLOW_TEXT 9855
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_WRITE_PERMISSION_FILE_TEXT 9856
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_WRITE_PERMISSION_DIRECTORY_TEXT 9857
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_READ_PERMISSION_FILE_TEXT 9858
#define IDS_FILE_SYSTEM_ACCESS_ORIGIN_SCOPED_READ_PERMISSION_DIRECTORY_TEXT 9859
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_FILE_TEXT 9860
#define IDS_FILE_SYSTEM_ACCESS_WRITE_PERMISSION_DIRECTORY_TEXT 9861
#define IDS_FILE_SYSTEM_ACCESS_READ_PERMISSION_FILE_TEXT 9862
#define IDS_FILE_SYSTEM_ACCESS_READ_PERMISSION_DIRECTORY_TEXT 9863
#define IDS_FILE_SYSTEM_ACCESS_EDIT_FILE_PERMISSION_TITLE 9864
#define IDS_FILE_SYSTEM_ACCESS_EDIT_DIRECTORY_PERMISSION_TITLE 9865
#define IDS_FILE_SYSTEM_ACCESS_READ_FILE_PERMISSION_TITLE 9866
#define IDS_FILE_SYSTEM_ACCESS_READ_DIRECTORY_PERMISSION_TITLE 9867
#define IDS_FILE_SYSTEM_ACCESS_EDIT_FILE_PERMISSION_ALLOW_TEXT 9868
#define IDS_FILE_SYSTEM_ACCESS_EDIT_DIRECTORY_PERMISSION_ALLOW_TEXT 9869
#define IDS_FILE_SYSTEM_ACCESS_VIEW_FILE_PERMISSION_ALLOW_TEXT 9870
#define IDS_FILE_SYSTEM_ACCESS_VIEW_DIRECTORY_PERMISSION_ALLOW_TEXT 9871
#define IDS_FILE_SYSTEM_ACCESS_WRITE_USAGE_TOOLTIP 9872
#define IDS_FILE_SYSTEM_ACCESS_DIRECTORY_USAGE_TOOLTIP 9873
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_WRITABLE_FILE_TEXT 9874
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_WRITABLE_FILES_TEXT 9875
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_WRITABLE_DIRECTORY_TEXT 9876
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_WRITABLE_DIRECTORIES_TEXT 9877
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_WRITABLE_FILES_AND_DIRECTORIES_TEXT 9878
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_READABLE_FILE_TEXT 9879
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READABLE_FILES_TEXT 9880
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SINGLE_READABLE_DIRECTORY_TEXT 9881
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READABLE_DIRECTORIES_TEXT 9882
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READABLE_FILES_AND_DIRECTORIES_TEXT 9883
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_READ_AND_WRITE 9884
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_SAVE_CHANGES 9885
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_VIEW_CHANGES 9886
#define IDS_FILE_SYSTEM_ACCESS_USAGE_BUBBLE_FILES_TEXT 9887
#define IDS_FILE_SYSTEM_ACCESS_USAGE_EXPAND 9888
#define IDS_FILE_SYSTEM_ACCESS_USAGE_COLLAPSE 9889
#define IDS_FILE_SYSTEM_ACCESS_USAGE_REMOVE_ACCESS 9890
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_DIRECTORY_TITLE 9891
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_DIRECTORY_TEXT 9892
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_DIRECTORY_BUTTON 9893
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_FILE_TITLE 9894
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_FILE_TEXT 9895
#define IDS_FILE_SYSTEM_ACCESS_RESTRICTED_FILE_BUTTON 9896
#define IDS_RELAUNCH_ACCEPT_BUTTON 9897
#define IDS_RELAUNCH_REQUIRED_CANCEL_BUTTON 9898
#define IDS_WEBAUTHN_GENERIC_TITLE 9899
#define IDS_WEBAUTHN_CONTINUE 9900
#define IDS_WEBAUTHN_TRANSPORT_SELECTION_TITLE 9901
#define IDS_WEBAUTHN_TRANSPORT_SELECTION_DESCRIPTION 9902
#define IDS_WEBAUTHN_TRANSPORT_USB 9903
#define IDS_WEBAUTHN_TRANSPORT_INTERNAL 9904
#define IDS_WEBAUTHN_TRANSPORT_CABLE 9905
#define IDS_WEBAUTHN_TRANSPORT_AOA 9906
#define IDS_WEBAUTHN_TRANSPORT_POPUP_DIFFERENT_AUTHENTICATOR_WIN 9907
#define IDS_WEBAUTHN_USB_ACTIVATE_DESCRIPTION 9908
#define IDS_WEBAUTHN_ERROR_GENERIC_TITLE 9909
#define IDS_WEBAUTHN_ERROR_WRONG_KEY_TITLE 9910
#define IDS_WEBAUTHN_ERROR_WRONG_KEY_REGISTER_DESCRIPTION 9911
#define IDS_WEBAUTHN_ERROR_WRONG_KEY_SIGN_DESCRIPTION 9912
#define IDS_WEBAUTHN_ERROR_TIMEOUT_DESCRIPTION 9913
#define IDS_WEBAUTHN_ERROR_INTERNAL_UNRECOGNIZED_TITLE 9914
#define IDS_WEBAUTHN_ERROR_INTERNAL_UNRECOGNIZED_DESCRIPTION 9915
#define IDS_WEBAUTHN_ERROR_NO_TRANSPORTS_TITLE 9916
#define IDS_WEBAUTHN_ERROR_NO_TRANSPORTS_DESCRIPTION 9917
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_AUTO_TITLE 9918
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_AUTO_DESCRIPTION 9919
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_AUTO_NEXT 9920
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_MANUAL_TITLE 9921
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_MANUAL_DESCRIPTION 9922
#define IDS_WEBAUTHN_BLUETOOTH_POWER_ON_MANUAL_NEXT 9923
#define IDS_WEBAUTHN_TRANSPORT_POPUP_LABEL 9924
#define IDS_WEBAUTHN_TRANSPORT_POPUP_USB 9925
#define IDS_WEBAUTHN_TRANSPORT_POPUP_PAIR_PHONE 9926
#define IDS_WEBAUTHN_TRANSPORT_POPUP_INTERNAL 9927
#define IDS_WEBAUTHN_TRANSPORT_POPUP_CABLE 9928
#define IDS_WEBAUTHN_TRANSPORT_POPUP_AOA 9929
#define IDS_WEBAUTHN_CABLE_ACTIVATE_TITLE 9930
#define IDS_WEBAUTHN_CABLE_ACTIVATE_TITLE_ALT 9931
#define IDS_WEBAUTHN_CABLE_ACTIVATE_DESCRIPTION 9932
#define IDS_WEBAUTHN_CABLEV2_SERVERLINK_DESCRIPTION 9933
#define IDS_WEBAUTHN_CABLEV2_SERVERLINK_TROUBLE 9934
#define IDS_WEBAUTHN_CABLEV2_SERVERLINK_TROUBLE_ALT 9935
#define IDS_WEBAUTHN_CABLEV2_AOA_TITLE 9936
#define IDS_WEBAUTHN_CABLEV2_AOA_DESCRIPTION 9937
#define IDS_WEBAUTHN_CABLEV2_AOA_REQUEST_DESCRIPTION 9938
#define IDS_WEBAUTHN_CABLEV2_2ND_FACTOR_DESCRIPTION 9939
#define IDS_WEBAUTHN_CABLEV2_ADD_PHONE 9940
#define IDS_WEBAUTHN_CABLE_ACTIVATE_DESCRIPTION_SHORT 9941
#define IDS_WEBAUTHN_CABLE_V2_ACTIVATE_TITLE 9942
#define IDS_WEBAUTHN_CABLE_V2_ACTIVATE_DESCRIPTION_SHORT 9943
#define IDS_WEBAUTHN_PIN_ENTRY_TITLE 9944
#define IDS_WEBAUTHN_PIN_ENTRY_DESCRIPTION 9945
#define IDS_WEBAUTHN_PIN_ENTRY_PIN_LABEL 9946
#define IDS_WEBAUTHN_PIN_ENTRY_NEXT 9947
#define IDS_WEBAUTHN_PIN_SETUP_DESCRIPTION 9948
#define IDS_WEBAUTHN_PIN_SETUP_CONFIRMATION_LABEL 9949
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_INVALID_CHARACTERS 9950
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_TOO_SHORT 9951
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_FAILED_RETRIES 9952
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_FAILED 9953
#define IDS_WEBAUTHN_PIN_SETUP_ERROR_FAILED 9954
#define IDS_WEBAUTHN_PIN_TAP_AGAIN_DESCRIPTION 9955
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_MISMATCH 9956
#define IDS_WEBAUTHN_CLIENT_PIN_SOFT_BLOCK_DESCRIPTION 9957
#define IDS_WEBAUTHN_CLIENT_PIN_HARD_BLOCK_DESCRIPTION 9958
#define IDS_WEBAUTHN_CLIENT_PIN_AUTHENTICATOR_REMOVED_DESCRIPTION 9959
#define IDS_WEBAUTHN_PIN_ENTRY_ERROR_SAME_AS_CURRENT 9960
#define IDS_WEBAUTHN_INLINE_ENROLLMENT_CANCEL_LABEL 9961
#define IDS_WEBAUTHN_UV_RETRY_TITLE 9962
#define IDS_WEBAUTHN_UV_RETRY_DESCRIPTION 9963
#define IDS_WEBAUTHN_UV_RETRY_ERROR_FAILED_RETRIES 9964
#define IDS_WEBAUTHN_UV_ERROR_LOCKED 9965
#define IDS_WEBAUTHN_FORCE_PIN_CHANGE 9966
#define IDS_WEBAUTHN_SELECT_ACCOUNT 9967
#define IDS_WEBAUTHN_UNKNOWN_ACCOUNT 9968
#define IDS_WEBAUTHN_RESIDENT_KEY_PRIVACY 9969
#define IDS_WEBAUTHN_RESIDENT_KEY_PREFERRED_PRIVACY 9970
#define IDS_WEBAUTHN_ERROR_MISSING_CAPABILITY_TITLE 9971
#define IDS_WEBAUTHN_ERROR_MISSING_CAPABILITY_DESC 9972
#define IDS_WEBAUTHN_STORAGE_FULL_DESC 9973
#define IDS_WEBAUTHN_REQUEST_ATTESTATION_PERMISSION_TITLE 9974
#define IDS_WEBAUTHN_REQUEST_ATTESTATION_PERMISSION_DESC 9975
#define IDS_WEBAUTHN_REQUEST_ENTERPRISE_ATTESTATION_PERMISSION_TITLE 9976
#define IDS_WEBAUTHN_REQUEST_ENTERPRISE_ATTESTATION_PERMISSION_DESC 9977
#define IDS_WEBAUTHN_ALLOW_ATTESTATION 9978
#define IDS_WEBAUTHN_DENY_ATTESTATION 9979
#define IDS_WEBAUTHN_RETRY 9980
#define IDS_WEBAUTHN_PLATFORM_AUTHENTICATOR_OFF_THE_RECORD_INTERSTITIAL_TITLE 9981
#define IDS_WEBAUTHN_PLATFORM_AUTHENTICATOR_OFF_THE_RECORD_INTERSTITIAL_DESCRIPTION 9982
#define IDS_WEBAUTHN_PLATFORM_AUTHENTICATOR_OFF_THE_RECORD_INTERSTITIAL_DENY 9983
#define IDS_WEBAUTHN_MANAGE_DEVICES 9984
#define IDS_ACCNAME_SIDE_SEARCH_TOOL 9985
#define IDS_ACCNAME_SIDE_SEARCH_TOOLBAR_BUTTON_ACTIVATED 9986
#define IDS_ACCNAME_SIDE_SEARCH_TOOLBAR_BUTTON_NOT_ACTIVATED 9987
#define IDS_ACCNAME_SIDE_SEARCH_CLOSE_BUTTON 9988
#define IDS_ACCNAME_SIDE_SEARCH_FEEDBACK_BUTTON 9989
#define IDS_SIDE_SEARCH_PROMO 9990
#define IDS_TOOLTIP_SIDE_SEARCH_TOOLBAR_BUTTON_ACTIVATED 9991
#define IDS_TOOLTIP_SIDE_SEARCH_TOOLBAR_BUTTON_NOT_ACTIVATED 9992
#define IDS_TOOLTIP_SIDE_SEARCH_CLOSE_BUTTON 9993
#define IDS_TOOLTIP_SIDE_SEARCH_FEEDBACK_BUTTON 9994
#define IDS_CABLEV2_MAKE_CREDENTIAL_NOTIFICATION_TITLE 9995
#define IDS_CABLEV2_GET_ASSERTION_NOTIFICATION_TITLE 9996
#define IDS_INCOGNITO_PROFILE_MENU_TITLE 9997
#define IDS_INCOGNITO_WINDOW_COUNT_MESSAGE 9998
#define IDS_INCOGNITO_PROFILE_MENU_CLOSE_BUTTON 9999
#define IDS_INCOGNITO_PROFILE_MENU_CLOSE_BUTTON_NEW 10000
#define IDS_INCOGNITO_CLEAR_BROWSING_DATA_DIALOG_PRIMARY_TEXT 10001
#define IDS_INCOGNITO_CLEAR_BROWSING_DATA_DIALOG_SECONDARY_TEXT 10002
#define IDS_INCOGNITO_CLEAR_BROWSING_DATA_DIALOG_CLOSE_WINDOWS_BUTTON 10003
#define IDS_INCOGNITO_HISTORY_BUBBLE_PRIMARY_TEXT 10004
#define IDS_INCOGNITO_HISTORY_BUBBLE_SECONDARY_TEXT 10005
#define IDS_INCOGNITO_HISTORY_BUBBLE_CLOSE_INCOGNITO_BUTTON_TEXT 10006
#define IDS_INCOGNITO_HISTORY_BUBBLE_CANCEL_BUTTON_TEXT 10007
#define IDS_GUEST_WINDOW_COUNT_MESSAGE 10008
#define IDS_GUEST_PROFILE_MENU_CLOSE_BUTTON 10009
#define IDS_HATS_BUBBLE_OK_LABEL 10010
#define IDS_HATS_BUBBLE_TEXT 10011
#define IDS_NOTIFICATION_DEFAULT_HELPFUL_BUTTON_TEXT 10012
#define IDS_NOTIFICATION_DEFAULT_UNHELPFUL_BUTTON_TEXT 10013
#define IDS_DEEP_SCANNING_DIALOG_TITLE 10014
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_PENDING_MESSAGE 10015
#define IDS_DEEP_SCANNING_DIALOG_PRINT_PENDING_MESSAGE 10016
#define IDS_DEEP_SCANNING_DIALOG_SUCCESS_MESSAGE 10017
#define IDS_DEEP_SCANNING_DIALOG_PRINT_SUCCESS_MESSAGE 10018
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_FAILURE_MESSAGE 10019
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_WARNING_MESSAGE 10020
#define IDS_DEEP_SCANNING_DIALOG_PRINT_WARNING_MESSAGE 10021
#define IDS_DEEP_SCANNING_DIALOG_TIMEOUT_MESSAGE 10022
#define IDS_DEEP_SCANNING_DIALOG_PROCEED_BUTTON 10023
#define IDS_DEEP_SCANNING_DIALOG_CANCEL_WARNING_BUTTON 10024
#define IDS_DEEP_SCANNING_DIALOG_CANCEL_UPLOAD_BUTTON 10025
#define IDS_DEEP_SCANNING_DIALOG_LARGE_FILE_FAILURE_MESSAGE 10026
#define IDS_DEEP_SCANNING_DIALOG_LARGE_PRINT_FAILURE_MESSAGE 10027
#define IDS_DEEP_SCANNING_DIALOG_ENCRYPTED_FILE_FAILURE_MESSAGE 10028
#define IDS_DEEP_SCANNING_DIALOG_CUSTOM_MESSAGE 10029
#define IDS_DEEP_SCANNING_DIALOG_DOWNLOADS_CUSTOM_MESSAGE 10030
#define IDS_DEEP_SCANNING_DIALOG_CUSTOM_MESSAGE_LEARN_MORE_LINK 10031
#define IDS_DEEP_SCANNING_DIALOG_DOWNLOADS_DISCARD_FILE_BUTTON 10032
#define IDS_DEEP_SCANNING_DIALOG_UPLOAD_BYPASS_JUSTIFICATION_LABEL 10033
#define IDS_DEEP_SCANNING_DIALOG_BYPASS_JUSTIFICATION_TEXT_LIMIT_LABEL 10034
#define IDS_DEEP_SCANNING_DIALOG_OPEN_NOW_TITLE 10035
#define IDS_DEEP_SCANNING_DIALOG_OPEN_NOW_MESSAGE 10036
#define IDS_DEEP_SCANNING_DIALOG_OPEN_NOW_ACCEPT_BUTTON 10037
#define IDS_DEEP_SCANNING_INFO_DIALOG_TITLE 10038
#define IDS_APP_DEEP_SCANNING_INFO_DIALOG_MESSAGE 10039
#define IDS_DEEP_SCANNING_INFO_DIALOG_MESSAGE 10040
#define IDS_DEEP_SCANNING_INFO_DIALOG_ACCEPT_BUTTON 10041
#define IDS_DEEP_SCANNING_INFO_DIALOG_CANCEL_BUTTON 10042
#define IDS_DEEP_SCANNING_INFO_DIALOG_OPEN_NOW_BUTTON 10043
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_TITLE 10044
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_MESSAGE 10045
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_ACCEPT_BUTTON 10046
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_CANCEL_BUTTON 10047
#define IDS_DEEP_SCANNING_TIMED_OUT_DIALOG_OPEN_NOW_BUTTON 10048
#define IDS_TAILORED_SECURITY_UNCONSENTED_MODAL_TITLE 10049
#define IDS_TAILORED_SECURITY_UNCONSENTED_ACCEPT_BUTTON 10050
#define IDS_TAILORED_SECURITY_UNCONSENTED_CANCEL_BUTTON 10051
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_MESSAGE_TITLE 10052
#define IDS_TAILORED_SECURITY_CONSENTED_ENABLE_MESSAGE_DESCRIPTION 10053
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_MESSAGE_TITLE 10054
#define IDS_TAILORED_SECURITY_CONSENTED_DISABLE_MESSAGE_DESCRIPTION 10055
#define IDS_TAILORED_SECURITY_CONSENTED_MESSAGE_OK_BUTTON 10056
#define IDS_FILE_SYSTEM_CONNECTOR_BOX 10057
#define IDS_FILE_SYSTEM_CONNECTOR_GOOGLE_DRIVE 10058
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_TITLE 10059
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_MESSAGE 10060
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_CANCEL_BUTTON 10061
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_CONFIRM_ACCEPT_BUTTON 10062
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_TITLE 10063
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_MESSAGE 10064
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_CANCEL_BUTTON 10065
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_REQUIRED_ACCEPT_BUTTON 10066
#define IDS_FILE_SYSTEM_CONNECTOR_SIGNIN_DIALOG_TITLE 10067
#define IDS_PROMPT_APP_UNINSTALL_TITLE 10068
#define IDS_APP_UNINSTALL_PROMPT_REMOVE_DATA_CHECKBOX_FOR_NON_GOOGLE 10069
#define IDS_APP_UNINSTALL_PROMPT_REMOVE_DATA_CHECKBOX_FOR_GOOGLE 10070
#define IDS_APP_UNINSTALL_PROMPT_LEARN_MORE 10071
#define IDS_UTILITY_PROCESS_SPEECH_RECOGNITION_SERVICE_NAME 10072
#define IDS_APP_PAUSE_PROMPT_TITLE 10073
#define IDS_APP_PAUSE_HEADING 10074
#define IDS_APP_PAUSE_HEADING_FOR_WEB_APPS 10075
#define IDS_APP_BLOCK_PROMPT_TITLE 10076
#define IDS_APP_BLOCK_HEADING_FOR_CHILD 10077
#define IDS_APP_BLOCK_HEADING 10078
#define IDS_ENTERPRISE_EXTENSION_REQUEST_APPROVED_TITLE 10079
#define IDS_ENTERPRISE_EXTENSION_REQUEST_REJECTED_TITLE 10080
#define IDS_ENTERPRISE_EXTENSION_REQUEST_FORCE_INSTALLED_TITLE 10081
#define IDS_ENTERPRISE_EXTENSION_REQUEST_CLICK_TO_INSTALL 10082
#define IDS_ENTERPRISE_EXTENSION_REQUEST_CLICK_TO_VIEW 10083
#define IDS_ENTERPRISE_EXTENSION_REQUEST_JUSTIFICATION 10084
#define IDS_ENTERPRISE_EXTENSION_REQUEST_JUSTIFICATION_PLACEHOLDER 10085
#define IDS_ENTERPRISE_EXTENSION_REQUEST_JUSTIFICATION_LENGTH_LIMIT 10086
#define IDS_NAME_WINDOW_PROMPT_TITLE 10087
#define IDS_QUICK_COMMANDS_LABEL 10088
#define IDS_QUICK_COMMANDS_PLACEHOLDER 10089
#define IDS_QUICK_COMMANDS_NO_RESULTS 10090
#define IDS_CHROMELABS_RELAUNCH_BUTTON_LABEL 10091
#define IDS_WINDOW_TITLE_EXPERIMENTS 10092
#define IDS_CHROMELABS_SEND_FEEDBACK 10093
#define IDS_CHROMELABS_SEND_FEEDBACK_DESCRIPTION_PLACEHOLDER 10094
#define IDS_CHROMELABS_DEFAULT 10095
#define IDS_CHROMELABS_ENABLED 10096
#define IDS_CHROMELABS_DISABLED 10097
#define IDS_CHROMELABS_ENABLED_WITH_VARIATION_NAME 10098
#define IDS_TAB_SEARCH_MEDIA_TABS_EXPERIMENT_NAME 10099
#define IDS_TAB_SEARCH_MEDIA_TABS_EXPERIMENT_DESCRIPTION 10100
#define IDS_MEDIA_TABS_ALSO_SHOWN_IN_OPEN_TABS_SECTION 10101
#define IDS_SIDE_PANEL_EXPERIMENT_NAME 10102
#define IDS_SIDE_PANEL_EXPERIMENT_DESCRIPTION 10103
#define IDS_TAB_SCROLLING_EXPERIMENT_NAME 10104
#define IDS_TAB_SCROLLING_EXPERIMENT_DESCRIPTION 10105
#define IDS_TABS_SHRINK_TO_PINNED_TAB_WIDTH 10106
#define IDS_TABS_SHRINK_TO_MEDIUM_WIDTH 10107
#define IDS_TABS_SHRINK_TO_LARGE_WIDTH 10108
#define IDS_TABS_DO_NOT_SHRINK 10109
#define IDS_TAB_SEARCH_EXPERIMENT_NAME 10110
#define IDS_TAB_SEARCH_EXPERIMENT_DESCRIPTION 10111
#define IDS_LENS_REGION_SEARCH_EXPERIMENT_NAME 10112
#define IDS_LENS_REGION_SEARCH_EXPERIMENT_DESCRIPTION 10113
#define IDS_LENS_REGION_SEARCH_BUBBLE_TEXT 10114
#define IDS_THUMBNAIL_TAB_STRIP_EXPERIMENT_NAME 10115
#define IDS_THUMBNAIL_TAB_STRIP_EXPERIMENT_DESCRIPTION 10116
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_NOTIFICATION_DESCRIPTION 10119
#define IDS_TAILORED_SECURITY_UNCONSENTED_PROMOTION_NOTIFICATION_ACCEPT 10120
#define IDS_ACCOUNT_SELECTION_SHEET_TITLE_EXPLICIT 10121
#define IDS_ACCOUNT_SELECTION_CONTINUE 10122
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT_NO_PP_OR_TOS 10123
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT_NO_PP 10124
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT_NO_TOS 10125
#define IDS_ACCOUNT_SELECTION_DATA_SHARING_CONSENT 10126
#define IDS_VERIFY_SHEET_TITLE 10127
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_TITLE 10145
#define IDS_PRIVACY_SANDBOX_BUBBLE_NOTICE_TITLE 10146
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_HEADER_1 10147
#define IDS_PRIVACY_SANDBOX_BUBBLE_NOTICE_DESCRIPTION 10148
#define IDS_PRIVACY_SANDBOX_BUBBLE_NOTICE_DESCRIPTION_ESTIMATES_INTERESTS_LINK 10149
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_DESCRIPTION_1 10150
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_BODY_HEADER_2 10151
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_HEADER 10152
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_LEARN_MORE_SECTION_2_BULLET_POINT_3 10153
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_ACCEPT_BUTTON 10154
#define IDS_PRIVACY_SANDBOX_DIALOG_CONSENT_DECLINE_BUTTON 10155
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_HEADER_1 10156
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_DESCRIPTION_1 10157
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_BODY_HEADER_2 10158
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_ACKNOWLEDGE_BUTTON 10159
#define IDS_PRIVACY_SANDBOX_DIALOG_NOTICE_OPEN_SETTINGS_BUTTON 10160

// ---------------------------------------------------------------------------
// From locale_settings.h:

#define IDS_SPELLCHECK_DICTIONARY 145
#define IDS_EDITBOOKMARK_DIALOG_WIDTH_CHARS 12000
#define IDS_EDITBOOKMARK_DIALOG_HEIGHT_LINES 12001
#define IDS_THEMES_GALLERY_URL 12002
#define IDS_WEBSTORE_URL 173
#define IDS_MEDIA_GALLERIES_DIALOG_CONTENT_WIDTH_CHARS 12003
#define IDS_SETTINGS_CLEAR_DATA_MYACTIVITY_URL_IN_DIALOG 12005
#define IDS_SETTINGS_CLEAR_DATA_MYACTIVITY_URL_IN_HISTORY 12006

// ---------------------------------------------------------------------------
// From omnibox_resources.h:

#define IDR_OMNIBOX_PEDAL_CONCEPTS 27640

// ---------------------------------------------------------------------------
// From platform_locale_settings.h:

#define IDS_STANDARD_FONT_FAMILY 106
#define IDS_FIXED_FONT_FAMILY 107
#define IDS_FIXED_FONT_FAMILY_ALT_WIN 108
#define IDS_SERIF_FONT_FAMILY 109
#define IDS_SANS_SERIF_FONT_FAMILY 110
#define IDS_NTP_FONT_FAMILY 12100
#define IDS_CURSIVE_FONT_FAMILY 111
#define IDS_FANTASY_FONT_FAMILY 112
#define IDS_MATH_FONT_FAMILY 365
#define IDS_STANDARD_FONT_FAMILY_CYRILLIC 131
#define IDS_FIXED_FONT_FAMILY_ARABIC 129
#define IDS_FIXED_FONT_FAMILY_CYRILLIC 132
#define IDS_SANS_SERIF_FONT_FAMILY_ARABIC 130
#define IDS_SERIF_FONT_FAMILY_CYRILLIC 133
#define IDS_SANS_SERIF_FONT_FAMILY_CYRILLIC 134
#define IDS_STANDARD_FONT_FAMILY_GREEK 135
#define IDS_FIXED_FONT_FAMILY_GREEK 136
#define IDS_SERIF_FONT_FAMILY_GREEK 137
#define IDS_SANS_SERIF_FONT_FAMILY_GREEK 138
#define IDS_STANDARD_FONT_FAMILY_JAPANESE 114
#define IDS_FIXED_FONT_FAMILY_JAPANESE 115
#define IDS_SERIF_FONT_FAMILY_JAPANESE 116
#define IDS_SANS_SERIF_FONT_FAMILY_JAPANESE 117
#define IDS_STANDARD_FONT_FAMILY_KOREAN 118
#define IDS_FIXED_FONT_FAMILY_KOREAN 139
#define IDS_SERIF_FONT_FAMILY_KOREAN 119
#define IDS_SANS_SERIF_FONT_FAMILY_KOREAN 120
#define IDS_CURSIVE_FONT_FAMILY_KOREAN 140
#define IDS_STANDARD_FONT_FAMILY_SIMPLIFIED_HAN 121
#define IDS_FIXED_FONT_FAMILY_SIMPLIFIED_HAN 141
#define IDS_SERIF_FONT_FAMILY_SIMPLIFIED_HAN 122
#define IDS_SANS_SERIF_FONT_FAMILY_SIMPLIFIED_HAN 123
#define IDS_CURSIVE_FONT_FAMILY_SIMPLIFIED_HAN 127
#define IDS_STANDARD_FONT_FAMILY_TRADITIONAL_HAN 124
#define IDS_FIXED_FONT_FAMILY_TRADITIONAL_HAN 142
#define IDS_SERIF_FONT_FAMILY_TRADITIONAL_HAN 125
#define IDS_SANS_SERIF_FONT_FAMILY_TRADITIONAL_HAN 126
#define IDS_CURSIVE_FONT_FAMILY_TRADITIONAL_HAN 128
#define IDS_MINIMUM_FONT_SIZE 143
#define IDS_MINIMUM_LOGICAL_FONT_SIZE 144

// ---------------------------------------------------------------------------
// From services_strings.h:

#define IDS_PROXY_RESOLVER_DISPLAY_NAME 300
#define IDS_WINDOWS_SYSTEM_PROXY_RESOLVER_DISPLAY_NAME 31400

// ---------------------------------------------------------------------------
// From ui_strings.h:

#define IDS_TIME_SECS 35920
#define IDS_TIME_LONG_SECS 35921
#define IDS_TIME_LONG_SECS_2ND 35922
#define IDS_TIME_MINS 35923
#define IDS_TIME_LONG_MINS 35924
#define IDS_TIME_LONG_MINS_1ST 35925
#define IDS_TIME_LONG_MINS_2ND 35926
#define IDS_TIME_HOURS 35927
#define IDS_TIME_HOURS_1ST 35928
#define IDS_TIME_HOURS_2ND 35929
#define IDS_TIME_DAYS 35930
#define IDS_TIME_DAYS_1ST 35931
#define IDS_TIME_MONTHS 35932
#define IDS_TIME_YEARS 35933
#define IDS_TIME_REMAINING_SECS 35934
#define IDS_TIME_REMAINING_LONG_SECS 35935
#define IDS_TIME_REMAINING_MINS 35936
#define IDS_TIME_REMAINING_LONG_MINS 35937
#define IDS_TIME_REMAINING_HOURS 35938
#define IDS_TIME_REMAINING_DAYS 35939
#define IDS_TIME_REMAINING_MONTHS 35940
#define IDS_TIME_REMAINING_YEARS 35941
#define IDS_TIME_ELAPSED_SECS 35942
#define IDS_TIME_ELAPSED_LONG_SECS 35943
#define IDS_TIME_ELAPSED_MINS 35944
#define IDS_TIME_ELAPSED_LONG_MINS 35945
#define IDS_TIME_ELAPSED_HOURS 35946
#define IDS_TIME_ELAPSED_DAYS 35947
#define IDS_TIME_ELAPSED_MONTHS 35948
#define IDS_TIME_ELAPSED_YEARS 35949
#define IDS_PAST_TIME_TODAY 35950
#define IDS_PAST_TIME_YESTERDAY 35951
#define IDS_APP_MENU_EMPTY_SUBMENU 363
#define IDS_CLIPBOARD_MENU_HTML 35952
#define IDS_CLIPBOARD_MENU_IMAGE 35953
#define IDS_CLIPBOARD_MENU_RTF_CONTENT 35954
#define IDS_CLIPBOARD_MENU_WEB_SMART_PASTE 35955
#define IDS_CLIPBOARD_MENU_CLIPBOARD 35956
#define IDS_CLIPBOARD_MENU_DELETE_ALL 35957
#define IDS_EXTENSION_PINNED 35958
#define IDS_EXTENSION_UNPINNED 35959
#define IDS_NEW_BADGE 35960
#define IDS_NEW_BADGE_SCREEN_READER_MESSAGE 35961
#define IDS_SENTENCE_END 35962
#define IDS_CONCAT_TWO_STRINGS_WITH_COMMA 35963
#define IDS_CONCAT_TWO_STRINGS_WITH_PERIODS 35964
#define IDS_APP_UNTITLED_SHORTCUT_FILE_NAME 35965
#define IDS_APP_SAVEAS_ALL_FILES 35966
#define IDS_APP_SAVEAS_EXTENSION_FORMAT 35967
#define IDS_SELECT_UPLOAD_FOLDER_DIALOG_TITLE 35968
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_MENU 35976
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_DEFAULT 35977
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_LTR 35978
#define IDS_CONTENT_CONTEXT_WRITING_DIRECTION_RTL 35979
#define IDS_SELECT_FOLDER_DIALOG_TITLE 35980
#define IDS_SAVE_AS_DIALOG_TITLE 35981
#define IDS_OPEN_FILE_DIALOG_TITLE 35982
#define IDS_OPEN_FILES_DIALOG_TITLE 35983
#define IDS_SAVEAS_ALL_FILES 35984
#define IDS_SELECT_UPLOAD_FOLDER_DIALOG_UPLOAD_BUTTON 35985
#define IDS_APP_ACCNAME_BACK 35986
#define IDS_APP_ACCNAME_CENTER 35987
#define IDS_APP_ACCNAME_CLOSE 249
#define IDS_APP_ACCNAME_MINIMIZE 246
#define IDS_APP_ACCNAME_MAXIMIZE 247
#define IDS_APP_ACCNAME_RESTORE 248
#define IDS_APP_ACCNAME_MENU 35988
#define IDS_APP_ACCNAME_FLOAT 35989
#define IDS_APP_ACCNAME_COLOR_CHOOSER_HEX_INPUT 35990
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLHERE 35991
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLLEFTEDGE 35992
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLRIGHTEDGE 35993
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLHOME 35994
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLEND 35995
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLPAGEUP 35996
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLPAGEDOWN 35997
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLLEFT 35998
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLRIGHT 35999
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLUP 36000
#define IDS_APP_SCROLLBAR_CXMENU_SCROLLDOWN 36001
#define IDS_APP_TABLE_COLUMN_NOT_SORTED_ACCNAME 36002
#define IDS_APP_TABLE_COLUMN_SORTED_ASC_ACCNAME 36003
#define IDS_APP_TABLE_COLUMN_SORTED_DESC_ACCNAME 36004
#define IDS_APP_UNDO 36005
#define IDS_APP_CUT 36006
#define IDS_APP_COPY 250
#define IDS_APP_PASTE 36007
#define IDS_APP_DELETE 36008
#define IDS_APP_SELECT_ALL 251
#define IDS_CONTENT_CONTEXT_EMOJI 36009
#define IDS_APP_OK 36011
#define IDS_APP_CANCEL 36012
#define IDS_APP_CLOSE 36013
#define IDS_APP_ESC_KEY 36014
#define IDS_APP_TAB_KEY 36015
#define IDS_APP_INSERT_KEY 36016
#define IDS_APP_HOME_KEY 36017
#define IDS_APP_DELETE_KEY 36018
#define IDS_APP_END_KEY 36019
#define IDS_APP_PAGEUP_KEY 36020
#define IDS_APP_PAGEDOWN_KEY 36021
#define IDS_APP_LEFT_ARROW_KEY 36022
#define IDS_APP_RIGHT_ARROW_KEY 36023
#define IDS_APP_UP_ARROW_KEY 36024
#define IDS_APP_DOWN_ARROW_KEY 36025
#define IDS_APP_ENTER_KEY 36026
#define IDS_APP_SPACE_KEY 36027
#define IDS_APP_F1_KEY 36028
#define IDS_APP_F5_KEY 36029
#define IDS_APP_F6_KEY 36030
#define IDS_APP_F11_KEY 359
#define IDS_APP_BACKSPACE_KEY 36031
#define IDS_APP_COMMA_KEY 36032
#define IDS_APP_PERIOD_KEY 36033
#define IDS_APP_MEDIA_NEXT_TRACK_KEY 36034
#define IDS_APP_MEDIA_PLAY_PAUSE_KEY 36035
#define IDS_APP_MEDIA_PREV_TRACK_KEY 36036
#define IDS_APP_MEDIA_STOP_KEY 36037
#define IDS_APP_ALT_KEY 36038
#define IDS_APP_COMMAND_KEY 36039
#define IDS_APP_CTRL_KEY 361
#define IDS_APP_SEARCH_KEY 36040
#define IDS_APP_SHIFT_KEY 364
#define IDS_APP_WINDOWS_KEY 36041
#define IDS_APP_ACCELERATOR_WITH_MODIFIER 362
#define IDS_APP_BYTES 36045
#define IDS_APP_KIBIBYTES 36046
#define IDS_APP_MEBIBYTES 36047
#define IDS_APP_GIBIBYTES 36048
#define IDS_APP_TEBIBYTES 36049
#define IDS_APP_PEBIBYTES 36050
#define IDS_APP_BYTES_PER_SECOND 36051
#define IDS_APP_KIBIBYTES_PER_SECOND 36052
#define IDS_APP_MEBIBYTES_PER_SECOND 36053
#define IDS_APP_GIBIBYTES_PER_SECOND 36054
#define IDS_APP_TEBIBYTES_PER_SECOND 36055
#define IDS_APP_PEBIBYTES_PER_SECOND 36056
#define IDS_MESSAGE_CENTER_ACCESSIBLE_NAME 36057
#define IDS_MESSAGE_CENTER_NOTIFICATION_ACCESSIBLE_NAME 36058
#define IDS_MESSAGE_CENTER_NOTIFICATION_ACCESSIBLE_NAME_PLURAL 36059
#define IDS_MESSAGE_CENTER_EXPAND_NOTIFICATION 36060
#define IDS_MESSAGE_CENTER_COLLAPSE_NOTIFICATION 36061
#define IDS_MESSAGE_CENTER_LIST_NOTIFICATION_MESSAGE_WITH_DIVIDER 36062
#define IDS_MESSAGE_CENTER_LIST_NOTIFICATION_HEADER_OVERFLOW_INDICATOR 36063
#define IDS_MESSAGE_CENTER_NOTIFICATION_PROGRESS_PERCENTAGE 36064
#define IDS_MESSAGE_CENTER_NOTIFICATION_CHROMEOS_SYSTEM 36065
#define IDS_MESSAGE_CENTER_NOTIFICATION_INLINE_REPLY_PLACEHOLDER 36066
#define IDS_MESSAGE_NOTIFICATION_NOW_STRING_SHORTEST 36067
#define IDS_MESSAGE_NOTIFICATION_DURATION_MINUTES_SHORTEST 36068
#define IDS_MESSAGE_NOTIFICATION_DURATION_HOURS_SHORTEST 36069
#define IDS_MESSAGE_NOTIFICATION_DURATION_DAYS_SHORTEST 36070
#define IDS_MESSAGE_NOTIFICATION_DURATION_YEARS_SHORTEST 36071
#define IDS_MESSAGE_NOTIFICATION_DURATION_MINUTES_SHORTEST_FUTURE 36072
#define IDS_MESSAGE_NOTIFICATION_DURATION_HOURS_SHORTEST_FUTURE 36073
#define IDS_MESSAGE_NOTIFICATION_DURATION_DAYS_SHORTEST_FUTURE 36074
#define IDS_MESSAGE_NOTIFICATION_DURATION_YEARS_SHORTEST_FUTURE 36075
#define IDS_MESSAGE_CENTER_BLOCK_ALL_NOTIFICATIONS_SITE 36076
#define IDS_MESSAGE_CENTER_BLOCK_ALL_NOTIFICATIONS_APP 36077
#define IDS_MESSAGE_CENTER_BLOCK_ALL_NOTIFICATIONS 36078
#define IDS_MESSAGE_CENTER_DONT_BLOCK_NOTIFICATIONS 36079
#define IDS_MESSAGE_CENTER_SETTINGS_DONE 36080
#define IDS_MESSAGE_CENTER_CLOSE_NOTIFICATION_BUTTON_ACCESSIBLE_NAME 36081
#define IDS_MESSAGE_CENTER_CLOSE_NOTIFICATION_BUTTON_TOOLTIP 36082
#define IDS_MESSAGE_CENTER_NOTIFICATION_SNOOZE_BUTTON_TOOLTIP 36083
#define IDS_MESSAGE_NOTIFICATION_SETTINGS_BUTTON_ACCESSIBLE_NAME 36084
#define IDS_MESSAGE_NOTIFICATION_ACCESSIBLE_NAME 36085
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_DEVICE_INFO 36086
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_CONFIRMATION_SUCCESS 36087
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_CONFIRMATION_FAILURE_TITLE 36088
#define IDS_MESSAGE_NOTIFICATION_SEND_TAB_TO_SELF_CONFIRMATION_FAILURE_MESSAGE 36089
#define IDS_CLIPBOARD_HISTORY_MENU_PNG_IMAGE 36091
#define IDS_CLIPBOARD_HISTORY_MENU_HTML_IMAGE 36092
#define IDS_CLIPBOARD_HISTORY_MENU_TITLE 36093
#define IDS_CLIPBOARD_HISTORY_DELETE_BUTTON 36094
#define IDS_CLIPBOARD_HISTORY_ITEM_DELETION 36095
#define IDS_DISPLAY_TOUCH_CALIBRATION_EXIT_LABEL 36096
#define IDS_DISPLAY_TOUCH_CALIBRATION_HINT_LABEL_TEXT 36097
#define IDS_DISPLAY_TOUCH_CALIBRATION_HINT_SUBLABEL_TEXT 36098
#define IDS_DISPLAY_TOUCH_CALIBRATION_TAP_HERE_LABEL 36099
#define IDS_DISPLAY_TOUCH_CALIBRATION_FINISH_LABEL 36100
#define IDS_DISPLAY_NAME_UNKNOWN 36101
#define IDS_DISPLAY_NAME_INTERNAL 36102
#define IDS_CROSTINI_USE_LOW_DENSITY 36103
#define IDS_CROSTINI_USE_HIGH_DENSITY 36104
#define IDS_CROSTINI_APP_RESTART_BODY 36105
#define IDS_SATURATED_BADGE_CONTENT 36106
#define IDS_BADGE_UNREAD_NOTIFICATIONS_SATURATED 36107
#define IDS_BADGE_UNREAD_NOTIFICATIONS_UNSPECIFIED 36108
#define IDS_BADGE_UNREAD_NOTIFICATIONS 36109
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_TITLE_LABEL 36110
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_TITLE_NO_DEVICES 36111
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_CALL_BUTTON_LABEL 36112
#define IDS_BROWSER_SHARING_OMNIBOX_SENDING_LABEL 36113
#define IDS_BROWSER_SHARING_DIALOG_DEVICE_SUBTITLE_LAST_ACTIVE_DAYS 36114
#define IDS_BROWSER_SHARING_CLICK_TO_CALL_DIALOG_INITIATING_ORIGIN 36115
#define IDS_BROWSER_SHARING_CONTENT_TYPE_TEXT 36116
#define IDS_BROWSER_SHARING_CONTENT_TYPE_NUMBER 36117
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TITLE_GENERIC_ERROR 36118
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TITLE_INTERNAL_ERROR 36119
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_NETWORK_ERROR 36120
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_DEVICE_ACK_TIMEOUT 36121
#define IDS_BROWSER_SHARING_ERROR_DIALOG_TEXT_INTERNAL_ERROR 36122
#define IDS_BROWSER_SHARING_SHARED_CLIPBOARD_ERROR_DIALOG_TITLE_PAYLOAD_TOO_LARGE 36123
#define IDS_BROWSER_SHARING_SHARED_CLIPBOARD_ERROR_DIALOG_TEXT_PAYLOAD_TOO_LARGE 36124
#define IDS_SETTINGS_PASSWORD_SHOW 36128
#define IDS_SETTINGS_PASSWORD_HIDE 36129
#define IDS_TABLE_VIEW_AX_ANNOUNCE_ROW_SELECTED 36130

#endif  // CEF_INCLUDE_CEF_PACK_STRINGS_H_
